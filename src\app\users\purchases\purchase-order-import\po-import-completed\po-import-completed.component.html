<div class="page-filters">
    <div class="page-filters-left">
        <div class="form-group form-group-sm filter-search">
            <div class="form-group-icon-start">
                <i class="th th-outline-search-normal-1 icon-broder "></i>
                <input type="text" class="form-control" placeholder="Search by Container ID" (change)="onSearch($event)"
                    [(ngModel)]="paginationRequest.searchByPoContainer">
            </div>
        </div>
        <div class="form-group form-group-sm filter-search">
            <div class="form-group-icon-start">
                <i class="th th-outline-search-normal-1 icon-broder "></i>
                <input type="text" class="form-control" placeholder="Search by Marka" (change)="onSearchMarka($event)"
                    [(ngModel)]="paginationRequest.searchText">
            </div>
        </div>
        <div class="form-group form-group-sm date-range-filter w-25">
            <div class="form-group-icon-end">
                <i (click)="open()" class="th th-outline-calendar ngx-daterangepicker-action"></i>
                <input pickerDirective class="form-control" readonly ngxDaterangepickerMd
                    [formControl]="paginationRequest.dateRangeControl" [showCustomRangeLabel]="true"
                    [alwaysShowCalendars]="true" [ranges]="utilsService.ranges" [linkedCalendars]="false"
                    [showClearButton]="false" placeholder="Loaded Date" [autoApply]="true"
                    [showRangeLabelOnInput]="true" startKey="start" endKey="end" [closeOnAutoApply]="true">
            </div>
        </div>
        <!-- <div class="form-group theme-ngselect form-group-sm">
            <ng-select (change)="onChangeActive()" placeholder="Supplier" [multiple]="false" [clearable]="true"
                [items]="dropdown?.supplierDropdown" bindLabel="displayName" bindValue="id"
                [(ngModel)]="paginationRequest.supplierId">
            </ng-select>
        </div> -->
        <!-- <div class="form-group theme-ngselect form-group-sm">
            <ng-select placeholder="Payment Status" [multiple]="false" [clearable]="false" [items]="[]" bindLabel="name"
                bindValue="id">
            </ng-select>
        </div> -->
        <div class="form-group theme-ngselect form-group-sm">
            <ng-select (change)="onChangeImporter()" placeholder="Importer" [multiple]="false" [clearable]="true"
                [items]="dropdown?.importer" bindLabel="label" bindValue="value"
                [(ngModel)]="paginationRequest.importerId">
            </ng-select>
        </div>
        <button (click)="onClear.emit()" class="btn btn-link btn-sm">Clear</button>


    </div>
    <div class="page-filters-right">
    </div>
</div>

<div class="card card-theme card-table-sticky3">
    <div class="card-body p-0">
        <div class="table-responsive ">
            <table class="table-theme table-hover table table-bordered tbl-collapse  table-sticky">
                <thead class="border-less">
                    <tr class="">
                        <th>
                            # Container No
                        </th>
                        <th>Loaded Date</th>
                        <th>Cartons</th>
                        <th>Delivery Date</th>
                        <th>CHA - Importer</th>
                        <th>Total Payment</th>
                        <th>Payment Status</th>
                        <th class="text-end">Action</th>
                    </tr>
                </thead>
                <tbody>
                    <ng-container *ngFor="let item of poImportList; index as i; trackBy: trackBy">
                        <tr [ngClass]="{'tbl-bg-secondary-two': item.isExpand}" (click)="toggleExpand(i)">
                            <td class=" tbl-user">
                                <div class="tbl-user-checkbox-srno">
                                    <span>{{(i + 1) | padNum}}.</span>
                                    <b class="text-black">
                                        {{item.containerName}}
                                    </b>
                                </div>
                            </td>
                            <td>{{item.loadedDate | date: 'dd/MM/yyyy'}}</td>
                            <td>{{item.totalLoadedCarton}}</td>
                            <td>{{item.expectedDeliveryDate | date: 'dd/MM/yyyy'}}</td>
                            <td>{{item.importerName}}</td>
                            <td>-</td>
                            <td>-</td>
                            <td class="tbl-action" (click)="$event.stopPropagation()">
                                <div class="tbl-action-group justify-content-end">
                                    <button (click)="toggleExpand(i)" class="btn btn-xs text-color btn-icon btn-link"
                                        data-bs-toggle="collapse" [ngClass]="{'collapse-arrow': item.isExpand}"
                                        role="button" aria-expanded="false" [attr.data.target]="'#table-collapse-2'+ i"
                                        [attr.aria-controls]="'table-collapse-2'+ i">
                                        <i class="th th-outline-arrow-right-3"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>

                        <tr *ngIf="item.isExpand" class="collapse" [id]="'table-collapse-2' + i"
                            [ngClass]="{'show': item.isExpand}">
                            <td colspan="30" class="p-0 tbl-collapse-child tbl-collapse-child-responsive">

                                <div class="table-responsive">
                                    <table class="table-theme table-hover table table-bordered table-sticky">
                                        <thead class="border-less">

                                            <tr>
                                                <ng-container
                                                    *ngFor="let th of headerObj?.optionsArray | filterByShippingType: item?.shippingTypes?.value; index as k">
                                                    <th *ngIf="th.show" [class]="th.class" [innerHTML]="th.displayName">
                                                    </th>
                                                </ng-container>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr
                                                *ngFor="let child of item.poImportItemList; index as l; trackBy: trackByChild">
                                                <ng-container *ngFor="let column of headerObj.columnArr | filterByShippingType: item?.shippingTypes?.value;">
                                                    <td class="tbl-user" *ngIf="column.show">
                                                        <ng-container [ngSwitch]="column.key">
                                                
                                                            <ng-container *ngSwitchCase="0">
                                                                <div class="tbl-user-checkbox-srno">
                                                                    <span>{{(l + 1) | padNum}}.</span>
                                                                    <div class="tbl-user-wrapper">
                                                                        <div class="tbl-user-image" *ngIf="child?.item">
                                                                            <img *ngIf="child.item?.formattedName" loading="lazy"
                                                                                [src]="child.item.formattedName ? (utilsService.imgPath + child.item.formattedName) : ''"
                                                                                alt="valamji">
                                                                            <ng-container *ngIf="!child.item?.formattedName">{{
                                                                                child.displayName?.charAt(0).toUpperCase()
                                                                                }}
                                                                            </ng-container>
                                                                        </div>
                                                                        <div class="tbl-user-text-action">
                                                                            <div class="tbl-user-text">
                                                                                <p>{{child.item?.skuId}}</p>
                                                                                <span class="tbl-description">{{child.item.displayName}}</span>
                                                                            </div>
                                                                        </div>
                                                                        <div class="dropdown" *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.VIEW_ITEM])">
                                                                            <button class="btn btn-xs btn-light-white btn-icon" id="actionDropDown" data-bs-toggle="dropdown"
                                                                                aria-expanded="false" data-bs-popper-config='{"strategy":"fixed"}' ngbTooltip="More Option" placement="bottom"
                                                                                container="body" triggers="hover" ngbTooltip="More Option" placement="bottom" container="body" triggers="hover">
                                                                                <i class="th th-outline-more"></i>
                                                                            </button>
                                                                            <ul class="dropdown-menu" aria-labelledby="actionDropDown">
                                                                                <li [pageAccess]="{page: utilsService.enumForPage.ITEM, action: utilsService.enumForPage.VIEW_ITEM}">
                                                                                    <a class="dropdown-item" (click)="utilsService.openItemDetailsInNewTab(child.itemId)">
                                                                                        <i class="th th-outline-eye"></i>View Item Details
                                                                                    </a>    
                                                                                </li>
                                                                            </ul>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="1">
                                                                <span>-</span>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="2">
                                                                <span class="d-flex flex-column align-items-start">
                                                                    <div>{{child.marka ? child.marka : '-'}}</div>
                                                                    <div>
                                                                        <ng-container>
                                                                            {{child.cartonLength ? child.cartonLength :
                                                                            '-'}} X
                                                                            {{child.cartonWidth ? child.cartonWidth :
                                                                            '-'}} X
                                                                            {{child.cartonHeight ? child.cartonHeight :
                                                                            '-'}}
                                                                            {{child?.cartonDimensionUnit ? child?.cartonDimensionUnit?.shortCode : ''}}
                                                                        </ng-container>
                                                                    </div>
                                                                    <div>
                                                                        <p>{{child.pricePerCarton ? child.pricePerCarton
                                                                            : '-'}}</p>
                                                                    </div>
                                                                    <div>
                                                                        <p class="tbl-po-notes">{{child.chinaComment ?
                                                                            child.chinaComment : ''}}</p>
                                                                    </div>
                                                                </span>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="3">
                                                                <span class="w-100 d-block"
                                                                    *ngFor="let v of child.colorName">
                                                                    {{!utilsService.isEmptyObjectOrNullUndefined(v) ? v
                                                                    : ''}}
                                                                </span>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="4">
                                                                <div class="tbl-po-notes">
                                                                    {{child.note ? child.note : '-'}}
                                                                </div>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="5">
                                                                <div class="tbl-po-notes">
                                                                    {{child.englishComment ? child.englishComment : '-'}}
                                                                </div>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="6">
                                                                <div class="tbl-po-notes">
                                                                    {{child.chinaComment ? child.chinaComment : '-'}}
                                                                </div>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="7">
                                                                <span>{{child.item?.groupCodeName ? child.item?.groupCodeName : '-'}}</span>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="8">
                                                                <span>-</span>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="9">
                                                                <span>{{child.orderDate | date: 'dd/MM/yyyy'}}</span>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="10">
                                                                <span>{{child.poCarton ? child.poCarton : 0}}</span>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="11">
                                                                <span>{{child.pricePerCarton ? child.pricePerCarton : 0}}</span>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="12">
                                                                <span>{{child.totalPcsQty ? child.totalPcsQty : 0}}</span>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="13">
                                                                <span>{{child.pendingPO_qty_carton ? child.pendingPO_qty_carton : 0}}</span>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="14">
                                                                <span>{{child.total_pendingPO_qty_carton ? child.total_pendingPO_qty_carton : 0}}</span>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="15">
                                                                <span>{{child.pricePerItem ? child.pricePerItem : 0}}</span>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="16">
                                                                <div>
                                                                    {{child.Total_Price ? (child.Total_Price |
                                                                    indianCurrency) : 0}}
                                                                </div>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="17">
                                                                <span>
                                                                    {{child.expDeliveryCost ? child.expDeliveryCost : 0}}
                                                                </span>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="18">
                                                                <div>
                                                                    {{child.totalAmountWithExp ?
                                                                    (child.totalAmountWithExp | indianCurrency) : 0}}
                                                                </div>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="19">
                                                                <span>{{child.conversationRate ? child.conversationRate : 0}}</span>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="20">
                                                                <span>{{child.totalAmountWithExpInINR ?
                                                                    (child.totalAmountWithExpInINR | indianCurrency) :
                                                                    '-'}}
                                                                </span>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="21">
                                                                <div>
                                                                    {{child.chinaFinalExpextedCode ?
                                                                    (child.chinaFinalExpextedCode) : 0}}
                                                                </div>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="22">
                                                                <span>{{item.shippingTypes ? item.shippingTypes?.label   : '-'}}</span>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="23">
                                                                <div>
                                                                    {{child.cartonLength ? child.cartonLength : '-'}} x
                                                                    {{child.cartonWidth ? child.cartonWidth : '-'}}
                                                                    x
                                                                    {{child.cartonHeight ? child.cartonHeight : '-'}}
                                                                    {{child?.cartonDimensionUnit ? child?.cartonDimensionUnit?.shortCode : ''}}
                                                                </div>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="24">
                                                                <span>{{child.dimAge}}</span>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="25">
                                                                <div>
                                                                    {{child.Total_Expense ? (child.Total_Expense |
                                                                    indianCurrency) : 0}}
                                                                </div>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="27">
                                                                <ng-container *ngIf="child.realCartonLength; else noDim">
                                                                    {{child.realCartonLength ? child.realCartonLength :
                                                                    '-'}} X
                                                                    {{child.realCartonWidth ? child.realCartonWidth :
                                                                    '-'}} X
                                                                    {{child.realCartonHeight ? child.realCartonHeight :
                                                                    '-'}}
                                                                    {{child.realCartonDimUnit}}
                                                                </ng-container>
                                                                <ng-template #noDim>
                                                                    <span>-</span>
                                                                </ng-template>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="28">
                                                                <span>{{child.realCBMPerCarton ? child.realCBMPerCarton : 0}}</span>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="29">
                                                                <span>{{child.totalCBM ? child.totalCBM : '-'}}</span>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="30">
                                                                <span>
                                                                    {{child.realCartonWeight ? child.realCartonWeight : 0}}
                                                                </span>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="31">
                                                                <span>{{child.qcCheckListName ? child.qcCheckListName : '-'}}</span>
                                                            </ng-container>
                                                            
                                                            <ng-container *ngSwitchCase="33">
                                                                <span>{{child.purchaseRatio ? child.purchaseRatio : '-'}}</span>
                                                            </ng-container>
                                                            
                                                            <ng-container *ngSwitchCase="34">
                                                                <span>{{child.totalExpenseAmount ? child.totalExpenseAmount : 0}}</span>
                                                            </ng-container>

                                                        </ng-container>
                                                    </td>
                                                </ng-container>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                            </td>
                        </tr>
                    </ng-container>
                    <ng-container>
                        <tr *ngIf="utilsService.isEmptyObjectOrNullUndefined(poImportList)">
                            <td colspan="20" class="text-center">
                                <span
                                    class="truncate">{{utilsService.serverVariableService.STRING_WHEN_NO_RECORDS_FOUND}}</span>
                            </td>
                        </tr>
                    </ng-container>

                </tbody>
            </table>
        </div>
    </div>
</div>

<div class="paginationbox pagination-fixed">
    <app-pagination (pagesizeData)="addPageSizeData($event)" (pageNumber)="pageNumber($event)"
        [page]="paginationRequest.pageNo" [pageSize]="paginationRequest.pageSize"
        [totalData]="paginationRequest.totalData">
    </app-pagination>
</div>