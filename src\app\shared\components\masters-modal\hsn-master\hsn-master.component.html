<div class="modal-body">
    <div class="row" [formGroup]="hsnGroup">
        <div class="col-12">
            <div class="form-group required">
                <label class="form-label">HSN Code </label>
                <input id="f" (keyup.enter)="onSaveHsnCode.emit()" [maxlength]="utilsService.validationService.MAX_20"
                    [(ngModel)]="hsnObj.hsnCode" formControlName="hsn_code" type="text" class="form-control"
                    placeholder="Enter HSN Code">
                <div class="message error-message"
                    *ngIf="hsnGroup.controls['hsn_code'].hasError('required') &&  hsnGroup.controls['hsn_code'].touched">
                    {{utilsService.validationService.HSN_NAME_REQ}}
                </div>
                <div class="message error-message"
                    *ngIf="!hsnGroup.controls['hsn_code'].hasError('required') && !hsnGroup.controls['hsn_code'].valid && hsnGroup.controls['hsn_code'].touched">
                    {{utilsService.validationService.HSN_NAME_INVALID}}
                </div>
            </div>
        </div>
        <div class="col-12">
            <div class="form-group theme-ngselect  required">
                <div class="form-label">GST Slab</div>
                <ng-select formControlName="slab" placeholder="Select GST Slab" [multiple]="false" [clearable]="false"
                    [items]="gstSlabDropdown" bindLabel="label" bindValue="value" [(ngModel)]="hsnObj.taxId">
                </ng-select>
                <div class="message error-message"
                    *ngIf="hsnGroup.controls['slab'].hasError('required') &&  hsnGroup.controls['slab'].touched">
                    {{utilsService.validationService.HSN_GST_SLAB_REQ}}
                </div>
            </div>
        </div>
        <div class="col-12">
            <div class="form-group">
                <label class="form-label">Description </label>
                <textarea [maxlength]="utilsService.validationService.MAX_150" [(ngModel)]="hsnObj.description"
                    formControlName="description" rows="3" placeholder="Add description here"
                    class="form-control"></textarea>
            </div>
        </div>
        <div class="col-12" *ngIf="page === utilsService.enumForPage.MASTER">
            <div class="form-group d-flex justify-content-between required ">
                <label class="form-label">Status</label>

                <div class="switch-box ">
                    <label class="switch" htmlFor="switch">
                        <input formControlName="status" type="checkbox" id='switch' [(ngModel)]="hsnObj.isActive" />
                        <div class="slider round"></div>
                    </label>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="modal-footer">
    <div class="modal-footer-group full-width-btn">
        <button (click)="onSaveHsnCode.emit()" type="button" class="btn btn-primary btn-icon-text"> <i
                class="th th-outline-tick-circle"></i>Save</button>
        <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
    </div>
</div>