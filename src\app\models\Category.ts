import { deserializeAs, serializeAs } from 'cerialize';

export class Category {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('parentCategoryId')
    @deserializeAs('parentCategoryId')
    private _parentCategoryId: number;

    @serializeAs('categoryCode')
    @deserializeAs('categoryCode')
    private _categoryCode: string;

    @serializeAs('isActive')
    @deserializeAs('isActive')
    private _isActive: boolean;

    // @serializeAs('isSelected')
    @deserializeAs('isSelected')
    private _isSelected: boolean;

    @serializeAs('lastModifiedDate')
    @deserializeAs('lastModifiedDate')
    private _lastModifiedDate: string;

    // @serializeAs('isExpand')
    @deserializeAs('isExpand')
    private _isExpand: boolean;

    @serializeAs('categoryName')
    @deserializeAs('categoryName')
    private _categoryName: string;

    @serializeAs('note')
    @deserializeAs('note')
    private _note: string;

    @serializeAs('indexNo')
    @deserializeAs('indexNo')
    private _indexNo: string;

    @serializeAs('categoryImg')
    @deserializeAs('categoryImg')
    private _categoryImg: any;

    // @serializeAs('categoryList')
    @deserializeAs('categoryList')
    private _categoryList: Category[];

    @serializeAs('originalName')
    @deserializeAs('originalName')
    private _originalName: string;

    @deserializeAs('index')
    private _index: number;

    constructor() {
        this.isSelected = false;
        this.isActive = false;
        this.isExpand = false;
        this.categoryList = [];
        this.parentCategoryId = null;
    }


    /**
     * Getter categoryCode
     * @return {string}
     */
	public get categoryCode(): string {
		return this._categoryCode;
	}

    /**
     * Setter categoryCode
     * @param {string} value
     */
	public set categoryCode(value: string) {
		this._categoryCode = value;
	}


    /**
     * Getter index
     * @return {number}
     */
	public get index(): number {
		return this._index;
	}

    /**
     * Setter index
     * @param {number} value
     */
	public set index(value: number) {
		this._index = value;
	}


    /**
     * Getter originalName
     * @return {string}
     */
	public get originalName(): string {
		return this._originalName;
	}

    /**
     * Setter originalName
     * @param {string} value
     */
	public set originalName(value: string) {
		this._originalName = value;
	}


    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Getter parentCategoryId
     * @return {number}
     */
	public get parentCategoryId(): number {
		return this._parentCategoryId;
	}

    /**
     * Getter isActive
     * @return {boolean}
     */
	public get isActive(): boolean {
		return this._isActive;
	}

    /**
     * Getter isSelected
     * @return {boolean}
     */
	public get isSelected(): boolean {
		return this._isSelected;
	}

    /**
     * Getter lastModifiedDate
     * @return {string}
     */
	public get lastModifiedDate(): string {
		return this._lastModifiedDate;
	}

    /**
     * Getter isExpand
     * @return {boolean}
     */
	public get isExpand(): boolean {
		return this._isExpand;
	}

    /**
     * Getter categoryName
     * @return {string}
     */
	public get categoryName(): string {
		return this._categoryName;
	}

    /**
     * Getter note
     * @return {string}
     */
	public get note(): string {
		return this._note;
	}

    /**
     * Getter indexNo
     * @return {string}
     */
	public get indexNo(): string {
		return this._indexNo;
	}

    /**
     * Getter categoryImg
     * @return {any}
     */
	public get categoryImg(): any {
		return this._categoryImg;
	}

    /**
     * Getter categoryList
     * @return {Category[]}
     */
	public get categoryList(): Category[] {
		return this._categoryList;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Setter parentCategoryId
     * @param {number} value
     */
	public set parentCategoryId(value: number) {
		this._parentCategoryId = value;
	}

    /**
     * Setter isActive
     * @param {boolean} value
     */
	public set isActive(value: boolean) {
		this._isActive = value;
	}

    /**
     * Setter isSelected
     * @param {boolean} value
     */
	public set isSelected(value: boolean) {
		this._isSelected = value;
	}

    /**
     * Setter lastModifiedDate
     * @param {string} value
     */
	public set lastModifiedDate(value: string) {
		this._lastModifiedDate = value;
	}

    /**
     * Setter isExpand
     * @param {boolean} value
     */
	public set isExpand(value: boolean) {
		this._isExpand = value;
	}

    /**
     * Setter categoryName
     * @param {string} value
     */
	public set categoryName(value: string) {
		this._categoryName = value;
	}

    /**
     * Setter note
     * @param {string} value
     */
	public set note(value: string) {
		this._note = value;
	}

    /**
     * Setter indexNo
     * @param {string} value
     */
	public set indexNo(value: string) {
		this._indexNo = value;
	}

    /**
     * Setter categoryImg
     * @param {any} value
     */
	public set categoryImg(value: any) {
		this._categoryImg = value;
	}

    /**
     * Setter categoryList
     * @param {Category[]} value
     */
	public set categoryList(value: Category[]) {
		this._categoryList = value;
	}


}