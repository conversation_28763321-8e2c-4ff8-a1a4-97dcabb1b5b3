import { deserializeAs, serializeAs } from 'cerialize';

export class Racks {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('additionalName')
    @deserializeAs('additionalName')
    private _additionalName: string;

    @serializeAs('qrCodeNo')
    @deserializeAs('qrCodeNo')
    private _qrCodeNo: string;

    @serializeAs('itemCount')
    @deserializeAs('itemCount')
    private _itemCount: number;

    @serializeAs('rackName')
    @deserializeAs('rackName')
    private _rackName: string;

    @serializeAs('isActive')
    @deserializeAs('isActive')
    private _isActive: boolean;

    @serializeAs('wareHouseId')
    @deserializeAs('wareHouseId')
    private _wareHouseId: number;

    @serializeAs('aislesId')
    @deserializeAs('aislesId')
    private _aislesId: number;

    constructor() {
        this.isActive = false;
    }


    /**
     * Getter aislesId
     * @return {number}
     */
	public get aislesId(): number {
		return this._aislesId;
	}

    /**
     * Setter aislesId
     * @param {number} value
     */
	public set aislesId(value: number) {
		this._aislesId = value;
	}


    /**
     * Getter isActive
     * @return {boolean}
     */
	public get isActive(): boolean {
		return this._isActive;
	}

    /**
     * Getter wareHouseId
     * @return {number}
     */
	public get wareHouseId(): number {
		return this._wareHouseId;
	}

    /**
     * Setter isActive
     * @param {boolean} value
     */
	public set isActive(value: boolean) {
		this._isActive = value;
	}

    /**
     * Setter wareHouseId
     * @param {number} value
     */
	public set wareHouseId(value: number) {
		this._wareHouseId = value;
	}
    

    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Getter additionalName
     * @return {string}
     */
	public get additionalName(): string {
		return this._additionalName;
	}

    /**
     * Getter qrCodeNo
     * @return {string}
     */
	public get qrCodeNo(): string {
		return this._qrCodeNo;
	}

    /**
     * Getter itemCount
     * @return {number}
     */
	public get itemCount(): number {
		return this._itemCount;
	}

    /**
     * Getter rackName
     * @return {string}
     */
	public get rackName(): string {
		return this._rackName;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Setter additionalName
     * @param {string} value
     */
	public set additionalName(value: string) {
		this._additionalName = value;
	}

    /**
     * Setter qrCodeNo
     * @param {string} value
     */
	public set qrCodeNo(value: string) {
		this._qrCodeNo = value;
	}

    /**
     * Setter itemCount
     * @param {number} value
     */
	public set itemCount(value: number) {
		this._itemCount = value;
	}

    /**
     * Setter rackName
     * @param {string} value
     */
	public set rackName(value: string) {
		this._rackName = value;
	}

}