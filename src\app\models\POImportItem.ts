import { deserializeAs, serializeAs } from 'cerialize';

export class POImportItem {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('associatedIds')
    @deserializeAs('associatedIds')
    private _associatedIds: number;

    @serializeAs('cartonDimUnitId')
    @deserializeAs('cartonDimUnitId')
    private _cartonDimUnitId: number;

    @deserializeAs('cartonQuantity')
    private _cartonQuantity: number;

    @deserializeAs('item')
    private _item: any;

    @deserializeAs('skuId')
    private _skuId: any;

    @serializeAs('marka')
    @deserializeAs('marka')
    private _marka: string;

    @deserializeAs('groupCodeName')
    private _groupCodeName: string;

    @serializeAs('note')
    @deserializeAs('note')
    private _note: string;

    @serializeAs('englishComment')
    @deserializeAs('englishComment')
    private _englishComment: string;

    @serializeAs('chinaComment')
    @deserializeAs('chinaComment')
    private _chinaComment: string;

    @serializeAs('poCarton')
    @deserializeAs('poCarton')
    private _poCarton: number;

    @serializeAs('pricePerCarton')
    @deserializeAs('pricePerCarton')
    private _pricePerCarton: number;

    @deserializeAs('showAssignedToCHA')
    private _showAssignedToCHA: boolean;

    @serializeAs('pricePerItem')
    @deserializeAs('pricePerItem')
    private _pricePerItem: number;

    @serializeAs('expDeliveryCost')
    @deserializeAs('expDeliveryCost')
    private _expDeliveryCost: number;

    @serializeAs('itemDimLength')
    @deserializeAs('itemDimLength')
    private _itemDimLength: number;

    @serializeAs('itemDimWidth')
    @deserializeAs('itemDimWidth')
    private _itemDimWidth: number;

    @serializeAs('itemDimHeight')
    @deserializeAs('itemDimHeight')
    private _itemDimHeight: number;

    @serializeAs('dimAge')
    @deserializeAs('dimAge')
    private _dimAge: number;

    @serializeAs('transportCharges')
    @deserializeAs('transportCharges')
    private _transportCharges: number;

    @serializeAs('craneExpense')
    @deserializeAs('craneExpense')
    private _craneExpense: number;

    @serializeAs('cartonWeight')
    @deserializeAs('cartonWeight')
    private _cartonWeight: number;

    @serializeAs('cartonWeightRu')
    @deserializeAs('cartonWeightRu')
    private _cartonWeightRu: number;

    @serializeAs('percentage')
    @deserializeAs('percentage')
    private _percentage: number;

    @serializeAs('expensePcs')
    @deserializeAs('expensePcs')
    private _expensePcs: number;

    @serializeAs('qcCheckListId')
    @deserializeAs('qcCheckListId')
    private _qcCheckListId: number;

    @serializeAs('extraExpense')
    @deserializeAs('extraExpense')
    private _extraExpense: number;

    @serializeAs('purchaseRation')
    @deserializeAs('purchaseRation')
    private _purchaseRation: number;

    @deserializeAs('purchaseRationFlag')
    private _purchaseRationFlag: boolean;

    @serializeAs('itemId')
    @deserializeAs('itemId')
    private _itemId: number;

    @serializeAs('cartonLength')
    @deserializeAs('cartonLength')
    private _cartonLength: number;

    @serializeAs('cartonHeight')
    @deserializeAs('cartonHeight')
    private _cartonHeight: number;

    @serializeAs('cartonWidth')
    @deserializeAs('cartonWidth')
    private _cartonWidth: number;

    @serializeAs('dimUpdatedDate')
    @deserializeAs('dimUpdatedDate')
    private _dimUpdatedDate: any;

    @serializeAs('dimUniteId')
    @deserializeAs('dimUniteId')
    private _dimUniteId: number;

    @deserializeAs('dimUniteName')
    private _dimUniteName: number;

    @deserializeAs('dimUnitShortCode')
    private _dimUnitShortCode: string;

    @deserializeAs('totalWeight')
    private _totalWeight: number;

    @deserializeAs('totalLoadAmt')
    private _totalLoadAmt: number;

    @deserializeAs('shippingCostperPieceINR')
    private _shippingCostperPieceINR: number;

    @deserializeAs('totalShippingExpWeight')
    private _totalShippingExpWeight: number;

    @serializeAs('colorId')
    @deserializeAs('colorId')
    private _colorId: number[];

    @deserializeAs('itemDropdown')
    private _itemDropdown: any[];

    @deserializeAs('colorDropdown')
    private _colorDropdown: any[];

    @deserializeAs('formattedName')
    private _formattedName: string;

    @deserializeAs('originalName')
    private _originalName: string;

    @deserializeAs('itemFileFormatedName')
    private _itemFileFormatedName: string;

    @deserializeAs('itemFileOriginalName')
    private _itemFileOriginalName: string;

    @deserializeAs('itemSKUId')
    private _itemSKUId: string;

    @deserializeAs('displayName')
    private _displayName: string;

    @deserializeAs('itemGroupName')
    private _itemGroupName: string;

    @deserializeAs('fromDate')
    private _fromDate: string;

    @deserializeAs('toDate')
    private _toDate: string;

    @deserializeAs('advanceDate')
    private _advanceDate: string;

    @deserializeAs('totalPcsQty')
    private _totalPcsQty: number;

    @deserializeAs('totalAmount')
    private _totalAmount: number;

    @deserializeAs('totalAmountWithExp')
    private _totalAmountWithExp: number;

    @deserializeAs('totalAmountWithExpInINR')
    private _totalAmountWithExpInINR: number;

    @deserializeAs('chinaFinalExpextedCode')
    private _chinaFinalExpextedCode: number;

    @deserializeAs('transportationChargesM2SperCarton')
    private _transportationChargesM2SperCarton: number;

    @deserializeAs('totalTransportationChargesM2S')
    private _totalTransportationChargesM2S: number;

    @deserializeAs('transportationChargesM2SperPCS')
    private _transportationChargesM2SperPCS: number;

    @deserializeAs('totalInsurance')
    private _totalInsurance: number;

    @deserializeAs('insurancePerPcs')
    private _insurancePerPcs: number;

    @deserializeAs('gstAmtPerPcs')
    private _gstAmtPerPcs: number;

    @deserializeAs('craneExpPcs')
    private _craneExpPcs: number;

    @deserializeAs('totalExp')
    private _totalExp: number;

    @deserializeAs('C2SFinalPrice')
    private _C2SFinalPrice: number;

    @deserializeAs('totalExpPCSper')
    private _totalExpPCSper: number;

    @deserializeAs('totalFinalCostPCSper')
    private _totalFinalCostPCSper: number;

    @deserializeAs('totalItemAmt')
    private _totalItemAmt: number;

    @deserializeAs('cbmPerCarton')
    private _cbmPerCarton: number;

    @deserializeAs('totalCbm')
    private _totalCbm: number;

    @deserializeAs('cbmPrice')
    private _cbmPrice: number;

    @deserializeAs('totalCBMExpenseINR')
    private _totalCBMExpenseINR: number;

    @deserializeAs('shippingExpPerPCS')
    private _shippingExpPerPCS: number;

    @deserializeAs('chinaToSuratPadtar')
    private _chinaToSuratPadtar: number;

    @deserializeAs('conversationRate')
    private _conversationRate: number;

    @deserializeAs('associatedItemColors')
    private _associatedItemColors: any[];

    @deserializeAs('checkList')
    private _checkList: any[];

    @deserializeAs('colorName')
    private _colorName: any;

    @deserializeAs('unitMaster')
    private _unitMaster: any;

    @deserializeAs('expectedDeliveryDate')
    private _expectedDeliveryDate: any;

    @deserializeAs('itemPrice')
    private _itemPrice: any;

    @deserializeAs('levelBreachQtys')
    private _levelBreachQtys: any;

    @deserializeAs('isSelected')
    private _isSelected: boolean;

    @deserializeAs('isMainLink')
    private _isMainLink: boolean;

    @deserializeAs('isLink')
    private _isLink: boolean;

    @deserializeAs('linkIds')
    private _linkIds: number[];

    @deserializeAs('lastRecord')
    private _lastRecord: any[];

    @serializeAs('isDraft')
    @deserializeAs('isDraft')
    private _isDraft: boolean;

    @deserializeAs('PoNo')
    private _PoNo: string;

    @deserializeAs('receivedCartonsCH')
    private _receivedCartonsCH: number;

    @deserializeAs('rcCarton')
    private _rcCarton: number;

    @deserializeAs('loadedQty')
    private _loadedQty: number;

    @deserializeAs('receivedQty')
    private _receivedQty: number;

    @deserializeAs('loadedQtyField')
    private _loadedQtyField: number;

    @deserializeAs('rcId')
    private _rcId: any;

    @deserializeAs('pendingQtyCarton')
    private _pendingQtyCarton: number;

    @deserializeAs('totalPendingQty')
    private _totalPendingQty: number;

    @deserializeAs('receivedChId')
    private _receivedChId: number;

    @deserializeAs('calcReceiveKey')
    private _calcReceiveKey: number;

    @deserializeAs('getSumLoadedQty')
    private _getSumLoadedQty: number;

    @deserializeAs('calcLoadedQty')
    private _calcLoadedQty: number;
    
    //CALCS

    @deserializeAs('totalShippingExpense')
    private _totalShippingExpense: number;

    @deserializeAs('totalCBMExpense')
    private _totalCBMExpense: number;

    @deserializeAs('shippingExpense')
    private _shippingExpense: number;

    @deserializeAs('ChinaToSuratFinalPrice')
    private _ChinaToSuratFinalPrice: number;

    @deserializeAs('totalFinalCost')
    private _totalFinalCost: number;

    @deserializeAs('TotalTransportationCharges')
    private _TotalTransportationCharges: number;

    @deserializeAs('total_load_amt_Ru')
    private _total_load_amt_Ru: number;

    @deserializeAs('totalExpense')
    private _totalExpense: number;

    @deserializeAs('Total_Price')
    private _Total_Price: number;
    
    @deserializeAs('totalReceivedAmount')
    private _totalReceivedAmount: number;
    
    @deserializeAs('totalLoadedQtyAmount')
    private _totalLoadedQtyAmount: number;

    @deserializeAs('insurancePCS')
    private _insurancePCS: number;

    @deserializeAs('Total_Expense')
    private _Total_Expense: number;

    @deserializeAs('shipingCost')
    private _shipingCost: number;

    @deserializeAs('totalItemAmount')
    private _totalItemAmount: number;

    @deserializeAs('cbmCarton')
    private _cbmCarton: number;

    @deserializeAs('item_Amount')
    private _item_Amount: number;

    @deserializeAs('total_Weight')
    private _total_Weight: number;

    @deserializeAs('TotalInsurance')
    private _TotalInsurance: number;

    @deserializeAs('Transportationcharges_Mumbai_to_Surat')
    private _Transportationcharges_Mumbai_to_Surat: number;
    
    @deserializeAs('GSTAmount_PCS')
    private _GSTAmount_PCS: number;

    @deserializeAs('CraneExpense_PCS')
    private _CraneExpense_PCS: number;

    @deserializeAs('pendingLoaded')
    private _pendingLoaded: number;

    @deserializeAs('shippingTypes')
    private _shippingTypes: any;

    @deserializeAs('gstAmt')
    private _gstAmt: number;

    @deserializeAs('gstAmount')
    private _gstAmount: number;

    @deserializeAs('totalPendingLoadedQty')
    private _totalPendingLoadedQty: number;

    @deserializeAs('poDate')
    private _poDate: any;

    @deserializeAs('conversationRateCurrencyName')
    private _conversationRateCurrencyName: any;

    @deserializeAs('PendingQtyCarton')
    private _PendingQtyCarton: number;

    @deserializeAs('PendingQtyCartonPcs')
    private _PendingQtyCartonPcs: number;

    @deserializeAs('rcCartonField')
    private _rcCartonField: number;

    @deserializeAs('Expense_PCS')
    private _Expense_PCS: number;

    @deserializeAs('Percentage')
    private _Percentage: number;

    @deserializeAs('Weight_kg')
    private _Weight_kg: number;

    @deserializeAs('Weight_Carton')
    private _Weight_Carton: number;

    @deserializeAs('container')
    private _container: any;

    @deserializeAs('poChinaMapId')
    private _poChinaMapId: number;

    @deserializeAs('poCreateIdF')
    private _poCreateIdF: number;

    @deserializeAs('grnReceivedQty')
    private _grnReceivedQty: number;

    @deserializeAs('totalQty')
    private _totalQty: number;
    
    @deserializeAs('releasedQty')
    private _releasedQty: number;

    @deserializeAs('branchCartonMapping')
    private _branchCartonMapping: any[];

    @deserializeAs('pendingField')
    private _pendingField: number;

    @deserializeAs('j4Amt')
    private _j4Amt: number;

    @deserializeAs('cartonWeightUnit')
    private _cartonWeightUnit: number;

    @deserializeAs('loadedMapId')
    private _loadedMapId: number;

    @deserializeAs('releaseQtyField')
    private _releaseQtyField: number;

    @deserializeAs('gstPers')
    private _gstPers: any;

    @deserializeAs('gst_amounts')
    private _gst_amounts: number;

    @deserializeAs('rate')
    private _rate: number;

    @deserializeAs('gst_amount')
    private _gst_amount: number;

    @deserializeAs('gstPer')
    private _gstPer: any;

    @deserializeAs('PendingReleasedQtyCarton')
    private _PendingReleasedQtyCarton: number;

    @deserializeAs('PendingReleasedQtyCartonPcs')
    private _PendingReleasedQtyCartonPcs: number;
    
    @deserializeAs('releasedDate')
    private _releasedDate: any;

    @deserializeAs('colors')
    private _colors: string[];

    @deserializeAs('isDoneCartonMapping')
    private _isDoneCartonMapping: string;

    @deserializeAs('poNumber')
    private _poNumber: string;

    @deserializeAs('poOrderDate')
    private _poOrderDate: string;

    @deserializeAs('realCartonLength')
    private _realCartonLength: number;

    @deserializeAs('realCartonWidth')
    private _realCartonWidth: number;

    @deserializeAs('realCartonHeight')
    private _realCartonHeight: number;

    @deserializeAs('realCBMPerCarton')
    private _realCBMPerCarton: number;

    @deserializeAs('realCartonWeight')
    private _realCartonWeight: number;
    
    @deserializeAs('totalCBM')
    private _totalCBM: number;

    @deserializeAs('poImportDate')
    private _poImportDate: string;

    @deserializeAs('purchaseOrder')
    private _purchaseOrder: string;

    @deserializeAs('supplierName')
    private _supplierName: string;

    @deserializeAs('price')
    private _price: number;

    @deserializeAs('totalPrice')
    private _totalPrice: number;

    @deserializeAs('status')
    private _status: string;

    @deserializeAs('realCartonDimUnit')
    private _realCartonDimUnit: string;

    @deserializeAs('containerName')
    private _containerName: string;

    @deserializeAs('grnReceivedCarton')
    private _grnReceivedCarton: number;

    @deserializeAs('isQRGenerated')
    private _isQRGenerated: boolean;
    
    @deserializeAs('cartonWeightDim')
    private _cartonWeightDim: any;

    @deserializeAs('grnQty')
    private _grnQty: number;

    @deserializeAs('PendingGrnQty')
    private _PendingGrnQty: number;

    @deserializeAs('totalGrnQty')
    private _totalGrnQty: number;

    @deserializeAs('assignToCHAQty')
    private _assignToCHAQty: number;

    @deserializeAs('cartonWeightDimName')
    private _cartonWeightDimName: string;

    @deserializeAs('cartonDimensionUnitName')
    private _cartonDimensionUnitName: string;

    @deserializeAs('cartonDimensionUnit')
    private _cartonDimensionUnit: any;

    @deserializeAs('orderDate')
    private _orderDate: string;

    @deserializeAs('total_pendingPO_qty_carton')
    private _total_pendingPO_qty_carton: number;

    @deserializeAs('pendingPO_qty_carton')
    private _pendingPO_qty_carton: number;

    @deserializeAs('realCartonWeightUnit')
    private _realCartonWeightUnit: string;

    @deserializeAs('qcCheckListName')
    private _qcCheckListName: string;

    @deserializeAs('totalExpenseAmount')
    private _totalExpenseAmount: number;

    @deserializeAs('purchaseRatio')
    private _purchaseRatio: number;

    @deserializeAs('tag')
    private _tag: string;

    @deserializeAs('conRate')
    private _conRate: number;

    constructor() {
        this.isLink = false;
        this.isMainLink = false;
        this.isSelected = false;
        this.itemDropdown = [];
        this.colorDropdown = [];
        this.associatedItemColors = [];
        this.checkList = []
        this.linkIds = []
        this.lastRecord = [];
        this.isDraft = false;
        this.branchCartonMapping = []
        this.isDoneCartonMapping = ''
        this.isQRGenerated = false;
        this.assignToCHAQty = 0;
        this.PendingGrnQty = 0;
        this.purchaseRationFlag = false;
        this.showAssignedToCHA = false;
    }

    /**
     * Getter showAssignedToCHA
     * @return {boolean}
     */
    public get showAssignedToCHA(): boolean {
        return this._showAssignedToCHA;
    }

    /**
     * Setter showAssignedToCHA
     * @param {boolean} value
     */
    public set showAssignedToCHA(value: boolean) {
        this._showAssignedToCHA = value;
    }

    /**
     * Getter conRate
     * @return {number}
     */
	public get conRate(): number {
		return this._conRate;
	}

    /**
     * Setter conRate
     * @param {number} value
     */
	public set conRate(value: number) {
		this._conRate = value;
	}

    /**
     * Getter cartonQuantity
     * @return {number}
     */
	public get cartonQuantity(): number {
		return this._cartonQuantity;
	}

    /**
     * Setter cartonQuantity
     * @param {number} value
     */
	public set cartonQuantity(value: number) {
		this._cartonQuantity = value;
	}

    /**
     * Getter tag
     * @return {string}
     */
	public get tag(): string {
		return this._tag;
	}

    /**
     * Setter tag
     * @param {string} value
     */
	public set tag(value: string) {
		this._tag = value;
	}

    /**
     * Getter purchaseRatio
     * @return {number}
     */
	public get purchaseRatio(): number {
		return this._purchaseRatio;
	}

    /**
     * Setter purchaseRatio
     * @param {number} value
     */
	public set purchaseRatio(value: number) {
		this._purchaseRatio = value;
	}
    

    /**
     * Getter purchaseRationFlag
     * @return {boolean}
     */
	public get purchaseRationFlag(): boolean {
		return this._purchaseRationFlag;
	}

    /**
     * Setter purchaseRationFlag
     * @param {boolean} value
     */
	public set purchaseRationFlag(value: boolean) {
		this._purchaseRationFlag = value;
	}
    

    /**
     * Getter totalExpenseAmount
     * @return {number}
     */
	public get totalExpenseAmount(): number {
		return this._totalExpenseAmount;
	}

    /**
     * Setter totalExpenseAmount
     * @param {number} value
     */
	public set totalExpenseAmount(value: number) {
		this._totalExpenseAmount = value;
	}

    /**
     * Getter qcCheckListName
     * @return {string}
     */
	public get qcCheckListName(): string {
		return this._qcCheckListName;
	}

    /**
     * Setter qcCheckListName
     * @param {string} value
     */
	public set qcCheckListName(value: string) {
		this._qcCheckListName = value;
	}

    /**
     * Getter realCartonWeightUnit
     * @return {string}
     */
	public get realCartonWeightUnit(): string {
		return this._realCartonWeightUnit;
	}

    /**
     * Setter realCartonWeightUnit
     * @param {string} value
     */
	public set realCartonWeightUnit(value: string) {
		this._realCartonWeightUnit = value;
	}

    /**
     * Getter pendingPO_qty_carton
     * @return {number}
     */
	public get pendingPO_qty_carton(): number {
		return this._pendingPO_qty_carton;
	}

    /**
     * Setter pendingPO_qty_carton
     * @param {number} value
     */
	public set pendingPO_qty_carton(value: number) {
		this._pendingPO_qty_carton = value;
	}

    /**
     * Getter total_pendingPO_qty_carton
     * @return {number}
     */
	public get total_pendingPO_qty_carton(): number {
		return this._total_pendingPO_qty_carton;
	}

    /**
     * Setter total_pendingPO_qty_carton
     * @param {number} value
     */
	public set total_pendingPO_qty_carton(value: number) {
		this._total_pendingPO_qty_carton = value;
	}

    /**
     * Getter orderDate
     * @return {string}
     */
	public get orderDate(): string {
		return this._orderDate;
	}

    /**
     * Setter orderDate
     * @param {string} value
     */
	public set orderDate(value: string) {
		this._orderDate = value;
	}


    /**
     * Getter groupCodeName
     * @return {string}
     */
	public get groupCodeName(): string {
		return this._groupCodeName;
	}

    /**
     * Setter groupCodeName
     * @param {string} value
     */
	public set groupCodeName(value: string) {
		this._groupCodeName = value;
	}



    /**
     * Getter cartonWeightDim
     * @return {any}
     */
	public get cartonWeightDim(): any {
		return this._cartonWeightDim;
	}

    /**
     * Setter cartonWeightDim
     * @param {any} value
     */
	public set cartonWeightDim(value: any) {
		this._cartonWeightDim = value;
	}
    
    /**
     * Getter cartonDimensionUnit
     * @return {any}
     */
	public get cartonDimensionUnit(): any {
		return this._cartonDimensionUnit;
	}

    /**
     * Setter cartonDimensionUnit
     * @param {any} value
     */
	public set cartonDimensionUnit(value: any) {
		this._cartonDimensionUnit = value;
	}


    /**
     * Getter cartonDimUnitId
     * @return {number}
     */
	public get cartonDimUnitId(): number {
		return this._cartonDimUnitId;
	}

    /**
     * Setter cartonDimUnitId
     * @param {number} value
     */
	public set cartonDimUnitId(value: number) {
		this._cartonDimUnitId = value;
	}


    /**
     * Getter cartonWeightDimName
     * @return {string}
     */
	public get cartonWeightDimName(): string {
		return this._cartonWeightDimName;
	}

    /**
     * Getter cartonDimensionUnitName
     * @return {string}
     */
	public get cartonDimensionUnitName(): string {
		return this._cartonDimensionUnitName;
	}

    /**
     * Setter cartonWeightDimName
     * @param {string} value
     */
	public set cartonWeightDimName(value: string) {
		this._cartonWeightDimName = value;
	}

    /**
     * Setter cartonDimensionUnitName
     * @param {string} value
     */
	public set cartonDimensionUnitName(value: string) {
		this._cartonDimensionUnitName = value;
	}

    
    /**
     * Getter assignToCHAQty
     * @return {number}
     */
	public get assignToCHAQty(): number {
		return this._assignToCHAQty;
	}

    /**
     * Setter assignToCHAQty
     * @param {number} value
     */
	public set assignToCHAQty(value: number) {
		this._assignToCHAQty = value;
	}


    /**
     * Getter totalGrnQty
     * @return {number}
     */
	public get totalGrnQty(): number {
		return this._totalGrnQty;
	}

    /**
     * Setter totalGrnQty
     * @param {number} value
     */
	public set totalGrnQty(value: number) {
		this._totalGrnQty = value;
	}


    /**
     * Getter grnQty
     * @return {number}
     */
	public get grnQty(): number {
		return this._grnQty;
	}

    /**
     * Getter PendingGrnQty
     * @return {number}
     */
	public get PendingGrnQty(): number {
		return this._PendingGrnQty;
	}

    /**
     * Setter grnQty
     * @param {number} value
     */
	public set grnQty(value: number) {
		this._grnQty = value;
	}

    /**
     * Setter PendingGrnQty
     * @param {number} value
     */
	public set PendingGrnQty(value: number) {
		this._PendingGrnQty = value;
	}


    /**
     * Getter isQRGenerated
     * @return {boolean}
     */
	public get isQRGenerated(): boolean {
		return this._isQRGenerated;
	}

    /**
     * Setter isQRGenerated
     * @param {boolean} value
     */
	public set isQRGenerated(value: boolean) {
		this._isQRGenerated = value;
	}


    /**
     * Getter grnReceivedCarton
     * @return {number}
     */
	public get grnReceivedCarton(): number {
		return this._grnReceivedCarton;
	}

    /**
     * Setter grnReceivedCarton
     * @param {number} value
     */
	public set grnReceivedCarton(value: number) {
		this._grnReceivedCarton = value;
	}


    /**
     * Getter containerName
     * @return {string}
     */
	public get containerName(): string {
		return this._containerName;
	}

    /**
     * Setter containerName
     * @param {string} value
     */
	public set containerName(value: string) {
		this._containerName = value;
	}


    /**
     * Getter realCartonDimUnit
     * @return {string}
     */
	public get realCartonDimUnit(): string {
		return this._realCartonDimUnit;
	}

    /**
     * Setter realCartonDimUnit
     * @param {string} value
     */
	public set realCartonDimUnit(value: string) {
		this._realCartonDimUnit = value;
	}


    /**
     * Getter status
     * @return {string}
     */
	public get status(): string {
		return this._status;
	}

    /**
     * Setter status
     * @param {string} value
     */
	public set status(value: string) {
		this._status = value;
	}


    /**
     * Getter price
     * @return {number}
     */
	public get price(): number {
		return this._price;
	}

    /**
     * Getter totalPrice
     * @return {number}
     */
	public get totalPrice(): number {
		return this._totalPrice;
	}

    /**
     * Setter price
     * @param {number} value
     */
	public set price(value: number) {
		this._price = value;
	}

    /**
     * Setter totalPrice
     * @param {number} value
     */
	public set totalPrice(value: number) {
		this._totalPrice = value;
	}


    /**
     * Getter supplierName
     * @return {string}
     */
	public get supplierName(): string {
		return this._supplierName;
	}

    /**
     * Setter supplierName
     * @param {string} value
     */
	public set supplierName(value: string) {
		this._supplierName = value;
	}


    /**
     * Getter poImportDate
     * @return {string}
     */
	public get poImportDate(): string {
		return this._poImportDate;
	}

    /**
     * Getter purchaseOrder
     * @return {string}
     */
	public get purchaseOrder(): string {
		return this._purchaseOrder;
	}

    /**
     * Setter poImportDate
     * @param {string} value
     */
	public set poImportDate(value: string) {
		this._poImportDate = value;
	}

    /**
     * Setter purchaseOrder
     * @param {string} value
     */
	public set purchaseOrder(value: string) {
		this._purchaseOrder = value;
	}
    

    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Getter associatedIds
     * @return {number}
     */
	public get associatedIds(): number {
		return this._associatedIds;
	}

    /**
     * Getter item
     * @return {any}
     */
	public get item(): any {
		return this._item;
	}

    /**
     * Getter skuId
     * @return {any}
     */
	public get skuId(): any {
		return this._skuId;
	}

    /**
     * Getter marka
     * @return {string}
     */
	public get marka(): string {
		return this._marka;
	}

    /**
     * Getter note
     * @return {string}
     */
	public get note(): string {
		return this._note;
	}

    /**
     * Getter englishComment
     * @return {string}
     */
	public get englishComment(): string {
		return this._englishComment;
	}

    /**
     * Getter chinaComment
     * @return {string}
     */
	public get chinaComment(): string {
		return this._chinaComment;
	}

    /**
     * Getter poCarton
     * @return {number}
     */
	public get poCarton(): number {
		return this._poCarton;
	}

    /**
     * Getter pricePerCarton
     * @return {number}
     */
	public get pricePerCarton(): number {
		return this._pricePerCarton;
	}

    /**
     * Getter pricePerItem
     * @return {number}
     */
	public get pricePerItem(): number {
		return this._pricePerItem;
	}

    /**
     * Getter expDeliveryCost
     * @return {number}
     */
	public get expDeliveryCost(): number {
		return this._expDeliveryCost;
	}

    /**
     * Getter itemDimLength
     * @return {number}
     */
	public get itemDimLength(): number {
		return this._itemDimLength;
	}

    /**
     * Getter itemDimWidth
     * @return {number}
     */
	public get itemDimWidth(): number {
		return this._itemDimWidth;
	}

    /**
     * Getter itemDimHeight
     * @return {number}
     */
	public get itemDimHeight(): number {
		return this._itemDimHeight;
	}

    /**
     * Getter dimAge
     * @return {number}
     */
	public get dimAge(): number {
		return this._dimAge;
	}

    /**
     * Getter transportCharges
     * @return {number}
     */
	public get transportCharges(): number {
		return this._transportCharges;
	}

    /**
     * Getter craneExpense
     * @return {number}
     */
	public get craneExpense(): number {
		return this._craneExpense;
	}

    /**
     * Getter cartonWeight
     * @return {number}
     */
	public get cartonWeight(): number {
		return this._cartonWeight;
	}

    /**
     * Getter cartonWeightRu
     * @return {number}
     */
	public get cartonWeightRu(): number {
		return this._cartonWeightRu;
	}

    /**
     * Getter percentage
     * @return {number}
     */
	public get percentage(): number {
		return this._percentage;
	}

    /**
     * Getter expensePcs
     * @return {number}
     */
	public get expensePcs(): number {
		return this._expensePcs;
	}

    /**
     * Getter qcCheckListId
     * @return {number}
     */
	public get qcCheckListId(): number {
		return this._qcCheckListId;
	}

    /**
     * Getter extraExpense
     * @return {number}
     */
	public get extraExpense(): number {
		return this._extraExpense;
	}

    /**
     * Getter purchaseRation
     * @return {number}
     */
	public get purchaseRation(): number {
		return this._purchaseRation;
	}

    /**
     * Getter itemId
     * @return {number}
     */
	public get itemId(): number {
		return this._itemId;
	}

    /**
     * Getter cartonLength
     * @return {number}
     */
	public get cartonLength(): number {
		return this._cartonLength;
	}

    /**
     * Getter cartonHeight
     * @return {number}
     */
	public get cartonHeight(): number {
		return this._cartonHeight;
	}

    /**
     * Getter cartonWidth
     * @return {number}
     */
	public get cartonWidth(): number {
		return this._cartonWidth;
	}

    /**
     * Getter dimUpdatedDate
     * @return {any}
     */
	public get dimUpdatedDate(): any {
		return this._dimUpdatedDate;
	}

    /**
     * Getter dimUniteId
     * @return {number}
     */
	public get dimUniteId(): number {
		return this._dimUniteId;
	}

    /**
     * Getter dimUniteName
     * @return {number}
     */
	public get dimUniteName(): number {
		return this._dimUniteName;
	}

    /**
     * Getter dimUnitShortCode
     * @return {string}
     */
	public get dimUnitShortCode(): string {
		return this._dimUnitShortCode;
	}

    /**
     * Getter totalWeight
     * @return {number}
     */
	public get totalWeight(): number {
		return this._totalWeight;
	}

    /**
     * Getter totalLoadAmt
     * @return {number}
     */
	public get totalLoadAmt(): number {
		return this._totalLoadAmt;
	}

    /**
     * Getter shippingCostperPieceINR
     * @return {number}
     */
	public get shippingCostperPieceINR(): number {
		return this._shippingCostperPieceINR;
	}

    /**
     * Getter totalShippingExpWeight
     * @return {number}
     */
	public get totalShippingExpWeight(): number {
		return this._totalShippingExpWeight;
	}

    /**
     * Getter colorId
     * @return {number[]}
     */
	public get colorId(): number[] {
		return this._colorId;
	}

    /**
     * Getter itemDropdown
     * @return {any[]}
     */
	public get itemDropdown(): any[] {
		return this._itemDropdown;
	}

    /**
     * Getter colorDropdown
     * @return {any[]}
     */
	public get colorDropdown(): any[] {
		return this._colorDropdown;
	}

    /**
     * Getter formattedName
     * @return {string}
     */
	public get formattedName(): string {
		return this._formattedName;
	}

    /**
     * Getter originalName
     * @return {string}
     */
	public get originalName(): string {
		return this._originalName;
	}

    /**
     * Getter itemFileFormatedName
     * @return {string}
     */
	public get itemFileFormatedName(): string {
		return this._itemFileFormatedName;
	}

    /**
     * Getter itemFileOriginalName
     * @return {string}
     */
	public get itemFileOriginalName(): string {
		return this._itemFileOriginalName;
	}

    /**
     * Getter itemSKUId
     * @return {string}
     */
	public get itemSKUId(): string {
		return this._itemSKUId;
	}

    /**
     * Getter displayName
     * @return {string}
     */
	public get displayName(): string {
		return this._displayName;
	}

    /**
     * Getter itemGroupName
     * @return {string}
     */
	public get itemGroupName(): string {
		return this._itemGroupName;
	}

    /**
     * Getter fromDate
     * @return {string}
     */
	public get fromDate(): string {
		return this._fromDate;
	}

    /**
     * Getter toDate
     * @return {string}
     */
	public get toDate(): string {
		return this._toDate;
	}

    /**
     * Getter advanceDate
     * @return {string}
     */
	public get advanceDate(): string {
		return this._advanceDate;
	}

    /**
     * Getter totalPcsQty
     * @return {number}
     */
	public get totalPcsQty(): number {
		return this._totalPcsQty;
	}

    /**
     * Getter totalAmount
     * @return {number}
     */
	public get totalAmount(): number {
		return this._totalAmount;
	}

    /**
     * Getter totalAmountWithExp
     * @return {number}
     */
	public get totalAmountWithExp(): number {
		return this._totalAmountWithExp;
	}

    /**
     * Getter totalAmountWithExpInINR
     * @return {number}
     */
	public get totalAmountWithExpInINR(): number {
		return this._totalAmountWithExpInINR;
	}

    /**
     * Getter chinaFinalExpextedCode
     * @return {number}
     */
	public get chinaFinalExpextedCode(): number {
		return this._chinaFinalExpextedCode;
	}

    /**
     * Getter transportationChargesM2SperCarton
     * @return {number}
     */
	public get transportationChargesM2SperCarton(): number {
		return this._transportationChargesM2SperCarton;
	}

    /**
     * Getter totalTransportationChargesM2S
     * @return {number}
     */
	public get totalTransportationChargesM2S(): number {
		return this._totalTransportationChargesM2S;
	}

    /**
     * Getter transportationChargesM2SperPCS
     * @return {number}
     */
	public get transportationChargesM2SperPCS(): number {
		return this._transportationChargesM2SperPCS;
	}

    /**
     * Getter totalInsurance
     * @return {number}
     */
	public get totalInsurance(): number {
		return this._totalInsurance;
	}

    /**
     * Getter insurancePerPcs
     * @return {number}
     */
	public get insurancePerPcs(): number {
		return this._insurancePerPcs;
	}

    /**
     * Getter gstAmtPerPcs
     * @return {number}
     */
	public get gstAmtPerPcs(): number {
		return this._gstAmtPerPcs;
	}

    /**
     * Getter craneExpPcs
     * @return {number}
     */
	public get craneExpPcs(): number {
		return this._craneExpPcs;
	}

    /**
     * Getter totalExp
     * @return {number}
     */
	public get totalExp(): number {
		return this._totalExp;
	}

    /**
     * Getter C2SFinalPrice
     * @return {number}
     */
	public get C2SFinalPrice(): number {
		return this._C2SFinalPrice;
	}

    /**
     * Getter totalExpPCSper
     * @return {number}
     */
	public get totalExpPCSper(): number {
		return this._totalExpPCSper;
	}

    /**
     * Getter totalFinalCostPCSper
     * @return {number}
     */
	public get totalFinalCostPCSper(): number {
		return this._totalFinalCostPCSper;
	}

    /**
     * Getter totalItemAmt
     * @return {number}
     */
	public get totalItemAmt(): number {
		return this._totalItemAmt;
	}

    /**
     * Getter cbmPerCarton
     * @return {number}
     */
	public get cbmPerCarton(): number {
		return this._cbmPerCarton;
	}

    /**
     * Getter totalCbm
     * @return {number}
     */
	public get totalCbm(): number {
		return this._totalCbm;
	}

    /**
     * Getter cbmPrice
     * @return {number}
     */
	public get cbmPrice(): number {
		return this._cbmPrice;
	}

    /**
     * Getter totalCBMExpenseINR
     * @return {number}
     */
	public get totalCBMExpenseINR(): number {
		return this._totalCBMExpenseINR;
	}

    /**
     * Getter shippingExpPerPCS
     * @return {number}
     */
	public get shippingExpPerPCS(): number {
		return this._shippingExpPerPCS;
	}

    /**
     * Getter chinaToSuratPadtar
     * @return {number}
     */
	public get chinaToSuratPadtar(): number {
		return this._chinaToSuratPadtar;
	}

    /**
     * Getter conversationRate
     * @return {number}
     */
	public get conversationRate(): number {
		return this._conversationRate;
	}

    /**
     * Getter associatedItemColors
     * @return {any[]}
     */
	public get associatedItemColors(): any[] {
		return this._associatedItemColors;
	}

    /**
     * Getter checkList
     * @return {any[]}
     */
	public get checkList(): any[] {
		return this._checkList;
	}

    /**
     * Getter colorName
     * @return {any}
     */
	public get colorName(): any {
		return this._colorName;
	}

    /**
     * Getter unitMaster
     * @return {any}
     */
	public get unitMaster(): any {
		return this._unitMaster;
	}

    /**
     * Getter expectedDeliveryDate
     * @return {any}
     */
	public get expectedDeliveryDate(): any {
		return this._expectedDeliveryDate;
	}

    /**
     * Getter itemPrice
     * @return {any}
     */
	public get itemPrice(): any {
		return this._itemPrice;
	}

    /**
     * Getter levelBreachQtys
     * @return {any}
     */
	public get levelBreachQtys(): any {
		return this._levelBreachQtys;
	}

    /**
     * Getter isSelected
     * @return {boolean}
     */
	public get isSelected(): boolean {
		return this._isSelected;
	}

    /**
     * Getter isMainLink
     * @return {boolean}
     */
	public get isMainLink(): boolean {
		return this._isMainLink;
	}

    /**
     * Getter isLink
     * @return {boolean}
     */
	public get isLink(): boolean {
		return this._isLink;
	}

    /**
     * Getter linkIds
     * @return {number[]}
     */
	public get linkIds(): number[] {
		return this._linkIds;
	}

    /**
     * Getter lastRecord
     * @return {any[]}
     */
	public get lastRecord(): any[] {
		return this._lastRecord;
	}

    /**
     * Getter isDraft
     * @return {boolean}
     */
	public get isDraft(): boolean {
		return this._isDraft;
	}

    /**
     * Getter PoNo
     * @return {string}
     */
	public get PoNo(): string {
		return this._PoNo;
	}

    /**
     * Getter receivedCartonsCH
     * @return {number}
     */
	public get receivedCartonsCH(): number {
		return this._receivedCartonsCH;
	}

    /**
     * Getter rcCarton
     * @return {number}
     */
	public get rcCarton(): number {
		return this._rcCarton;
	}

    /**
     * Getter loadedQty
     * @return {number}
     */
	public get loadedQty(): number {
		return this._loadedQty;
	}

    /**
     * Getter receivedQty
     * @return {number}
     */
	public get receivedQty(): number {
		return this._receivedQty;
	}

    /**
     * Getter loadedQtyField
     * @return {number}
     */
	public get loadedQtyField(): number {
		return this._loadedQtyField;
	}

    /**
     * Getter rcId
     * @return {any}
     */
	public get rcId(): any {
		return this._rcId;
	}

    /**
     * Getter pendingQtyCarton
     * @return {number}
     */
	public get pendingQtyCarton(): number {
		return this._pendingQtyCarton;
	}

    /**
     * Getter totalPendingQty
     * @return {number}
     */
	public get totalPendingQty(): number {
		return this._totalPendingQty;
	}

    /**
     * Getter receivedChId
     * @return {number}
     */
	public get receivedChId(): number {
		return this._receivedChId;
	}

    /**
     * Getter calcReceiveKey
     * @return {number}
     */
	public get calcReceiveKey(): number {
		return this._calcReceiveKey;
	}

    /**
     * Getter getSumLoadedQty
     * @return {number}
     */
	public get getSumLoadedQty(): number {
		return this._getSumLoadedQty;
	}

    /**
     * Getter calcLoadedQty
     * @return {number}
     */
	public get calcLoadedQty(): number {
		return this._calcLoadedQty;
	}

    /**
     * Getter totalShippingExpense
     * @return {number}
     */
	public get totalShippingExpense(): number {
		return this._totalShippingExpense;
	}

    /**
     * Getter totalCBMExpense
     * @return {number}
     */
	public get totalCBMExpense(): number {
		return this._totalCBMExpense;
	}

    /**
     * Getter shippingExpense
     * @return {number}
     */
	public get shippingExpense(): number {
		return this._shippingExpense;
	}

    /**
     * Getter ChinaToSuratFinalPrice
     * @return {number}
     */
	public get ChinaToSuratFinalPrice(): number {
		return this._ChinaToSuratFinalPrice;
	}

    /**
     * Getter totalFinalCost
     * @return {number}
     */
	public get totalFinalCost(): number {
		return this._totalFinalCost;
	}

    /**
     * Getter TotalTransportationCharges
     * @return {number}
     */
	public get TotalTransportationCharges(): number {
		return this._TotalTransportationCharges;
	}

    /**
     * Getter total_load_amt_Ru
     * @return {number}
     */
	public get total_load_amt_Ru(): number {
		return this._total_load_amt_Ru;
	}

    /**
     * Getter totalExpense
     * @return {number}
     */
	public get totalExpense(): number {
		return this._totalExpense;
	}

    /**
     * Getter Total_Price
     * @return {number}
     */
	public get Total_Price(): number {
		return this._Total_Price;
	}

    /**
     * Getter totalReceivedAmount
     * @return {number}
     */
	public get totalReceivedAmount(): number {
		return this._totalReceivedAmount;
	}

    /**
     * Getter totalLoadedQtyAmount
     * @return {number}
     */
	public get totalLoadedQtyAmount(): number {
		return this._totalLoadedQtyAmount;
	}

    /**
     * Getter insurancePCS
     * @return {number}
     */
	public get insurancePCS(): number {
		return this._insurancePCS;
	}

    /**
     * Getter Total_Expense
     * @return {number}
     */
	public get Total_Expense(): number {
		return this._Total_Expense;
	}

    /**
     * Getter shipingCost
     * @return {number}
     */
	public get shipingCost(): number {
		return this._shipingCost;
	}

    /**
     * Getter totalItemAmount
     * @return {number}
     */
	public get totalItemAmount(): number {
		return this._totalItemAmount;
	}

    /**
     * Getter cbmCarton
     * @return {number}
     */
	public get cbmCarton(): number {
		return this._cbmCarton;
	}

    /**
     * Getter item_Amount
     * @return {number}
     */
	public get item_Amount(): number {
		return this._item_Amount;
	}

    /**
     * Getter total_Weight
     * @return {number}
     */
	public get total_Weight(): number {
		return this._total_Weight;
	}

    /**
     * Getter TotalInsurance
     * @return {number}
     */
	public get TotalInsurance(): number {
		return this._TotalInsurance;
	}

    /**
     * Getter Transportationcharges_Mumbai_to_Surat
     * @return {number}
     */
	public get Transportationcharges_Mumbai_to_Surat(): number {
		return this._Transportationcharges_Mumbai_to_Surat;
	}

    /**
     * Getter GSTAmount_PCS
     * @return {number}
     */
	public get GSTAmount_PCS(): number {
		return this._GSTAmount_PCS;
	}

    /**
     * Getter CraneExpense_PCS
     * @return {number}
     */
	public get CraneExpense_PCS(): number {
		return this._CraneExpense_PCS;
	}

    /**
     * Getter pendingLoaded
     * @return {number}
     */
	public get pendingLoaded(): number {
		return this._pendingLoaded;
	}

    /**
     * Getter shippingTypes
     * @return {any}
     */
	public get shippingTypes(): any {
		return this._shippingTypes;
	}

    /**
     * Getter gstAmt
     * @return {number}
     */
	public get gstAmt(): number {
		return this._gstAmt;
	}

    /**
     * Getter gstAmount
     * @return {number}
     */
	public get gstAmount(): number {
		return this._gstAmount;
	}

    /**
     * Getter totalPendingLoadedQty
     * @return {number}
     */
	public get totalPendingLoadedQty(): number {
		return this._totalPendingLoadedQty;
	}

    /**
     * Getter poDate
     * @return {any}
     */
	public get poDate(): any {
		return this._poDate;
	}

    /**
     * Getter conversationRateCurrencyName
     * @return {any}
     */
	public get conversationRateCurrencyName(): any {
		return this._conversationRateCurrencyName;
	}

    /**
     * Getter PendingQtyCarton
     * @return {number}
     */
	public get PendingQtyCarton(): number {
		return this._PendingQtyCarton;
	}

    /**
     * Getter PendingQtyCartonPcs
     * @return {number}
     */
	public get PendingQtyCartonPcs(): number {
		return this._PendingQtyCartonPcs;
	}

    /**
     * Getter rcCartonField
     * @return {number}
     */
	public get rcCartonField(): number {
		return this._rcCartonField;
	}

    /**
     * Getter Expense_PCS
     * @return {number}
     */
	public get Expense_PCS(): number {
		return this._Expense_PCS;
	}

    /**
     * Getter Percentage
     * @return {number}
     */
	public get Percentage(): number {
		return this._Percentage;
	}

    /**
     * Getter Weight_kg
     * @return {number}
     */
	public get Weight_kg(): number {
		return this._Weight_kg;
	}

    /**
     * Getter Weight_Carton
     * @return {number}
     */
	public get Weight_Carton(): number {
		return this._Weight_Carton;
	}

    /**
     * Getter container
     * @return {any}
     */
	public get container(): any {
		return this._container;
	}

    /**
     * Getter poChinaMapId
     * @return {number}
     */
	public get poChinaMapId(): number {
		return this._poChinaMapId;
	}

    /**
     * Getter poCreateIdF
     * @return {number}
     */
	public get poCreateIdF(): number {
		return this._poCreateIdF;
	}

    /**
     * Getter grnReceivedQty
     * @return {number}
     */
	public get grnReceivedQty(): number {
		return this._grnReceivedQty;
	}

    /**
     * Getter totalQty
     * @return {number}
     */
	public get totalQty(): number {
		return this._totalQty;
	}

    /**
     * Getter releasedQty
     * @return {number}
     */
	public get releasedQty(): number {
		return this._releasedQty;
	}

    /**
     * Getter branchCartonMapping
     * @return {any[]}
     */
	public get branchCartonMapping(): any[] {
		return this._branchCartonMapping;
	}

    /**
     * Getter pendingField
     * @return {number}
     */
	public get pendingField(): number {
		return this._pendingField;
	}

    /**
     * Getter j4Amt
     * @return {number}
     */
	public get j4Amt(): number {
		return this._j4Amt;
	}

    /**
     * Getter cartonWeightUnit
     * @return {number}
     */
	public get cartonWeightUnit(): number {
		return this._cartonWeightUnit;
	}

    /**
     * Getter loadedMapId
     * @return {number}
     */
	public get loadedMapId(): number {
		return this._loadedMapId;
	}

    /**
     * Getter releaseQtyField
     * @return {number}
     */
	public get releaseQtyField(): number {
		return this._releaseQtyField;
	}

    /**
     * Getter gstPers
     * @return {any}
     */
	public get gstPers(): any {
		return this._gstPers;
	}

    /**
     * Getter gst_amounts
     * @return {number}
     */
	public get gst_amounts(): number {
		return this._gst_amounts;
	}

    /**
     * Getter rate
     * @return {number}
     */
	public get rate(): number {
		return this._rate;
	}

    /**
     * Getter gst_amount
     * @return {number}
     */
	public get gst_amount(): number {
		return this._gst_amount;
	}

    /**
     * Getter gstPer
     * @return {any}
     */
	public get gstPer(): any {
		return this._gstPer;
	}

    /**
     * Getter PendingReleasedQtyCarton
     * @return {number}
     */
	public get PendingReleasedQtyCarton(): number {
		return this._PendingReleasedQtyCarton;
	}

    /**
     * Getter PendingReleasedQtyCartonPcs
     * @return {number}
     */
	public get PendingReleasedQtyCartonPcs(): number {
		return this._PendingReleasedQtyCartonPcs;
	}

    /**
     * Getter releasedDate
     * @return {any}
     */
	public get releasedDate(): any {
		return this._releasedDate;
	}

    /**
     * Getter colors
     * @return {string[]}
     */
	public get colors(): string[] {
		return this._colors;
	}

    /**
     * Getter isDoneCartonMapping
     * @return {string}
     */
	public get isDoneCartonMapping(): string {
		return this._isDoneCartonMapping;
	}

    /**
     * Getter poNumber
     * @return {string}
     */
	public get poNumber(): string {
		return this._poNumber;
	}

    /**
     * Getter poOrderDate
     * @return {string}
     */
	public get poOrderDate(): string {
		return this._poOrderDate;
	}

    /**
     * Getter realCartonLength
     * @return {number}
     */
	public get realCartonLength(): number {
		return this._realCartonLength;
	}

    /**
     * Getter realCartonWidth
     * @return {number}
     */
	public get realCartonWidth(): number {
		return this._realCartonWidth;
	}

    /**
     * Getter realCartonHeight
     * @return {number}
     */
	public get realCartonHeight(): number {
		return this._realCartonHeight;
	}

    /**
     * Getter realCBMPerCarton
     * @return {number}
     */
	public get realCBMPerCarton(): number {
		return this._realCBMPerCarton;
	}

    /**
     * Getter realCartonWeight
     * @return {number}
     */
	public get realCartonWeight(): number {
		return this._realCartonWeight;
	}

    /**
     * Getter totalCBM
     * @return {number}
     */
	public get totalCBM(): number {
		return this._totalCBM;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Setter associatedIds
     * @param {number} value
     */
	public set associatedIds(value: number) {
		this._associatedIds = value;
	}

    /**
     * Setter item
     * @param {any} value
     */
	public set item(value: any) {
		this._item = value;
	}

    /**
     * Setter skuId
     * @param {any} value
     */
	public set skuId(value: any) {
		this._skuId = value;
	}

    /**
     * Setter marka
     * @param {string} value
     */
	public set marka(value: string) {
		this._marka = value;
	}

    /**
     * Setter note
     * @param {string} value
     */
	public set note(value: string) {
		this._note = value;
	}

    /**
     * Setter englishComment
     * @param {string} value
     */
	public set englishComment(value: string) {
		this._englishComment = value;
	}

    /**
     * Setter chinaComment
     * @param {string} value
     */
	public set chinaComment(value: string) {
		this._chinaComment = value;
	}

    /**
     * Setter poCarton
     * @param {number} value
     */
	public set poCarton(value: number) {
		this._poCarton = value;
	}

    /**
     * Setter pricePerCarton
     * @param {number} value
     */
	public set pricePerCarton(value: number) {
		this._pricePerCarton = value;
	}

    /**
     * Setter pricePerItem
     * @param {number} value
     */
	public set pricePerItem(value: number) {
		this._pricePerItem = value;
	}

    /**
     * Setter expDeliveryCost
     * @param {number} value
     */
	public set expDeliveryCost(value: number) {
		this._expDeliveryCost = value;
	}

    /**
     * Setter itemDimLength
     * @param {number} value
     */
	public set itemDimLength(value: number) {
		this._itemDimLength = value;
	}

    /**
     * Setter itemDimWidth
     * @param {number} value
     */
	public set itemDimWidth(value: number) {
		this._itemDimWidth = value;
	}

    /**
     * Setter itemDimHeight
     * @param {number} value
     */
	public set itemDimHeight(value: number) {
		this._itemDimHeight = value;
	}

    /**
     * Setter dimAge
     * @param {number} value
     */
	public set dimAge(value: number) {
		this._dimAge = value;
	}

    /**
     * Setter transportCharges
     * @param {number} value
     */
	public set transportCharges(value: number) {
		this._transportCharges = value;
	}

    /**
     * Setter craneExpense
     * @param {number} value
     */
	public set craneExpense(value: number) {
		this._craneExpense = value;
	}

    /**
     * Setter cartonWeight
     * @param {number} value
     */
	public set cartonWeight(value: number) {
		this._cartonWeight = value;
	}

    /**
     * Setter cartonWeightRu
     * @param {number} value
     */
	public set cartonWeightRu(value: number) {
		this._cartonWeightRu = value;
	}

    /**
     * Setter percentage
     * @param {number} value
     */
	public set percentage(value: number) {
		this._percentage = value;
	}

    /**
     * Setter expensePcs
     * @param {number} value
     */
	public set expensePcs(value: number) {
		this._expensePcs = value;
	}

    /**
     * Setter qcCheckListId
     * @param {number} value
     */
	public set qcCheckListId(value: number) {
		this._qcCheckListId = value;
	}

    /**
     * Setter extraExpense
     * @param {number} value
     */
	public set extraExpense(value: number) {
		this._extraExpense = value;
	}

    /**
     * Setter purchaseRation
     * @param {number} value
     */
	public set purchaseRation(value: number) {
		this._purchaseRation = value;
	}

    /**
     * Setter itemId
     * @param {number} value
     */
	public set itemId(value: number) {
		this._itemId = value;
	}

    /**
     * Setter cartonLength
     * @param {number} value
     */
	public set cartonLength(value: number) {
		this._cartonLength = value;
	}

    /**
     * Setter cartonHeight
     * @param {number} value
     */
	public set cartonHeight(value: number) {
		this._cartonHeight = value;
	}

    /**
     * Setter cartonWidth
     * @param {number} value
     */
	public set cartonWidth(value: number) {
		this._cartonWidth = value;
	}

    /**
     * Setter dimUpdatedDate
     * @param {any} value
     */
	public set dimUpdatedDate(value: any) {
		this._dimUpdatedDate = value;
	}

    /**
     * Setter dimUniteId
     * @param {number} value
     */
	public set dimUniteId(value: number) {
		this._dimUniteId = value;
	}

    /**
     * Setter dimUniteName
     * @param {number} value
     */
	public set dimUniteName(value: number) {
		this._dimUniteName = value;
	}

    /**
     * Setter dimUnitShortCode
     * @param {string} value
     */
	public set dimUnitShortCode(value: string) {
		this._dimUnitShortCode = value;
	}

    /**
     * Setter totalWeight
     * @param {number} value
     */
	public set totalWeight(value: number) {
		this._totalWeight = value;
	}

    /**
     * Setter totalLoadAmt
     * @param {number} value
     */
	public set totalLoadAmt(value: number) {
		this._totalLoadAmt = value;
	}

    /**
     * Setter shippingCostperPieceINR
     * @param {number} value
     */
	public set shippingCostperPieceINR(value: number) {
		this._shippingCostperPieceINR = value;
	}

    /**
     * Setter totalShippingExpWeight
     * @param {number} value
     */
	public set totalShippingExpWeight(value: number) {
		this._totalShippingExpWeight = value;
	}

    /**
     * Setter colorId
     * @param {number[]} value
     */
	public set colorId(value: number[]) {
		this._colorId = value;
	}

    /**
     * Setter itemDropdown
     * @param {any[]} value
     */
	public set itemDropdown(value: any[]) {
		this._itemDropdown = value;
	}

    /**
     * Setter colorDropdown
     * @param {any[]} value
     */
	public set colorDropdown(value: any[]) {
		this._colorDropdown = value;
	}

    /**
     * Setter formattedName
     * @param {string} value
     */
	public set formattedName(value: string) {
		this._formattedName = value;
	}

    /**
     * Setter originalName
     * @param {string} value
     */
	public set originalName(value: string) {
		this._originalName = value;
	}

    /**
     * Setter itemFileFormatedName
     * @param {string} value
     */
	public set itemFileFormatedName(value: string) {
		this._itemFileFormatedName = value;
	}

    /**
     * Setter itemFileOriginalName
     * @param {string} value
     */
	public set itemFileOriginalName(value: string) {
		this._itemFileOriginalName = value;
	}

    /**
     * Setter itemSKUId
     * @param {string} value
     */
	public set itemSKUId(value: string) {
		this._itemSKUId = value;
	}

    /**
     * Setter displayName
     * @param {string} value
     */
	public set displayName(value: string) {
		this._displayName = value;
	}

    /**
     * Setter itemGroupName
     * @param {string} value
     */
	public set itemGroupName(value: string) {
		this._itemGroupName = value;
	}

    /**
     * Setter fromDate
     * @param {string} value
     */
	public set fromDate(value: string) {
		this._fromDate = value;
	}

    /**
     * Setter toDate
     * @param {string} value
     */
	public set toDate(value: string) {
		this._toDate = value;
	}

    /**
     * Setter advanceDate
     * @param {string} value
     */
	public set advanceDate(value: string) {
		this._advanceDate = value;
	}

    /**
     * Setter totalPcsQty
     * @param {number} value
     */
	public set totalPcsQty(value: number) {
		this._totalPcsQty = value;
	}

    /**
     * Setter totalAmount
     * @param {number} value
     */
	public set totalAmount(value: number) {
		this._totalAmount = value;
	}

    /**
     * Setter totalAmountWithExp
     * @param {number} value
     */
	public set totalAmountWithExp(value: number) {
		this._totalAmountWithExp = value;
	}

    /**
     * Setter totalAmountWithExpInINR
     * @param {number} value
     */
	public set totalAmountWithExpInINR(value: number) {
		this._totalAmountWithExpInINR = value;
	}

    /**
     * Setter chinaFinalExpextedCode
     * @param {number} value
     */
	public set chinaFinalExpextedCode(value: number) {
		this._chinaFinalExpextedCode = value;
	}

    /**
     * Setter transportationChargesM2SperCarton
     * @param {number} value
     */
	public set transportationChargesM2SperCarton(value: number) {
		this._transportationChargesM2SperCarton = value;
	}

    /**
     * Setter totalTransportationChargesM2S
     * @param {number} value
     */
	public set totalTransportationChargesM2S(value: number) {
		this._totalTransportationChargesM2S = value;
	}

    /**
     * Setter transportationChargesM2SperPCS
     * @param {number} value
     */
	public set transportationChargesM2SperPCS(value: number) {
		this._transportationChargesM2SperPCS = value;
	}

    /**
     * Setter totalInsurance
     * @param {number} value
     */
	public set totalInsurance(value: number) {
		this._totalInsurance = value;
	}

    /**
     * Setter insurancePerPcs
     * @param {number} value
     */
	public set insurancePerPcs(value: number) {
		this._insurancePerPcs = value;
	}

    /**
     * Setter gstAmtPerPcs
     * @param {number} value
     */
	public set gstAmtPerPcs(value: number) {
		this._gstAmtPerPcs = value;
	}

    /**
     * Setter craneExpPcs
     * @param {number} value
     */
	public set craneExpPcs(value: number) {
		this._craneExpPcs = value;
	}

    /**
     * Setter totalExp
     * @param {number} value
     */
	public set totalExp(value: number) {
		this._totalExp = value;
	}

    /**
     * Setter C2SFinalPrice
     * @param {number} value
     */
	public set C2SFinalPrice(value: number) {
		this._C2SFinalPrice = value;
	}

    /**
     * Setter totalExpPCSper
     * @param {number} value
     */
	public set totalExpPCSper(value: number) {
		this._totalExpPCSper = value;
	}

    /**
     * Setter totalFinalCostPCSper
     * @param {number} value
     */
	public set totalFinalCostPCSper(value: number) {
		this._totalFinalCostPCSper = value;
	}

    /**
     * Setter totalItemAmt
     * @param {number} value
     */
	public set totalItemAmt(value: number) {
		this._totalItemAmt = value;
	}

    /**
     * Setter cbmPerCarton
     * @param {number} value
     */
	public set cbmPerCarton(value: number) {
		this._cbmPerCarton = value;
	}

    /**
     * Setter totalCbm
     * @param {number} value
     */
	public set totalCbm(value: number) {
		this._totalCbm = value;
	}

    /**
     * Setter cbmPrice
     * @param {number} value
     */
	public set cbmPrice(value: number) {
		this._cbmPrice = value;
	}

    /**
     * Setter totalCBMExpenseINR
     * @param {number} value
     */
	public set totalCBMExpenseINR(value: number) {
		this._totalCBMExpenseINR = value;
	}

    /**
     * Setter shippingExpPerPCS
     * @param {number} value
     */
	public set shippingExpPerPCS(value: number) {
		this._shippingExpPerPCS = value;
	}

    /**
     * Setter chinaToSuratPadtar
     * @param {number} value
     */
	public set chinaToSuratPadtar(value: number) {
		this._chinaToSuratPadtar = value;
	}

    /**
     * Setter conversationRate
     * @param {number} value
     */
	public set conversationRate(value: number) {
		this._conversationRate = value;
	}

    /**
     * Setter associatedItemColors
     * @param {any[]} value
     */
	public set associatedItemColors(value: any[]) {
		this._associatedItemColors = value;
	}

    /**
     * Setter checkList
     * @param {any[]} value
     */
	public set checkList(value: any[]) {
		this._checkList = value;
	}

    /**
     * Setter colorName
     * @param {any} value
     */
	public set colorName(value: any) {
		this._colorName = value;
	}

    /**
     * Setter unitMaster
     * @param {any} value
     */
	public set unitMaster(value: any) {
		this._unitMaster = value;
	}

    /**
     * Setter expectedDeliveryDate
     * @param {any} value
     */
	public set expectedDeliveryDate(value: any) {
		this._expectedDeliveryDate = value;
	}

    /**
     * Setter itemPrice
     * @param {any} value
     */
	public set itemPrice(value: any) {
		this._itemPrice = value;
	}

    /**
     * Setter levelBreachQtys
     * @param {any} value
     */
	public set levelBreachQtys(value: any) {
		this._levelBreachQtys = value;
	}

    /**
     * Setter isSelected
     * @param {boolean} value
     */
	public set isSelected(value: boolean) {
		this._isSelected = value;
	}

    /**
     * Setter isMainLink
     * @param {boolean} value
     */
	public set isMainLink(value: boolean) {
		this._isMainLink = value;
	}

    /**
     * Setter isLink
     * @param {boolean} value
     */
	public set isLink(value: boolean) {
		this._isLink = value;
	}

    /**
     * Setter linkIds
     * @param {number[]} value
     */
	public set linkIds(value: number[]) {
		this._linkIds = value;
	}

    /**
     * Setter lastRecord
     * @param {any[]} value
     */
	public set lastRecord(value: any[]) {
		this._lastRecord = value;
	}

    /**
     * Setter isDraft
     * @param {boolean} value
     */
	public set isDraft(value: boolean) {
		this._isDraft = value;
	}

    /**
     * Setter PoNo
     * @param {string} value
     */
	public set PoNo(value: string) {
		this._PoNo = value;
	}

    /**
     * Setter receivedCartonsCH
     * @param {number} value
     */
	public set receivedCartonsCH(value: number) {
		this._receivedCartonsCH = value;
	}

    /**
     * Setter rcCarton
     * @param {number} value
     */
	public set rcCarton(value: number) {
		this._rcCarton = value;
	}

    /**
     * Setter loadedQty
     * @param {number} value
     */
	public set loadedQty(value: number) {
		this._loadedQty = value;
	}

    /**
     * Setter receivedQty
     * @param {number} value
     */
	public set receivedQty(value: number) {
		this._receivedQty = value;
	}

    /**
     * Setter loadedQtyField
     * @param {number} value
     */
	public set loadedQtyField(value: number) {
		this._loadedQtyField = value;
	}

    /**
     * Setter rcId
     * @param {any} value
     */
	public set rcId(value: any) {
		this._rcId = value;
	}

    /**
     * Setter pendingQtyCarton
     * @param {number} value
     */
	public set pendingQtyCarton(value: number) {
		this._pendingQtyCarton = value;
	}

    /**
     * Setter totalPendingQty
     * @param {number} value
     */
	public set totalPendingQty(value: number) {
		this._totalPendingQty = value;
	}

    /**
     * Setter receivedChId
     * @param {number} value
     */
	public set receivedChId(value: number) {
		this._receivedChId = value;
	}

    /**
     * Setter calcReceiveKey
     * @param {number} value
     */
	public set calcReceiveKey(value: number) {
		this._calcReceiveKey = value;
	}

    /**
     * Setter getSumLoadedQty
     * @param {number} value
     */
	public set getSumLoadedQty(value: number) {
		this._getSumLoadedQty = value;
	}

    /**
     * Setter calcLoadedQty
     * @param {number} value
     */
	public set calcLoadedQty(value: number) {
		this._calcLoadedQty = value;
	}

    /**
     * Setter totalShippingExpense
     * @param {number} value
     */
	public set totalShippingExpense(value: number) {
		this._totalShippingExpense = value;
	}

    /**
     * Setter totalCBMExpense
     * @param {number} value
     */
	public set totalCBMExpense(value: number) {
		this._totalCBMExpense = value;
	}

    /**
     * Setter shippingExpense
     * @param {number} value
     */
	public set shippingExpense(value: number) {
		this._shippingExpense = value;
	}

    /**
     * Setter ChinaToSuratFinalPrice
     * @param {number} value
     */
	public set ChinaToSuratFinalPrice(value: number) {
		this._ChinaToSuratFinalPrice = value;
	}

    /**
     * Setter totalFinalCost
     * @param {number} value
     */
	public set totalFinalCost(value: number) {
		this._totalFinalCost = value;
	}

    /**
     * Setter TotalTransportationCharges
     * @param {number} value
     */
	public set TotalTransportationCharges(value: number) {
		this._TotalTransportationCharges = value;
	}

    /**
     * Setter total_load_amt_Ru
     * @param {number} value
     */
	public set total_load_amt_Ru(value: number) {
		this._total_load_amt_Ru = value;
	}

    /**
     * Setter totalExpense
     * @param {number} value
     */
	public set totalExpense(value: number) {
		this._totalExpense = value;
	}

    /**
     * Setter Total_Price
     * @param {number} value
     */
	public set Total_Price(value: number) {
		this._Total_Price = value;
	}

    /**
     * Setter totalReceivedAmount
     * @param {number} value
     */
	public set totalReceivedAmount(value: number) {
		this._totalReceivedAmount = value;
	}

    /**
     * Setter totalLoadedQtyAmount
     * @param {number} value
     */
	public set totalLoadedQtyAmount(value: number) {
		this._totalLoadedQtyAmount = value;
	}

    /**
     * Setter insurancePCS
     * @param {number} value
     */
	public set insurancePCS(value: number) {
		this._insurancePCS = value;
	}

    /**
     * Setter Total_Expense
     * @param {number} value
     */
	public set Total_Expense(value: number) {
		this._Total_Expense = value;
	}

    /**
     * Setter shipingCost
     * @param {number} value
     */
	public set shipingCost(value: number) {
		this._shipingCost = value;
	}

    /**
     * Setter totalItemAmount
     * @param {number} value
     */
	public set totalItemAmount(value: number) {
		this._totalItemAmount = value;
	}

    /**
     * Setter cbmCarton
     * @param {number} value
     */
	public set cbmCarton(value: number) {
		this._cbmCarton = value;
	}

    /**
     * Setter item_Amount
     * @param {number} value
     */
	public set item_Amount(value: number) {
		this._item_Amount = value;
	}

    /**
     * Setter total_Weight
     * @param {number} value
     */
	public set total_Weight(value: number) {
		this._total_Weight = value;
	}

    /**
     * Setter TotalInsurance
     * @param {number} value
     */
	public set TotalInsurance(value: number) {
		this._TotalInsurance = value;
	}

    /**
     * Setter Transportationcharges_Mumbai_to_Surat
     * @param {number} value
     */
	public set Transportationcharges_Mumbai_to_Surat(value: number) {
		this._Transportationcharges_Mumbai_to_Surat = value;
	}

    /**
     * Setter GSTAmount_PCS
     * @param {number} value
     */
	public set GSTAmount_PCS(value: number) {
		this._GSTAmount_PCS = value;
	}

    /**
     * Setter CraneExpense_PCS
     * @param {number} value
     */
	public set CraneExpense_PCS(value: number) {
		this._CraneExpense_PCS = value;
	}

    /**
     * Setter pendingLoaded
     * @param {number} value
     */
	public set pendingLoaded(value: number) {
		this._pendingLoaded = value;
	}

    /**
     * Setter shippingTypes
     * @param {any} value
     */
	public set shippingTypes(value: any) {
		this._shippingTypes = value;
	}

    /**
     * Setter gstAmt
     * @param {number} value
     */
	public set gstAmt(value: number) {
		this._gstAmt = value;
	}

    /**
     * Setter gstAmount
     * @param {number} value
     */
	public set gstAmount(value: number) {
		this._gstAmount = value;
	}

    /**
     * Setter totalPendingLoadedQty
     * @param {number} value
     */
	public set totalPendingLoadedQty(value: number) {
		this._totalPendingLoadedQty = value;
	}

    /**
     * Setter poDate
     * @param {any} value
     */
	public set poDate(value: any) {
		this._poDate = value;
	}

    /**
     * Setter conversationRateCurrencyName
     * @param {any} value
     */
	public set conversationRateCurrencyName(value: any) {
		this._conversationRateCurrencyName = value;
	}

    /**
     * Setter PendingQtyCarton
     * @param {number} value
     */
	public set PendingQtyCarton(value: number) {
		this._PendingQtyCarton = value;
	}

    /**
     * Setter PendingQtyCartonPcs
     * @param {number} value
     */
	public set PendingQtyCartonPcs(value: number) {
		this._PendingQtyCartonPcs = value;
	}

    /**
     * Setter rcCartonField
     * @param {number} value
     */
	public set rcCartonField(value: number) {
		this._rcCartonField = value;
	}

    /**
     * Setter Expense_PCS
     * @param {number} value
     */
	public set Expense_PCS(value: number) {
		this._Expense_PCS = value;
	}

    /**
     * Setter Percentage
     * @param {number} value
     */
	public set Percentage(value: number) {
		this._Percentage = value;
	}

    /**
     * Setter Weight_kg
     * @param {number} value
     */
	public set Weight_kg(value: number) {
		this._Weight_kg = value;
	}

    /**
     * Setter Weight_Carton
     * @param {number} value
     */
	public set Weight_Carton(value: number) {
		this._Weight_Carton = value;
	}

    /**
     * Setter container
     * @param {any} value
     */
	public set container(value: any) {
		this._container = value;
	}

    /**
     * Setter poChinaMapId
     * @param {number} value
     */
	public set poChinaMapId(value: number) {
		this._poChinaMapId = value;
	}

    /**
     * Setter poCreateIdF
     * @param {number} value
     */
	public set poCreateIdF(value: number) {
		this._poCreateIdF = value;
	}

    /**
     * Setter grnReceivedQty
     * @param {number} value
     */
	public set grnReceivedQty(value: number) {
		this._grnReceivedQty = value;
	}

    /**
     * Setter totalQty
     * @param {number} value
     */
	public set totalQty(value: number) {
		this._totalQty = value;
	}

    /**
     * Setter releasedQty
     * @param {number} value
     */
	public set releasedQty(value: number) {
		this._releasedQty = value;
	}

    /**
     * Setter branchCartonMapping
     * @param {any[]} value
     */
	public set branchCartonMapping(value: any[]) {
		this._branchCartonMapping = value;
	}

    /**
     * Setter pendingField
     * @param {number} value
     */
	public set pendingField(value: number) {
		this._pendingField = value;
	}

    /**
     * Setter j4Amt
     * @param {number} value
     */
	public set j4Amt(value: number) {
		this._j4Amt = value;
	}

    /**
     * Setter cartonWeightUnit
     * @param {number} value
     */
	public set cartonWeightUnit(value: number) {
		this._cartonWeightUnit = value;
	}

    /**
     * Setter loadedMapId
     * @param {number} value
     */
	public set loadedMapId(value: number) {
		this._loadedMapId = value;
	}

    /**
     * Setter releaseQtyField
     * @param {number} value
     */
	public set releaseQtyField(value: number) {
		this._releaseQtyField = value;
	}

    /**
     * Setter gstPers
     * @param {any} value
     */
	public set gstPers(value: any) {
		this._gstPers = value;
	}

    /**
     * Setter gst_amounts
     * @param {number} value
     */
	public set gst_amounts(value: number) {
		this._gst_amounts = value;
	}

    /**
     * Setter rate
     * @param {number} value
     */
	public set rate(value: number) {
		this._rate = value;
	}

    /**
     * Setter gst_amount
     * @param {number} value
     */
	public set gst_amount(value: number) {
		this._gst_amount = value;
	}

    /**
     * Setter gstPer
     * @param {any} value
     */
	public set gstPer(value: any) {
		this._gstPer = value;
	}

    /**
     * Setter PendingReleasedQtyCarton
     * @param {number} value
     */
	public set PendingReleasedQtyCarton(value: number) {
		this._PendingReleasedQtyCarton = value;
	}

    /**
     * Setter PendingReleasedQtyCartonPcs
     * @param {number} value
     */
	public set PendingReleasedQtyCartonPcs(value: number) {
		this._PendingReleasedQtyCartonPcs = value;
	}

    /**
     * Setter releasedDate
     * @param {any} value
     */
	public set releasedDate(value: any) {
		this._releasedDate = value;
	}

    /**
     * Setter colors
     * @param {string[]} value
     */
	public set colors(value: string[]) {
		this._colors = value;
	}

    /**
     * Setter isDoneCartonMapping
     * @param {string} value
     */
	public set isDoneCartonMapping(value: string) {
		this._isDoneCartonMapping = value;
	}

    /**
     * Setter poNumber
     * @param {string} value
     */
	public set poNumber(value: string) {
		this._poNumber = value;
	}

    /**
     * Setter poOrderDate
     * @param {string} value
     */
	public set poOrderDate(value: string) {
		this._poOrderDate = value;
	}

    /**
     * Setter realCartonLength
     * @param {number} value
     */
	public set realCartonLength(value: number) {
		this._realCartonLength = value;
	}

    /**
     * Setter realCartonWidth
     * @param {number} value
     */
	public set realCartonWidth(value: number) {
		this._realCartonWidth = value;
	}

    /**
     * Setter realCartonHeight
     * @param {number} value
     */
	public set realCartonHeight(value: number) {
		this._realCartonHeight = value;
	}

    /**
     * Setter realCBMPerCarton
     * @param {number} value
     */
	public set realCBMPerCarton(value: number) {
		this._realCBMPerCarton = value;
	}

    /**
     * Setter realCartonWeight
     * @param {number} value
     */
	public set realCartonWeight(value: number) {
		this._realCartonWeight = value;
	}

    /**
     * Setter totalCBM
     * @param {number} value
     */
	public set totalCBM(value: number) {
		this._totalCBM = value;
	}

}