<div class="page-content">
  <div class="page-title-wrapper">
    <div class="page-title-left">
      <h4>Payments</h4>
    </div>
    <div class="page-title-right">
      <button class="btn btn-sm btn-primary btn-icon-text" [routerLink]="['/users/purchases/payments/new-payments']">
        <i class="th th-outline-add-circle"></i>Add New</button>

    </div>
  </div>
  <div class="content-area">
    <div class="page-filters">
      <div class="page-filters-left">
        <div class="form-group form-group-sm filter-search">
          <div class="form-group-icon-start">
            <i class="th th-outline-search-normal-1 icon-broder "></i>
            <input type="search" class="form-control" placeholder="Search by name">
          </div>
        </div>
        <div class="form-group form-group-sm">
          <div class="form-group-icon-end">
            <i class="th th-outline-calendar"></i>
            <input type="text" class="form-control" placeholder="Select Date">
          </div>
        </div>
        <div class="form-group theme-ngselect form-group-sm">
          <ng-select placeholder="Shop No" [multiple]="false" [clearable]="false" bindLabel="name"
            bindValue="id">
          </ng-select>
        </div>
      </div>
      <div class="page-filters-right">
        <div class="dropdown export-dropdown">
          <button type="button" class="btn btn-sm btn-outline-white dropdown-toggle" data-bs-toggle="dropdown"
            aria-expanded="false">
            Export
          </button>
          <ul class="dropdown-menu">
            <li><a class="dropdown-item" href="#">Action</a></li>
            <li><a class="dropdown-item" href="#">Another action</a></li>
            <li><a class="dropdown-item" href="#">Something else here</a></li>
          </ul>
        </div>
      </div>
    </div>
    <div class="card card-theme card-table-sticky">
      <div class="card-body p-0">

        <div class="table-responsive">
          <table class="table-theme table table-bordered tbl-collapse table-sticky">
            <thead class="border-less">
              <tr>
                <th>
                  Shop No
                </th>
                <th>Supplier Name</th>
                <th>Mobile No</th>
                <th>Credit</th>
                <th>Due Payment</th>
                <th class="text-center">Action</th>
              </tr>
            </thead>
            <tbody>
              <ng-container *ngFor="let item of [1]">
                <tr>
                  <td class="tbl-bold">54554554</td>
                  <td>Yen - Chiko</td>
                  <td>************</td>
                  <td>
                    <span class="text-success fw-600">+5000</span>
                  </td>
                  <td>0.00</td>

                  <td class="tbl-action">
                    <div class="tbl-action-group justify-content-start">
                      <button class="btn btn-xs btn-light-warning btn-icon" ngbTooltip="Tooltip" placement="bottom"
                        container="body" triggers="hover">
                        <i class="bi bi-currency-yen"></i>
                      </button>
                      <button class="btn btn-xs btn-light-danger btn-icon" data-bs-toggle="modal"
                        data-bs-target="#deleteModal" ngbTooltip="Delete" placement="bottom" container="body"
                        triggers="hover">
                        <i class="th th-outline-trash"></i>
                      </button>
                      <button class="btn btn-xs btn-light-white btn-icon collapse-arrow collapsed"
                        data-bs-toggle="collapse" data-bs-target="#collapseHierarchyLevel1" aria-expanded="false"
                        aria-controls="collapseHierarchyLevel1"><i class="th th-outline-arrow-right-3"></i></button>
                    </div>
                  </td>
                </tr>
                <tr class="collapse" id="collapseHierarchyLevel1">
                  <td colspan="100" class="p-2">
                    <div class="table-responsive">
                      <table class="table-theme table-hover table table-bordered">
                        <thead class="border-less">
                          <tr>
                            <th>Payment ID</th>
                            <th>Date</th>
                            <th>Amount Paid (RMB)</th>
                            <th>Conversion Rate</th>
                            <th>Amount Paid (INR)</th>
                            <th>GSTIN Account</th>
                            <th class="text-center">Action</th>
                          </tr>
                        </thead>

                        <ng-container *ngFor="let item of [1,2]">
                          <tbody>
                            <tr>
                              <td class="tbl-bold">PAY001</td>
                              <td>08/07/2025</td>
                              <td>4100</td>
                              <td>12</td>
                              <td>49200</td>
                              <td>Alpyog</td>
                              <td class="tbl-action">
                                <div class="tbl-action-group justify-content-start">
                                  <button class="btn btn-xs btn-outline-white btn-icon" ngbTooltip="Tooltip"
                                    placement="bottom" container="body" triggers="hover">
                                    <i class="th th-outline-eye"></i>
                                  </button>
                                  <button class="btn btn-xs btn-light-danger btn-icon" data-bs-toggle="modal"
                                    data-bs-target="#deleteModal" ngbTooltip="Delete" placement="bottom"
                                    container="body" triggers="hover">
                                    <i class="th th-outline-trash"></i>
                                  </button>
                                  <button class="btn btn-xs text-color btn-icon btn-link collapse-arrow collapsed"
                                    data-bs-toggle="collapse" (click)="toggleExpand()" [attr.aria-expanded]="isExpanded"
                                    href="#table-collapse-child-1" role="button" aria-expanded="false"
                                    aria-controls="table-collapse-child-1">
                                    <i class="th th-outline-arrow-right-3"></i>
                                  </button>
                                </div>
                              </td>
                            </tr>

                            <tr class="collapse" id="table-collapse-child-1">
                              <td colspan="100" class="tbl-bg-gray p-2">
                                <div class="table-responsive">
                                  <table class="table-theme table table-bordered ">
                                    <thead class="border-less">
                                      <tr>
                                        <th class="">
                                          Received ID
                                        </th>
                                        <th>Date</th>
                                        <th>Amount (RMB)</th>
                                        <th>Conversion Rate</th>
                                        <th>Amount (INR)</th>
                                        <th>Payment Status</th>
                                      </tr>
                                    </thead>
                                    <tbody>
                                      <tr *ngFor="let item of [1,2,3]">
                                        <td>#545455</td>
                                        <td>08/07/2025</td>
                                        <td>100</td>
                                        <td>12.0</td>
                                        <td>1200</td>
                                        <td>
                                          <span class="text-success fw-600">Paid</span>
                                        </td>
                                      </tr>
                                    </tbody>
                                  </table>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </ng-container>
                      </table>
                    </div>
                  </td>
                </tr>
              </ng-container>
            </tbody>
          </table>
        </div>




      </div>
    </div>
    <div class="paginationbox pagination-fixed">
      <app-pagination></app-pagination>
    </div>
  </div>
</div>

<!-- ----------------------------------------------------------------------- -->
<!--                           Delete Modal Start                            -->
<!-- ----------------------------------------------------------------------- -->
<div class="modal modal-theme modal-confirmation modal-reject fade" id="deleteModal" tabindex="-1"
  aria-labelledby="deleteModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
            class='th th-close'></i></button>
      </div>
      <div class="modal-body">
        <div class="modal-confirmation-container">
          <div class="modal-confirmation-icon">
            <i class="th th-bold-trash"></i>
          </div>
          <div class="modal-confirmation-content">
            <h5>Are You Sure?</h5>
            <p>You want to Delete <b>{{"SelectedRecords"}}</b> Payment.</p>
          </div>
        </div>
        <div class="modal-button-group modal-full-width-btn">
          <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
          <button type="button" class="btn btn-primary btn-icon-text"> <i class="th th-outline-tick-circle"></i>
            Delete</button>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                           Delete Modal End                            -->
<!-- ----------------------------------------------------------------------- -->