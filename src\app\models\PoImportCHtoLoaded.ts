import { deserializeAs, serializeAs } from 'cerialize';

export class POImportCHtoLoaded {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('importerId')
    @deserializeAs('importerId')
    private _importerId: number;

    @serializeAs('containerId')
    @deserializeAs('containerId')
    private _containerId: number;

    @serializeAs('containerName')
    @deserializeAs('containerName')
    private _containerName: string;

    @deserializeAs('selectedContainer')
    private _selectedContainer: any;

    @serializeAs('expectedDeliveryDate')
    @deserializeAs('expectedDeliveryDate')
    private _expectedDeliveryDate: string;

    @deserializeAs('t_expectedDeliveryDate')
    private _t_expectedDeliveryDate: any;

    @serializeAs('expectedDeliveryDay')
    @deserializeAs('expectedDeliveryDay')
    private _expectedDeliveryDay: number;

    @serializeAs('shippingTypes')
    @deserializeAs('shippingTypes')
    private _shippingTypes: string;

    @serializeAs('cbmPrice')
    @deserializeAs('cbmPrice')
    private _cbmPrice: number;

    @serializeAs('trackingLink')
    @deserializeAs('trackingLink')
    private _trackingLink: string;

    @serializeAs('note')
    @deserializeAs('note')
    private _note: string;

    @serializeAs('poLoadedItem')
    @deserializeAs('poLoadedItem')
    private _poLoadedItem: any[];

    @deserializeAs('loadedDate')
    private _loadedDate: string;

    @deserializeAs('NoOfCarton')
    private _NoOfCarton: number;

    @deserializeAs('noOfCarton')
    private _noOfCarton: number;

    @serializeAs('status')
    @deserializeAs('status')
    private _status: any;

    @deserializeAs('rcId')
    private _rcId: string;

    @deserializeAs('isSelected')
    private _isSelected: boolean;

    @deserializeAs('releasedDate')
    private _releasedDate: number;

    constructor() {
        this.poLoadedItem = [];
        this.isSelected = false;
    }


    /**
     * Getter releasedDate
     * @return {number}
     */
	public get releasedDate(): number {
		return this._releasedDate;
	}

    /**
     * Setter releasedDate
     * @param {number} value
     */
	public set releasedDate(value: number) {
		this._releasedDate = value;
	}


    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Getter importerId
     * @return {number}
     */
	public get importerId(): number {
		return this._importerId;
	}

    /**
     * Getter containerId
     * @return {number}
     */
	public get containerId(): number {
		return this._containerId;
	}

    /**
     * Getter containerName
     * @return {string}
     */
	public get containerName(): string {
		return this._containerName;
	}

    /**
     * Getter selectedContainer
     * @return {any}
     */
	public get selectedContainer(): any {
		return this._selectedContainer;
	}

    /**
     * Getter expectedDeliveryDate
     * @return {string}
     */
	public get expectedDeliveryDate(): string {
		return this._expectedDeliveryDate;
	}

    /**
     * Getter t_expectedDeliveryDate
     * @return {any}
     */
	public get t_expectedDeliveryDate(): any {
		return this._t_expectedDeliveryDate;
	}

    /**
     * Getter expectedDeliveryDay
     * @return {number}
     */
	public get expectedDeliveryDay(): number {
		return this._expectedDeliveryDay;
	}

    /**
     * Getter shippingTypes
     * @return {string}
     */
	public get shippingTypes(): string {
		return this._shippingTypes;
	}

    /**
     * Getter cbmPrice
     * @return {number}
     */
	public get cbmPrice(): number {
		return this._cbmPrice;
	}

    /**
     * Getter trackingLink
     * @return {string}
     */
	public get trackingLink(): string {
		return this._trackingLink;
	}

    /**
     * Getter note
     * @return {string}
     */
	public get note(): string {
		return this._note;
	}

    /**
     * Getter poLoadedItem
     * @return {any[]}
     */
	public get poLoadedItem(): any[] {
		return this._poLoadedItem;
	}

    /**
     * Getter loadedDate
     * @return {string}
     */
	public get loadedDate(): string {
		return this._loadedDate;
	}

    /**
     * Getter NoOfCarton
     * @return {number}
     */
	public get NoOfCarton(): number {
		return this._NoOfCarton;
	}

    /**
     * Getter noOfCarton
     * @return {number}
     */
	public get noOfCarton(): number {
		return this._noOfCarton;
	}

    /**
     * Getter status
     * @return {any}
     */
	public get status(): any {
		return this._status;
	}

    /**
     * Getter rcId
     * @return {string}
     */
	public get rcId(): string {
		return this._rcId;
	}

    /**
     * Getter isSelected
     * @return {boolean}
     */
	public get isSelected(): boolean {
		return this._isSelected;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Setter importerId
     * @param {number} value
     */
	public set importerId(value: number) {
		this._importerId = value;
	}

    /**
     * Setter containerId
     * @param {number} value
     */
	public set containerId(value: number) {
		this._containerId = value;
	}

    /**
     * Setter containerName
     * @param {string} value
     */
	public set containerName(value: string) {
		this._containerName = value;
	}

    /**
     * Setter selectedContainer
     * @param {any} value
     */
	public set selectedContainer(value: any) {
		this._selectedContainer = value;
	}

    /**
     * Setter expectedDeliveryDate
     * @param {string} value
     */
	public set expectedDeliveryDate(value: string) {
		this._expectedDeliveryDate = value;
	}

    /**
     * Setter t_expectedDeliveryDate
     * @param {any} value
     */
	public set t_expectedDeliveryDate(value: any) {
		this._t_expectedDeliveryDate = value;
	}

    /**
     * Setter expectedDeliveryDay
     * @param {number} value
     */
	public set expectedDeliveryDay(value: number) {
		this._expectedDeliveryDay = value;
	}

    /**
     * Setter shippingTypes
     * @param {string} value
     */
	public set shippingTypes(value: string) {
		this._shippingTypes = value;
	}

    /**
     * Setter cbmPrice
     * @param {number} value
     */
	public set cbmPrice(value: number) {
		this._cbmPrice = value;
	}

    /**
     * Setter trackingLink
     * @param {string} value
     */
	public set trackingLink(value: string) {
		this._trackingLink = value;
	}

    /**
     * Setter note
     * @param {string} value
     */
	public set note(value: string) {
		this._note = value;
	}

    /**
     * Setter poLoadedItem
     * @param {any[]} value
     */
	public set poLoadedItem(value: any[]) {
		this._poLoadedItem = value;
	}

    /**
     * Setter loadedDate
     * @param {string} value
     */
	public set loadedDate(value: string) {
		this._loadedDate = value;
	}

    /**
     * Setter NoOfCarton
     * @param {number} value
     */
	public set NoOfCarton(value: number) {
		this._NoOfCarton = value;
	}

    /**
     * Setter noOfCarton
     * @param {number} value
     */
	public set noOfCarton(value: number) {
		this._noOfCarton = value;
	}

    /**
     * Setter status
     * @param {any} value
     */
	public set status(value: any) {
		this._status = value;
	}

    /**
     * Setter rcId
     * @param {string} value
     */
	public set rcId(value: string) {
		this._rcId = value;
	}

    /**
     * Setter isSelected
     * @param {boolean} value
     */
	public set isSelected(value: boolean) {
		this._isSelected = value;
	}

    
}