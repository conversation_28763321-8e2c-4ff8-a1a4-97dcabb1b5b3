<div class="page-content"
    [pageAccess]="{page: utilsService.enumForPage.MASTER, action: utilsService.enumForPage.VIEW_MASTER, view: true}">
    <div class="page-title-wrapper">
        <div class="page-title-left">
            <h4>Importer Master</h4>
        </div>
        <div class="page-title-right">
            <button class="btn btn-sm btn-primary btn-icon-text"
                [pageAccess]="{page: utilsService.enumForPage.MASTER, action: utilsService.enumForPage.ADD_MASTER}"
                (click)="openAddEditModal(null, 'Add')">
                <i class="th th-outline-add-circle"></i>Add New
            </button>
            <button (click)="getImporterList()" class="btn btn-sm btn-icon btn-outline-white" ngbTooltip="Refresh"
                placement="left" container="body" triggers="hover">
                <i class="th th-outline-refresh-2"></i>
            </button>
        </div>
    </div>
    <!-- ---------------------------- content-area ----------------------------- -->
    <div class="content-area">
        <div class="page-filters">
            <div class="page-filters-left">
                <div class="form-group form-group-sm filter-search">
                    <div class="form-group-icon-start">
                        <i class="th th-outline-search-normal-1 icon-broder "></i>
                        <input (input)="onSearch($event)" [(ngModel)]="searchText" type="search" class="form-control"
                            placeholder="Search by name / contact">
                    </div>
                </div>

                <div class="form-group theme-ngselect form-group-sm">
                    <ng-select (change)="onChangeActive()" class="" placeholder="Status" [multiple]="false"
                        [clearable]="true" [items]="activeInactiveStatus" bindLabel="label" bindValue="value"
                        [(ngModel)]="activeFlag" [hideSelected]="false">
                    </ng-select>
                </div>

            </div>
            <div class="page-filters-right">
                <div class="form-group theme-ngselect form-group-sm form-group-export">
                    <div class="dropdown export-dropdown">
                        <button [disabled]="utilsService.isEmptyObjectOrNullUndefined(importerList)" type="button"
                            class="btn btn-sm btn-outline-white dropdown-toggle" data-bs-toggle="dropdown"
                            aria-expanded="false">
                            Export
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" (click)="exportReport()">Excel</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="card card-theme card-table-sticky ">
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table-theme table-hover table table-bordered table-sticky">
                        <thead class="border-less">
                            <tr>
                                <th *ngFor="let th of importerTH; index as j" [class]="th.class"
                                    [ngClass]="{'sorting-asc': sortColumn==th.keyName && sortOrder === enumForSortOrder.A, 
                                                'sorting-desc': sortColumn==th.keyName && sortOrder === enumForSortOrder.D }"
                                    (click)="!utilsService.isNullUndefinedOrBlank(th.keyName) ? onSortTH(th.keyName) : $event.preventDefault()">
                                    <div (click)="$event.stopPropagation()" *ngIf="j === 0"
                                        class="checkbox checkbox-primary checkbox-small">
                                        <input [disabled]="utilsService.isEmptyObjectOrNullUndefined(importerList)"
                                            (change)="selectAll()" [(ngModel)]="flagForSelectAll" type="checkbox"
                                            id="tbl-checkbox" class="material-inputs filled-in" />
                                        <label for="tbl-checkbox"></label>
                                    </div>
                                    {{th.displayName}}
                                </th>
                                <!-- <th class="d-flex align-items-center gap-2">
                                    <div class=" checkbox checkbox-primary checkbox-small">
                                        <input type="checkbox" id="tbl-checkbox" class="material-inputs filled-in" />
                                        <label for="tbl-checkbox"></label>
                                    </div>
                                    Importer Name
                                </th>
                                <th>Contact No</th>
                                <th>Short Code</th>
                                <th>Address</th>
                                <th>Date Created</th> -->
                                <th class="tbl-switch"
                                    *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.EDIT_MASTER])">
                                    Status</th>
                                <th class="text-center"
                                    *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.EDIT_MASTER, this.utilsService.enumForPage.DELETE_MASTER])">
                                    Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr *ngFor="let item of importerList; index as i; trackBy: trackBy">
                                <td class="tbl-user">
                                    <div class="tbl-user-checkbox-srno">
                                        <div class="checkbox checkbox-primary checkbox-small">
                                            <input type="checkbox" id="tbl-checkbox2-{{i}}"
                                                [(ngModel)]="item.isSelected" class="material-inputs filled-in"
                                                (change)="selectUnselect(item.id, i, item.isSelected)" />
                                            <label for="tbl-checkbox2-{{i}}"></label>
                                        </div>
                                        <div class="tbl-user-wrapper">
                                            <div class="tbl-user-image">
                                                <!-- <img src="assets/images/avatar.jpg" alt="valamji"> -->
                                                {{ item.firstName?.charAt(0).toUpperCase() }}
                                            </div>
                                            <div class="tbl-user-text-action">
                                                <div class="tbl-user-text">
                                                    <p>{{item.firstName}} {{item.lastName}}</p>
                                                    <span *ngIf="item.email">{{item.email}}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td>{{item.contactExtension?.countryExtension}} {{item.contactNo}}</td>
                                <td>{{item.shortCode}}</td>
                                <td class="tbl-description" [ngbTooltip]="item.address" placement="bottom" container="body" triggers="hover"><div>{{item.address ? item.address : "-"}}</div></td>
                                <td>{{item.lastModifiedDate | date: 'dd/MM/YYYY h:mm a'}}</td>
                                <td class="tbl-switch"
                                    *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.EDIT_MASTER])">
                                    <div class="switch-box">
                                        <label class="switch" htmlFor="switch-{{i}}">
                                            <input type="checkbox" id='switch-{{i}}'
                                                (change)="onChangeStatus(item, item.isActive, i)"
                                                [(ngModel)]="item.isActive" />
                                            <div class="slider round"></div>
                                        </label>
                                    </div>
                                </td>
                                <td class="tbl-action"
                                    *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.EDIT_MASTER, this.utilsService.enumForPage.DELETE_MASTER])">
                                    <div class="tbl-action-group">
                                        <button *ngIf="!item.isDefault"
                                            class="btn btn-xs btn-light-white btn-icon"
                                            [pageAccess]="{page: this.utilsService.enumForPage.MASTER, action: this.utilsService.enumForPage.EDIT_MASTER}"
                                            (click)="openAddEditModal(item, 'Edit')" ngbTooltip="Edit"
                                            placement="bottom" container="body" triggers="hover">
                                            <i class="th th-outline-edit"></i>
                                        </button>
                                        <button class="btn btn-xs btn-light-danger btn-icon"
                                            [pageAccess]="{page: this.utilsService.enumForPage.MASTER, action: this.utilsService.enumForPage.DELETE_MASTER}"
                                            (click)="openDeleteImporterModal(item)" ngbTooltip="Delete" placement="left"
                                            container="body" triggers="hover">
                                            <i class="th th-outline-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr *ngIf="utilsService.isEmptyObjectOrNullUndefined(importerList)">
                                <td colspan="20" class="text-center">
                                    <span
                                        class="truncate">{{utilsService.serverVariableService.STRING_WHEN_NO_RECORDS_FOUND}}</span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="paginationbox pagination-fixed">
            <app-pagination (pagesizeData)="addPageSizeData($event)" (pageNumber)="pageNumber($event)" [page]="pageNo"
                [pageSize]="pageSize" [totalData]="totalData"></app-pagination>
        </div>
    </div>
</div>

<!-- ----------------------------------------------------------------------- -->
<!--                     Add and Edit Forms Modal Start                      -->
<!-- ----------------------------------------------------------------------- -->
<div class="modal modal-theme fade" id="importerMasterModal" tabindex="-1" aria-labelledby="importerMasterModalLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">{{statusForModal === 'Add' ? 'Add New' : 'Edit'}}
                    Importer</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
                        class='th th-close'></i></button>
            </div>
            <div class="modal-body" [formGroup]="importerGroup">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group required">
                            <label class="form-label">First Name </label>
                            <input (keyup.enter)="onSaveImporter()" id="f" [(ngModel)]="importerObj.firstName"
                                formControlName="name" [maxlength]="utilsService.validationService.MAX_50" type="text"
                                class="form-control" placeholder="Enter First Name">
                            <div class="message error-message"
                                *ngIf="importerGroup.controls['name'].hasError('required') &&  importerGroup.controls['name'].touched">
                                {{utilsService.validationService.FIRST_NAME_REQ}}
                            </div>
                            <div class="message error-message"
                                *ngIf="!importerGroup.controls['name'].hasError('required') && !importerGroup.controls['name'].valid && importerGroup.controls['name'].touched">
                                {{utilsService.validationService.FIRST_NAME_INVALID}}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group required">
                            <label class="form-label">Last Name </label>
                            <input (keyup.enter)="onSaveImporter()" [maxlength]="utilsService.validationService.MAX_50"
                                formControlName="l_name" [(ngModel)]="importerObj.lastName" type="text"
                                class="form-control" placeholder="Enter Last Name">
                            <div class="message error-message"
                                *ngIf="importerGroup.controls['l_name'].hasError('required') &&  importerGroup.controls['l_name'].touched">
                                {{utilsService.validationService.LAST_NAME_REQ}}
                            </div>
                            <div class="message error-message"
                                *ngIf="!importerGroup.controls['l_name'].hasError('required') && !importerGroup.controls['l_name'].valid && importerGroup.controls['l_name'].touched">
                                {{utilsService.validationService.LAST_NAME_INVALID}}
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="form-group theme-ngselect">
                            <label class="form-label">Contact No</label>
                            <div class="form-control-wrapper">
                                <div class="input-group input-group-select">
                                    <ng-select (search)="onSearchCountryCode($event)" class="" placeholder="Ph." [multiple]="false" [clearable]="false" [items]="dropdown?.countryPh"
                                        bindLabel="countryExtension" bindValue="id" formControlName="countryExtension"
                                        [(ngModel)]="importerObj.contactCountryExtensionId">
                                    </ng-select>
                                    <input (keyup.enter)="onSaveImporter()" [(ngModel)]="importerObj.contactNo" formControlName="mobile" type="text"
                                        placeholder="Enter Contact No" class="form-control">
                                </div>
                                <div class="message error-message"
                                    *ngIf="importerGroup.controls['countryExtension'].hasError('required') && importerGroup.controls['countryExtension'].touched">
                                    {{utilsService.validationService.PHONE_NO_EXTENSION_REQ}}
                                </div>
                                <div class="message error-message"
                                    *ngIf="importerGroup.controls['mobile'].hasError('required') &&  importerGroup.controls['mobile'].touched && importerGroup.controls['countryExtension'].valid">
                                    {{utilsService.validationService.CONTACT_REQUIRED}}
                                </div>
                                <div class="message error-message"
                                    *ngIf="!importerGroup.controls['mobile'].hasError('required') && !importerGroup.controls['mobile'].valid && importerGroup.controls['mobile'].touched && importerGroup.controls['countryExtension'].valid">
                                    {{utilsService.validationService.CONTACT_INVALID}}
                                </div>
                            </div>
                            <!-- <input (keyup.enter)="onSaveImporter()" [(ngModel)]="importerObj.contactNo"
                                formControlName="mobile" type="text" class="form-control"
                                placeholder="Enter Contact No">
                            <div class="message error-message"
                                *ngIf="importerGroup.controls['mobile'].hasError('required') &&  importerGroup.controls['mobile'].touched">
                                {{utilsService.validationService.CONTACT_REQUIRED}}
                            </div>
                            <div class="message error-message"
                                *ngIf="!importerGroup.controls['mobile'].hasError('required') && !importerGroup.controls['mobile'].valid && importerGroup.controls['mobile'].touched">
                                {{utilsService.validationService.CONTACT_INVALID}}
                            </div> -->
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="form-group">
                            <label class="form-label">Email </label>
                            <input (keyup.enter)="onSaveImporter()" [(ngModel)]="importerObj.email"
                                formControlName="email" type="text" class="form-control" placeholder="Enter Email">
                            <div class="message error-message"
                                *ngIf="importerGroup.controls['email'].hasError('required') &&  importerGroup.controls['email'].touched">
                                {{utilsService.validationService.EMAIL_REQUIRED}}
                            </div>
                            <div class="message error-message"
                                *ngIf="!importerGroup.controls['email'].hasError('required') && !importerGroup.controls['email'].valid && importerGroup.controls['email'].touched">
                                {{utilsService.validationService.EMAIL_INVALID}}
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="form-group">
                            <label class="form-label">Short Code</label>
                            <input [maxlength]="utilsService.validationService.MAX_10" (keyup.enter)="onSaveImporter()"
                                formControlName="short_code" [(ngModel)]="importerObj.shortCode" type="text"
                                class="form-control" placeholder="Enter Short Code">
                            <div class="message error-message"
                                *ngIf="importerGroup.controls['short_code'].hasError('required') &&  importerGroup.controls['short_code'].touched">
                                {{utilsService.validationService.SHORT_CODE_REQ}}
                            </div>
                            <div class="message error-message"
                                *ngIf="!importerGroup.controls['short_code'].hasError('required') && !importerGroup.controls['short_code'].valid && importerGroup.controls['short_code'].touched">
                                {{utilsService.validationService.SHORT_CODE_INVALID}}
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="form-group">
                            <label class="form-label">Address Line</label>
                            <input [maxlength]="utilsService.validationService.MAX_200" formControlName="address"
                                [(ngModel)]="importerObj.address" type="text" class="form-control"
                                placeholder="Enter Address Line">
                            <div class="message error-message"
                                *ngIf="importerGroup.controls['address'].hasError('required') &&  importerGroup.controls['address'].touched">
                                {{utilsService.validationService.ADDRESS_REQ}}
                            </div>
                            <div class="message error-message"
                                *ngIf="!importerGroup.controls['address'].hasError('required') && !importerGroup.controls['address'].valid && importerGroup.controls['address'].touched">
                                {{utilsService.validationService.ADDRESS_INVALID}}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group theme-ngselect ">
                            <div class="form-label">Country</div>
                            <ng-select (change)="onChangeCountryState(true)" formControlName="country" class=""
                                placeholder="Select" [multiple]="false" [clearable]="false" [items]="dropdown?.country"
                                bindLabel="name" bindValue="id" [(ngModel)]="importerObj.countryId">
                            </ng-select>
                            <div class="message error-message"
                                *ngIf="importerGroup.controls['country'].hasError('required') &&  importerGroup.controls['country'].touched">
                                {{utilsService.validationService.COUNTRY_REQ}}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group theme-ngselect ">
                            <div class="form-label">State</div>
                            <ng-select (change)="onChangeCountryState(false)" formControlName="state" class=""
                                placeholder="Select" [multiple]="false" [clearable]="false" [items]="dropdown?.state"
                                bindLabel="label" bindValue="value" [(ngModel)]="importerObj.stateId">
                            </ng-select>
                            <div class="message error-message"
                                *ngIf="importerGroup.controls['state'].hasError('required') &&  importerGroup.controls['state'].touched">
                                {{utilsService.validationService.STATE_REQ}}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group theme-ngselect ">
                            <div class="form-label">City</div>
                            <ng-select formControlName="city" class="" placeholder="Select" [multiple]="false"
                                [clearable]="false" [items]="dropdown?.city" bindLabel="label" bindValue="value"
                                [(ngModel)]="importerObj.cityId">
                            </ng-select>
                            <div class="message error-message"
                                *ngIf="importerGroup.controls['city'].hasError('required') &&  importerGroup.controls['city'].touched">
                                {{utilsService.validationService.CITY_REQ}}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <div class="form-label">Zip Code</div>
                            <input (keyup.enter)="onSaveImporter()" [maxlength]="utilsService.validationService.MAX_10"
                                [(ngModel)]="importerObj.zipCode" formControlName="zip" type="text" class="form-control"
                                placeholder="Zip Code">
                            <div class="message error-message"
                                *ngIf="importerGroup.controls['zip'].hasError('required') &&  importerGroup.controls['zip'].touched">
                                {{utilsService.validationService.ZIP_REQ}}
                            </div>
                            <div class="message error-message"
                                *ngIf="!importerGroup.controls['zip'].hasError('required') && !importerGroup.controls['zip'].valid && importerGroup.controls['zip'].touched">
                                {{utilsService.validationService.ZIP_INVALID}}
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="form-group d-flex justify-content-between required ">
                            <label class="form-label">Status</label>

                            <div class="switch-box">
                                <label class="switch" htmlFor="switch">
                                    <input type="checkbox" id='switch' [(ngModel)]="importerObj.isActive"
                                        formControlName="status" />
                                    <div class="slider round"></div>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <div class="modal-footer-group full-width-btn">
                    <button (click)="onSaveImporter()" type="button" class="btn btn-primary btn-icon-text"> <i
                            class="th th-outline-tick-circle"></i>
                        {{statusForModal === 'Add' ? 'Save' : 'Update'}}</button>
                    <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                      Add and Edit Forms Modal End                       -->
<!-- ----------------------------------------------------------------------- -->

<!-- ----------------------------------------------------------------------- -->
<!--                           Delete Modal Start                            -->
<!-- ----------------------------------------------------------------------- -->
<div class="modal modal-theme modal-confirmation modal-reject fade" id="deleteImporterMasterModal" tabindex="-1"
    aria-labelledby="deleteImporterMasterModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
                        class='th th-close'></i></button>
            </div>
            <div class="modal-body">
                <div class="modal-confirmation-container">
                    <div class="modal-confirmation-icon">
                        <i class="th th-bold-trash"></i>
                    </div>
                    <div class="modal-confirmation-content">
                        <h5>Are You Sure?</h5>
                        <p>You want to Delete <b>{{importerObj.firstName}} {{importerObj.lastName}}</b> importer.</p>
                    </div>
                </div>
                <div class="modal-button-group modal-full-width-btn">
                    <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
                    <button (click)="deleteImporterCompany()" type="button" class="btn btn-primary btn-icon-text"> <i
                            class="th th-outline-tick-circle"></i>
                        Delete</button>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                           Delete Modal End                            -->
<!-- ----------------------------------------------------------------------- -->