import { Component, OnDestroy, OnInit } from '@angular/core';
import { UtilsService } from '../../services/utils.service';
import { BranchSideBar } from '../../constants/interface';
import { navItems } from '../../constants/constant';
import { Subscription, tap } from 'rxjs';
import { RxStompService } from '../../socket_config/rx-stomp.service';
import { SidebarService } from './sidebar.service';
import { Router, NavigationEnd } from '@angular/router';

@Component({
  selector: 'app-sidebar',
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.css']
})
export class SidebarComponent implements OnInit, OnDestroy {

  options = { autoHide: true };
  searchBranch: string;

  originalBranches: BranchSideBar[];
  navItems = navItems

  branchSwitchSub: Subscription;
  routeSub: Subscription;

  constructor(public utilsService: UtilsService, private rxStompService: RxStompService, private sideBarService: SidebarService, private route: Router) {
    this.navItems.sort((a, b) => a.name.localeCompare(b.name));
  }

  // -------------------------- Toggle Menu State --------------------------
  SidebarSize: 'sm' | 'lg' | 'smlg' = 'lg';
  resizeListener: () => void;
  hoverListenersAttached = false;


  // -------------------------- Lifecycle --------------------------

  ngOnInit(): void {
    this.setInitialSidebarSize();
    this.setupResizeAndHoverListeners();
    this.initializeUserBranchLogic();

    this.routeSub = this.route.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        if (this.utilsService.isEmptyObjectOrNullUndefined(this.utilsService.defaultBranch)) {
          this.getUserBranch();
        }
      }
    });
  }

  ngOnDestroy(): void {
    this.routeSub.unsubscribe();
    this.sideBarService.destroy$.complete();
    this.cleanupResizeAndHoverListeners();
    if (this.branchSwitchSub) {
      this.branchSwitchSub.unsubscribe();
    }
  }


  // -------------------------- Sidebar Toggle Menu Code --------------------------

  setInitialSidebarSize() {
    const width = window.innerWidth;
    const defaultSize: 'sm' | 'lg' = width <= 1024 ? 'sm' : 'lg';
    this.SidebarSize = defaultSize;
    document.documentElement.setAttribute('data-sidebar-size', defaultSize);
  }

  toggleMenu() {
    const currentSize = document.documentElement.getAttribute('data-sidebar-size') as 'sm' | 'lg' | null;
    const newSize: 'sm' | 'lg' = currentSize === 'sm' ? 'lg' : 'sm';
    this.SidebarSize = newSize;
    document.documentElement.setAttribute('data-sidebar-size', newSize);
    document.documentElement.classList.remove('hide-lg');
  }

  handleMouseEnter = () => {
    if (document.documentElement.getAttribute('data-sidebar-size') === 'sm') {
      document.documentElement.setAttribute('data-sidebar-size', 'smlg');
    }
  };

  handleMouseLeave = () => {
    if (document.documentElement.getAttribute('data-sidebar-size') === 'smlg') {
      document.documentElement.setAttribute('data-sidebar-size', 'sm');
    }
  };

  setupResizeAndHoverListeners() {
    this.resizeListener = () => {
      const width = window.innerWidth;
      const newSize: 'sm' | 'lg' = width <= 1024 ? 'sm' : 'lg';
      this.SidebarSize = newSize;
      document.documentElement.setAttribute('data-sidebar-size', newSize);
    };
    window.addEventListener('resize', this.resizeListener);

    const appMenu = document.querySelector('.app-menu.navbar-menu');
    if (appMenu && !this.hoverListenersAttached) {
      appMenu.addEventListener('mouseenter', this.handleMouseEnter);
      appMenu.addEventListener('mouseleave', this.handleMouseLeave);
      this.hoverListenersAttached = true;
    }
  }

  cleanupResizeAndHoverListeners() {
    window.removeEventListener('resize', this.resizeListener);
    const appMenu = document.querySelector('.app-menu.navbar-menu');
    if (appMenu && this.hoverListenersAttached) {
      appMenu.removeEventListener('mouseenter', this.handleMouseEnter);
      appMenu.removeEventListener('mouseleave', this.handleMouseLeave);
      this.hoverListenersAttached = false;
    }
  }

  // -------------------------- User Branch & WebSocket --------------------------


  initializeUserBranchLogic() {
    const userData = JSON.parse(localStorage.getItem('userData'));
    if (userData) {
      this.getUserBranch();
      this.branchSwitchSub = this.rxStompService.watch(`/topic/user/logout/${this.utilsService.userId}`).subscribe((message) => {
        if (message.body === 'logout') {
          this.utilsService.toasterService.info(`Please contact administrator.`, '', {
            positionClass: 'toast-top-right',
            closeButton: true,
            timeOut: 10000
          });
          this.utilsService.logout(false);
        } else if (message.body === 'refresh') {
          this.getUserBranch();
        }
      });
    }
  }

  getUserBranch = () => {
    this.sideBarService.getDefaultBranch(false).pipe(
      tap(response => {
        if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
          this.originalBranches = null;
          this.utilsService.userBranchList = response;
          this.utilsService.defaultBranch = response.find(branch => branch.isLoggedInBranch);
        }
      })
    ).subscribe();
  }

  // getUserBranchOld() {
  //   this.utilsService.getMethodAPI(false, this.utilsService.serverVariableService.USER_BRANCHES + `?userId=${this.utilsService.userId}`, null, (response) => {
  //     if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
  //       this.originalBranches = null;
  //       this.utilsService.userBranchList = response;
  //       this.utilsService.defaultBranch = this.utilsService.userBranchList.find(v => v.isLoggedInBranch);
  //     }
  //   })
  // }

  onSaveUserBranch(branchId: number) {
    this.utilsService.postMethodAPI(true, this.utilsService.serverVariableService.SAVE_USER_BRANCHES + `?branchID=${branchId}`, null, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.getUserBranch();
        this.utilsService.redirectTo('/users/dashboard');
      }
    })
  }

  // Search Branch
  customSearchFn() {
    if (!this.originalBranches) {
      this.originalBranches = [...this.utilsService.userBranchList];
    }
    if (!this.searchBranch) {
      this.utilsService.userBranchList = [...this.originalBranches];
    } else {
      this.utilsService.userBranchList = this.originalBranches.filter(employee =>
        (!this.utilsService.isNullUndefinedOrBlank(employee.branchName) && employee.branchName.toLowerCase().includes(this.searchBranch.trim().toLowerCase())) ||
        (!this.utilsService.isNullUndefinedOrBlank(employee.branchEmail) && employee.branchEmail.toLowerCase().includes(this.searchBranch.trim().toLowerCase()))
      );
    }
  }

  checkIfOtherBranchPresent(): boolean {
    const isEmpty = this.utilsService.userBranchList.filter(v => !v.isLoggedInBranch).length === 0 ? false : true; return isEmpty;
  }

  openSideBranchDropdown() {
    this.getUserBranch();
  }

}
