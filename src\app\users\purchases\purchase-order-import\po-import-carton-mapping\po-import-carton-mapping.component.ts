import { Component, inject, OnInit, OnD<PERSON>roy } from '@angular/core';
import { FormControl } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { Deserialize, Serialize } from 'cerialize';
import { debounceTime, distinctUntilChanged, Subscription } from 'rxjs';
import { CartonMapping, CartonMappingItemList, WarehouseMapping } from 'src/app/models/CartonMapping';
import { UtilsService } from 'src/app/shared/services/utils.service';

@Component({
  selector: 'app-po-import-carton-mapping',
  templateUrl: './po-import-carton-mapping.component.html',
  styleUrls: ['./po-import-carton-mapping.component.scss'],
})
export class PoImportCartonMappingComponent implements OnInit, OnDestroy {

  utilsService = inject(UtilsService);

  cartonMappingList: CartonMapping[] = [];
  oldItems: CartonMapping[] = []

  branchHeaderList: any[] = [];
  warehouseHeaderList: any[] = [];

  stage: number;
  searchControl = new FormControl('');

  containerDropdown: any[] = [];
  dropoffLocationDropdown: any[] = [];
  selectedContainer: number[];

  cartonMappingAisleRackList: CartonMappingItemList[] = []
  oldItemsAisleRackList: CartonMappingItemList[] = []

  fromWarehouse: boolean = false;
  containerId: number;

  searchSub: Subscription;

  constructor(private route: ActivatedRoute) {
    if (this.route.snapshot.routeConfig.path.includes('warehouse-allocation')) {
      this.fromWarehouse = true;
    }

    if (this.route.snapshot.paramMap.get('containerId')) {
      this.containerId = +this.route.snapshot.paramMap.get('containerId');
    }
  }

  ngOnInit(): void {

    this.searchSub = new Subscription();

    this.searchSub = this.searchControl.valueChanges.pipe(debounceTime(50), distinctUntilChanged()).subscribe(searchTerm => {
      if (this.stage == 1 || this.stage == 2) {
        this.filterItems(searchTerm);
      }

      if (this.stage == 3) {
        this.filterItemsS3(searchTerm);
      }
    });

    // Redirected from warehouse details
    if (this.fromWarehouse) {
      this.changeStage(2)
    }
    else {
      this.changeStage(1)
    }
  }

  ngOnDestroy(): void {
    localStorage.removeItem('containerIDs');
    this.searchSub.unsubscribe();
  }

  changeStage(value: number) {

    if(this.stage === value) {
      return;
    }

    this.stage = value;

    this.oldItems = []
    this.cartonMappingList = []
    this.warehouseHeaderList = []
    this.oldItemsAisleRackList = []
    this.cartonMappingAisleRackList = []

    this.searchControl.reset();
    this.selectedContainer = [];

    switch (this.stage) {
      case 1:
        this.getRequiredDataB(false);
        break;
      case 2:
        this.getRequiredDataW();
        break;
      case 3:
        this.getRequiredDataAR();
        break;
      default:
        break;
    }
  }

  getRequiredDataB = (afterSave: boolean) => {

    this.containerDropdown = [];

    this.utilsService.getMethodAPI(false, this.utilsService.serverVariableService.PO_CARTON_MAPPING_BRANCH_REQ, null, (response) => {
      if (this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.cartonMappingList = null;
        return;
      }

      this.branchHeaderList = response.allBranches;

      this.oldItems = Deserialize(response.loadedItems, CartonMapping);
      this.cartonMappingList = [...this.oldItems];

      this.cartonMappingList.forEach(a => a.branches.map(v => {
        if (v.branches) {
          v.branchHeaderList = []
          const branches = (v.branches) as any[]
          this.branchHeaderList.forEach(item => {
            const b = (branches || []).find(branch => branch.branchID === item.id);
            if (b) {
              v.branchHeaderList.push(b)
            }
          });
        }
      }))
      this.containerDropdown = response.containers;

      if(afterSave) {
        this.onChangeContainer()
      }

      if (this.containerId && this.containerDropdown.some(a => a.id === this.containerId)) {
        this.selectedContainer = [this.containerId]
        this.onChangeContainer()
      }

      // Redirect from Carton Mapping Modal from Tempo
      if (localStorage.getItem('containerIDs')) {
        this.selectedContainer = JSON.parse(localStorage.getItem('containerIDs')).containerIDs;
        this.onChangeContainer()
        localStorage.removeItem('containerIDs');
      }

      this.dropoffLocationDropdown = this.utilsService.transformDropdownItems(response.dropLocation);
      this.dropoffLocationDropdown = this.utilsService.filterIsActive(this.dropoffLocationDropdown, null);
    })
  }

  getRequiredDataW = () => {
    this.utilsService.getMethodAPI(false, this.utilsService.serverVariableService.PO_CARTON_MAPPING_WAREHOUSE_REQ, null, (response) => {
      if (this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.cartonMappingList = null;
        return;
      }

      // sorting main to first
      this.warehouseHeaderList = response.warehouses;
      const mainWarehouse = this.warehouseHeaderList.find(ware => ware.isMainWarehouse);
      if (mainWarehouse) {
        this.warehouseHeaderList = [mainWarehouse, ...this.warehouseHeaderList.filter(ware => ware !== mainWarehouse)];
      }
      this.warehouseHeaderList.sort((a, b) => a.sortOrder - b.sortOrder);

      this.oldItems = Deserialize(response.releasedItems, CartonMapping);
      this.oldItems.forEach(a => {
        const mainWarehouses = a.warehouses.map(ware => {
          ware.isMainWarehouse = (this.warehouseHeaderList.find(header => header.id == ware.id).isMainWarehouse)
          ware.allAisleRacks = (this.warehouseHeaderList.find(header => header.id == ware.id).allAisleRacks || [])
          ware.aisleRackQty = ware.aisleRackQty ? ware.aisleRackQty : null
          ware.breachQty = ware.breachQty ? ware.breachQty : null
          ware.sortOrder = (this.warehouseHeaderList.find(header => header.id == ware.id).sortOrder)
          return ware;
        });
        a.warehouses = mainWarehouses.sort((a, b) => a.sortOrder - b.sortOrder);
      });
      this.cartonMappingList = Serialize(this.oldItems)
    })
  }

  getRequiredDataAR = () => {
    this.utilsService.getMethodAPI(false, this.utilsService.serverVariableService.PO_CARTON_MAPPING_AR_REQ, null, (response) => {
      if (this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.cartonMappingList = null;
        return;
      }

      this.warehouseHeaderList = response.warehouses;

      this.oldItemsAisleRackList = Deserialize(response.itemList, CartonMappingItemList);

      this.oldItemsAisleRackList.forEach(a => {
        a.warehouses = this.warehouseHeaderList.find(w => w.id == a.wareHouseId)
      })

      this.cartonMappingAisleRackList = [...this.oldItemsAisleRackList];
    })
  }

  //on Save
  onSave = () => {

    const formData = new FormData();

    for (let item of this.cartonMappingList) {
      if (item.customerOrderQty && !item.dropLocationId) {
        return;
      }
      if (this.isCustomerExceeded(item)) {
        return;
      }

      for (let i = 0; i < this.branchHeaderList.length; i++) {
        const branch = item.branches[i];
        // checking for non default branch
        if (branch.transferQty && !branch.dropLocationId && branch.branchID !== this.utilsService.defaultBranch.id) {
          return;
        }
        if (this.isTotalExceeded(item, i)) {
          return;
        }
      }
    }

    let param = this.cartonMappingList.map(a => {
      const branches = a.branches.filter(b => b.transferQty !== undefined && b.transferQty !== null && b.transferQty !== '' && b.transferQty !== 0).map(b => ({
        branchID: b.branchID,
        transferQty: b.transferQty ? b.transferQty : null,
        branchCartonMappingID: b.branchCartonMappingID,
        dropLocationId: b.dropLocationId ? b.dropLocationId : null,
      }));
      return {
        customerOrderQty: a.customerOrderQty ? a.customerOrderQty : null,
        loadedItemMapID: a.loadedItemMapID,
        dropLocationId: a.dropLocationId ? a.dropLocationId : null,
        branches
      };
    }).filter(item => item.branches.length > 0 || item.customerOrderQty > 0)

    formData.set('requestInfo', JSON.stringify(param));

    ///
    let deleteParam = Serialize(this.cartonMappingList) as CartonMapping[]
    let resultArr = [];
    deleteParam.forEach(a => {
      if (a.branches.every(v => v.transferQty == 0 || v.transferQty == null || v.transferQty === '')) {
        resultArr.push(a.loadedItemMapID);
      }
    });

    formData.set('deleteMapping', JSON.stringify(resultArr));

    this.utilsService.postMethodAPI(true, this.utilsService.serverVariableService.PO_CARTON_MAPPING_BRANCH_SAVE, formData, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.getRequiredDataB(true);
      }
    })
  }

  //branch TH
  getBranchData = (item: CartonMapping, index: number) => {
    if (!item.branches || index >= item.branches.length) {
      return null;
    }
    return item.branches[index];
  }

  getWarehouseData = (item: CartonMapping, index: number) => {
    if (!item.warehouses || index >= item.warehouses.length) {
      return null;
    }
    return item.warehouses[index];
  }

  filterItems = (searchTerm: string): void => {
    if (searchTerm) {
      searchTerm = searchTerm.toLowerCase().trim();
    }

    this.cartonMappingList = this.oldItems.filter(item => {
      const search = searchTerm ? item.importedItem?.marka?.toLowerCase().includes(searchTerm) : true;
      const container = this.selectedContainer.length > 0 ? this.selectedContainer.includes(item.importedItem?.container?.id) : true;
      return search && container;
    });
  }

  onChangeContainer() {
    const searchTerm = this.searchControl.value?.toLowerCase().trim() || '';

    this.cartonMappingList = this.oldItems.filter(item => {
      const container = this.selectedContainer.length > 0 ? this.selectedContainer.includes(item.importedItem?.container?.id) : true;
      const search = searchTerm ? item.importedItem?.marka?.toLowerCase().includes(searchTerm) : true;
      return container && search;
    });
  }

  filterItemsS3 = (searchTerm: string): void => {
    if (!searchTerm) {
      this.cartonMappingAisleRackList = [];
    }
    if (!searchTerm) {
      this.cartonMappingAisleRackList = [...this.oldItemsAisleRackList];
    }

    if (searchTerm) {
      searchTerm = searchTerm.toLowerCase().trim();
    }

    this.cartonMappingAisleRackList = this.oldItemsAisleRackList.filter(item =>
      item.items.some(a =>
        a.importedItem?.marka?.toLowerCase().includes(searchTerm)
      )
    );
  }

  /// Sum of Branches Qty

  checkTotalTransfer = (item: CartonMapping, branchIndex: number): void => {
    const value = item.branches?.[branchIndex]?.transferQty
    if (value === null || value === undefined || value === '' || value === 0) {
      item.branches[branchIndex].dropLocationId = null;
    }
    this.isTotalExceeded(item, branchIndex);
  }

  isCustomerExceeded = (item: CartonMapping): boolean => {
    if (!item.customerOrderQty) {
      return false;
    }

    const totalTransfer = item.branches.reduce((sum, branch) => { return sum + (Number(branch.transferQty) || 0) }, 0);
    return item.customerOrderQty > (item.totalCarton - totalTransfer);
  }

  isTotalExceeded = (item: CartonMapping, branchIndex: number): boolean => {

    if (!item.branches?.[branchIndex]?.transferQty) {
      return false;
    }

    const totalTransfer = item.branches.reduce((sum, branch) => { return sum + (Number(branch.transferQty) || 0) }, 0);
    return totalTransfer > (item.totalCarton - (item.customerOrderQty || 0));
  }

  calculatePending = (item: CartonMapping): number => {
    const totalTransfer = item.branches.reduce((sum, branch) => { return sum + (Number(branch.transferQty) || 0) }, 0);
    const pending = item.totalCarton - (item.customerOrderQty || 0) - totalTransfer;
    return pending < 0 ? 0 : pending;
  }

  // Validations for Warehouse Qty

  warehouseTotalExceed = (item: CartonMapping, warehouseIndex: number) => {
    if (!item.warehouses[warehouseIndex].transferQty) {
      return false;
    }

    let totalMainWarehouseTotal = 0;
    const mainWarehouseIndex = item.warehouses.findIndex(a => a.isMainWarehouse && (a.aisleRackQty?.length || a.transferQty));
    if (mainWarehouseIndex !== -1) {
      const warehouse = item.warehouses[mainWarehouseIndex];
      totalMainWarehouseTotal = warehouse?.aisleRackQty ? (warehouse.aisleRackQty || []).reduce((sum, ar) => sum + (Number(ar.transferQty) || 0), 0) : 0;

      const sum = warehouse.aisleRackQty?.reduce((sum, ar) => sum + (Number(ar.transferQty) || 0), 0) === 0

      if (sum) {
        totalMainWarehouseTotal = Number(warehouse.transferQty) || 0;
      }
    }

    let transferQty = item.warehouses.filter(a => !a.isMainWarehouse).reduce((sum, a) => {
      const qty = Number(a.transferQty)
      return !isNaN(qty) ? sum + qty : sum;
    }, 0);
    const totalTransfer = Number(transferQty) + Number(totalMainWarehouseTotal);
    return totalTransfer > (item.assignedCarton);
  }

  warehouseWithoutAR = (item: CartonMapping, warehouseIndex: number) => {
    if (!item.warehouses[warehouseIndex].transferQty) {
      return false;
    }

    let transferQtyNonMain = item.warehouses.filter(a => !a.isMainWarehouse).reduce((sum, a) => {
      const qty = Number(a.transferQty)
      return !isNaN(qty) ? sum + qty : sum;
    }, 0)

    let transferQty = item.warehouses[warehouseIndex].isMainWarehouse ? (item.warehouses[warehouseIndex].transferQty || 0) : 0
    const totalTransfer = Number(transferQty) + Number(transferQtyNonMain);
    return totalTransfer > (item.assignedCarton);
  }

  totalARExceed = (item: CartonMapping, warehouseIndex: number, arIndex: number): boolean => {
    if (!item.warehouses[warehouseIndex].aisleRackQty[arIndex].transferQty) {
      return false;
    }

    let transferQtyNonMain = item.warehouses.filter(a => !a.isMainWarehouse).reduce((sum, a) => {
      const qty = Number(a.transferQty)
      return !isNaN(qty) ? sum + qty : sum;
    }, 0);

    let transferQty = item.warehouses[warehouseIndex].isMainWarehouse ? (item.warehouses[warehouseIndex].transferQty || 0) : 0
    const totalTransfer = (transferQty) + transferQtyNonMain;
    return totalTransfer > (item.assignedCarton);
  };

  calculatePendingWarehouse = (item: CartonMapping): number => {

    let totalMainWarehouseTotal = 0;
    const mainWarehouseIndex = item.warehouses.findIndex(a => a.isMainWarehouse && (a.aisleRackQty?.length || a.transferQty));

    if (mainWarehouseIndex !== -1) {
      const warehouse = item.warehouses[mainWarehouseIndex];
      totalMainWarehouseTotal = warehouse?.aisleRackQty ? (warehouse.aisleRackQty || []).reduce((sum, ar) => sum + (Number(ar.transferQty) || 0), 0) : warehouse.transferQty;

      // const sum = warehouse.aisleRackQty?.reduce((sum, ar) => sum + (Number(ar.transferQty) || 0), 0) === 0
      // if (sum) {
      //   totalMainWarehouseTotal = Number(warehouse.transferQty) || 0;
      // }
      totalMainWarehouseTotal = Number(warehouse.transferQty) || 0;
    }

    let transferQty = item.warehouses.filter(a => !a.isMainWarehouse).reduce((sum, a) => {
      const qty = Number(a.transferQty)
      return !isNaN(qty) ? sum + qty : sum;
    }, 0);

    const totalTransfer = transferQty + (totalMainWarehouseTotal);
    const pending = (item.assignedCarton || 0) - totalTransfer;
    return pending < 0 ? 0 : pending;
  }

  /// Add New A/R

  addNewAR = (index: number, a: number) => {
    const obj = {
      transferQty: null,
      displayName: null,
      totalAvailQty: null,
    };

    if (!this.cartonMappingList[index].warehouses[a].aisleRackQty) {
      this.cartonMappingList[index].warehouses[a].aisleRackQty = [];
    }

    this.cartonMappingList[index].warehouses[a].aisleRackQty.push(obj);
  }

  onSaveWarehouseCM = () => {

    for (let item of this.cartonMappingList) {
      for (let i = 0; i < this.warehouseHeaderList.length; i++) {
        if (this.warehouseTotalExceed(item, i)) {  
          return;
        }
        if (this.warehouseWithoutAR(item, i)) {
          return;
        }
      }
      if (this.checkForErrorsWarehouse(item)) {
        return;
      }
      if (this.checkAisleRackEmpty(item)) {
        return;
      }
    }

    let sameAisleRack: boolean = false;

    let param = this.cartonMappingList.map(a => {
      const warehouses = a.warehouses.map(b => ({
        wareHouseTransferID: b.wareHouseTransferID ? b.wareHouseTransferID : null,
        warehouseID: b.id,
        transferQty: (b.transferQty || null),
        locations: b.isMainWarehouse
          ? (b.aisleRackQty || []).map(ar => {
            const obj = {
              aisleRackTransferID: ar.aisleRackTransferID ? ar.aisleRackTransferID : null,
              transferQty: ar.transferQty ? ar.transferQty : null,
              rackID: b.allAisleRacks.find(a => a.displayName == ar.displayName)?.rackID || null,
              aisleID: b.allAisleRacks.find(a => a.displayName == ar.displayName)?.aisleID || null,
            }
            if (!this.utilsService.isEverythingUnique(b.aisleRackQty.filter(aisleRack => aisleRack.displayName), 'displayName') && ar.displayName) {
              sameAisleRack = true;
            }
            return obj;
          }).filter(obj => !Object.values(obj).every(value => value === null) && obj.transferQty != null)
          : []
      })).filter(f => f.transferQty !== undefined && f.transferQty !== null && (f.transferQty as any) !== '');
      return {
        branchCartonMappingID: a.cartonMappingID,
        warehouses: (warehouses || []).filter(b => b.transferQty !== undefined && b.transferQty !== null && (b.transferQty as any) !== '')
      };
    })

    if (sameAisleRack) {
      this.utilsService.toasterService.error(`Aisle-Rack should be unique.`, '', {
        positionClass: 'toast-top-right',
        closeButton: true,
        timeOut: 10000
      });
      return;
    }

    this.utilsService.postMethodAPI(true, this.utilsService.serverVariableService.PO_CARTON_MAPPING_WAREHOUSE_SAVE, param, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.getRequiredDataW();
      }
    })
  }

  onChangeAisleRackTransferQty = (b: WarehouseMapping) => {
    b.transferQty = (b.aisleRackQty && b.aisleRackQty.length > 0 && b.isMainWarehouse) ? b.aisleRackQty.map(a => a.transferQty).reduce((x, y) => Number(x) + Number(y), 0) : 0
  }

  isTransferQtyDisabled(w: WarehouseMapping): boolean {
    const hasAisleRack = w.aisleRackQty && w.aisleRackQty.length > 0;
    const aisleRackHasDisplayName = w.aisleRackQty?.some(a => a.displayName);
    const hasAnyNonZeroQty = hasAisleRack && w.aisleRackQty?.some(a => Number(a.transferQty) > 0);
    return hasAisleRack && aisleRackHasDisplayName && hasAnyNonZeroQty;
  }

  onChangeAR(w: WarehouseMapping) {
    this.onChangeAisleRackTransferQty(w)
  }

  checkForErrorsWarehouse = (item: CartonMapping): boolean => {
    let hasError = false;
    item.warehouses.forEach((warehouse, warehouseIndex) => {
      if (this.warehouseTotalExceed(item, warehouseIndex)) {
        hasError = true;
      }
      if (warehouse.aisleRackQty) {
        warehouse.aisleRackQty.forEach((_, arIndex) => {
          if (this.totalARExceed(item, warehouseIndex, arIndex)) {
            hasError = true;
          }
        });
      }
    });
    return hasError;
  }

  /// add new third 

  addNewThird = (mainIndex: number, itemIndex: number) => {
    const obj = {
      transferQty: null,
      displayName: null,
      totalAvailQty: null,
    };

    if (!this.cartonMappingAisleRackList[mainIndex].items[itemIndex].locations) {
      this.cartonMappingAisleRackList[mainIndex].items[itemIndex].locations = [];
    }

    this.cartonMappingAisleRackList[mainIndex].items[itemIndex].locations.push(obj);
  }

  onSaveThird = () => {

    let sameAisleRack: boolean = false;

    let param = this.oldItemsAisleRackList.map(a => {
      const warehouses = a.items.map(b => ({
        warehouseCartonMappingID: b.cartonMappingID,
        locations: (b.locations || []).map(ar => {
          const obj = {
            aisleRackTransferID: ar.aisleRackTransferID ? ar.aisleRackTransferID : null,
            transferQty: ar.transferQty ? ar.transferQty : null,
            rackID: a.warehouses.allAisleRacks.find(b => b.displayName === ar.displayName)?.rackID || null,
            aisleID: a.warehouses.allAisleRacks.find(b => b.displayName === ar.displayName)?.aisleID || null,
          }

          if (!this.utilsService.isEverythingUnique(b.locations.filter(aisleRack => aisleRack.displayName), 'displayName') && ar.displayName) {
            sameAisleRack = true;
          }

          return obj;
        }).filter(f => f.transferQty !== undefined && f.transferQty !== null && (f.transferQty as any) !== '')
      }))
      return warehouses
    })
    if (sameAisleRack) {
      this.utilsService.toasterService.error(`Aisle-Rack should be unique.`, '', {
        positionClass: 'toast-top-right',
        closeButton: true,
        timeOut: 10000
      });
      return;
    }

    for (let item of this.oldItemsAisleRackList) {
      for (let index = 0; index < item.items.length; index++) {
        const a = item.items[index];
        if (this.locationTotalExceed(a, index)) {
          return;
        }
      }
    }

    for (let item of param.flat()) {
      let hasError = false;
      for (let i = 0; i < item.locations.length; i++) {
        const ar = item.locations[i];
        if (!ar.aisleID && ar.transferQty) {
          hasError = true;
          this.utilsService.toasterService.error('Please select a valid Aisle Rack before entering a Mapped Quantity.', '', {
            positionClass: 'toast-top-right',
            closeButton: true,
            timeOut: 10000
          });
        }
      };
      if (hasError) {
        return;
      }
    }

    this.utilsService.postMethodAPI(true, this.utilsService.serverVariableService.PO_CARTON_MAPPING_AR_SAVE, param.flat(), (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.getRequiredDataAR();
      }
    })

  }

  locationTotalExceed = (item: CartonMapping, locationIndex: number) => {
    if (!item.locations || item.locations.length === 0 || !item.locations[locationIndex] || !item.locations[locationIndex].transferQty) {
      return false;
    }

    const totalTransfer = (item.locations || []).reduce((sum, ware) => { return sum + (Number(ware.transferQty) || 0) }, 0);
    return totalTransfer > (item.assignedCarton || 0);
  }

  /// Check if Aisle/Rack empty

  checkAisleRackEmpty = (item: CartonMapping): boolean => {
    let hasError = false;
    if (item?.warehouses) {
      (item.warehouses as WarehouseMapping[]).forEach((warehouse) => {
        if (warehouse?.aisleRackQty?.length > 0) {
          warehouse.aisleRackQty.forEach((ar) => {
            if (!ar.displayName && ar.transferQty) {
              hasError = true;
              this.utilsService.toasterService.error('Please select a valid Aisle Rack before entering a Mapped Quantity.', '', {
                positionClass: 'toast-top-right',
                closeButton: true,
                timeOut: 10000
              });
            }
          });
        }
      });
    }
    return hasError;
  }

  handleFocus(): void {
    setTimeout(() => {
      const myCustomClass: string = "custom-cm-class"
      const panel = document.querySelector('.ng-dropdown-panel');
      panel.classList.add(myCustomClass);
    }, 0);
  }

  // Customer order drop location reset if value changes
  onCustomerOrderChange(item: CartonMapping): void {
    const value = item.customerOrderQty as unknown
    if (value === null || value === undefined || value === '' || value === 0) {
      item.dropLocationId = null;
    }
  }
}
