<div class="card card-theme ">
  <div class="card-body pt-0 p">
    <div class="page-filters p-0">
      <div class="page-filters-left">
        <div class="form-group form-group-sm filter-search">
          <div class="form-group-icon-start">
            <i class="th th-outline-search-normal-1 icon-broder "></i>
            <input (input)="onSearch($event)" [(ngModel)]="searchItem" type="search" class="form-control"
              placeholder="Search by name">
          </div>
        </div>
        <div class="form-group theme-ngselect form-group-sm">
          <ng-select (change)="onChangeStatus($event)" class="" placeholder="Status" [multiple]="false"
            [clearable]="true" [items]="activeInactiveStatus" bindLabel="label" bindValue="value" [(ngModel)]="selectedItemStatus">
          </ng-select>
        </div>
      </div>
      <div class="page-filters-right">
        <!-- <div class="form-group theme-ngselect form-group-sm form-group-export">
          <ng-select class="" placeholder="Export All" [multiple]="false" [clearable]="false" [items]="Option"
            bindLabel="name" bindValue="id" [(ngModel)]="selectedOption">
          </ng-select>
        </div> -->
      </div>
    </div>
  </div>
  <div class="card-body p-0">
    <div class="table-responsive mb-5">
      <table class="table-theme table-hover table table-bordered ">
        <thead class="border-less">
          <tr>
            <th>
              <div class="d-flex align-items-center gap-2">
                <!-- <div class="checkbox checkbox-primary checkbox-small">
                  <input type="checkbox" id="tbl-checkbox" class="material-inputs filled-in">
                  <label for="tbl-checkbox"></label>
                </div> -->
                Sr. / Item Name
              </div>
            </th>
            <th>SKU</th>
            <th>Supplier SKU</th>
            <th>Total Qty</th>
            <th>Breach Alert</th>
            <th>Market Type</th>
            <th>Average Price</th>
            <th>Sale price (Carton)</th>
            <th>Sale Price (Loose)</th>
            <th [pageAccess]="{page: utilsService.enumForPage.REG, action: utilsService.enumForPage.EDIT_REG}">Status</th>

          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let item of regObjDetail.associatedItems; index as i; trackBy: trackBy">
            <td class="tbl-user">
              <div class="tbl-user-checkbox-srno">
                <!-- <div class="checkbox checkbox-primary checkbox-small">
                  <input type="checkbox" id="tbl-checkboxItem-{{i}}" class="material-inputs filled-in">
                  <label for="tbl-checkboxItem-{{i}}"></label>
                </div> -->
                <span>{{(i + 1) | padNum}}.</span>
                <div class="tbl-user-wrapper">
                  <div class="tbl-user-image cursor-pointer" [routerLink]="['/users/inventory/items/item-details/' + item.itemId]">
                    <img [src]="(utilsService.imgPath + item.itemFileFormatedName)" alt="valamji">
                  </div>
                  <div class="tbl-user-text-action">
                    <div class="tbl-user-text">
                      <p>{{item.displayName}}</p>
                    </div>

                  </div>
                </div>
              </div>
            </td>
            <td>{{item.itemSKUId}}</td>
            <td>{{item.supplierSku}}</td>
            <td>-</td>
            <td>
              <span class="w-100 d-block" *ngFor="let v of getLevels(i)">
                {{!utilsService.isEmptyObjectOrNullUndefined(v) ? v : ''}}
            </span>
            </td>
            <td>{{item.marketType ? item.marketType : '-'}}</td>
            <td>-</td>
            <td><b>{{item.itemCarton ? item.itemCarton : '-'}}</b></td>

            <td><b>{{item.itemPrice ? item.itemPrice : '-'}}</b></td>
            <td class="tbl-switch" [pageAccess]="{page: utilsService.enumForPage.REG, action: utilsService.enumForPage.EDIT_REG}">
              <div class="switch-box">
                <label htmlFor="switch-{{i}}" class="switch">
                  <input [disabled]="(!item.itemIsActive && !item.isActive)" type="checkbox" id="switch-{{i}}"
                    [(ngModel)]="item.isActive" (change)="onChangeStatusIndividualItem(item, item.isActive, i)">
                  <div class="slider round"></div>
                </label>
              </div>
            </td>
          </tr>
          <tr *ngIf="utilsService.isEmptyObjectOrNullUndefined(regObjDetail.associatedItems)">
            <td colspan="20" class="text-center">
              <span class="truncate">{{utilsService.serverVariableService.STRING_WHEN_NO_RECORDS_FOUND}}</span>
            </td>
          </tr>
        </tbody>

      </table>
      <!-- <app-pagination></app-pagination> -->
    </div>




  </div>
</div>




<!-- ----------------------------------------------------------------------- -->
<!--                           Delete Modal Start                            -->
<!-- ----------------------------------------------------------------------- -->
<div class="modal modal-theme modal-confirmation modal-reject fade" id="deleteModal" tabindex="-1"
  aria-labelledby="deleteModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
            class='th th-close'></i></button>
      </div>
      <div class="modal-body">
        <div class="modal-confirmation-container">
          <div class="modal-confirmation-icon">
            <i class="th th-bold-trash"></i>
          </div>
          <div class="modal-confirmation-content">
            <h5>Are You Sure?</h5>
            <p>You want to Delete <b>Selected</b> Aisle & Racks.</p>
          </div>
        </div>
        <div class="modal-button-group modal-full-width-btn">
          <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
          <button type="button" class="btn btn-primary btn-icon-text"> <i class="th th-outline-tick-circle"></i>
            Delete</button>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                           Delete Modal End                            -->
<!-- ----------------------------------------------------------------------- -->