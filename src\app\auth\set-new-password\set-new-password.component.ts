import { Component, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import { UtilsService } from 'src/app/shared/services/utils.service';

@Component({
  selector: 'app-set-new-password',
  templateUrl: './set-new-password.component.html',
  styleUrls: ['./set-new-password.component.scss']
})
export class SetNewPasswordComponent implements OnInit {

  flagForPasswordHideShow: boolean;
  flagForCPasswordHideShow: boolean;

  userOb = {
    email: null
  };
  passwordForm: FormGroup;

  constructor(public utilsService: UtilsService, private fb: FormBuilder) {
    this.userOb.email = localStorage.getItem('email');

    if (this.utilsService.isNullUndefinedOrBlank(this.userOb.email)) {
      this.utilsService.redirectTo('auth/forgot-password')
    }
  }

  ngOnInit(): void {
    this.flagForPasswordHideShow = true;
    this.flagForCPasswordHideShow = true;
    this.passwordFormGroup();
  }

  passwordFormGroup() {
    this.passwordForm = this.fb.group({
      password: ['', Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.PATTERN_FOR_PASSWORD)])],
      confirm_password: ['', Validators.compose([Validators.required])],
    }, { validators: this.checkPasswords })
  }

  checkPasswords: ValidatorFn = (group: AbstractControl): ValidationErrors | null => {
    let password = group.get('password').value;
    let confirm_password = group.get('confirm_password').value
    return password === confirm_password ? null : { confirmed_check: true }
  }

  changePassword() {

    if (this.passwordForm.invalid) {
      this.passwordForm.markAllAsTouched();
      return;
    }

    const passwordForm = this.passwordForm.value;

    const param = {
      email: this.userOb.email,
      password: passwordForm.password,
    };

    this.utilsService.postMethodAPI(true, this.utilsService.serverVariableService.CHANGE_PASSWORD_FORGOT, param, (response) => {
      if (!this.utilsService.isNullUndefinedOrBlank(response)) {
        setTimeout(() => {
          this.utilsService.redirectTo('auth/login');
          localStorage.removeItem('email');
        }, 200);
      }
    })

  }

}
