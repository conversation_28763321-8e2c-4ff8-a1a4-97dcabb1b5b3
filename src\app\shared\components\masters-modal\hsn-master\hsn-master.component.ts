import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { HsnCode } from 'src/app/models/HsnCode';
import { UtilsService } from 'src/app/shared/services/utils.service';

@Component({
  selector: 'app-hsn-master',
  templateUrl: './hsn-master.component.html',
  styleUrls: ['./hsn-master.component.css']
})
export class HsnMasterComponent implements OnInit {

  @Input({ alias: 'hsnGroup', required: true }) hsnGroup: FormGroup;
  @Input({ alias: 'gstSlabDropdown', required: true }) gstSlabDropdown: any[];
  @Input({ alias: 'hsnObj', required: true }) hsnObj: HsnCode;
  @Input({ alias: 'page', required: true }) page: string;

  @Output() onSaveHsnCode: EventEmitter<any> = new EventEmitter<any>();

  constructor(public utilsService: UtilsService) { }

  ngOnInit() {
  }

}
