<div class="page-filters">
    <div class="page-filters-left">
        <div class="form-group form-group-sm filter-search">
            <div class="form-group-icon-start">
                <i class="th th-outline-search-normal-1 icon-broder "></i>
                <input (change)="onSearch($event)" [(ngModel)]="searchText" type="search" class="form-control"
                    placeholder="Search By Marka">
            </div>
        </div>
    </div>
    <div class="page-filters-right">
        <div class="dropdown export-dropdown">
            <button [disabled]="utilsService.isEmptyObjectOrNullUndefined(itemObjDetail.stockDetails)" type="button"
                class="btn btn-sm btn-outline-white dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                Export
            </button>
            <ul class="dropdown-menu">
                <li><a (click)="exportReport()" class="dropdown-item">Export</a></li>
            </ul>
        </div>
    </div>
</div>
<div class="card card-theme card-table-sticky">
    <div class="card-body p-0" *ngIf="!utilsService.isEmptyObjectOrNullUndefined(itemObjDetail.stockDetails) else noRecord">
        <div class="stock-details-accordion accordion accordion-group mb-0" id="accordionSingle">
            <div class="accordion-item" *ngFor="let item of itemObjDetail.stockDetails; index as i; trackBy: trackBy">
                <h2 class="accordion-header" id="accordionSingleHeadingOne_stock">
                    <button [disabled]="utilsService.isEmptyObjectOrNullUndefined(item.stockDetailsResponses)" (click)="onCollapse(i)"
                        class="accordion-button" [ngClass]="{'collapsed': !item.isExpand}" type="button" data-bs-toggle="collapse"
                        [attr.data.target]="'#accordionSingleCollapseOne'+ i" aria-expanded="true"
                        [attr.aria-controls]="'accordionSingleCollapseOne'+ i">
                        <div class="accordion-header-left">
                            <h6>{{item.warehouseName}}</h6>
                        </div>
                        <div class="accordion-header-right">
                        </div>
                    </button>
                </h2>
                <div *ngIf="item.isExpand" [id]="'accordionSingleCollapseOne' + i" [ngClass]="{'show': item.isExpand}"
                    class="accordion-collapse collapse" aria-labelledby="accordionSingleHeadingOne_stock"
                    data-bs-parent="#accordionSingle">
                    <div class="accordion-body pt-0">
                        <div class="table-responsive">
                            <table class="table-theme table-hover table table-bordered table-sticky">
                                <thead class="border-less">
                                    <tr>
                                        <th class="d-flex align-items-center gap-2">
                                            Marka
                                        </th>

                                        <th>Qty/Carton</th>
                                        <th>Total Carton Qty</th>
                                        <th>Loose Qty</th>
                                        <th>Total Qty</th>
                                        <th>Weight & Dimension</th>
                                        <th>Quality</th>
                                        <th>Images</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr
                                        *ngFor="let child of item.stockDetailsResponses; index as j; trackBy: trackByChild">
                                        <td class="tbl-user tbl-bold">
                                            {{child.marka}}
                                        </td>
                                        <td>{{child.cartonQty}}</td>
                                        <td>{{child.totalCartonQty}}</td>
                                        <td>-</td>
                                        <td>{{child.totalQty}}</td>
                                        <td class="tbl-level">
                                            <span>{{child.weight}} {{child.weightShortCode}}</span>
                                            <span>{{child.length}} x {{child.width}} x {{child.height}} {{child.uniteShortCode}}</span>
                                        </td>
                                        <td>-</td>
                                        <td class="tbl-action">
                                            <div class="tbl-action-group">
                                                <ng-container *ngIf="child.grnGroupLinkId">
                                                    <span>{{child.countOfImage > 5 ? '5+' : child.countOfImage}}</span>
                                                    <a (click)="utilsService.openStockDetailsInNewTab(child.grnGroupLinkId)" class="btn-link text-primary">
                                                        Link
                                                    </a>
                                                </ng-container>
                                                <button (click)="onCopy()" [copyText]="getGrnGroupLink(child.grnGroupLinkId)"
                                                    class="btn btn-xs btn-transparent btn-icon" ngbTooltip="Copy" placement="bottom" container="body" triggers="hover">
                                                    <i class="th th-outline-copy"></i>
                                                </button>
                                                <!-- <button class="btn btn-xs btn-light-primary btn-icon"
                                                    ngbTooltip="Upload" placement="bottom" container="body"
                                                    triggers="hover">
                                                    <i class="bi bi-upload"></i>
                                                </button> -->
                                                <button (click)="openStockImageModal(child)" class="btn btn-xs btn-light-white btn-icon" ngbTooltip="Edit"
                                                    placement="left" container="body" triggers="hover">
                                                    <i class="th th-outline-edit"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr *ngIf="utilsService.isEmptyObjectOrNullUndefined(item.stockDetailsResponses)">
                                        <td colspan="20" class="text-center">
                                            <span class="truncate">{{utilsService.serverVariableService.STRING_WHEN_NO_RECORDS_FOUND}}</span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <ng-template #noRecord>
        <app-no-record />
    </ng-template>
</div>

