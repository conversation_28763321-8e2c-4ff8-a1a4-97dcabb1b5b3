import { deserializeAs, serializeAs } from 'cerialize';

export class TransportBranchSheet {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('createdDate')
    @deserializeAs('createdDate')
    private _createdDate: string;

    @serializeAs('lastModifiedDate')
    @deserializeAs('lastModifiedDate')
    private _lastModifiedDate: string;

    @serializeAs('branchCode')
    @deserializeAs('branchCode')
    private _branchCode: string;

    @serializeAs('city')
    @deserializeAs('city')
    private _city: string;

    @serializeAs('pinCode')
    @deserializeAs('pinCode')
    private _pinCode: string;

    @serializeAs('address')
    @deserializeAs('address')
    private _address: string;

    @serializeAs('transportMasterName')
    @deserializeAs('transportMasterName')
    private _transportMasterName: string;

    @serializeAs('transporterID')
    @deserializeAs('transporterID')
    private _transporterID: string;

    @serializeAs('locationLink')
    @deserializeAs('locationLink')
    private _locationLink: string;

    @serializeAs('booking')
    @deserializeAs('booking')
    private _booking: boolean;

    @serializeAs('delivery')
    @deserializeAs('delivery')
    private _delivery: boolean;

    @serializeAs('isNeedStamp')
    @deserializeAs('isNeedStamp')
    private _isNeedStamp: boolean;

    @serializeAs('isActive')
    @deserializeAs('isActive')
    private _isActive: boolean;

    @serializeAs('isEwayBill')
    @deserializeAs('isEwayBill')
    private _isEwayBill: boolean;

    @serializeAs('contacts')
    @deserializeAs('contacts')
    private _contacts: ContactsBT[];

    @serializeAs('emails')
    @deserializeAs('emails')
    private _emails: EmailBT[];

    @serializeAs('landlineNos')
    @deserializeAs('landlineNos')
    private _landlineNos: LandlineBT[];

    @serializeAs('deleteId')
    @deserializeAs('deleteId')
    private _deleteId: number[];


    constructor() {
        this.booking = false;
        this.delivery = false;
        this.contacts = [];
        this.emails = [];
        this.landlineNos = [];
        this.isEwayBill = false;
        this.isNeedStamp = false;
        this.isActive = false;
    }


    /**
     * Getter transportMasterName
     * @return {string}
     */
	public get transportMasterName(): string {
		return this._transportMasterName;
	}

    /**
     * Setter transportMasterName
     * @param {string} value
     */
	public set transportMasterName(value: string) {
		this._transportMasterName = value;
	}


    /**
     * Getter isActive
     * @return {boolean}
     */
	public get isActive(): boolean {
		return this._isActive;
	}

    /**
     * Setter isActive
     * @param {boolean} value
     */
	public set isActive(value: boolean) {
		this._isActive = value;
	}


    /**
     * Getter isNeedStamp
     * @return {boolean}
     */
	public get isNeedStamp(): boolean {
		return this._isNeedStamp;
	}

    /**
     * Getter isEwayBill
     * @return {boolean}
     */
	public get isEwayBill(): boolean {
		return this._isEwayBill;
	}

    /**
     * Setter isNeedStamp
     * @param {boolean} value
     */
	public set isNeedStamp(value: boolean) {
		this._isNeedStamp = value;
	}

    /**
     * Setter isEwayBill
     * @param {boolean} value
     */
	public set isEwayBill(value: boolean) {
		this._isEwayBill = value;
	}


    /**
     * Getter deleteId
     * @return {number[]}
     */
	public get deleteId(): number[] {
		return this._deleteId;
	}

    /**
     * Setter deleteId
     * @param {number[]} value
     */
	public set deleteId(value: number[]) {
		this._deleteId = value;
	}


    /**
     * Getter contacts
     * @return {ContactsBT[]}
     */
	public get contacts(): ContactsBT[] {
		return this._contacts;
	}

    /**
     * Getter emails
     * @return {EmailBT[]}
     */
	public get emails(): EmailBT[] {
		return this._emails;
	}

    /**
     * Getter landlineNos
     * @return {LandlineBT[]}
     */
	public get landlineNos(): LandlineBT[] {
		return this._landlineNos;
	}

    /**
     * Setter contacts
     * @param {ContactsBT[]} value
     */
	public set contacts(value: ContactsBT[]) {
		this._contacts = value;
	}

    /**
     * Setter emails
     * @param {EmailBT[]} value
     */
	public set emails(value: EmailBT[]) {
		this._emails = value;
	}

    /**
     * Setter landlineNos
     * @param {LandlineBT[]} value
     */
	public set landlineNos(value: LandlineBT[]) {
		this._landlineNos = value;
	}


    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Getter createdDate
     * @return {string}
     */
	public get createdDate(): string {
		return this._createdDate;
	}

    /**
     * Getter lastModifiedDate
     * @return {string}
     */
	public get lastModifiedDate(): string {
		return this._lastModifiedDate;
	}

    /**
     * Getter branchCode
     * @return {string}
     */
	public get branchCode(): string {
		return this._branchCode;
	}


    /**
     * Getter city
     * @return {string}
     */
	public get city(): string {
		return this._city;
	}

    /**
     * Getter pinCode
     * @return {string}
     */
	public get pinCode(): string {
		return this._pinCode;
	}

    /**
     * Getter address
     * @return {string}
     */
	public get address(): string {
		return this._address;
	}

    /**
     * Getter transporterID
     * @return {string}
     */
	public get transporterID(): string {
		return this._transporterID;
	}

    /**
     * Getter locationLink
     * @return {string}
     */
	public get locationLink(): string {
		return this._locationLink;
	}

    /**
     * Getter booking
     * @return {boolean}
     */
	public get booking(): boolean {
		return this._booking;
	}

    /**
     * Getter delivery
     * @return {boolean}
     */
	public get delivery(): boolean {
		return this._delivery;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Setter createdDate
     * @param {string} value
     */
	public set createdDate(value: string) {
		this._createdDate = value;
	}

    /**
     * Setter lastModifiedDate
     * @param {string} value
     */
	public set lastModifiedDate(value: string) {
		this._lastModifiedDate = value;
	}

    /**
     * Setter branchCode
     * @param {string} value
     */
	public set branchCode(value: string) {
		this._branchCode = value;
	}


    /**
     * Setter city
     * @param {string} value
     */
	public set city(value: string) {
		this._city = value;
	}

    /**
     * Setter pinCode
     * @param {string} value
     */
	public set pinCode(value: string) {
		this._pinCode = value;
	}

    /**
     * Setter address
     * @param {string} value
     */
	public set address(value: string) {
		this._address = value;
	}

    /**
     * Setter transporterID
     * @param {string} value
     */
	public set transporterID(value: string) {
		this._transporterID = value;
	}

    /**
     * Setter locationLink
     * @param {string} value
     */
	public set locationLink(value: string) {
		this._locationLink = value;
	}

    /**
     * Setter booking
     * @param {boolean} value
     */
	public set booking(value: boolean) {
		this._booking = value;
	}

    /**
     * Setter delivery
     * @param {boolean} value
     */
	public set delivery(value: boolean) {
		this._delivery = value;
	}


   
}

export class EmailBT {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('email')
    @deserializeAs('email')
    private _email: string;

    @serializeAs('type')
    @deserializeAs('type')
    private _type: string;


    /**
     * Getter type
     * @return {string}
     */
	public get type(): string {
		return this._type;
	}

    /**
     * Setter type
     * @param {string} value
     */
	public set type(value: string) {
		this._type = value;
	}


    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Getter email
     * @return {string}
     */
	public get email(): string {
		return this._email;
	}

    /**
     * Setter email
     * @param {string} value
     */
	public set email(value: string) {
		this._email = value;
	}


}

export class LandlineBT {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('landlineNo')
    @deserializeAs('landlineNo')
    private _landlineNo: any;

    @serializeAs('type')
    @deserializeAs('type')
    private _type: string;


    /**
     * Getter type
     * @return {string}
     */
	public get type(): string {
		return this._type;
	}

    /**
     * Setter type
     * @param {string} value
     */
	public set type(value: string) {
		this._type = value;
	}



    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Getter landlineNo
     * @return {any}
     */
	public get landlineNo(): any {
		return this._landlineNo;
	}

    /**
     * Setter landlineNo
     * @param {any} value
     */
	public set landlineNo(value: any) {
		this._landlineNo = value;
	}


}

export class ContactsBT {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('name')
    @deserializeAs('name')
    private _name: string;

    @serializeAs('mobileNo')
    @deserializeAs('mobileNo')
    private _mobileNo: string;

    @serializeAs('type')
    @deserializeAs('type')
    private _type: string;

    constructor() {}


    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Getter name
     * @return {string}
     */
	public get name(): string {
		return this._name;
	}

    /**
     * Setter name
     * @param {string} value
     */
	public set name(value: string) {
		this._name = value;
	}

    /**
     * Getter mobileNo
     * @return {string}
     */
	public get mobileNo(): string {
		return this._mobileNo;
	}

    /**
     * Setter mobileNo
     * @param {string} value
     */
	public set mobileNo(value: string) {
		this._mobileNo = value;
	}

    /**
     * Getter type
     * @return {string}
     */
	public get type(): string {
		return this._type;
	}

    /**
     * Setter type
     * @param {string} value
     */
	public set type(value: string) {
		this._type = value;
	}
    


}