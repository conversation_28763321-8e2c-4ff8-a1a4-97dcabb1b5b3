/* -------------------------------------------------------------------------- */
/*                         Date-Time Picker Component Styles                 */
/* -------------------------------------------------------------------------- */

// Single Card Design - Compact and optimized
.picker-dropdown-menu {
  padding: 0.75rem; // Reduced padding for more compact design
  border: 1px solid $stock_light;
  box-shadow: 0 0.25rem 0.5rem rgba($black_color, 0.1);
  border-radius: 0.5rem;
  background: $white_color;
  width: 300px; // Slightly smaller width for compact design

  // Time-only mode - more compact
  &.time-only-dropdown {
    width: 260px; // Smaller width for time-only mode
  }

  // Right layout - optimized width
  &.time-right-layout {
    width: 520px !important; // Reduced width for more compact design
    max-width: 580px !important;
  }
}

// Simple dropdown positioning
.picker-dropdown-menu {
  // Ensure consistent z-index
  z-index: 1050 !important;

  // When inside a modal, use higher z-index
  .modal & {
    z-index: 1060 !important;
  }
}

// Single Card Date-Time Picker - Clean and Simple
.date-time-picker {
  // Remove all complex styling - let dropdown handle the card design
  background: transparent;
  border: none;
  padding: 0;
  box-shadow: none;
  width: 100%;
  min-height: auto;

  // All variants use same simple approach
  &.time-only-picker,
  &.time-right-picker {
    background: transparent;
    border: none;
    padding: 0;
    box-shadow: none;
    width: 100%;
  }
}

// Content Container Styles
.picker-content {
  // Horizontal layout for right time position
  &.picker-content-horizontal {
    display: flex;
    gap: 1rem;
    align-items: flex-start;
  }
}

// Section Styles
.picker-section {
  margin-bottom: 1rem;

  &:last-child {
    margin-bottom: 0;
  }

  &.date-section {
    // Only show border when there's a time section following it
    &:not(:last-child) {
      border-bottom: 1px solid $stock_light;
      padding-bottom: 1rem;
    }

    // Right layout date section
    &.date-section-right-layout {
      border-bottom: none;
      border-right: 1px solid $stock_light;
      padding-bottom: 0;
      padding-right: 1rem;
      flex: 1;
      min-width: 280px;
      max-width: 320px;
    }
  }

  &.time-section {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;

    // Right layout time section
    &.time-section-right-layout {
      padding-top: 0;
      padding-left: 1rem;
      flex: 0 0 auto;
      min-width: 220px; // Reduced minimum width for compact design
      max-width: 250px; // Reduced maximum width

      // Time controls container for right layout - more compact
      .time-controls-container {
        width: 100%;
        max-width: 220px; // Reduced width for more compact design

        // HTML Select styling for time controls - compact design
        .form-select {
          font-size: 0.75rem; // Smaller font size
          padding: 0.25rem 0.5rem; // Reduced padding
          border: 1px solid $stock_light;
          border-radius: 0.25rem;
          background-color: $white_color;
          color: $text_title_color;
          transition: all 0.1s ease-out;
          height: 2rem; // Fixed height for consistency

          &:focus {
            border-color: $primary_bg_color;
            box-shadow: 0 0 0 0.1rem rgba($primary_color, 0.2); // Smaller focus ring
            outline: none;
          }

          &:disabled {
            background-color: $bg_grey;
            color: $text_black_color;
            opacity: 0.6;
            cursor: not-allowed;
          }

          // Disabled options styling
          option:disabled {
            color: $text_black_color;
            background-color: $bg_grey;
          }
        }
      }
    }
  }
}

// Header Styles
.picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.75rem;
  padding: 0.25rem 0;

  .btn-nav {
    background: none;
    border: none; // No permanent border
    padding: 0.5rem; // Better padding for optimal click target
    border-radius: 0.375rem; // Slightly more rounded for modern look
    color: $gray_color;
    cursor: pointer;
    transition: all 0.15s ease-in-out;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 2.25rem; // Consistent width
    min-height: 2.25rem; // Consistent height

    &:hover:not(:disabled) {
      background-color: rgba($primary_color, 0.15); // Light primary background on hover
      color: $primary_color; // Primary color for consistency
      transform: scale(1.05); // Subtle scale effect
    }

    &:active:not(:disabled) {
      transform: scale(0.95); // Quick press feedback
      background-color: rgba($primary_color, 0.15);
    }

    &:disabled {
      opacity: 0.4;
      cursor: not-allowed;

      &:hover {
        transform: none; // No scale effect when disabled
        background-color: transparent;
        color: $gray_color; // Keep original color when disabled
      }
    }

    i {
      font-size: 1.1rem; // Slightly larger for better visibility
      line-height: 1;
      display: block;
    }
  }

  .header-center {
    display: flex;
    align-items: center;
    justify-content: center; // Better center alignment
    flex: 1; // Take available space for better centering

    .btn-month,
    .btn-year {
      background: none;
      border: none;
      padding: 0.35rem 0.75rem; // Better padding for optimal design
      border-radius: 0.375rem; // Slightly more rounded for modern look
      color: $text_black_color;
      font-weight: 600; // Slightly bolder for better visibility
      cursor: pointer;
      transition: all 0.15s ease-in-out;
      min-height: 2rem; // Consistent height
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover:not(:disabled) {
        background-color: rgba($primary_color, 0.15); // Light primary color like days
        color: $primary_color; // Primary color text for consistency
        border-color: rgba($primary_color, 0.3); // Subtle border highlight
        transform: translateY(-1px); // Subtle lift effect
      }

      &:active {
        transform: translateY(0); // Reset on click
        background-color: rgba($primary_color, 0.25); // Slightly darker on click
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;

        &:hover {
          background-color: transparent;
          transform: none;
        }
      }
    }

    .btn-month {
      font-size: 1.05rem; // Slightly larger for prominence
      letter-spacing: 0.025em; // Better letter spacing
    }

    .btn-year {
      font-size: 0.95rem; // Balanced with month
      color: $text_black_color; // Same color as month for consistency
      font-weight: 600; // Match month button weight
    }

    // Year range display for paginated year view
    .year-range-display {
      font-size: 1.1rem;
      font-weight: 600;
      color: $text_black_color;
      padding: 0.35rem 0.75rem;
      text-align: center;
      letter-spacing: 0.025em;
      min-height: 2rem;
      display: flex;
      align-items: center;
      justify-content: center;

      // Clickable year range styling
      &.clickable {
        background: none;
        border: none;
        border-radius: 0.375rem;
        cursor: pointer;
        transition: all 0.15s ease-in-out;

        &:hover:not(:disabled) {
          background-color: rgba($primary_color, 0.15);
          color: $primary_color;
          transform: translateY(-1px);
        }

        &:active {
          transform: translateY(0);
          background-color: rgba($primary_color, 0.25);
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;

          &:hover {
            background-color: transparent;
            transform: none;
          }
        }
      }
    }
  }
}

// Time Header
.time-header {
  margin-bottom: 0.75rem;
  text-align: center;

  .time-label {
    font-weight: 500;
    color: $text_black_color;
    font-size: 0.9rem;
  }
}

// Grid Styles
.picker-grid {
  display: grid;
  gap: 0.125rem;
  justify-content: center;

  &.months-grid {
    grid-template-columns: repeat(3, 1fr);
    max-width: 240px;
    margin: 0 auto;
  }

  &.years-grid {
    grid-template-columns: repeat(4, 1fr); // 4x4 grid for 16 years (consistent height)
    grid-template-rows: repeat(4, 1fr); // Explicit 4 rows for consistent height
    max-width: 280px; // Optimized width for 4x4 layout
    margin: 0 auto;
    gap: 0.4rem; // Balanced spacing for 4x4 grid
    min-height: 200px; // Ensure consistent height with date view
    // Remove scrolling - now using pagination
  }

  &.days-grid {
    grid-template-columns: repeat(7, 1fr);
    max-width: 280px;
    margin: 0 auto;
  }
}

// Picker Item Styles
.picker-item {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 1.8rem;
  min-width: 1.8rem;
  padding: 0.2rem 0.3rem;
  border-radius: 0.25rem;
  cursor: pointer;
  transition: all 0.1s ease-out; // Faster, smoother transition
  font-size: 0.8rem;
  font-weight: 500;
  color: $text_title_color;
  background-color: transparent;
  border: 1px solid transparent;

  // Active state for quick feedback
  &:active {
    transform: scale(0.95);
    transition: all 0.05s ease-out; // Very quick active feedback
  }

  &:hover:not(.disabled):not(.day-header) {
    background-color: rgba($primary_color, 0.15); // Light primary color for better visibility
    color: $primary_color; // Primary color text for better contrast
    border-color: rgba($primary_color, 0.3); // Subtle border highlight
  }

  &.selected {
    background-color: $primary_color;
    color: $white_color;
    font-weight: 500;

    &:hover {
      background-color: $primary_hover_color !important; // Keep primary color on hover
      color: $white_color !important; // Ensure text stays white
    }

    &:active {
      background-color: darken($primary_color, 10%) !important;
      transform: scale(0.95);
    }
  }

  &.today:not(.selected) {
    background-color: $warning_light_color;
    color: $warning_color;
    border-color: $warning_bg_color;
    font-weight: 600;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: 2px;
      left: 50%;
      transform: translateX(-50%);
      width: 4px;
      height: 4px;
      background-color: $warning_color;
      border-radius: 50%;
    }
  }

  &.selected.today::after {
    background-color: $white_color;
  }

  &.current:not(.selected) {
    background-color: $primary_light_color;
    color: $primary_color;
    border-color: $primary_bg_color;
    font-weight: 600;
  }

  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    color: $stock_dark;

    &:hover {
      background-color: transparent;
    }
  }

  &.day-header {
    font-weight: bold;
    color: $primary_color;
    cursor: default;
    font-size: 0.7rem;
    text-transform: uppercase;

    &:hover {
      background-color: transparent;
    }
  }

  &.empty-cell {
    background-color: transparent;
    border: none;
    cursor: default;

    &:hover {
      background-color: transparent;
    }
  }

  &.other-month {
    color: $text_black_color;
    opacity: 0.85;
    cursor: pointer; // Make clickable
    font-weight: 400;

    &:hover {
      background-color: rgba($primary_color, 0.12); // Light primary color for consistency
      color: $primary_color; // Primary color text
      opacity: 1;
      border-color: rgba($primary_color, 0.25); // Subtle border highlight
    }

    &.selected {
      background-color: $primary_color;
      color: $white_color;
      opacity: 1;
      font-weight: 500;

      &:hover {
        background-color: $primary_hover_color !important; // Keep primary color on hover
        color: $white_color !important; // Ensure text stays white
      }

      &:active {
        background-color: darken($primary_color, 10%) !important;
        transform: scale(0.95);
      }
    }

    &.today:not(.selected) {
      background-color: $warning_light_color;
      color: $warning_color;
      border-color: $warning_bg_color;
      font-weight: 600;
      opacity: 1;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        bottom: 2px;
        left: 50%;
        transform: translateX(-50%);
        width: 4px;
        height: 4px;
        background-color: $warning_color;
        border-radius: 50%;
      }
    }

    &.selected.today::after {
      background-color: $white_color;
    }

    &.disabled {
      opacity: 0.3;
      cursor: not-allowed;

      &:hover {
        background-color: transparent;
        opacity: 0.3;
      }
    }

    &.prev-month,
    &.next-month {
      font-style: normal;
    }
  }

  &.month-item,
  &.year-item {
    min-height: 2.5rem; // Better height for optimal design
    font-size: 0.9rem; // Slightly larger for better readability
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;

    // Consistent hover effect with light primary color like days
    &:hover:not(.disabled):not(.selected) {
      background-color: rgba($primary_color, 0.15) !important; // Light primary color
      color: $primary_color !important; // Primary color text
      border-color: rgba($primary_color, 0.3) !important; // Subtle border highlight
      transform: translateY(-1px); // Subtle lift effect
    }

    &.current:not(.selected) {
      background-color: $primary_light_color;
      color: $primary_color;
      border-color: $primary_bg_color;
      font-weight: 600;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        bottom: 3px;
        left: 50%;
        transform: translateX(-50%);
        width: 6px;
        height: 6px;
        background-color: $primary_color;
        border-radius: 50%;
      }

      // Override current styling on hover for consistency
      &:hover {
        background-color: rgba($primary_color, 0.15) !important;
        color: $primary_color !important;
        border-color: rgba($primary_color, 0.3) !important;
        transform: translateY(-1px);
      }
    }

    &.selected {
      background-color: $primary_color;
      color: $white_color;
      font-weight: 600;

      &:hover {
        background-color: $primary_hover_color !important; // Keep primary color on hover
        color: $white_color !important; // Ensure text stays white
        transform: none; // No lift effect for selected items
      }

      &.current::after {
        background-color: $white_color;
      }
    }
  }

  &.day-item {
    min-height: 1.8rem;
    min-width: 1.8rem;
  }
}

// Time Controls Styles - Compact and optimized
.time-controls {
  display: flex;
  gap: 0.4rem; // Reduced gap for more compact design
  align-items: flex-end;

  // Time-only controls - compact and centered
  &.time-only-controls {
    gap: 0.5rem; // Reduced gap
    justify-content: center;
    max-width: 100%;

    .time-control {
      flex: 0 1 auto;
      min-width: 55px; // Smaller minimum width
      max-width: 70px; // Smaller maximum width

      .time-control-label {
        font-size: 0.65rem; // Smaller font size
        margin-bottom: 0.15rem; // Reduced margin
        text-align: center;
        font-weight: 600; // Bolder for better readability
        color: $primary_color; // Primary color for consistency
      }
    }
  }

  // Right layout time controls - compact and optimized
  &.time-right-controls {
    gap: 0.4rem; // Reduced gap for more compact design
    align-items: flex-end;
    justify-content: flex-start;
    flex-wrap: nowrap; // Ensure controls stay in one row

    .time-control {
      flex: 0 1 auto;
      min-width: 50px; // Smaller minimum width
      max-width: 60px; // Smaller maximum width

      .time-control-label {
        font-size: 0.6rem; // Smaller font size
        margin-bottom: 0.1rem; // Reduced margin
        text-align: center;
        font-weight: 600; // Bolder for better readability
        color: $primary_color; // Primary color for consistency
      }
    }

    // Period control - more compact
    .period-control {
      min-width: 45px; // Smaller minimum width
      max-width: 55px; // Smaller maximum width
    }

    // Center alignment for 24-hour format (no AM/PM button)
    &.time-24hour {
      justify-content: center;

      .time-control {
        flex: 0 0 auto;
      }
    }
  }

  .time-control {
    flex: 1;
    min-width: 0;

    .time-control-label {
      display: block;
      font-size: 0.65rem; // Smaller label font
      font-weight: 600; // Slightly bolder for better readability
      color: $primary_color; // Use primary color for better visibility
      margin-bottom: 0.15rem; // Reduced margin for compact design
      text-transform: uppercase;
      letter-spacing: 0.025em;
      text-align: center; // Center align labels
    }

    // HTML Select styling for time controls - Compact design
    .form-select {
      font-size: 0.75rem; // Smaller font size
      padding: 0.25rem 0.5rem; // Reduced padding for compact look
      border: 1px solid $stock_light;
      border-radius: 0.25rem;
      background-color: $white_color;
      color: $text_title_color;
      transition: all 0.1s ease-out;
      width: 100%;
      height: 2rem; // Fixed height for consistency
      line-height: 1.2; // Better line height for compact design

      &:focus {
        border-color: $primary_bg_color;
        box-shadow: 0 0 0 0.1rem rgba($primary_color, 0.2); // Smaller focus ring
        outline: none;
      }

      &:disabled {
        background-color: $bg_grey;
        color: $text_black_color;
        opacity: 0.6;
        cursor: not-allowed;
      }

      // Disabled options styling
      option:disabled {
        color: $text_black_color;
        background-color: $bg_grey;
      }

      // Placeholder option styling
      option[value=""] {
        color: $gray_color;
        font-style: italic;
      }
    }

    // Period control specific styling - more compact
    &.period-control {
      flex: 0 0 auto;
      min-width: 50px; // Smaller minimum width
      max-width: 60px; // Smaller maximum width

      // Remove label spacing since no label
      .time-control-label {
        display: none;
      }
    }
  }
}

// Period Toggle Button Styles - Compact design
.period-toggle-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 2rem; // Match form-select height for consistency
  background-color: $white_color;
  border: 1px solid $stock_light;
  border-radius: 0.25rem;
  font-size: 0.65rem; // Smaller font size for compact design
  font-weight: 600; // Bolder for better readability
  color: $text_black_color;
  cursor: pointer;
  transition: all 0.1s ease-out; // Faster, smoother transition
  padding: 0;
  margin-top: 0.75rem; // Reduced margin for compact design

  &:hover:not(:disabled) {
    border-color: $primary_bg_color;
    box-shadow: 0 0 0 0.15rem rgba($primary_color, 0.25);
  }

  &:focus:not(:disabled) {
    outline: none;
    border-color: $primary_bg_color;
    box-shadow: 0 0 0 0.15rem rgba($primary_color, 0.25);
  }

  // Active state for quick feedback
  &:active:not(:disabled) {
    transform: scale(0.98);
    transition: all 0.05s ease-out; // Very quick active feedback
    background-color: $bg_grey;
  }

  &:disabled {
    background-color: $bg_grey;
    opacity: 0.65;
    cursor: not-allowed;
  }

  .period-option {
    flex: 1;
    padding: 0.2rem 0.3rem;
    text-align: center;
    transition: all 0.15s ease-in-out;
    border-radius: 0.2rem;
    margin: 1px;

    &.active {
      background-color: $primary_color;
      color: $white_color;
      font-weight: 600;
    }

    &:not(.active) {
      color: $gray_color;
    }
  }

  .period-divider {
    color: $stock_light;
    font-weight: 300;
    margin: 0 2px;
    user-select: none;
  }

  // State-based styling
  &.am-selected .period-option:first-child,
  &.pm-selected .period-option:last-child {
    background-color: $primary_color;
    color: $white_color;
  }
}

// Time Controls ng-select styles - width only + search input visibility
.time-controls {
  .time-control {
    ::ng-deep .ng-select {
      width: 100%;

      // Ensure search input is visible when typing
      &.ng-select-searchable {
        .ng-select-container {
          .ng-value-container {
            .ng-input {
              input {
                background-color: #ffffff !important;
                color: #292D32 !important;
                opacity: 1 !important;
                visibility: visible !important;
                width: unset;
              }
            }
          }
        }
      }
    }
  }
}

// Responsive Styles for Mobile
@media (max-width: 575.98px) {
  .picker-dropdown-menu {
    width: 300px;

    &.time-only-dropdown {
      width: 260px;
    }

    &.time-right-layout {
      width: 300px; // Convert to single column on mobile
    }
  }

  // Mobile layout adjustments
  .picker-content-horizontal {
    flex-direction: column !important;
    gap: 1rem;

    .date-section-right-layout {
      border-right: none !important;
      border-bottom: 1px solid $stock_light;
      padding-right: 0 !important;
      padding-bottom: 1rem;
    }

    .time-section-right-layout {
      padding-left: 0 !important;
      padding-top: 0.5rem;
    }
  }

  .time-controls {
    gap: 0.4rem;

    &.time-right-controls {
      gap: 0.4rem;
      justify-content: center;

      .time-control {
        flex: 1;
        min-width: 45px;
        max-width: 60px;

        .time-control-label {
          font-size: 0.65rem;
        }
      }
    }

    .time-control {
      .time-control-label {
        font-size: 0.65rem;
      }

      ::ng-deep .ng-select {
        width: 100%;
      }

      // Period toggle button responsive styles
      &.period-control {
        .period-toggle-btn {
          height: 1.6rem;
          font-size: 0.7rem;
          margin-top: 0.85rem; // Adjusted for mobile label size

          .period-option {
            padding: 0.15rem 0.25rem;
          }

          .period-divider {
            margin: 0 1px;
          }
        }
      }
    }
  }

  .picker-grid {
    &.months-grid {
      grid-template-columns: repeat(3, 1fr);
      max-width: 200px;
    }

    &.days-grid {
      grid-template-columns: repeat(7, 1fr);
      max-width: 240px;
    }

    &.years-grid {
      grid-template-columns: repeat(4, 1fr); // Keep 4x4 on mobile for consistency
      grid-template-rows: repeat(4, 1fr); // Explicit 4 rows
      max-width: 240px;
      gap: 0.3rem; // Slightly smaller gap on mobile
      min-height: 180px; // Slightly smaller height on mobile
    }
  }

  .picker-item {
    min-height: 1.6rem;
    min-width: 1.6rem;
    padding: 0.2rem 0.3rem;
    font-size: 0.7rem;

    &.today:not(.selected)::after,
    &.current:not(.selected)::after {
      width: 3px;
      height: 3px;
      bottom: 1px;
    }

    &.month-item,
    &.year-item {
      &.current:not(.selected)::after {
        width: 4px;
        height: 4px;
        bottom: 2px;
      }
    }
  }
}

// Simple responsive adjustments
@media (max-width: 768px) {
  .picker-dropdown-menu {
    &.time-right-layout {
      width: 400px; // Smaller on tablets
    }
  }
}

/* -------------------------------------------------------------------------- */
/*                         Date-Range Selection Component Styles               */
/* -------------------------------------------------------------------------- */

// NgBootstrap Dropdown Menu Styles
.picker-dropdown-menu {
  padding: 0;
  border: none;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  border-radius: 0.375rem;
  min-width: 280px;
  max-width: 320px;
}

// Simple dropdown positioning
.picker-dropdown-menu {
  // Ensure consistent z-index
  z-index: 1050 !important;

  // When inside a modal, use higher z-index
  .modal & {
    z-index: 1060 !important;
  }
}

// Month-Day Picker Styles (Clean Bootstrap-like design)
.month-day-picker {
  font-size: 0.875rem;
  background: $white_color;
  padding: 0.875rem;
  box-shadow: 0 0.125rem 0.25rem rgba($black_color, 0.075);
  max-width: 280px;
  min-width: 240px;
  min-height: 280px; // Fixed height to prevent dropdown position jumping

  .picker-section {
    margin-bottom: 0.75rem;

    &:last-child {
      margin-bottom: 0;
    }

    &.picker-section-days {
      border-top: 1px solid $stock_light;
      padding-top: 0.75rem;
      margin-top: 0.75rem;
      min-height: 180px; // Consistent height for days section
    }

    // Month selection view - ensure same height as days section
    &:has(.months-grid) {
      min-height: 180px;
      display: flex;
      flex-direction: column;
      justify-content: center; // Center the content vertically
    }
  }

  // Month Navigation Header
  .month-navigation-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: $bg_light;

    .current-month-display {
      flex: 1;
      text-align: center;

      .month-title {
        margin: 0;
        font-size: 0.875rem;
        font-weight: 600;
        color: $text_black_color;
      }

      .month-title-btn {
        background: none;
        border: none;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        padding: 0.5rem 0.75rem;
        border-radius: 0.25rem;
        transition: all 0.15s ease-in-out;
        margin: 0 auto;

        &:hover:not(:disabled) {
          background-color: rgba(13, 110, 253, 0.1);
        }

        &:focus {
          outline: 0;
          box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        &:disabled {
          cursor: not-allowed;
          opacity: 0.65;
        }

        .month-dropdown-icon {
          font-size: 0.75rem;
          color: $gray_color;
        }
      }

      .select-month-btn {
        font-size: 0.75rem;
        padding: 0.25rem 0.75rem;
      }
    }

    .nav-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 2rem;
      height: 2rem;
      border: 1px solid $stock_light;
      border-radius: 0.25rem;
      background-color: $white_color;
      color: $gray_color;
      cursor: pointer;
      transition: all 0.15s ease-in-out;
      font-size: 0.875rem;

      &:hover:not(:disabled) {
        background-color: rgba($primary_color, 0.1); // Light primary color
        border-color: rgba($primary_color, 0.3); // Primary border
        color: $primary_color; // Primary color text
      }

      &:focus {
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgba($info_color, 0.25);
        border-color: $info_color;
      }

      &:disabled {
        opacity: 0.65;
        cursor: not-allowed;
        background-color: $bg_light;
      }
    }
  }





  // Month Selection Title
  .month-selection-title {
    text-align: center;
    margin-bottom: 1rem; // Slightly more space
    padding-bottom: 0.75rem; // More padding for better proportion
    border-bottom: 1px solid $stock_light;

    .selection-title {
      margin: 0;
      font-size: 0.875rem;
      font-weight: 600;
      color: $text_black_color;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
  }
  .picker-grid {
    display: grid;
    gap: 0.125rem;
    justify-content: center;

    &.months-grid {
      grid-template-columns: repeat(3, 1fr);
      max-width: 240px; // Increased width to reduce side gaps
      margin: 0 auto;
      gap: 0.5rem; // Slightly larger gap for better spacing
      align-content: center; // Center align for better visual balance
    }

    &.days-grid {
      grid-template-columns: repeat(7, 1fr);
      max-width: 240px;
      margin: 0 auto;
    }
  }

  .picker-item {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 2rem;
    min-width: 2rem;
    padding: 0.375rem;
    border: 1px solid transparent;
    border-radius: 0.375rem;
    background-color: $white_color;
    color: $text_black_color;
    cursor: pointer;
    transition: all 0.15s ease-in-out;
    font-weight: 400;
    text-align: center;
    user-select: none;
    position: relative;
    font-size: 0.8rem;

    &:hover:not(.disabled) {
      background-color: rgba($primary_color, 0.1);
      border-color: $primary_color;
      color: $primary_color;
      transform: scale(1.05);
    }

    &:focus {
      outline: 0;
      box-shadow: 0 0 0 0.2rem rgba($info_color, 0.25);
      border-color: $info_color;
    }

    &.selected {
      background-color: $primary_color;
      border-color: $primary_color;
      color: $white_color;
      font-weight: 600;

      &:hover {
        background-color: $primary_hover_color;
        border-color: $primary_hover_color;
      }
    }



    &.today {
      position: relative;
      font-weight: 600;

      &:not(.selected) {
        background-color: $warning_light_color;
        border-color: $warning_bg_color;
        color: $warning_color;
      }

      &::after {
        content: '';
        position: absolute;
        bottom: 2px;
        left: 50%;
        transform: translateX(-50%);
        width: 4px;
        height: 4px;
        background-color: currentColor;
        border-radius: 50%;
      }

      &.selected::after {
        background-color: $white_color;
      }
    }

    &.disabled {
      background-color: $bg_light;
      color: $gray_color;
      cursor: not-allowed;
      opacity: 0.65;

      &:hover {
        background-color: $bg_light;
        border-color: transparent;
      }
    }
  }

  .month-item {
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    font-size: 0.9rem; // Better font size for optimal design
    min-height: 2.75rem; // Better proportion for month selection
    min-width: 3rem; // Ensure adequate width
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;

    // Clean design for month selection in date-month picker
    &:not(.range-start):not(.range-end):not(.in-range):not(.hover-range-start):not(.hover-range-end):not(.in-hover-range) {
      background-color: $white_color;
      border: 1px solid $stock_light;

      &:hover:not(.disabled) {
        background-color: rgba($primary_color, 0.15) !important; // Consistent light primary color
        border-color: rgba($primary_color, 0.3) !important; // Consistent border highlight
        color: $primary_color !important; // Primary color text
        transform: translateY(-1px); // Subtle lift effect like other items
      }

      &.selected {
        background-color: $primary_color;
        border-color: $primary_color;
        color: $white_color;

        &:hover {
          background-color: $primary_hover_color !important;
          border-color: $primary_hover_color !important;
          transform: none; // No lift effect for selected items
        }
      }

      &.today:not(.selected) {
        background-color: $warning_light_color;
        border-color: $warning_bg_color;
        color: $warning_color;
        font-weight: 600;

        &:hover {
          background-color: rgba($primary_color, 0.15) !important; // Override with primary on hover
          border-color: rgba($primary_color, 0.3) !important;
          color: $primary_color !important;
          transform: translateY(-1px);
        }
      }
    }

    // Range selection styles
    &.range-start {
      background-color: $primary_color;
      color: $white_color;
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
      position: relative;
      font-weight: 500; // Keep consistent font-weight

      &::after {
        content: '';
        position: absolute;
        top: 0;
        right: -1px;
        bottom: 0;
        width: 1px;
        background-color: $primary_color;
      }
    }

    &.range-end {
      background-color: $primary_color;
      color: $white_color;
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
      font-weight: 500; // Keep consistent font-weight
    }

    &.in-range:not(.range-start):not(.range-end) {
      background-color: rgba($primary_color, 0.2);
      color: $primary_color;
      border-radius: 0;
      border-color: rgba($primary_color, 0.3);
      font-weight: 500; // Keep consistent font-weight
    }

    &.selecting-range {
      cursor: pointer;

      &:hover:not(.disabled) {
        background-color: rgba(13, 110, 253, 0.1);
        border-color: rgba(13, 110, 253, 0.3);
      }
    }

    // Hover preview styles during range selection
    &.hover-range-start {
      background-color: rgba($primary_color, 0.7);
      color: $text_black_color;
      border: 1px solid $primary_color; // Keep same border width
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
      font-weight: 500;
      box-shadow: 0 0 0 1px $primary_color; // Add box-shadow for emphasis without changing size
    }

    &.hover-range-end {
      background-color: rgba($primary_color, 0.7);
      color: $text_black_color;
      border: 1px solid $primary_color; // Keep same border width
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
      font-weight: 500;
      box-shadow: 0 0 0 1px $primary_color; // Add box-shadow for emphasis without changing size
    }

    &.in-hover-range:not(.hover-range-start):not(.hover-range-end):not(.range-start):not(.range-end) {
      background-color: rgba($primary_color, 0.1);
      color: $text_black_color;
      border-radius: 0;
      border: 1px solid rgba($primary_color, 0.3); // Keep same border width
      font-weight: 500;
    }
  }

  .day-item {
    font-weight: 400;
    font-size: 0.8rem;
  }


}

// Responsive adjustments
@media (max-width: 576px) {
  .month-day-picker {
    padding: 0.5rem;
    min-width: 240px;
    max-width: 280px;

    .picker-grid {
      &.months-grid {
        grid-template-columns: repeat(3, 1fr);
        max-width: 200px;
      }

      &.days-grid {
        grid-template-columns: repeat(7, 1fr);
        max-width: 240px;
      }
    }

    .picker-item {
      min-height: 1.6rem;
      min-width: 1.6rem;
      padding: 0.2rem 0.3rem;
      font-size: 0.7rem;
    }
  }
}

// Accessibility improvements
@media (prefers-reduced-motion: reduce) {
  .month-day-picker .picker-item {
    transition: none;
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .month-day-picker {
    border-color: $black_color;

    .picker-item {
      border-color: $black_color;

      &.selected {
        background-color: $black_color;
        color: $white_color;
      }

      &.today:not(.selected) {
        background-color: $white_color;
        border-color: $black_color;
        color: $black_color;
      }
    }
  }
}