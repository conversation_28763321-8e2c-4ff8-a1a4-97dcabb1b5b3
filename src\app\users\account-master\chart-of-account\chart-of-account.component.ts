import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgLabelTemplateDirective, NgOptionTemplateDirective, NgSelectComponent } from '@ng-select/ng-select';
import { Deserialize, Serialize } from 'cerialize';
import { debounceTime, distinctUntilChanged, Subject } from 'rxjs';
import { AccountChart } from 'src/app/models/AccountChart';
import { activeInactiveStatus, CHART_OF_ACCOUNTS_MASTER_COLS } from 'src/app/shared/constants/constant';
import { UtilsService } from 'src/app/shared/services/utils.service';
declare var window: any;

@Component({
    selector: 'app-chart-of-account',
    templateUrl: './chart-of-account.component.html',
    styleUrls: ['./chart-of-account.component.css'],
})
export class ChartOfAccountComponent implements OnInit {
    demo = [
        { id: 1, name: 'Option 1' },
        { id: 2, name: 'Option 2' },
        { id: 3, name: 'Option 3' },
    ];

    selectedAccount: any;

    activeInactiveStatus = activeInactiveStatus;
    searchSubject: Subject<string> = new Subject<string>();
    selectedIds: any[] = [];
    enumForSortOrder = this.utilsService.enumForSortOrder;
    pageNo: number = 1;
    pageSize: string = '100';
    sortOrder: string;
    sortColumn: string;
    activeFlag: boolean = true;
    selectedAccountType: any;
    searchText: any;
    chartAcTH: any[] = [];
    accountChartList: AccountChart[] = [];
    accountChartObj = new AccountChart();
    flagForSelectAll: boolean = false;
    totalData: number;
    pagination: any;

    statusForModal: string = null;
    addEditModel: any;
    deleteModel: any;

    chartOfAccountForm: FormGroup;
    accountTypes: any[];
    pageAccountTypes: any[];
    filteredAccountTypes : any[];
    parentAccounts: any[];
    flattenedParentAccounts: any[];
    isDelivery: any;
    defaultAccountType : any;
    isExpandedIDs: any[] = [];
    isExpandAll : boolean;

    constructor(public utilsService: UtilsService, private fb: FormBuilder) {
        this.chartAcTH = CHART_OF_ACCOUNTS_MASTER_COLS;
    }

    ngOnInit() {
        this.searchSubject.pipe(debounceTime(300), distinctUntilChanged()).subscribe((res: string) => {
            this.pageNo = 1;
            this.searchText = res;
            this.getChartData();
        });
        this.getChartData();
        this.getFormGroup();
        this.chartOfAccountForm?.get('markAsSubAccount').valueChanges.subscribe(val => {
            this.accountChartObj.isSubAccount = val;
            if (this.accountChartObj.isSubAccount) {
                this.chartOfAccountForm.controls['parentAccount'].setValidators([Validators.required]);
            } else {
                this.chartOfAccountForm.controls['parentAccount'].clearValidators();
            }
            this.chartOfAccountForm.controls['parentAccount'].updateValueAndValidity();
        });
        this.addEditModel = new window.bootstrap.Modal(
            document.getElementById('chartOfAccountModal')
        );

        this.deleteModel = new window.bootstrap.Modal(
            document.getElementById('deleteModal')
        );

        document.getElementById('chartOfAccountModal').addEventListener('shown.bs.modal', () => {
            document.getElementById('name').focus();
        });
        this.getRequiredData();
    }

    getChartData() {
        this.selectedIds = []
        this.flagForSelectAll = false;
        this.totalData = 0;

        const param = {
            pageNo: this.pageNo,
            pageSize: this.pageSize,
            sortOrder: this.sortOrder,
            sortColumn: this.sortColumn,
            name: this.searchText,
            isActive: this.activeFlag,
            id: this.selectedAccountType
        }

        this.utilsService.postMethodAPI(false, this.utilsService.serverVariableService.CHART_OF_ACCOUNTS_LISTING, param, (response) => {
            this.accountChartList = [];
            if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
                this.accountChartList = Deserialize(response.content, AccountChart);
                (this.accountChartList || []).forEach(aChart => {
                    this.addClass(aChart);
                })
                this.totalData = response.totalElements;
                this.pagination = response;

                if (!this.utilsService.isNullUndefinedOrBlank(this.isExpandedIDs)) {
                    this.checkCollapseIDs(this.accountChartList);
                  }
            }
        })
    }

    addClass(accountChart: AccountChart) {
        if (accountChart.accountChartList?.length > 1) {
            accountChart['categoryClass'] = 'category-level-' + (accountChart.index + 1);
            accountChart.accountChartList.forEach(chart => {
                this.addClass(chart)
            })
        }
    }

    // status 
    onChangeStatus(item: AccountChart, value, index) {
        this.utilsService.putMethodAPI(true, this.utilsService.serverVariableService.CHART_OF_ACCOUNT_STATUS_CHANGE + `${item.id}/${value}`, {}, '', (response) => {
            if (!this.utilsService.isEmptyObjectOrNullUndefined(response.data)) {
                this.accountChartList[index].isActive = value
            } else {
                this.accountChartList[index].isActive = !value
            }
            if (!this.pagination?.first && this.pagination?.last && this.pagination?.numberOfElements === 1 && this.activeFlag) {
                this.pageNo = this.pageNo - 1
            }
            this.getChartData()
        }, true);
    }


    addPageSizeData(event) {
        this.pageNo = 1;
        this.pageSize = event;
        this.getChartData();
    }

    pageNumber(event) {
        this.pageNo = event
        this.getChartData();
    }

    trackBy(index: number, item: AccountChart): number {
        return item.id;
    }

    selectAll() {
        if (this.flagForSelectAll === true) {
            this.selectedIds = new Array<string>();
        }
        const obj = this.accountChartList.filter((val, index) => {
            if (this.flagForSelectAll === true) {
                val['isSelected'] = true;
                this.selectedIds.push(val.id);
            } else {
                val['isSelected'] = false;
                this.selectedIds.splice(index, 1);
            }
        });
        if (this.flagForSelectAll === false) {
            this.selectedIds = new Array<string>();
        }
    }

    selectUnselect(id: number, index, value) {
        const isSelected = this.selectedIds.includes(id);
        if (value && !isSelected) {
            this.selectedIds.push(id);
        } else if (!value && isSelected) {
            const assetIndex = this.selectedIds.indexOf(id);
            this.selectedIds.splice(assetIndex, 1);
        }
        this.flagForSelectAll = this.checkIfAllSelected();
    }

    checkIfAllSelected() {
        let flag = true;
        this.accountChartList.filter((val, index) => {
            if (val['isSelected'] === false) {
                flag = false;
                return;
            }
        });
        return flag;
    }

    // sorting 

    onSortTH(key) {
        if (this.utilsService.isEmptyObjectOrNullUndefined(this.accountChartList)) {
            return;
        }

        if (key === this.sortColumn) {
            if (this.sortOrder === this.enumForSortOrder.A) {
                this.sortOrder = this.enumForSortOrder.D;
            } else if (this.sortOrder === this.enumForSortOrder.D) {
                this.sortOrder = this.enumForSortOrder.A;
            }
        } else {
            this.sortOrder = this.enumForSortOrder.D;
        }

        this.sortColumn = key;
        this.getChartData();
    }

    getFormGroup() {
        this.chartOfAccountForm = this.fb.group({
            name: ['', Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.PATTERN_FOR_ALPHANUMERIC_WITH_SPECIAL_CHAR)])],
            code: ['', Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.PATTERN_FOR_ALPHANUMERIC_WITH_SPECIAL_CHAR)])],
            account_type: ['', Validators.compose([Validators.required])],
            description: [''],
            parentAccount: [''],
            status: [true],
            markAsSubAccount: [''],
        })
    }

    onSaveChartOfAccounts() {
        if (this.chartOfAccountForm.invalid) {
            this.chartOfAccountForm.markAllAsTouched();
            return;
        }

        let param = this.utilsService.trimObjectValues(Serialize(this.accountChartObj));
        this.utilsService.postMethodAPI(true, this.utilsService.serverVariableService.CHART_OF_ACCOUNT_SAVE_EDIT_DELETE, param, (response) => {
            if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
                this.addEditModel.hide();
                this.getChartData();
            }
        })
    }

    openAddEditModal(obj: AccountChart, status: string) {
      this.accountChartObj = new AccountChart();
      this.chartOfAccountForm.reset();
      this.statusForModal = status;
      this.getRequiredData();
  
      if (this.statusForModal === 'Add') {
        setTimeout(() => {
            this.accountChartObj.isActive = true;
            this.filteredAccountTypes = this.utilsService.filterIsActive(this.accountTypes, null);
            this.accountChartObj.accountTypeId = this.defaultAccountType?.id;
            this.getParentAccountData();
        }, 200);
      }
  
      if (obj) {
        setTimeout(() => {
            this.accountChartObj = Serialize(obj);
            if(obj.parentAccountId) {
                this.accountChartObj.isSubAccount = true
            }
            this.getParentAccountData(obj);
            this.filteredAccountTypes = this.utilsService.filterIsActive(this.accountTypes, this.accountChartObj.accountTypeId);
        }, 200);
      }
      this.addEditModel.show();
    }

    openDeleteModel(obj: AccountChart) {
        this.accountChartObj = Serialize(obj)
        this.deleteModel.show();
    }

    onChangeActive() {
        this.pageNo = 1;
        // this.pageSize = '100';
        this.getChartData();
    }

    //Search
    onSearch(event: any) {
        this.searchSubject.next(event.target.value);
    }

    //required data
    getRequiredData() {
      this.accountTypes = [];
      this.utilsService.getMethodAPI(false, this.utilsService.serverVariableService.CHART_OF_ACCOUNT_REQUIRED_DATA, null, (response) => {
        if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
          this.accountTypes = this.utilsService.transformDropdownItems(response['Account_type']);
          this.defaultAccountType = (this.accountTypes || []).find(a => a.markAsDefault);
          this.pageAccountTypes = response['Account_type']
          this.accountTypes.forEach(type => {
            type['groupValue'] = type['accountGroup'] ? type['accountGroup'].label : '';
          })
        }
      })
    }

    modifyParentResponse(accounts: any[], editObj?, index?) {
        let flatList = [];
        for (let parent of (accounts || [])) {
            parent.className = "ng-select-option-" + (parent.index + 1);
            if (editObj && (parent.id == editObj.id)) {
                editObj = null;
                continue;
            } else if (index && parent.index > index) {
                continue;
            
            }
            // parent.disabled = index && parent.index > index;
            flatList.push(parent);
            if (parent.accountChartList && parent.accountChartList.length > 0) {
                let data = (parent.accountChartList || []).filter(p => p.index < 3);
                for (let d of data) {
                    d.className = "ng-select-option-" + (d.index + 1);
                    d.isChild = true;
                }
                flatList = flatList.concat(this.modifyParentResponse(data, editObj, index));
            }
        }
        return flatList;
    }

    deleteObj() {
        this.utilsService.deleteMethodAPI(true, this.utilsService.serverVariableService.CHART_OF_ACCOUNT_SAVE_EDIT_DELETE + `?accountChartId=${this.accountChartObj.id}`, {}, (response) => {
            if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
                this.deleteModel.hide();
                if (!this.pagination?.first && this.pagination?.last && this.pagination?.numberOfElements === 1) {
                    this.pageNo = this.pageNo - 1
                }
                this.getChartData();
            }
        })
    }

    getIndentation(index: number): string {
        return `${index * 25}px`; // Increase indentation with each level
    }

    onChangeAccountType() {
        this.accountChartObj.isSubAccount = false;
        this.accountChartObj.parentAccountId = null;
        this.getParentAccountData();
    }

    getParentAccountData(editObj?) {
        let data = (this.accountChartList || []).filter(list => list.accountTypeId == this.accountChartObj.accountTypeId && list.index == 0);
        if (editObj) {
            data = (data || []).filter(list => list.id != editObj.id)
        }
        this.flattenedParentAccounts = this.modifyParentResponse(data, editObj, editObj?.index);
    }

    onCollapse(parentAccount : AccountChart) {
        parentAccount.isExpand = !parentAccount.isExpand;
    
        this.collapseIDSave(this.accountChartList)
    
        if (parentAccount.accountChartList) {
          this.collapseAll(parentAccount.accountChartList);
        }
      }
    
    collapseIDSave(accountChartList: AccountChart[]) {
        for (const chart of accountChartList) {
            if (chart.isExpand) {
                if (!this.isExpandedIDs.includes(chart.id)) {
                    this.isExpandedIDs.push(chart.id);
                }
            } else {
                const index = this.isExpandedIDs.indexOf(chart.id);
                if (index !== -1) {
                    this.isExpandedIDs.splice(index, 1);
                }
            }
            if (chart.accountChartList && chart.accountChartList.length > 0) {
                this.collapseIDSave(chart.accountChartList);
            }
        }
    }
    
    collapseAll(accountChartList: AccountChart[]) {
        accountChartList.forEach(chart => {
            chart.isExpand = false;
            if (chart.accountChartList) {
                this.collapseAll(chart.accountChartList);
            }
        });
    }

    checkCollapseIDs(accountChartList: AccountChart[]) {
        for (const chart of accountChartList) {
            chart.isExpand = this.isExpandedIDs.includes(chart.id);
            if (chart.accountChartList && chart.accountChartList.length > 0) {
                this.checkCollapseIDs(chart.accountChartList);
            }
        }
    }

    onExpandAll() {
        this.isExpandAll = !this.isExpandAll;
        this.accountChartList.forEach(chart => {
            this.onExpandAndCollapseAll(chart);
        })
    }

    onExpandAndCollapseAll(chart : AccountChart) {
        chart.isExpand = !this.isExpandAll;
        this.onCollapse(chart);
        (chart.accountChartList || []).forEach(ele => {
            this.onExpandAndCollapseAll(ele);
        });
    }
}
