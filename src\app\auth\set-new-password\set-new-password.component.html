<div class="authentication">
  <div class="authentication-wrapper authentication-wrapper2">
    <div class="authentication-inner-wrapper">

      <div class="authentication-left">

        <div class="authentication-image">
          <img src="assets/images/Set-New-Password.svg" alt="valamji">
        </div>
      </div>
      <div class="authentication-right">


        <div class="auth-logo">
          <img src="assets/images/auth-logo.png" alt="valamji">
        </div>
        <div class="auth-card">
          <div class="auth-card-icon">
            <i class="th th-outline-key"></i>
          </div>
          <div class="auth-card-title">
            <h2>Set New Password</h2>
          </div>

          <div class="auth-form" [formGroup]="passwordForm">
            <div class="form-group">
              <div class="form-label">New Password</div>
              <div class="form-group-icon-start form-group-password">
                <i class="th th-outline-lock"></i>
                <input (keyup.enter)="changePassword()" formControlName="password"
                  [type]="flagForPasswordHideShow ? 'password' : 'text'" class="form-control"
                  placeholder="Enter New Password">
                <button (click)="flagForPasswordHideShow = !flagForPasswordHideShow" class="btn-password">
                  <i class="th th th-outline-eye"
                    [ngClass]="{'th th-outline-eye': flagForPasswordHideShow === false, 'th th-outline-eye-slash': flagForPasswordHideShow}"></i>
                </button>
              </div>
              <div class="message error-message"
                *ngIf="passwordForm.controls['password'].hasError('required') && passwordForm.controls['password'].touched">
                {{utilsService.validationService.PASSWORD_REQUIRED}}
              </div>
              <div class="message error-message"
                *ngIf="passwordForm.controls['password'].hasError('pattern') && passwordForm.controls['password'].touched">
                {{ utilsService.validationService.PASSWORD_INVALID }}
              </div>
            </div>
            <div class="form-group">
              <div class="form-label">Confirm Password</div>
              <div class="form-group-icon-start form-group-password">
                <i class="th th-outline-lock"></i>
                <input (keyup.enter)="changePassword()" formControlName="confirm_password"
                  [type]="flagForCPasswordHideShow? 'password' : 'text'" class="form-control"
                  placeholder="Confirm Password">
                <button (click)="flagForCPasswordHideShow = !flagForCPasswordHideShow" class="btn-password">
                  <i class="th th th-outline-eye"
                    [ngClass]="{'th th-outline-eye': flagForCPasswordHideShow === false, 'th th-outline-eye-slash': flagForCPasswordHideShow}"></i>
                </button>
              </div>
              <div class="message error-message"
                *ngIf="passwordForm.controls['confirm_password'].hasError('required') && passwordForm.controls['confirm_password'].touched">
                {{utilsService.validationService.CONFIRM_PASSWORD_REQUIRED}}
              </div>
              <div class="message error-message"
                *ngIf="passwordForm.hasError('confirmed_check') && passwordForm.controls['password'].touched && !passwordForm.controls['confirm_password'].hasError('required')">
                {{utilsService.validationService.CONFIRM_PASSWORD_NOT_MATCHED}}
              </div>
            </div>
          </div>

          <div class="auth-btn-group">
            <button (click)="changePassword()" class="btn btn-primary">Reset Password</button>
          </div>

          <div class="auth-form-link">
            <p>
              Don’t want to change? <a [routerLink]="['/auth/login']">Back to login</a>
            </p>

          </div>


        </div>
      </div>
    </div>

    <div class="authentication-footer">
      <p>{{utilsService.version}}</p>
    </div>
  </div>

</div>