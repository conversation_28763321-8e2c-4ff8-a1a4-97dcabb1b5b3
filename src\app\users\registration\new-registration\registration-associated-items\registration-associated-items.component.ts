import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormArray, FormControl, FormGroup } from '@angular/forms';
import { Registration } from 'src/app/models/Registration';
import { RegistrationAI } from 'src/app/models/RegistrationAssociateItem';
import { RegDropdown } from 'src/app/shared/constants/interface';
import { UtilsService } from 'src/app/shared/services/utils.service';

@Component({
  selector: 'app-registration-associated-items',
  templateUrl: './registration-associated-items.component.html',
  styleUrls: ['./registration-associated-items.component.css']
})
export class RegistrationAssociatedItemsComponent implements OnInit {

  @Input({ alias: 'associatedItems', required: true }) associatedItems: RegistrationAI[];
  @Input({ alias: 'regForm', required: true }) regForm: FormGroup;
  @Input({ alias: 'dropdown', required: true }) dropdown: RegDropdown;
  @Input({ alias: 'flagForSelectAllItems', required: true }) flagForSelectAllItems: boolean;
  @Input({ alias: 'selectedItems', required: true }) selectedItems: any[];
  @Input({ alias: 'regId', required: true }) regId: number;
  @Input({ alias: 'regObj', required: true }) regObj: Registration;

  @Output() openRemoveAIModal: EventEmitter<any> = new EventEmitter<any>();
  @Output() addAssociateItems: EventEmitter<any> = new EventEmitter<any>();
  @Output() onAddImageToItem: EventEmitter<any> = new EventEmitter<any>();
  @Output() openRemoveImageItemModal: EventEmitter<any> = new EventEmitter<any>();
  @Output() generateMeasurementCode: EventEmitter<any> = new EventEmitter<any>();

  selectedIndex: number;

  constructor(public utilsService: UtilsService) { }

  ngOnInit() {
  }

  getSupplierType = () => {
    return this.regForm.get('supplierType').value
  }

  get isActive(): boolean {
    return this.regForm.get('isActive').value as boolean
  }

  get items() {
    return (this.regForm.get('items') as FormArray);
  }

  openRemoveModal(index: number) {
    this.openRemoveAIModal.emit(index)
  }

  customSearchFn(term: string, item: any) {
    term = term.toLocaleLowerCase();
    return item.skuId.toLocaleLowerCase().indexOf(term) > -1 ||
      item.displayName.toLocaleLowerCase().indexOf(term) > -1 ||
      (item.skuId + " - " + item.displayName).toLocaleLowerCase().indexOf(term) > -1;
  }

  onSelectImages(event, index: number) {
    this.onAddImageToItem.emit({event: event, index: index})
  }

  //associate item
  setValueToAssociateItem(item: any, index: number) {
    const obj = this.associatedItems[index].itemDropdown.find(v => v.id === item.itemId);
    this.associatedItems[index].hsnCode = obj ? obj.hsnCode : null;
    // this.associatedItems[index].supplierSku = obj ? obj.skuId : null;

    let selectedItem = this.associatedItems[index].itemDropdown.find(item => item.id == this.associatedItems[index].itemId);
    if (selectedItem) {
      // this.associatedItems[index].cartonHeight = selectedItem.itemHeight;
      // this.associatedItems[index].cartonWidth = selectedItem.itemWidth;
      this.associatedItems[index].pricePerCarton = selectedItem?.itemCarton;
      this.associatedItems[index].pricePerItem = selectedItem?.itemPrice;
      // this.associatedItems[index].unitId = (this.associatedItems[index].unitDropdown || []).find(unit => (unit.shortCode)?.toLowerCase() == ('CM')?.toLowerCase())?.id;
      // this.associatedItems[index].cartonWeightDim = (this.associatedItems[index].unitDropdown || []).find(unit => (unit.shortCode)?.toLowerCase() == ('KG')?.toLowerCase())?.id;
      // this.associatedItems[index].cartonWeight = selectedItem.itemsWeight;
      // this.associatedItems[index].cartonWeightDim = (this.associatedItems[index].cartownWdropdown || []).find(unit => unit.id == selectedItem.itemWeightDim)?.id;
      this.associatedItems[index].colorDropdown = (selectedItem?.color || []);
      this.onChangeTagQty(this.associatedItems[index], index);
      this.calculateCBM(this.associatedItems[index]);
    } else {
      this.associatedItems[index].pricePerCarton = null;
      this.associatedItems[index].pricePerItem = null;
      this.associatedItems[index].unitId = null;
      this.associatedItems[index].cartonWeightDim = null;
      this.associatedItems[index].colorDropdown = null;
      this.associatedItems[index].measurementCode = null;
    }
  }

  // remove img item
  openImgItem(index: number, childIndex: number) {
    this.selectedIndex = index;
    this.openRemoveImageItemModal.emit({index: index, childIndex: childIndex})
  }

  onChangeActiveDeactive(item: RegistrationAI, index: number) {
    if (!item.isActive) {
      this.associatedItems[index].isSelected = false;
      let deleteIndex = this.selectedItems.findIndex(a => a == item.id);
      this.selectedItems.splice(deleteIndex, 1);
    }
  }

  // Select Deselect 
  selectAll() {
    if (this.flagForSelectAllItems === true) {
      this.selectedItems = []
    }
    const obj = this.associatedItems.filter((val, index) => {
      if (this.flagForSelectAllItems === true && val.isActive) {
        val['isSelected'] = true;
        this.selectedItems.push(val.id);
      } else {
        val['isSelected'] = false;
        this.selectedItems.splice(index, 1);
      }
    });
    if (this.flagForSelectAllItems === false) {
      this.selectedItems = []
    }
  }

  selectUnselect(id: number, index, value) {

    const isSelected = this.selectedItems.includes(id);

    if (value && !isSelected) {

      this.selectedItems.push(id);

    } else if (!value && isSelected) {

      const assetIndex = this.selectedItems.indexOf(id);
      this.selectedItems.splice(assetIndex, 1);
    }
    this.flagForSelectAllItems = this.checkIfAllSelected();
  }

  checkIfAllSelected() {
    let flag = true;
    this.associatedItems.filter((val, index) => {
      if (val['isSelected'] === false) {
        flag = false;
        return;
      }
    });
    return flag;
  }

  checkIfSomeDisabled() {
    let flag = true;
    if (this.associatedItems.some(a => !a.isActive)) {
      flag = false;
    }
    return flag;
  }

  calculateCBM(associateItem : RegistrationAI) {
    if (associateItem.cartonLength && associateItem.cartonHeight && associateItem.cartonWidth && associateItem.unitId) {
      let unit = associateItem.unitDropdown.find(u => u.id == associateItem.unitId);
      let conversion = Math.pow(unit.conversionToMeter, 3);
      associateItem.cbm = (associateItem.cartonLength * associateItem.cartonHeight * associateItem.cartonWidth) / conversion;
    }
    else {
      associateItem.cbm = 0
    }
  }

  createPO = () => {

    const param = {
      regId: this.regId,
      items: (this.selectedItems || []).filter(a => a !== null || a !== undefined) || []
    }

    localStorage.setItem('registration', JSON.stringify(param))
    this.utilsService.redirectTo('/users/purchases/po-import/new-po-import/')
  }

  preventRemovalColor(event, item: RegistrationAI) {
    return false
  }

  onChangeTagQty(item: RegistrationAI, index: number) {
    this.generateMeasurementCode.emit({item: item, index: index})
  }
}
