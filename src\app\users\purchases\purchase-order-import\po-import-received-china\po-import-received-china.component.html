<div class="page-filters">
    <div class="page-filters-left">
        <div class="form-group form-group-sm filter-search">
            <div class="form-group-icon-start">
                <i class="th th-outline-search-normal-1 icon-broder "></i>
                <input (change)="onSearch($event)" [(ngModel)]="paginationRequest.purchaseOrderSearch" type="text"
                    class="form-control" placeholder="Search by ID">
            </div>
        </div>
        <div class="form-group form-group-sm filter-search">
            <div class="form-group-icon-start">
                <i class="th th-outline-search-normal-1 icon-broder "></i>
                <input (change)="onSearchPOItemComments($event)" [(ngModel)]="paginationRequest.searchPurchaseComments"
                    type="text" class="form-control" placeholder="Search by SKU">
            </div>
        </div>
        <div class="form-group form-group-sm date-range-filter">
            <div class="form-group-icon-end">
                <i (click)="open()" class="th th-outline-calendar ngx-daterangepicker-action"></i>
                <input pickerDirective class="form-control" readonly ngxDaterangepickerMd [formControl]="paginationRequest.dateRangeControl"
                    [showCustomRangeLabel]="true" [alwaysShowCalendars]="true" [ranges]="utilsService.ranges"
                    [linkedCalendars]="false" [showClearButton]="false" placeholder="RC Date"
                    [autoApply]="true" [showRangeLabelOnInput]="true" startKey="start" endKey="end"
                    [closeOnAutoApply]="true">
            </div>
        </div>
        <div class="form-group theme-ngselect form-group-sm w-25">
            <ng-select (change)="onChangeActive()" placeholder="Supplier" [multiple]="false" [clearable]="true"
                [items]="dropdown?.supplierDropdown" bindLabel="displayName" bindValue="id"
                [(ngModel)]="paginationRequest.supplierId">
            </ng-select>
        </div>
        <!-- <div class="form-group theme-ngselect form-group-sm w-25">
            <ng-select placeholder="Payment Status" [multiple]="false" [clearable]="false" [items]="demo"
                bindLabel="name" bindValue="id">
            </ng-select>
        </div> -->
        <button (click)="onClear.emit()" class="btn btn-link btn-sm">Clear</button>
    </div>
    <div class="page-filters-right">
        <!-- <app-table-column-filter-dropdown-new [allHeaderArr]="allHeaderArr" [columnArr]="columnArr"
            (saveCol)="saveCol.emit()" (checkIfAllSelected)="checkIfAllSelected.emit()" /> -->
    </div>
</div>

<div class="card card-theme card-table-sticky3">
    <div class="card-body p-0">
        <div class="table-responsive  ">
            <table class="table-theme  table table-bordered tbl-collapse">
                <thead class="border-less">
                    <tr class="">
                        <th>
                            # Shop No
                        </th>
                        <th>Supplier Name</th>
                        <th>Mobile No</th>
                        <th>Total Amount (RMB)</th>
                        <th class="text-end">Action</th>
                    </tr>
                </thead>


                <tbody>
                    <ng-container *ngFor="let item of poImportList; index as i; trackBy: trackBy">
                        <tr [ngClass]="{'tbl-bg-secondary': item.isExpand}" (click)="toggleExpand(i)">
                            <td class=" tbl-user">
                                <div class="tbl-user-checkbox-srno">
                                    <span>{{(i + 1) | padNum}}.</span>
                                    <b class="text-black">
                                        {{item.supplierShortCode}}
                                    </b>
                                </div>
                            </td>
                            <td>{{item.supplierName}}</td>
                            <td>{{item.countryExtension}} {{item.phone}}</td>
                            <td>{{item.supplierAmount ? (item.supplierAmount | indianCurrency) : 0}}</td>
                            <td class="tbl-action" (click)="$event.stopPropagation()">
                                <div class="tbl-action-group justify-content-end">
                                    <button [pageAccess]="{page: utilsService.enumForPage.PO, action: utilsService.enumForPage.MOVE_TO_LOADED}"
                                        class="btn btn-xs btn-light-primary btn-icon" (click)="openRcToLoaded(item, null, false)"
                                        ngbTooltip="Move to Loaded" placement="left" container="body" triggers="hover">
                                        <i class="th-outline-arrow-right-1"></i>
                                    </button>
                                    <!-- <div class="dropdown" *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.EDIT_RC_CHINA])">
                                        <button class="btn btn-xs btn-light-white btn-icon" id="actionDropDown"
                                            data-bs-toggle="dropdown" aria-expanded="false"
                                            data-bs-popper-config='{"strategy":"fixed"}' ngbTooltip="More Option"
                                            placement="bottom" container="body" triggers="hover">
                                            <i class="th th-outline-more"></i>
                                        </button>
                                        <ul class="dropdown-menu" aria-labelledby="actionDropDown">
                                            <li [pageAccess]="{page: utilsService.enumForPage.PO, action: utilsService.enumForPage.EDIT_RC_CHINA}">
                                                <a (click)="openPoToChina(item, null, true)" class="dropdown-item"><i class="th th-outline-edit"></i>
                                                    Edit
                                                </a>
                                            </li>
                                        </ul>
                                    </div> -->
                                    <button (click)="toggleExpand(i)" class="btn btn-xs text-color btn-icon btn-link"
                                        data-bs-toggle="collapse" [ngClass]="{'collapse-arrow': item.isExpand}"
                                        role="button" aria-expanded="false" [attr.data.target]="'#table-collapse-2'+ i"
                                        [attr.aria-controls]="'table-collapse-2'+ i">
                                        <i class="th th-outline-arrow-right-3"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>

                        <tr *ngIf="item.isExpand" class="collapse" [ngClass]="{'show': item.isExpand}"
                            [id]="'table-collapse-2' + i">
                            <td colspan="30" class=" tbl-collapse-child tbl-collapse-child-responsive">

                                <table class="table-theme  table table-bordered">
                                    <thead class="border-less">
                                        <tr>
                                            <th> # Received Id </th>
                                            <th>RC Date</th>
                                            <th>Received Cartons</th>
                                            <th>Total Amount (RMB)</th>
                                            <!-- <th>Order Note</th> -->
                                            <th class="text-end">Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <ng-container
                                            *ngFor="let subItem of item.poImportReceivedCh; index as j; trackBy: trackBySubItem">
                                            <tr class="tbl-bg-gray" (click)="toggleExpandChild(i, j)">
                                                <td class=" tbl-user ">
                                                    <div class="tbl-user-checkbox-srno">
                                                        <span>{{(j + 1) | padNum}}.</span>
                                                        <b class="text-black">
                                                            {{subItem.rcId}}
                                                        </b>
                                                    </div>
                                                </td>
                                                <td class="">{{subItem.rcDate ? (subItem.rcDate | date: 'dd/MM/yyyy h:mm a') :
                                                    '-'}}</td>
                                                <td class="">{{subItem.receivedCarton ? subItem.receivedCarton : '-'}}</td>
                                                <td class="">{{subItem.totalAmount ? (subItem.totalAmount | indianCurrency) : 0}}</td>
                                                <!-- <td class="tbl-description">{{subItem.notes ? subItem.notes : '-'}}</td> -->
                                                <td class="tbl-action " (click)="$event.stopPropagation()">
                                                    <div class="tbl-action-group justify-content-end">
                                                        <button (click)="onPacking(subItem.id)" class="btn btn-outline-primary btn-icon-text btn-sm"> <i
                                                                class="bi bi-download "></i>
                                                            Download Packing </button>
                                                        <button [pageAccess]="{page: utilsService.enumForPage.PO, action: utilsService.enumForPage.MOVE_TO_LOADED}"
                                                            (click)="openRcToLoaded(item, subItem, false)" class="btn btn-xs btn-light-primary btn-icon"
                                                            ngbTooltip="Move to Loaded" placement="bottom" container="body" triggers="hover">
                                                            <i class="th-outline-arrow-right-1"></i>
                                                        </button>
                                                        <div class="dropdown">
                                                            <button class="btn btn-xs btn-light-white btn-icon"
                                                                id="actionDropDown" data-bs-toggle="dropdown"
                                                                aria-expanded="false"
                                                                data-bs-popper-config='{"strategy":"fixed"}'
                                                                ngbTooltip="More Option" placement="bottom"
                                                                container="body" triggers="hover">
                                                                <i class="th th-outline-more"></i>
                                                            </button>
                                                            <ul class="dropdown-menu" aria-labelledby="actionDropDown">
                                                                <li [pageAccess]="{page: utilsService.enumForPage.PO, action: utilsService.enumForPage.EDIT_RC_CHINA}">
                                                                    <a (click)="openPoToChina(item, subItem, true)" class="dropdown-item"><i class="th th-outline-edit"></i>
                                                                        Edit
                                                                    </a>
                                                                </li>
                                                                <!-- <li>
                                                                    <a class="dropdown-item">
                                                                        <i class="th th-outline-printer"></i>Print QR
                                                                        Code</a>
                                                                </li> -->
                                                                <li>
                                                                    <a class="dropdown-item">
                                                                        <i class="th th-outline-dollar-circle"></i>Add
                                                                        Payment</a>
                                                                </li>
                                                                <li>
                                                                    <hr class="dropdown-divider">
                                                                </li>
                                                                <li [pageAccess]="{page: utilsService.enumForPage.PO, action: utilsService.enumForPage.DEL_RC_CHINA}">
                                                                    <a (click)="openDeleteModal(subItem)" class="dropdown-item text-danger"><i
                                                                            class="th th-outline-trash"></i>Delete</a>
                                                                </li>
                                                            </ul>

                                                        </div>
                                                        <button (click)="toggleExpandChild(i, j)"
                                                            class="btn btn-xs text-color btn-icon btn-link"
                                                            data-bs-toggle="collapse"
                                                            [ngClass]="{'collapse-arrow': subItem.isExpand}"
                                                            role="button" aria-expanded="false"
                                                            [attr.data.target]="'#table-collapse-2'+ i + j"
                                                            [attr.aria-controls]="'table-collapse-2'+ i + j">
                                                            <i class="th th-outline-arrow-right-3"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>

                                            <tr *ngIf="subItem.isExpand" class="collapse"
                                                [ngClass]="{'show': subItem.isExpand}"
                                                [id]="'table-collapse-2' + i + j">
                                                <td colspan="30" class=" tbl-collapse-child tbl-collapse-child-responsive">

                                                    <div class="table-responsive">
                                                        <table
                                                            class="table-theme  table table-bordered table-sticky">
                                                            <thead class="border-less">
                                                                <!-- <tr class="tbl-bg-light-three">
                                                                    <ng-container
                                                                        *ngFor="let th of headerObj?.optionsArray; index as i">
                                                                        <th *ngIf="th.show" [class]="th.class">{{th.header}}
                                                                        </th>
                                                                    </ng-container>
                                                                </tr> -->
                                                                <tr>
                                                                    <ng-container
                                                                        *ngFor="let th of headerObj?.optionsArray; index as k">
                                                                        <th *ngIf="th.show" [class]="th.class"
                                                                            [innerHTML]="th.displayName"></th>
                                                                    </ng-container>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                <tr [ngClass]="{'tbl-bg-danger': child.purchaseRationFlag}"
                                                                    *ngFor="let child of subItem.poImportItemList; index as l; trackBy: trackByChild">
                                                                    <ng-container
                                                                        *ngFor="let column of headerObj.columnArr">
                                                                        <td class="tbl-user" *ngIf="column.show">
                                                                            <ng-container [ngSwitch]="column.key">

                                                                                <ng-container *ngSwitchCase="0">
                                                                                    <div class="tbl-user-checkbox-srno">
                                                                                        <span>{{(l + 1) | padNum}}.</span>
                                                                                        <div class="tbl-user-wrapper">
                                                                                            <div class="tbl-user-image"
                                                                                                *ngIf="child?.item">
                                                                                                <img *ngIf="child.item?.formattedName"
                                                                                                    loading="lazy"
                                                                                                    [src]="child.item.formattedName ? (utilsService.imgPath + child.item.formattedName) : ''"
                                                                                                    alt="valamji">
                                                                                                <ng-container
                                                                                                    *ngIf="!child.item?.formattedName">{{
                                                                                                    child.displayName?.charAt(0).toUpperCase()
                                                                                                    }}
                                                                                                </ng-container>
                                                                                            </div>
                                                                                            <div
                                                                                                class="tbl-user-text-action">
                                                                                                <div
                                                                                                    class="tbl-user-text">
                                                                                                    <p>{{child.item?.skuId}}
                                                                                                    </p>
                                                                                                    <span class="tbl-description">{{child.item.displayName}}</span>
                                                                                                </div>
                                                                                            </div>
                                                                                            <div class="dropdown" *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.VIEW_ITEM, this.utilsService.enumForPage.DEL_PO_ITEM])">
                                                                                                <button
                                                                                                    class="btn btn-xs btn-light-white btn-icon"
                                                                                                    id="actionDropDown"
                                                                                                    data-bs-toggle="dropdown"
                                                                                                    aria-expanded="false"
                                                                                                    data-bs-popper-config='{"strategy":"fixed"}'
                                                                                                    ngbTooltip="More Option"
                                                                                                    placement="bottom"
                                                                                                    container="body"
                                                                                                    triggers="hover">
                                                                                                    <i
                                                                                                        class="th th-outline-more"></i>
                                                                                                </button>
                                                                                                <ul class="dropdown-menu" aria-labelledby="actionDropDown">
                                                                                                    <li [pageAccess]="{page: utilsService.enumForPage.ITEM, action: utilsService.enumForPage.VIEW_ITEM}">
                                                                                                        <a class="dropdown-item" (click)="utilsService.openItemDetailsInNewTab(child.itemId)">
                                                                                                            <i class="th th-outline-eye"></i>View Item Details
                                                                                                        </a>
                                                                                                    </li>
                                                                                                    <li>
                                                                                                        <a class="dropdown-item" (click)="onStatusOpen(child, item, subItem)">
                                                                                                            <i class="th th-outline-status"></i>
                                                                                                            Change Status
                                                                                                        </a>
                                                                                                    </li>
                                                                                                    <ng-container>
                                                                                                        <li *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.VIEW_ITEM, this.utilsService.enumForPage.DEL_PO_ITEM])">
                                                                                                            <hr class="dropdown-divider">
                                                                                                        </li>
                                                                                                        <li [pageAccess]="{page: utilsService.enumForPage.PO, action: utilsService.enumForPage.DEL_PO_ITEM}">
                                                                                                            <a (click)="onItemDel(child, subItem)" class="dropdown-item text-danger">
                                                                                                                <i class="th th-outline-trash"></i>
                                                                                                                Delete
                                                                                                            </a>
                                                                                                        </li>
                                                                                                    </ng-container>
                                                                                                    <!-- <li><a class="dropdown-item" href="" [routerLink]="['/users/purchases/new-po-import-expenses']"><i
                                                                                                                class="th th-outline-add-circle"></i>Add
                                                                                                            Expense</a></li>
                                                                                                    <li><a class="dropdown-item" href="" [routerLink]="['/users/audit-tickets/list']"><i
                                                                                                                class="th th-outline-box-1"></i>Audit
                                                                                                            Ticket</a>
                                                                                                    </li>
                                                                                                    <li><a class="dropdown-item" href="" [routerLink]="['/users/purchases/new-payments']"><i
                                                                                                                class="th th-outline-dollar-circle"></i>Make
                                                                                                            Payment</a></li>
                                                                                                    <li><a class="dropdown-item" href=""><i class="th th-outline-tick-circle"></i>
                                                                                                            Mark As Received</a>
                                                                                                    </li>
                                                                                                    <li>
                                                                                                        <hr class="dropdown-divider">
                                                                                                    </li>
                                                                                                    <li><a class="dropdown-item text-danger" href=""><i class="th th-outline-trash"></i>Delete</a>
                                                                                                    </li> -->
                                                                                                </ul>

                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                </ng-container>

                                                                                <ng-container *ngSwitchCase="1">
                                                                                    <span>
                                                                                        {{child.PoNo ? child.PoNo :
                                                                                        '-'}}
                                                                                    </span>
                                                                                </ng-container>

                                                                                <ng-container *ngSwitchCase="2">
                                                                                    <span
                                                                                        class="d-flex flex-column align-items-start">
                                                                                        <div>{{child.marka ? child.marka
                                                                                            : '-'}}</div>
                                                                                        <div>
                                                                                            <ng-container>
                                                                                                {{child.cartonLength ?
                                                                                                child.cartonLength :
                                                                                                '-'}} X
                                                                                                {{child.cartonWidth ?
                                                                                                child.cartonWidth :
                                                                                                '-'}} X
                                                                                                {{child.cartonHeight ?
                                                                                                child.cartonHeight :
                                                                                                '-'}}
                                                                                                {{child?.cartonDimensionUnit ?
                                                                                                child?.cartonDimensionUnit?.shortCode :
                                                                                                ''}}
                                                                                            </ng-container>
                                                                                        </div>
                                                                                        <div>
                                                                                            <p>{{child.pricePerCarton ?
                                                                                                child.pricePerCarton :
                                                                                                0}}</p>
                                                                                        </div>
                                                                                        <div>
                                                                                            <p class="tbl-po-notes">{{child.chinaComment ?
                                                                                                child.chinaComment :
                                                                                                ''}}</p>
                                                                                        </div>
                                                                                    </span>
                                                                                </ng-container>

                                                                                <ng-container *ngSwitchCase="3">
                                                                                    <span class="w-100 d-block" *ngFor="let v of child.colorName">
                                                                                        {{!utilsService.isEmptyObjectOrNullUndefined(v) ? v : ''}}
                                                                                    </span>
                                                                                </ng-container>

                                                                                <ng-container *ngSwitchCase="4">
                                                                                    -
                                                                                </ng-container>

                                                                                <ng-container *ngSwitchCase="5">
                                                                                    <div>
                                                                                        {{child.poDate ?
                                                                                        (child.poDate |
                                                                                        date: 'dd/MM/YYY') : '-'}}
                                                                                    </div>
                                                                                </ng-container>

                                                                                <ng-container *ngSwitchCase="6">
                                                                                    <div>
                                                                                        {{child.expectedDeliveryDate ?
                                                                                        (child.expectedDeliveryDate |
                                                                                        date: 'dd/MM/YYY') : '-'}}
                                                                                    </div>
                                                                                </ng-container>

                                                                                <ng-container *ngSwitchCase="7">
                                                                                    <div>
                                                                                        {{child.poCarton ?
                                                                                        (child.poCarton) : 0}}
                                                                                    </div>
                                                                                </ng-container>

                                                                                <ng-container *ngSwitchCase="8">
                                                                                    <div>
                                                                                        <!-- {{child.rcCarton ? (child.rcCarton) : 0}} -->
                                                                                        {{child.pricePerCarton ?
                                                                                        (child.pricePerCarton) : 0}}
                                                                                    </div>
                                                                                </ng-container>
                                                                                
                                                                                <ng-container *ngSwitchCase="9">
                                                                                    <div>
                                                                                        <!-- {{child.getSumLoadedQty ? (child.getSumLoadedQty) : 0}} -->
                                                                                        {{(child.totalPcsQty) ?
                                                                                        (child.totalPcsQty) : 0}}
                                                                                    </div>
                                                                                </ng-container>

                                                                                <ng-container *ngSwitchCase="10">
                                                                                    <div>
                                                                                        <!-- {{child.pricePerCarton ?
                                                                                        (child.pricePerCarton) : 0}} -->
                                                                                         {{child.rcCarton ? (child.rcCarton) : 0}}
                                                                                    </div>
                                                                                </ng-container>

                                                                                <ng-container *ngSwitchCase="11">
                                                                                    <div>
                                                                                        <!-- {{(child.totalPcsQty) ?
                                                                                        (child.totalPcsQty) : 0}} -->
                                                                                        {{child.getSumLoadedQty ? (child.getSumLoadedQty) : 0}} 
                                                                                    </div>
                                                                                </ng-container>

                                                                                <ng-container *ngSwitchCase="12">
                                                                                    <div>
                                                                                        {{child.pendingLoaded ? (child.pendingLoaded) : 0}}
                                                                                    </div>
                                                                                </ng-container>

                                                                                <ng-container *ngSwitchCase="13">
                                                                                    <div>
                                                                                        {{child.totalPendingLoadedQty ?
                                                                                        (child.totalPendingLoadedQty) : 0}}
                                                                                    </div>
                                                                                </ng-container>

                                                                                <ng-container *ngSwitchCase="14">
                                                                                    <div>
                                                                                        {{child.pricePerItem ?
                                                                                        child.pricePerItem : 0}}
                                                                                    </div>
                                                                                </ng-container>

                                                                                <ng-container *ngSwitchCase="15">
                                                                                    <div>
                                                                                        {{child.Total_Price ? (child.Total_Price | indianCurrency) : 0}}
                                                                                    </div>
                                                                                </ng-container>

                                                                                <ng-container *ngSwitchCase="30">
                                                                                    <div>
                                                                                        {{child.totalReceivedAmount ? ( child.totalReceivedAmount | indianCurrency) : 0}}
                                                                                    </div>
                                                                                </ng-container>

                                                                                <ng-container *ngSwitchCase="'shippingTypes'">
                                                                                    {{child.shippingTypes ?
                                                                                    (child.shippingTypes.value) :
                                                                                    '-'}}
                                                                                </ng-container>

                                                                                <ng-container *ngSwitchCase="16">
                                                                                    {{child.cbmCarton ? child.cbmCarton : 0}}
                                                                                </ng-container>

                                                                                <ng-container *ngSwitchCase="17">
                                                                                    {{child.totalCbm ? child.totalCbm :
                                                                                    0}}
                                                                                </ng-container>

                                                                                <ng-container *ngSwitchCase="18">
                                                                                    {{child.cbmPrice ? (child.cbmPrice | indianCurrency) :
                                                                                    0}}
                                                                                </ng-container>

                                                                                <ng-container *ngSwitchCase="19">
                                                                                    {{child.totalCBMExpense ? (child.totalCBMExpense | indianCurrency) : 0}}
                                                                                </ng-container>

                                                                                <ng-container *ngSwitchCase="20">
                                                                                    {{child.shippingExpense ? (child.shippingExpense | indianCurrency) : 0}}
                                                                                </ng-container>

                                                                                <!-- <ng-container *ngSwitchCase="114">
                                                                                    <ng-container
                                                                                        *ngFor="let ls of child?.lastRecord">
                                                                                        {{ls?.supplierShortCode}},
                                                                                        {{ls?.pricePerCarton}},
                                                                                        {{ls?.poCarton}} <br />
                                                                                    </ng-container>
                                                                                </ng-container> -->

                                                                                <ng-container *ngSwitchCase="21">
                                                                                    -
                                                                                </ng-container>

                                                                                <ng-container *ngSwitchCase="22">
                                                                                    -
                                                                                </ng-container>

                                                                                <ng-container *ngSwitchCase="23">
                                                                                    {{child.gst_amount ? (child.gst_amount | indianCurrency) : 0}}
                                                                                </ng-container>

                                                                                <ng-container *ngSwitchCase="24">
                                                                                    {{child.gstPer ? (child.gstPer ): 0}}
                                                                                </ng-container>

                                                                                <ng-container *ngSwitchCase="25">
                                                                                    <div>
                                                                                        {{child.extraExpense ?
                                                                                        (child.extraExpense | indianCurrency) : 0}}
                                                                                    </div>
                                                                                </ng-container>

                                                                                <ng-container *ngSwitchCase="26">
                                                                                    {{child.purchaseRatio ? child.purchaseRatio : 0}}
                                                                                </ng-container>

                                                                                <ng-container>
                                                                                    <ng-container *ngIf="column.key =='cartonWeight'">
                                                                                        {{child.Weight_Carton ?
                                                                                        child.Weight_Carton : 0}}
                                                                                    </ng-container>
                                                                                    <ng-container *ngIf="column.key =='totalWeight'">
                                                                                        {{child.total_Weight ? child.total_Weight : 0}}
                                                                                    </ng-container>
                                                                                    <ng-container *ngIf="column.key =='cartonWeightRu'">
                                                                                        {{child.Weight_kg ?
                                                                                        child.Weight_kg : 0}}
                                                                                    </ng-container>
                                                                                    <ng-container *ngIf="column.key =='totalLoadAmt'">
                                                                                        {{child.total_load_amt_Ru ? child.total_load_amt_Ru : 0}}
                                                                                    </ng-container>
                                                                                    <ng-container *ngIf="column.key =='shippingCostperPieceINR'">
                                                                                        {{child.shipingCost ? child.shipingCost : 0}}
                                                                                    </ng-container>
                                                                                    <ng-container *ngIf="column.key =='totalShippingExpWeight'">
                                                                                        {{child.totalShippingExpense ? child.totalShippingExpense : 0}}
                                                                                    </ng-container>
                                                                                </ng-container>
                                                                                
                                                                                <ng-container>
                                                                                
                                                                                    <ng-container *ngIf="column.key =='percentage'">
                                                                                        {{child.Percentage ? child.Percentage :
                                                                                        0}}
                                                                                    </ng-container>
                                                                                    <ng-container *ngIf="column.key =='totalExpPCSper'">
                                                                                        {{child.totalExpense ?
                                                                                        child.totalExpense : 0}}
                                                                                    </ng-container>
                                                                                    <ng-container *ngIf="column.key =='totalFinalCostPCSper'">
                                                                                        {{child.totalFinalCost ?
                                                                                        child.totalFinalCost : 0}}
                                                                                    </ng-container>
                                                                                
                                                                                </ng-container>

                                                                                <ng-container *ngIf="column.key =='expensePcs'">
                                                                                    {{child.Expense_PCS ?
                                                                                    (child.Expense_PCS | indianCurrency) :
                                                                                    0}}
                                                                                </ng-container>
                                                                                <ng-container *ngIf="column.key =='totalItemAmt'">
                                                                                    {{child.totalItemAmount ? (child.totalItemAmount | indianCurrency) :
                                                                                    0}}
                                                                                </ng-container>
                                                                                
                                                                                <ng-container>
                                                                                    <ng-container *ngIf="column.key ==50">
                                                                                        {{child.item_Amount ?
                                                                                        (child.item_Amount | indianCurrency) : 0}}
                                                                                    </ng-container>
                                                                                    <ng-container *ngIf="column.key ==51">
                                                                                        {{child.item_Amount ?
                                                                                        (child.item_Amount | indianCurrency) : 0}}
                                                                                    </ng-container>
                                                                                </ng-container>

                                                                            </ng-container>
                                                                        </td>
                                                                    </ng-container>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>

                                                </td>
                                            </tr>
                                        </ng-container>
                                    </tbody>
                                </table>

                            </td>
                        </tr>
                    </ng-container>
                    <ng-container>
                        <tr *ngIf="utilsService.isEmptyObjectOrNullUndefined(poImportList)">
                            <td colspan="20" class="text-center">
                                <span
                                    class="truncate">{{utilsService.serverVariableService.STRING_WHEN_NO_RECORDS_FOUND}}</span>
                            </td>
                        </tr>
                    </ng-container>
                </tbody>

            </table>
        </div>
    </div>
</div>

<div class="paginationbox pagination-fixed">
    <app-pagination (pagesizeData)="addPageSizeData($event)" (pageNumber)="pageNumber($event)"
        [page]="paginationRequest.pageNo" [pageSize]="paginationRequest.pageSize"
        [totalData]="paginationRequest.totalData"></app-pagination>
</div>