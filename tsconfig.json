/* To learn more about this file see: https://angular.io/config/tsconfig. */
{
  "compileOnSave": false,
  "compilerOptions": {
    "baseUrl": "./",
    "outDir": "./dist/out-tsc",
    "forceConsistentCasingInFileNames": true,
    "strict": false,
    "esModuleInterop": true,
    "noImplicitOverride": true,
    "noPropertyAccessFromIndexSignature": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "sourceMap": true,
    "declaration": false,
    "experimentalDecorators": true,
    "moduleResolution": "node",
    "importHelpers": true,
    "target": "ES2022",
    "module": "ES2022",
    "useDefineForClassFields": false,
    "lib": [
      "ES2022",
      "dom"
    ],
    "types": [
      "jquery",
      "slick-carousel"
    ],
    "paths": {
      "@service/*": ["src/app/shared/services/*"],
      "@directives/*": ["src/app/shared/directives/*"],
      "@pipes/*": ["src/app/shared/pipes/*"],
      "@enums/*": ["src/app/shared/enums/*"],
      "@guards/*": ["src/app/shared/guards/*"],
      "@constant/*": ["src/app/shared/constant/*"],
      "@modal/*": ["src/app/models/*"],
      "@common/*": ["src/app/shared/components/*"],
      "@env/*": ["src/environments/*"],
    },
  },
  "angularCompilerOptions": {
    "enableI18nLegacyMessageIdFormat": false,
    "strictInjectionParameters": true,
    "strictInputAccessModifiers": true,
    "strictTemplates": true
  }
}
