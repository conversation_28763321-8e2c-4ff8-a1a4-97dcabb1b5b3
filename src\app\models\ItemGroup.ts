import { deserializeAs, serializeAs } from 'cerialize';
import { Category } from './Category';
import { HsnCode } from './HsnCode';
import { AssociateItem } from './AssociateItem';

export class ItemGroup {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    // @serializeAs('createdDate')
    @deserializeAs('createdDate')
    private _createdDate: string;

    @serializeAs('isActive')
    @deserializeAs('isActive')
    private _isActive: boolean;

    // @serializeAs('isSelected')
    @deserializeAs('isSelected')
    private _isSelected: boolean;

    @serializeAs('groupName')
    @deserializeAs('groupName')
    private _groupName: string;

    // @serializeAs('category')
    @deserializeAs('category')
    private _category: Category;

    // @serializeAs('hsnCodeMaster')
    @deserializeAs('hsnCodeMaster')
    private _hsnCodeMaster: HsnCode;

    // @serializeAs('lastModifiedDate')
    @deserializeAs('lastModifiedDate')
    private _lastModifiedDate: string;

    // @serializeAs('itemGroupDocs')
    @deserializeAs('itemGroupDocs')
    private _itemGroupDocs: ItemGroupDocs[];

    @serializeAs('deletedDocsID')
    @deserializeAs('deletedDocsID')
    private _deletedDocsID: any[];

    @deserializeAs('image')
    private _image: string;

    @serializeAs('categoryId')
    @deserializeAs('categoryId')
    private _categoryId: number;

    @deserializeAs('groupCode')
    private _groupCode: string;

    @deserializeAs('items')
    private _items: AssociateItem[];

    @deserializeAs('categoryName')
    private _categoryName: string;

    @serializeAs('hsnCodeId')
    @deserializeAs('hsnCodeId')
    private _hsnCodeId: number[];

    // @serializeAs('hsnCodeMasters')
    @deserializeAs('hsnCodeMasters')
    private _hsnCodeMasters: number[];

    constructor() {
        this.isActive = false;
        this.isSelected = false;
        this.itemGroupDocs = [];
        this.deletedDocsID = [];
        this.items = [];
        this.hsnCodeMasters = []
    }


    /**
     * Getter hsnCodeMasters
     * @return {number[]}
     */
	public get hsnCodeMasters(): number[] {
		return this._hsnCodeMasters;
	}

    /**
     * Setter hsnCodeMasters
     * @param {number[]} value
     */
	public set hsnCodeMasters(value: number[]) {
		this._hsnCodeMasters = value;
	}


    /**
     * Getter hsnCodeId
     * @return {number[]}
     */
	public get hsnCodeId(): number[] {
		return this._hsnCodeId;
	}

    /**
     * Setter hsnCodeId
     * @param {number[]} value
     */
	public set hsnCodeId(value: number[]) {
		this._hsnCodeId = value;
	}


    /**
     * Getter categoryName
     * @return {string}
     */
	public get categoryName(): string {
		return this._categoryName;
	}

    /**
     * Setter categoryName
     * @param {string} value
     */
	public set categoryName(value: string) {
		this._categoryName = value;
	}


    /**
     * Getter items
     * @return {AssociateItem[]}
     */
	public get items(): AssociateItem[] {
		return this._items;
	}

    /**
     * Setter items
     * @param {AssociateItem[]} value
     */
	public set items(value: AssociateItem[]) {
		this._items = value;
	}


    /**
     * Getter groupCode
     * @return {string}
     */
	public get groupCode(): string {
		return this._groupCode;
	}

    /**
     * Setter groupCode
     * @param {string} value
     */
	public set groupCode(value: string) {
		this._groupCode = value;
	}


    /**
     * Getter categoryId
     * @return {number}
     */
	public get categoryId(): number {
		return this._categoryId;
	}

    /**
     * Setter categoryId
     * @param {number} value
     */
	public set categoryId(value: number) {
		this._categoryId = value;
	}

    /**
     * Getter image
     * @return {string}
     */
	public get image(): string {
		return this._image;
	}

    /**
     * Setter image
     * @param {string} value
     */
	public set image(value: string) {
		this._image = value;
	}



    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Getter createdDate
     * @return {string}
     */
	public get createdDate(): string {
		return this._createdDate;
	}

    /**
     * Getter isActive
     * @return {boolean}
     */
	public get isActive(): boolean {
		return this._isActive;
	}

    /**
     * Getter isSelected
     * @return {boolean}
     */
	public get isSelected(): boolean {
		return this._isSelected;
	}

    /**
     * Getter groupName
     * @return {string}
     */
	public get groupName(): string {
		return this._groupName;
	}

    /**
     * Getter category
     * @return {Category}
     */
	public get category(): Category {
		return this._category;
	}

    /**
     * Getter hsnCodeMaster
     * @return {HsnCode}
     */
	public get hsnCodeMaster(): HsnCode {
		return this._hsnCodeMaster;
	}

    /**
     * Getter lastModifiedDate
     * @return {string}
     */
	public get lastModifiedDate(): string {
		return this._lastModifiedDate;
	}

    /**
     * Getter itemGroupDocs
     * @return {ItemGroupDocs[]}
     */
	public get itemGroupDocs(): ItemGroupDocs[] {
		return this._itemGroupDocs;
	}

    /**
     * Getter deletedDocsID
     * @return {any[]}
     */
	public get deletedDocsID(): any[] {
		return this._deletedDocsID;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Setter createdDate
     * @param {string} value
     */
	public set createdDate(value: string) {
		this._createdDate = value;
	}

    /**
     * Setter isActive
     * @param {boolean} value
     */
	public set isActive(value: boolean) {
		this._isActive = value;
	}

    /**
     * Setter isSelected
     * @param {boolean} value
     */
	public set isSelected(value: boolean) {
		this._isSelected = value;
	}

    /**
     * Setter groupName
     * @param {string} value
     */
	public set groupName(value: string) {
		this._groupName = value;
	}

    /**
     * Setter category
     * @param {Category} value
     */
	public set category(value: Category) {
		this._category = value;
	}

    /**
     * Setter hsnCodeMaster
     * @param {HsnCode} value
     */
	public set hsnCodeMaster(value: HsnCode) {
		this._hsnCodeMaster = value;
	}

    /**
     * Setter lastModifiedDate
     * @param {string} value
     */
	public set lastModifiedDate(value: string) {
		this._lastModifiedDate = value;
	}

    /**
     * Setter itemGroupDocs
     * @param {ItemGroupDocs[]} value
     */
	public set itemGroupDocs(value: ItemGroupDocs[]) {
		this._itemGroupDocs = value;
	}

    /**
     * Setter deletedDocsID
     * @param {any[]} value
     */
	public set deletedDocsID(value: any[]) {
		this._deletedDocsID = value;
	}
    

}

export class ItemGroupDocs {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('file')
    @deserializeAs('file')
    private _file: File;

    @serializeAs('formattedName')
    @deserializeAs('formattedName')
    private _formattedName: string;

    @serializeAs('originalName')
    @deserializeAs('originalName')
    private _originalName: string;

    @serializeAs('fileName')
    @deserializeAs('fileName')
    private _fileName: string;

    @serializeAs('isMarkDefault')
    @deserializeAs('isMarkDefault')
    private _isMarkDefault: boolean;

    constructor() {
        this.isMarkDefault = false;
    }


    /**
     * Getter fileName
     * @return {string}
     */
	public get fileName(): string {
		return this._fileName;
	}

    /**
     * Setter fileName
     * @param {string} value
     */
	public set fileName(value: string) {
		this._fileName = value;
	}


    /**
     * Getter file
     * @return {File}
     */
	public get file(): File {
		return this._file;
	}

    /**
     * Setter file
     * @param {File} value
     */
	public set file(value: File) {
		this._file = value;
	}


    /**
     * Getter id
     * @return {number}
     */
    public get id(): number {
        return this._id;
    }

    /**
     * Setter id
     * @param {number} value
     */
    public set id(value: number) {
        this._id = value;
    }

    /**
     * Getter formattedName
     * @return {string}
     */
    public get formattedName(): string {
        return this._formattedName;
    }

    /**
     * Setter formattedName
     * @param {string} value
     */
    public set formattedName(value: string) {
        this._formattedName = value;
    }

    /**
     * Getter originalName
     * @return {string}
     */
    public get originalName(): string {
        return this._originalName;
    }

    /**
     * Setter originalName
     * @param {string} value
     */
    public set originalName(value: string) {
        this._originalName = value;
    }

    /**
     * Getter isMarkDefault
     * @return {boolean}
     */
    public get isMarkDefault(): boolean {
        return this._isMarkDefault;
    }

    /**
     * Setter isMarkDefault
     * @param {boolean} value
     */
    public set isMarkDefault(value: boolean) {
        this._isMarkDefault = value;
    }


}