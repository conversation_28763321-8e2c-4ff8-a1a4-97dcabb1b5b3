import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { Category } from '@modal/Category';
import { CategoryPagination } from '@modal/request/CategoryPagination';
import { UtilsService } from '@service/utils.service';
import { Serialize, Deserialize } from 'cerialize';
import { saveAs } from 'file-saver';
import { debounceTime, distinctUntilChanged } from 'rxjs';
import { activeInactiveStatus, CATEGORY } from 'src/app/shared/constants/constant';
declare var window: any;

@Component({
  selector: 'app-category',
  templateUrl: './category.component.html',
  styleUrls: ['./category.component.css']
})
export class CategoryComponent implements OnInit {

  activeInactiveStatus = activeInactiveStatus
  enumForSortOrder = this.utilsService.enumForSortOrder;
  categoryTH: any[] = []
  paginationRequest = new CategoryPagination();
  categoryList: Category[] = [];
  categoryObj = new Category();
  categoryGroup: FormGroup;
  subCategoryGroup: FormGroup;
  statusForModal: string;
  isExpandedIDs: any[] = [];

  categoryMasterModal: any;
  subCategoryMasterModal: any;
  deleteCategoryModal: any;

  @ViewChild('flag') flag: ElementRef;
  filenameForFlag: string;
  flagForInvalidDocSize = false;
  flagForInvalidExtension = false;
  selectedFlag: File;
  fileLocal: any;

  categoryListDrop: Category[];
  flattenedParentCategory: any[];

  flagForAllExpand: boolean = false;

  constructor(public utilsService: UtilsService, private fb: FormBuilder) {
    this.categoryTH = CATEGORY
  }

  ngOnInit() {

    this.paginationRequest.searchSubject.pipe(debounceTime(300), distinctUntilChanged()).subscribe((res: string) => {
      this.paginationRequest.pageNo = 1;
      this.paginationRequest.pageSize = '100';
      this.paginationRequest.searchText = null;
      this.paginationRequest.searchText = res;
      this.getAllCategory();
    });

    this.categoryGroup = this.fb.group({
      name: ['', Validators.compose([Validators.required])],
      note: [''],
      categoryCode: ['', Validators.compose([Validators.required])],
      status: [true, Validators.compose([Validators.required])]
    })

    this.subCategoryGroup = this.fb.group({
      category: ['', Validators.compose([Validators.required])],
      sub_category: [null, Validators.compose([Validators.required])],
      categoryCode: ['', Validators.compose([Validators.required])],
      status: [true, Validators.compose([Validators.required])],
      note: [''],
    })

    this.getAllCategory();

    this.categoryMasterModal = new window.bootstrap.Modal(
      document.getElementById('categoryMasterModal')
    );

    document.getElementById('categoryMasterModal').addEventListener('shown.bs.modal', () => {
      document.getElementById('f1').focus();
    });

    this.subCategoryMasterModal = new window.bootstrap.Modal(
      document.getElementById('subCategoryMasterModal')
    );

    this.deleteCategoryModal = new window.bootstrap.Modal(
      document.getElementById('deleteCategoryModal')
    );
  }

  getAllCategory() {

    this.paginationRequest.flagForSelectAll = false;
    this.paginationRequest.selectedIds = [];
    this.paginationRequest.isFilter = false;
    
    if(this.paginationRequest.searchText) {
      this.paginationRequest.isFilter = true
    } 

    this.utilsService.postMethodAPI(false, this.utilsService.serverVariableService.CATEGORY_LISTING, Serialize(this.paginationRequest), (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.categoryList = Deserialize(response.content, Category);
        this.paginationRequest.totalData = response.totalElements;
        this.paginationRequest.pagination = response;

        if (!this.utilsService.isNullUndefinedOrBlank(this.isExpandedIDs)) {
          this.checkCollapseIDs(this.categoryList);
        }

        if (!this.utilsService.isNullUndefinedOrBlank(this.paginationRequest.searchText)) {
          if (this.categoryList) {
            this.flagForAllExpand = false;
            this.onExpandAll()
          }
        }

      } else {
        this.categoryList = [];
      }
    })
  }

  openAddEditModal(obj: Category, status: string, isSub: boolean) {
    this.fileLocal = null;
    this.selectedFlag = null;
    this.filenameForFlag = null;
    if (this.flag?.nativeElement) {
      this.flag.nativeElement.value = "";
    }
    this.categoryObj = new Category();
    this.statusForModal = status;

    isSub ? this.subCategoryGroup.reset() : this.categoryGroup.reset();

    if (this.statusForModal === 'Add') {
      setTimeout(() => {
        this.categoryObj.isActive = true;
      }, 100);
    }

    if (obj && this.statusForModal !== 'Add') {
      setTimeout(() => {
        this.categoryObj = Serialize(obj);
      }, 150);
    }

    if (obj && isSub) {
      setTimeout(() => {
        this.categoryObj.parentCategoryId = obj.id
        this.getRequiredData();
      }, 150);
    }

    isSub ? this.subCategoryMasterModal.show() : this.categoryMasterModal.show();
  }

  addPageSizeData(event) {
    this.paginationRequest.pageNo = 1;
    this.paginationRequest.pageSize = event;
    this.getAllCategory();
  }

  pageNumber(event) {
    this.paginationRequest.pageNo = event
    this.getAllCategory();
  }

  trackBy(index: number, item: Category): number {
    return item.id;
  }

  // Select Deselect 
  selectAll() {
    if (this.paginationRequest.flagForSelectAll) {
      this.paginationRequest.selectedIds = [];
    }
    this.processCategories(this.categoryList);
    if (!this.paginationRequest.flagForSelectAll) {
      this.paginationRequest.selectedIds = [];
    }
  }

  processCategories(categories: Category[]): void {
    if (!categories || categories.length === 0) return;

    for (const category of categories) {
      if (this.paginationRequest.flagForSelectAll) {
        category.isSelected = true;
        this.paginationRequest.selectedIds.push(category.id);
      } else {
        category.isSelected = false;
        const index = this.paginationRequest.selectedIds.indexOf(category.id);
        if (index > -1) {
          this.paginationRequest.selectedIds.splice(index, 1);
        }
      }
      if (category.categoryList && category.categoryList.length > 0) {
        this.processCategories(category.categoryList);
      }
    }
  }

  selectUnselect(id: number, index, value) {

    const isSelected = this.paginationRequest.selectedIds.includes(id);

    if (value && !isSelected) {

      this.paginationRequest.selectedIds.push(id);

    } else if (!value && isSelected) {

      const assetIndex = this.paginationRequest.selectedIds.indexOf(id);
      this.paginationRequest.selectedIds.splice(assetIndex, 1);
    }
    this.paginationRequest.flagForSelectAll = this.checkIfAllSelected();
  }

  checkIfAllSelected() {
    let flag = true;
    this.categoryList.filter((val, index) => {
      if (val['isSelected'] === false) {
        flag = false;
        return;
      }
    });
    return flag;
  }

  // sorting 
  onSortTH(key) {

    if (this.utilsService.isEmptyObjectOrNullUndefined(this.categoryList)) {
      return;
    }

    if (key === this.paginationRequest.sortColumn) {
      if (this.paginationRequest.sortOrder === this.enumForSortOrder.A) {
        this.paginationRequest.sortOrder = this.enumForSortOrder.D;
      } else if (this.paginationRequest.sortOrder === this.enumForSortOrder.D) {
        this.paginationRequest.sortOrder = this.enumForSortOrder.A;
      }
    } else {
      this.paginationRequest.sortOrder = this.enumForSortOrder.D;
    }

    this.paginationRequest.sortColumn = key;
    this.getAllCategory();
  }

  /// Expand All

  onExpandAll() {
    this.flagForAllExpand = !this.flagForAllExpand;
    const expandAll = (categoryList: Category[], isExpand: boolean) => {
      categoryList.forEach((c: Category) => {
        c.isExpand = isExpand;
        if (c.categoryList && c.categoryList.length > 0) {
          expandAll(c.categoryList, isExpand);
        }
        this.collapseIDSave(categoryList)
      });
    };
    
    expandAll(this.categoryList, this.flagForAllExpand);
  }
  
  checkIfAllExpand() {
    let flag = true;
    this.categoryList.filter((val, index) => {
      if (val['isExpand'] === false) {
        flag = false;
        return;
      }
    });
    return flag;
  }

  // collapse
  onCollapse(parentIndex: number) {
    const parentCategory = this.categoryList[parentIndex];
    parentCategory.isExpand = !parentCategory.isExpand;
    this.flagForAllExpand = !parentCategory.isExpand;

    this.collapseIDSave(this.categoryList)

    if (parentCategory.categoryList) {
      this.collapseAll(parentCategory.categoryList);
    }
  }

  onCollapseChild(parentIndex: number, childIndex: number) {
    const childCategory = this.categoryList[parentIndex].categoryList[childIndex];
    childCategory.isExpand = !childCategory.isExpand;

    this.collapseIDSave(this.categoryList)

    if (childCategory.categoryList) {
      this.collapseAll(childCategory.categoryList);
    }
  }

  onCollapseSubChild(parentIndex: number, childIndex: number, subIndex: number) {
    const subChildCategory = this.categoryList[parentIndex].categoryList[childIndex].categoryList[subIndex];
    subChildCategory.isExpand = !subChildCategory.isExpand;

    this.collapseIDSave(this.categoryList)

    if (subChildCategory.categoryList) {
      this.collapseAll(subChildCategory.categoryList);
    }
  }

  collapseIDSave(categoryList: Category[]) {
    for (const category of categoryList) {
      if (category.isExpand) {
        if (!this.isExpandedIDs.includes(category.id)) {
          this.isExpandedIDs.push(category.id);
        }
      } else {
        const index = this.isExpandedIDs.indexOf(category.id);
        if (index !== -1) {
          this.isExpandedIDs.splice(index, 1);
        }
      }
      if (category.categoryList && category.categoryList.length > 0) {
        this.collapseIDSave(category.categoryList);
      }
    }
  }

  collapseAll(categoryList: any[]) {
    categoryList.forEach(category => {
      category.isExpand = false;
      if (category.categoryList) {
        this.collapseAll(category.categoryList);
      }
    });
  }

  checkCollapseIDs(categoryList: any[]) {
    for (const category of categoryList) {
      category.isExpand = this.isExpandedIDs.includes(category.id);
      if (category.categoryList && category.categoryList.length > 0) {
        this.checkCollapseIDs(category.categoryList);
      }
    }
  }

  // status 

  onChangeStatus(item: Category, value, index) {

    this.utilsService.putMethodAPI(true, this.utilsService.serverVariableService.CATEGORY_CHANGE + `${item.id}/${value}`, {}, '', (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response.data)) {
        this.categoryList[index].isActive = value
      } else {
        this.categoryList[index].isActive = !value
      }
      if (!this.paginationRequest.pagination?.first && this.paginationRequest.pagination?.last && this.paginationRequest.pagination?.numberOfElements === 1 && this.paginationRequest.isActive) {
        this.paginationRequest.pageNo = this.paginationRequest.pageNo - 1
      }
      this.getAllCategory()
    }, true);
  }

  //Search
  onSearch(event: any) {
    this.paginationRequest.searchSubject.next(event.target.value);
  }

  // active/inactive

  onChangeActive() {
    this.paginationRequest.pageNo = 1;
    this.getAllCategory();
  }

  // export 

  exportReport() {

    const param = {
      searchText: this.paginationRequest.searchText,
      isActive: this.paginationRequest.isActive,
      sortOrder: this.paginationRequest.sortOrder,
      sortColumn: this.paginationRequest.sortColumn,
      ids: this.paginationRequest.selectedIds ? this.paginationRequest.selectedIds : []
    }

    this.utilsService.exportReport(param, this.utilsService.serverVariableService.CATEGORY_EXPORT).subscribe((data: any) => {
      saveAs(new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' }), 'Category Sheet');
    });
  }

  // delete
  openDeleteCategoryModal(obj: Category) {
    this.categoryObj = Serialize(obj)
    this.deleteCategoryModal.show();
  }

  deleteCategory() {

    this.utilsService.deleteMethodAPI(true, this.utilsService.serverVariableService.CATEGORY_SAVE_EDIT_DELETE + `?id=${this.categoryObj.id}`, {}, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.deleteCategoryModal.hide();
        if (!this.paginationRequest.pagination?.first && this.paginationRequest.pagination?.last && this.paginationRequest.pagination?.numberOfElements === 1) {
          this.paginationRequest.pageNo = this.paginationRequest.pageNo - 1
        }
        this.getAllCategory();
      }
    })
  }

  serializeObj(categories: Category): void {
    for (const category of categories.categoryList) {
      category.categoryList = Serialize(category.categoryList);
      if (category.categoryList && category.categoryList.length > 0) {
        this.serializeObj(category);
      }
    }
  }

  onSaveCategory(isSub: boolean) {

    const formData = new FormData();

    let group = isSub ? this.subCategoryGroup : this.categoryGroup
    let modal = isSub ? this.subCategoryMasterModal : this.categoryMasterModal

    if (group.invalid) {
      group.markAllAsTouched();
      return;
    }

    if (this.selectedFlag) {
      formData.set('categoryImg', this.selectedFlag);
    }

    if (!this.categoryObj.categoryImg) {
      this.utilsService.toasterService.error('Category Image is required!', '', {
        positionClass: 'toast-top-right',
        closeButton: true,
        timeOut: 10000
      });
      return;
    }

    let param = this.utilsService.trimObjectValues(Serialize(this.categoryObj));
    delete param['isSelected'];
    delete param['isExpand'];
    delete param['categoryList'];
    delete param['originalName'];
    formData.set('saveCategoryInfo', JSON.stringify(Serialize(param)));

    this.utilsService.postMethodAPI(true, this.utilsService.serverVariableService.CATEGORY_SAVE_EDIT_DELETE, formData, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        modal.hide();
        this.getAllCategory();
      }
    })
  }

  onSelectProfile(event): void {

    if (event.target.files && event.target.files[0]) {
      this.flagForInvalidExtension = false;
      this.flagForInvalidDocSize = false;
      this.fileLocal = null;
      const reader = new FileReader();
      const max_file_size = 5242880;
      reader.readAsDataURL(event.target.files[0]); // read file as data url
      const selectedFile = event.target.files[0];
      if (selectedFile) {
        const ext = selectedFile.name.substr(selectedFile.name.lastIndexOf('.') + 1);
        const ext1 = (ext).toLowerCase();

        if (ext1 === 'jpeg' || ext1 === 'png' || ext1 === 'jpg' || ext1 === 'jfif') {
          const fileUrl = URL.createObjectURL(event.target.files[0]);
          if (max_file_size < selectedFile.size) {
            this.flagForInvalidDocSize = true;
            this.filenameForFlag = '';
            this.utilsService.toasterService.error(this.utilsService.validationService.IMAGE_MAX_FILE_SIZE)
            this.selectedFlag = undefined;
            if (this.flag?.nativeElement) {
              this.flag.nativeElement.value = "";
            }
          } else {
            this.filenameForFlag = event.target.files[0].name;
            this.categoryObj.categoryImg = event.target.files[0].name
            this.selectedFlag = event.target.files[0];
            this.fileLocal = fileUrl
          }
        } else {
          this.flagForInvalidExtension = true;
          this.filenameForFlag = '';
          this.selectedFlag = undefined;
          this.utilsService.toasterService.error(this.utilsService.validationService.IMAGE_INVALID_EXTENSION)
          if (this.flag?.nativeElement) {
            this.flag.nativeElement.value = "";
          }
        }
      }

    }
  }

  // getRequiredData() {
  //   this.paginationRequest.dropdown = [];
  //   this.utilsService.getMethodAPI(false, this.utilsService.serverVariableService.CATEGORY_DROPDOWN, null, (response) => {
  //     if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
  //       this.paginationRequest.dropdown = response.category;
  //       this.paginationRequest.dropdown = this.paginationRequest.dropdown ? this.utilsService.transformDropdownItems(this.paginationRequest.dropdown) : [];
  //       this.paginationRequest.dropdown = this.utilsService.filterIsActiveLV(this.paginationRequest.dropdown, this.categoryObj.parentCategoryId ? this.categoryObj.parentCategoryId : null);
  //     }
  //   })
  // }

  getRequiredData() {
    this.categoryListDrop = [];
    this.utilsService.getMethodAPI(false, this.utilsService.serverVariableService.IG_DROPDOWN, null, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.categoryListDrop = (response?.category);
        setTimeout(() => {
          this.getParentCategory();
        }, 150);
      }
    })
  }

  openLink(link) {
    const filePreview = `${this.utilsService.imgPath}${link}`
    if (filePreview) {
      if (link) {
        window.open(filePreview, "_blank");
      }
    }
  }

  //// category dropdown
  modifyParentResponse(category: any[]) {
    let flatList = [];
    for (let parent of (category || [])) {
      parent.className = "ng-select-option-" + (parent.index + 1);
      flatList.push(parent);
      if (parent.categoryList && parent.categoryList.length > 0) {
        let data = (parent.categoryList || []).filter(p => p.index < 4);
        for (let d of data) {
          d.className = "ng-select-option-" + (d.index + 1);
          d.isChild = true;
        }
        flatList = flatList.concat(this.modifyParentResponse(data));
      }
    }
    return flatList;
  }

  getParentCategory() {
    let data = this.assignCategoryIndex(this.categoryListDrop)
    data = (data || []).filter(list => list.index == 0);
    this.flattenedParentCategory = this.modifyParentResponse(data);
    this.flattenedParentCategory = this.flattenedParentCategory.filter(v => v.index < 3);
    this.flattenedParentCategory = this.flattenedParentCategory ? this.utilsService.transformDropdownItems(this.flattenedParentCategory) : [];
    this.flattenedParentCategory = this.utilsService.filterIsActive(this.flattenedParentCategory, this.categoryObj.parentCategoryId ? this.categoryObj.parentCategoryId : null);
  }

  assignCategoryIndex(categories: Category[], depth: number = 0): any[] {
    return categories.map((category) => {
      category.index = depth;
      if (category.categoryList && category.categoryList.length > 0) {
        category.categoryList = this.assignCategoryIndex(category.categoryList, depth + 1);
      }
      return category;
    });
  }

}
