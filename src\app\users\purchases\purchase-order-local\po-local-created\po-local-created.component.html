<div class="page-filters">
  <div class="page-filters-left">
    <div class="form-group form-group-sm filter-search">
      <div class="form-group-icon-start">
        <i class="th th-outline-search-normal-1 icon-broder "></i>
        <input type="text" class="form-control" placeholder="Search by ID">
      </div>
    </div>
    <div class="form-group form-group-sm">
      <div class="form-group-icon-end">
        <i class="th th-outline-calendar"></i>
        <input type="text" class="form-control" placeholder="Select Date">
      </div>
    </div>
    <div class="form-group theme-ngselect form-group-sm">
      <ng-select placeholder="Supplier" [multiple]="false" [clearable]="false" [items]="demo" bindLabel="name"
        bindValue="id" [(ngModel)]="selectedDemo1">
      </ng-select>
    </div>
    <div class="form-group form-group-sm">
      <input type="text" class="form-control" placeholder="Price From">
    </div>
    <div class="form-group form-group-sm">
      <input type="text" class="form-control" placeholder="Price To">
    </div>
    <div class="form-group theme-ngselect form-group-sm">
      <ng-select placeholder="Status" [multiple]="false" [clearable]="false" [items]="demo" bindLabel="name"
        bindValue="id" [(ngModel)]="selectedDemo1">
      </ng-select>
    </div>
  </div>
  <div class="page-filters-right">
    <div class="dropdown export-dropdown">
      <button type="button" class="btn btn-sm btn-outline-white dropdown-toggle" data-bs-toggle="dropdown"
        aria-expanded="false">
        Export
      </button>
      <ul class="dropdown-menu">
        <li><a class="dropdown-item" href="#">Action</a></li>
        <li><a class="dropdown-item" href="#">Another action</a></li>
        <li><a class="dropdown-item" href="#">Something else here</a></li>
      </ul>
    </div>
    <app-table-column-filter-dropdown />
  </div>
</div>

<div class="card card-theme card-table-sticky3">
  <div class="card-body p-0">
    <div class="accordion accordion-group accordion-parent" id="accordionDoubleParent">
      <div class="accordion-item accordion-parent-item">
        <h2 class="accordion-header accordion-parent-header" id="accordionDoubleParent_headingOne">
          <button class="accordion-button" type="button" data-bs-toggle="collapse"
            data-bs-target="#accordionDoubleParent_CollapseOne" aria-expanded="true"
            aria-controls="accordionDoubleParent_CollapseOne">
            <div class="accordion-header-left">
              <ul class="accordion-header-item">
                <li>Supplier Name: Nilishbhai</li>
                <li>Mobile No +91 98985 985452</li>
              </ul>
            </div>
            <div class="accordion-header-right">
              <button class="btn btn-xs btn-light-primary btn-icon" data-bs-toggle="modal"
                data-bs-target="#moveToTransitsModal" ngbTooltip="Tooltip" placement="bottom" container="body"
                triggers="hover"><i class="th th-outline-arrow-right-1"></i></button>
              <div class="dropdown">
                <button class="btn btn-xs btn-light-white btn-icon" id="actionDropDown" data-bs-toggle="dropdown"
                  aria-expanded="false" ngbTooltip="More Option" placement="bottom" container="body" triggers="hover">
                  <i class="th th-outline-more"></i>
                </button>
                <ul class="dropdown-menu" aria-labelledby="actionDropDown">
                  <li><a class="dropdown-item" [routerLink]="['/users/purchases/new-po-local']"><i
                        class="th th-outline-edit"></i>Edit</a></li>
                  <li><a class="dropdown-item" [routerLink]="['/users/purchases/new-po-local-expenses']"><i
                        class="th th-outline-add-circle"></i>Add Expense</a></li>
                  <li><a class="dropdown-item" href="#"><i class="bi bi-download"></i>Download PDF</a></li>
                  <li><a class="dropdown-item" href="#"><i class="th th-outline-whatsapp"></i>Send WA/Tel.</a></li>
                  <li><a class="dropdown-item" href="#"><i class="th th-outline-barcode"></i>Print Carton QR Code</a>
                  </li>
                  <li><a class="dropdown-item" [routerLink]="['/users/purchases/po-local-carton-mapping']"><i
                        class="th th-outline-box"></i>Carton Mapping</a></li>
                  <li><a class="dropdown-item" href="#"><i class="th th-outline-box-1"></i>Audit Ticket</a></li>
                  <li><a class="dropdown-item" href="#"><i class="th th-outline-dollar-circle"></i>Make Payment</a>
                  </li>
                  <li><a class="dropdown-item" href="#"><i class="th th-outline-tick-circle"></i>Mark As Received</a>
                  </li>
                  <li><a class="dropdown-item" href="#"><i class="th th-outline-close-circle"></i>Cancel Order</a>
                  </li>
                  <li>
                    <hr class="dropdown-divider">
                  </li>
                  <li><a class="dropdown-item text-danger" href="#"><i class="th th-outline-trash"></i>Delete</a></li>
                </ul>
              </div>
            </div>
          </button>
        </h2>
        <div id="accordionDoubleParent_CollapseOne" class="accordion-collapse collapse show"
          aria-labelledby="accordionDoubleParent_headingOne" data-bs-parent="#accordionDoubleParent">
          <div class="accordion-body tbl-accordion-body">
            <div class="accordion accordion-group " id="accordionDoubleNormal">
              <div class="accordion-item">
                <h2 class="accordion-header" id="accordionDoubleNormal_HeadingOne">
                  <button class="accordion-button" type="button" data-bs-toggle="collapse"
                    data-bs-target="#accordionDoubleNormal_CollapseOne" aria-expanded="true"
                    aria-controls="accordionDoubleNormal_CollapseOne">
                    <div class="accordion-header-left ">
                      <ul class="accordion-header-item accordion-header-item-normal">
                        <li>PO ID: #458555</li>
                        <li>Date: 5/10/2024</li>
                      </ul>
                    </div>
                    <div class="accordion-header-right">
                      <button class="btn btn-xs btn-light-primary btn-icon" data-bs-toggle="modal"
                        data-bs-target="#moveToTransitsModal" ngbTooltip="Tooltip" placement="bottom" container="body"
                        triggers="hover"><i class="th th-outline-arrow-right-1"></i></button>
                      <div class="dropdown">
                        <button class="btn btn-xs btn-light-white btn-icon" id="actionDropDown"
                          data-bs-toggle="dropdown" aria-expanded="false" ngbTooltip="More Option" placement="bottom"
                          container="body" triggers="hover">
                          <i class="th th-outline-more"></i>
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="actionDropDown">
                          <li><a class="dropdown-item" [routerLink]="['/users/purchases/new-po-local']"><i
                                class="th th-outline-edit"></i>Edit</a></li>
                          <li><a class="dropdown-item" href="#"><i class="th th-outline-add-circle"></i>Add Expense</a>
                          </li>
                          <li><a class="dropdown-item" href="#"><i class="bi bi-download"></i>Download PDF</a></li>
                          <li><a class="dropdown-item" href="#"><i class="th th-outline-whatsapp"></i>Send WA/Tel.</a>
                          </li>
                          <li><a class="dropdown-item" href="#"><i class="th th-outline-barcode"></i>Print Carton QR
                              Code</a>
                          </li>
                          <li><a class="dropdown-item" [routerLink]="['/users/purchases/po-local-carton-mapping']"><i
                                class="th th-outline-box"></i>Carton Mapping</a></li>
                          <li><a class="dropdown-item" href="#"><i class="th th-outline-box-1"></i>Audit Ticket</a></li>
                          <li><a class="dropdown-item" href="#"><i class="th th-outline-dollar-circle"></i>Make
                              Payment</a>
                          </li>
                          <li><a class="dropdown-item" href="#"><i class="th th-outline-tick-circle"></i>Mark As
                              Received</a>
                          </li>
                          <li><a class="dropdown-item" href="#"><i class="th th-outline-close-circle"></i>Cancel
                              Order</a>
                          </li>
                          <li>
                            <hr class="dropdown-divider">
                          </li>
                          <li><a class="dropdown-item text-danger" href="#"><i
                                class="th th-outline-trash"></i>Delete</a></li>
                        </ul>
                      </div>
                    </div>
                  </button>
                </h2>
                <div id="accordionDoubleNormal_CollapseOne" class="accordion-collapse collapse show"
                  aria-labelledby="accordionDoubleNormal_HeadingOne" data-bs-parent="#accordionDoubleNormal">
                  <div class="accordion-body tbl-accordion-body">
                    <div class="table-responsive">
                      <table class="table-theme table-hover table table-bordered ">
                        <thead class="border-less">
                          <tr>
                            <th>Item Details</th>
                            <th>HSN Code</th>
                            <th>Purchase <br />Breach Qty</th>
                            <th>Available Qty</th>
                            <th>Difference <br />(Formula[B])</th>
                            <th>Sale Price/<br />PCS</th>
                            <th>Sale Price/<br />Carton</th>
                            <th>Last Sale Price <br />(Carton)</th>
                            <th>Last Purchase<br />(Purchase By/<br />CTN)</th>
                            <th>Other Supplier Price</th>
                            <th>Order Type</th>
                            <th>Cartons <br />(PO)</th>
                            <th>PCS/Carton<br />(PO)</th>
                            <th>Total Quantity<br />(PO)</th>
                            <th>Purchase <br />Price</th>
                            <th>Total Item <br />Amount</th>
                            <th>GST TAX %</th>
                            <th>GST AMT</th>
                            <th>Total Amount<br />(With TAX)</th>
                            <th>Note</th>
                            <th>Inquiry (Cus <br />Count) - (Total <br />inq. Count)</th>
                            <th>Item Rating</th>
                            <th>Market Type</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr *ngFor="let item of [1,2,3,4,5]">
                            <td class="tbl-user">
                              <div class="tbl-user-checkbox-srno">
                                <div class="checkbox checkbox-primary checkbox-small">
                                  <input type="checkbox" id="tbl-checkbox2" class="material-inputs filled-in" />
                                  <label for="tbl-checkbox2"></label>
                                </div>
                                <span>01.</span>
                                <div class="tbl-user-wrapper">
                                  <div class="tbl-user-image">
                                    <img src="assets/images/avatar.jpg" alt="valamji">
                                  </div>
                                  <div class="tbl-user-text-action">
                                    <div class="tbl-user-text">
                                      <p>Ink Pen</p>
                                      <span>SKU #58545854585</span>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </td>
                            <td>458555</td>
                            <td>
                              <a class="btn-link text-primary" data-bs-toggle="modal"
                                data-bs-target="#poStockDetailsModal">20</a>
                            </td>
                            <td>25</td>
                            <td>20</td>
                            <td>10</td>
                            <td>100</td>
                            <td>105,106,104</td>
                            <td>
                              <p class="mb-0">Kavya, 50 </p>
                              <p class="mb-0">Nirmal,45</p>
                              <p class="mb-0">Ashopalav,45</p>
                              <div class="dropdown dropdown-with-tables">
                                <button class="btn btn-link text-primary" type="button" data-bs-toggle="dropdown"
                                  aria-expanded="false" data-bs-auto-close="outside"
                                  data-bs-popper-config='{"strategy":"fixed"}'>
                                  View more
                                </button>
                                <div class="dropdown-menu">
                                  <div class="table-responsive">
                                    <table class="table-theme table-hover table table-bordered table-sticky ">
                                      <thead class="border-less">
                                        <tr>
                                          <th>Supplier Name</th>
                                          <th>Carton Qty</th>
                                          <th>Qty/Carton</th>
                                          <th>Total Qty</th>
                                          <th>Price</th>
                                          <th>Dimension</th>
                                        </tr>
                                      </thead>
                                      <tbody>
                                        <tr *ngFor="let item of [1,2,3,4,5]">
                                          <td class="tbl-user">
                                            <div class="tbl-user-checkbox-srno">
                                              <div class="tbl-user-wrapper">
                                                <div class="tbl-user-image">
                                                  <i class="th th-outline-user"></i>
                                                </div>
                                                <div class="tbl-user-text">
                                                  <p>Alpeshbhai</p>
                                                  <span>alpeshbhai56&#64;gmail.com</span>
                                                </div>
                                              </div>
                                            </div>
                                          </td>
                                          <td>15</td>
                                          <td>20</td>
                                          <td>300</td>
                                          <td>85</td>
                                          <td>20 x 30 x 49</td>
                                        </tr>
                                      </tbody>
                                    </table>
                                  </div>
                                </div>
                              </div>
                            </td>
                            <td>
                              <div class="d-flex align-items-center">
                                <div class="dropdown dropdown-with-tables">
                                  <button class="btn btn-link text-primary" type="button" data-bs-toggle="dropdown"
                                    aria-expanded="false" data-bs-auto-close="outside"
                                    data-bs-popper-config='{"strategy":"fixed"}'>
                                    Amish, 45
                                  </button>
                                  <div class="dropdown-menu">
                                    <div class="table-responsive">
                                      <table class="table-theme table-hover table table-bordered table-sticky ">
                                        <thead class="border-less">
                                          <tr>
                                            <th>Supplier Name</th>
                                            <th>Qty/Carton</th>
                                            <th>Last Purchased on</th>
                                            <th>Price</th>
                                          </tr>
                                        </thead>
                                        <tbody>
                                          <tr *ngFor="let item of [1,2,3,4,5]">
                                            <td class="tbl-user">
                                              <div class="tbl-user-checkbox-srno">
                                                <div class="tbl-user-wrapper">
                                                  <div class="tbl-user-image">
                                                    <i class="th th-outline-user"></i>
                                                  </div>
                                                  <div class="tbl-user-text">
                                                    <p>Alpeshbhai</p>
                                                    <span>alpeshbhai56&#64;gmail.com</span>
                                                  </div>
                                                </div>
                                              </div>
                                            </td>
                                            <td>15</td>
                                            <td>12/05/2024</td>
                                            <td>85</td>
                                          </tr>
                                        </tbody>
                                      </table>
                                    </div>
                                  </div>
                                </div>
                                <i class="th th-outline-info-circle ms-auto text-danger"
                                  ngbTooltip="Other Supplier Price	" placement="bottom" container="body"
                                  triggers="hover"></i>
                              </div>
                            </td>
                            <td class="tbl-form-group-borderless">
                              <div class="form-group theme-ngselect">
                                <ng-select placeholder="Order Type" [multiple]="false" [clearable]="false"
                                  [items]="demo" bindLabel="name" bindValue="id" [(ngModel)]="selectedDemo1"
                                  appendTo="body">
                                </ng-select>
                              </div>
                            </td>
                            <td>5</td>
                            <td>10</td>
                            <td>50</td>
                            <td>100</td>
                            <td>5000</td>
                            <td>18</td>
                            <td>900</td>
                            <td>5900</td>
                            <td>kale mokalshe-gujrat transport ma</td>
                            <td>3 [15]</td>
                            <td>2.1</td>
                            <td class="tbl-form-group-borderless">
                              <div class="form-group theme-ngselect">
                                <ng-select placeholder="Market Type" [multiple]="false" [clearable]="false"
                                  [items]="demo" bindLabel="name" bindValue="id" [(ngModel)]="selectedDemo1"
                                  appendTo="body">
                                </ng-select>
                              </div>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
              <div class="accordion-item">
                <h2 class="accordion-header" id="accordionDoubleNormal_HeadingTwo">
                  <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#accordionDoubleNormal_CollapseTwo" aria-expanded="false"
                    aria-controls="accordionDoubleNormal_CollapseTwo">
                    <div class="accordion-header-left ">
                      <ul class="accordion-header-item accordion-header-item-normal">
                        <li>PO ID: #458555</li>
                        <li>Date: 5/10/2024</li>
                      </ul>
                    </div>
                    <div class="accordion-header-right">
                      <button class="btn btn-xs btn-light-primary btn-icon" data-bs-toggle="modal"
                        data-bs-target="#moveToTransitsModal" ngbTooltip="Tooltip" placement="bottom" container="body"
                        triggers="hover"><i class="th th-outline-arrow-right-1"></i></button>
                      <div class="dropdown">
                        <button class="btn btn-xs btn-light-white btn-icon" id="actionDropDown"
                          data-bs-toggle="dropdown" aria-expanded="false" ngbTooltip="More Option" placement="bottom"
                          container="body" triggers="hover">
                          <i class="th th-outline-more"></i>
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="actionDropDown">
                          <li><a class="dropdown-item" [routerLink]="['/users/purchases/new-po-local']"><i
                                class="th th-outline-edit"></i>Edit</a></li>
                          <li><a class="dropdown-item" href="#"><i class="th th-outline-add-circle"></i>Add Expense</a>
                          </li>
                          <li><a class="dropdown-item" href="#"><i class="bi bi-download"></i>Download PDF</a></li>
                          <li><a class="dropdown-item" href="#"><i class="th th-outline-whatsapp"></i>Send WA/Tel.</a>
                          </li>
                          <li><a class="dropdown-item" href="#"><i class="th th-outline-barcode"></i>Print Carton QR
                              Code</a>
                          </li>
                          <li><a class="dropdown-item" [routerLink]="['/users/purchases/po-local-carton-mapping']"><i
                                class="th th-outline-box"></i>Carton Mapping</a></li>
                          <li><a class="dropdown-item" href="#"><i class="th th-outline-box-1"></i>Audit Ticket</a></li>
                          <li><a class="dropdown-item" href="#"><i class="th th-outline-dollar-circle"></i>Make
                              Payment</a>
                          </li>
                          <li><a class="dropdown-item" href="#"><i class="th th-outline-tick-circle"></i>Mark As
                              Received</a>
                          </li>
                          <li><a class="dropdown-item" href="#"><i class="th th-outline-close-circle"></i>Cancel
                              Order</a>
                          </li>
                          <li>
                            <hr class="dropdown-divider">
                          </li>
                          <li><a class="dropdown-item text-danger" href="#"><i
                                class="th th-outline-trash"></i>Delete</a></li>
                        </ul>
                      </div>
                    </div>
                  </button>
                </h2>
                <div id="accordionDoubleNormal_CollapseTwo" class="accordion-collapse collapse"
                  aria-labelledby="accordionDoubleNormal_HeadingTwo" data-bs-parent="#accordionDoubleNormal">
                  <div class="accordion-body tbl-accordion-body">
                    <div class="table-responsive">
                      <table class="table-theme table-hover table table-bordered ">
                        <thead class="border-less">
                          <tr>
                            <th>Item Details</th>
                            <th>HSN Code</th>
                            <th>Purchase <br />Breach Qty</th>
                            <th>Available Qty</th>
                            <th>Difference <br />(Formula[B])</th>
                            <th>Sale Price/<br />PCS</th>
                            <th>Sale Price/<br />Carton</th>
                            <th>Last Sale Price <br />(Carton)</th>
                            <th>Last Purchase<br />(Purchase By/<br />CTN)</th>
                            <th>Other Supplier Price</th>
                            <th>Order Type</th>
                            <th>Cartons <br />(PO)</th>
                            <th>PCS/Carton<br />(PO)</th>
                            <th>Total Quantity<br />(PO)</th>
                            <th>Purchase <br />Price</th>
                            <th>Total Item <br />Amount</th>
                            <th>GST TAX %</th>
                            <th>GST AMT</th>
                            <th>Total Amount<br />(With TAX)</th>
                            <th>Note</th>
                            <th>Inquiry (Cus <br />Count) - (Total <br />inq. Count)</th>
                            <th>Item Rating</th>
                            <th>Market Type</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr *ngFor="let item of [1,2,3,4,5]">
                            <td class="tbl-user">
                              <div class="tbl-user-checkbox-srno">
                                <div class="checkbox checkbox-primary checkbox-small">
                                  <input type="checkbox" id="tbl-checkbox2" class="material-inputs filled-in" />
                                  <label for="tbl-checkbox2"></label>
                                </div>
                                <span>01.</span>
                                <div class="tbl-user-wrapper">
                                  <div class="tbl-user-image">
                                    <img src="assets/images/avatar.jpg" alt="valamji">
                                  </div>
                                  <div class="tbl-user-text-action">
                                    <div class="tbl-user-text">
                                      <p>Ink Pen</p>
                                      <span>SKU #58545854585</span>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </td>
                            <td>458555</td>
                            <td>
                              <a class="btn-link text-primary" data-bs-toggle="modal"
                                data-bs-target="#poStockDetailsModal">20</a>
                            </td>
                            <td>25</td>
                            <td>20</td>
                            <td>10</td>
                            <td>100</td>
                            <td>105,106,104</td>
                            <td>
                              <p class="mb-0">Kavya, 50 </p>
                              <p class="mb-0">Nirmal,45</p>
                              <p class="mb-0">Ashopalav,45</p>
                              <div class="dropdown dropdown-with-tables">
                                <button class="btn btn-link text-primary" type="button" data-bs-toggle="dropdown"
                                  aria-expanded="false" data-bs-auto-close="outside"
                                  data-bs-popper-config='{"strategy":"fixed"}'>
                                  View more
                                </button>
                                <div class="dropdown-menu">
                                  <div class="table-responsive">
                                    <table class="table-theme table-hover table table-bordered table-sticky ">
                                      <thead class="border-less">
                                        <tr>
                                          <th>Supplier Name</th>
                                          <th>Carton Qty</th>
                                          <th>Qty/Carton</th>
                                          <th>Total Qty</th>
                                          <th>Price</th>
                                          <th>Dimension</th>
                                        </tr>
                                      </thead>
                                      <tbody>
                                        <tr *ngFor="let item of [1,2,3,4,5]">
                                          <td class="tbl-user">
                                            <div class="tbl-user-checkbox-srno">
                                              <div class="tbl-user-wrapper">
                                                <div class="tbl-user-image">
                                                  <i class="th th-outline-user"></i>
                                                </div>
                                                <div class="tbl-user-text">
                                                  <p>Alpeshbhai</p>
                                                  <span>alpeshbhai56&#64;gmail.com</span>
                                                </div>
                                              </div>
                                            </div>
                                          </td>
                                          <td>15</td>
                                          <td>20</td>
                                          <td>300</td>
                                          <td>85</td>
                                          <td>20 x 30 x 49</td>
                                        </tr>
                                      </tbody>
                                    </table>
                                  </div>
                                </div>
                              </div>
                            </td>
                            <td>
                              <div class="d-flex align-items-center">
                                <div class="dropdown dropdown-with-tables">
                                  <button class="btn btn-link text-primary" type="button" data-bs-toggle="dropdown"
                                    aria-expanded="false" data-bs-auto-close="outside"
                                    data-bs-popper-config='{"strategy":"fixed"}'>
                                    Amish, 45
                                  </button>
                                  <div class="dropdown-menu">
                                    <div class="table-responsive">
                                      <table class="table-theme table-hover table table-bordered table-sticky ">
                                        <thead class="border-less">
                                          <tr>
                                            <th>Supplier Name</th>
                                            <th>Qty/Carton</th>
                                            <th>Last Purchased on</th>
                                            <th>Price</th>
                                          </tr>
                                        </thead>
                                        <tbody>
                                          <tr *ngFor="let item of [1,2,3,4,5]">
                                            <td class="tbl-user">
                                              <div class="tbl-user-checkbox-srno">
                                                <div class="tbl-user-wrapper">
                                                  <div class="tbl-user-image">
                                                    <i class="th th-outline-user"></i>
                                                  </div>
                                                  <div class="tbl-user-text">
                                                    <p>Alpeshbhai</p>
                                                    <span>alpeshbhai56&#64;gmail.com</span>
                                                  </div>
                                                </div>
                                              </div>
                                            </td>
                                            <td>15</td>
                                            <td>12/05/2024</td>
                                            <td>85</td>
                                          </tr>
                                        </tbody>
                                      </table>
                                    </div>
                                  </div>
                                </div>
                                <i class="th th-outline-info-circle ms-auto text-danger"
                                  ngbTooltip="Other Supplier Price	" placement="bottom" container="body"
                                  triggers="hover"></i>
                              </div>
                            </td>
                            <td class="tbl-form-group-borderless">
                              <div class="form-group theme-ngselect">
                                <ng-select placeholder="Order Type" [multiple]="false" [clearable]="false"
                                  [items]="demo" bindLabel="name" bindValue="id" [(ngModel)]="selectedDemo1"
                                  appendTo="body">
                                </ng-select>
                              </div>
                            </td>
                            <td>5</td>
                            <td>10</td>
                            <td>50</td>
                            <td>100</td>
                            <td>5000</td>
                            <td>18</td>
                            <td>900</td>
                            <td>5900</td>
                            <td>kale mokalshe-gujrat transport ma</td>
                            <td>3 [15]</td>
                            <td>2.1</td>
                            <td class="tbl-form-group-borderless">
                              <div class="form-group theme-ngselect">
                                <ng-select placeholder="Market Type" [multiple]="false" [clearable]="false"
                                  [items]="demo" bindLabel="name" bindValue="id" [(ngModel)]="selectedDemo1"
                                  appendTo="body">
                                </ng-select>
                              </div>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="accordion-item accordion-parent-item">
        <h2 class="accordion-header accordion-parent-header" id="accordionDoubleParent_headingTwo">
          <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
            data-bs-target="#accordionDoubleParent_CollapseTwo" aria-expanded="false"
            aria-controls="accordionDoubleParent_CollapseTwo">
            <div class="accordion-header-left ">
              <ul class="accordion-header-item">
                <li>Supplier Name: Nilishbhai</li>
                <li>Mobile No +91 98985 985452</li>
              </ul>
            </div>
            <div class="accordion-header-right">
              <button class="btn btn-xs btn-light-primary btn-icon" data-bs-toggle="modal"
                data-bs-target="#moveToTransitsModal" ngbTooltip="Tooltip" placement="bottom" container="body"
                triggers="hover"><i class="th th-outline-arrow-right-1"></i></button>
              <div class="dropdown">
                <button class="btn btn-xs btn-light-white btn-icon" id="actionDropDown" data-bs-toggle="dropdown"
                  aria-expanded="false" ngbTooltip="More Option" placement="bottom" container="body" triggers="hover">
                  <i class="th th-outline-more"></i>
                </button>
                <ul class="dropdown-menu" aria-labelledby="actionDropDown">
                  <li><a class="dropdown-item" [routerLink]="['/users/purchases/new-po-local']"><i
                        class="th th-outline-edit"></i>Edit</a></li>
                  <li><a class="dropdown-item" [routerLink]="['/users/purchases/new-po-local-expenses']"><i
                        class="th th-outline-add-circle"></i>Add Expense</a></li>
                  <li><a class="dropdown-item" href="#"><i class="bi bi-download"></i>Download PDF</a></li>
                  <li><a class="dropdown-item" href="#"><i class="th th-outline-whatsapp"></i>Send WA/Tel.</a></li>
                  <li><a class="dropdown-item" href="#"><i class="th th-outline-barcode"></i>Print Carton QR Code</a>
                  </li>
                  <li><a class="dropdown-item" [routerLink]="['/users/purchases/po-local-carton-mapping']"><i
                        class="th th-outline-box"></i>Carton Mapping</a></li>
                  <li><a class="dropdown-item" href="#"><i class="th th-outline-box-1"></i>Audit Ticket</a></li>
                  <li><a class="dropdown-item" href="#"><i class="th th-outline-dollar-circle"></i>Make Payment</a>
                  </li>
                  <li><a class="dropdown-item" href="#"><i class="th th-outline-tick-circle"></i>Mark As Received</a>
                  </li>
                  <li><a class="dropdown-item" href="#"><i class="th th-outline-close-circle"></i>Cancel Order</a>
                  </li>
                  <li>
                    <hr class="dropdown-divider">
                  </li>
                  <li><a class="dropdown-item text-danger" href="#"><i class="th th-outline-trash"></i>Delete</a></li>
                </ul>
              </div>
            </div>
          </button>
        </h2>
        <div id="accordionDoubleParent_CollapseTwo" class="accordion-collapse collapse"
          aria-labelledby="accordionDoubleParent_headingTwo" data-bs-parent="#accordionDoubleParent">
          <div class="accordion-body tbl-accordion-body">
            <div class="accordion accordion-group " id="accordionDoubleNormal_2">
              <div class="accordion-item">
                <h2 class="accordion-header" id="accordionDoubleNormal_2_HeadingOne">
                  <button class="accordion-button" type="button" data-bs-toggle="collapse"
                    data-bs-target="#accordionDoubleNormal_2_CollapseOne" aria-expanded="true"
                    aria-controls="accordionDoubleNormal_2_CollapseOne">
                    <div class="accordion-header-left ">
                      <ul class="accordion-header-item accordion-header-item-normal">
                        <li>PO ID: #458555</li>
                        <li>Date: 5/10/2024</li>
                      </ul>
                    </div>
                    <div class="accordion-header-right">
                      <button class="btn btn-xs btn-light-primary btn-icon" data-bs-toggle="modal"
                        data-bs-target="#moveToTransitsModal" ngbTooltip="Tooltip" placement="bottom" container="body"
                        triggers="hover"><i class="th th-outline-arrow-right-1"></i></button>
                      <div class="dropdown">
                        <button class="btn btn-xs btn-light-white btn-icon" id="actionDropDown"
                          data-bs-toggle="dropdown" aria-expanded="false" ngbTooltip="More Option" placement="bottom"
                          container="body" triggers="hover">
                          <i class="th th-outline-more"></i>
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="actionDropDown">
                          <li><a class="dropdown-item" [routerLink]="['/users/purchases/new-po-local']"><i
                                class="th th-outline-edit"></i>Edit</a></li>
                          <li><a class="dropdown-item" href="#"><i class="th th-outline-add-circle"></i>Add Expense</a>
                          </li>
                          <li><a class="dropdown-item" href="#"><i class="bi bi-download"></i>Download PDF</a></li>
                          <li><a class="dropdown-item" href="#"><i class="th th-outline-whatsapp"></i>Send WA/Tel.</a>
                          </li>
                          <li><a class="dropdown-item" href="#"><i class="th th-outline-barcode"></i>Print Carton QR
                              Code</a>
                          </li>
                          <li><a class="dropdown-item" [routerLink]="['/users/purchases/po-local-carton-mapping']"><i
                                class="th th-outline-box"></i>Carton Mapping</a></li>
                          <li><a class="dropdown-item" href="#"><i class="th th-outline-box-1"></i>Audit Ticket</a></li>
                          <li><a class="dropdown-item" href="#"><i class="th th-outline-dollar-circle"></i>Make
                              Payment</a>
                          </li>
                          <li><a class="dropdown-item" href="#"><i class="th th-outline-tick-circle"></i>Mark As
                              Received</a>
                          </li>
                          <li><a class="dropdown-item" href="#"><i class="th th-outline-close-circle"></i>Cancel
                              Order</a>
                          </li>
                          <li>
                            <hr class="dropdown-divider">
                          </li>
                          <li><a class="dropdown-item text-danger" href="#"><i
                                class="th th-outline-trash"></i>Delete</a></li>
                        </ul>
                      </div>
                    </div>
                  </button>
                </h2>
                <div id="accordionDoubleNormal_2_CollapseOne" class="accordion-collapse collapse show"
                  aria-labelledby="accordionDoubleNormal_2_HeadingOne" data-bs-parent="#accordionDoubleNormal_2">
                  <div class="accordion-body tbl-accordion-body">
                    <div class="table-responsive">
                      <table class="table-theme table-hover table table-bordered ">
                        <thead class="border-less">
                          <tr>
                            <th>Item Details</th>
                            <th>HSN Code</th>
                            <th>Purchase <br />Breach Qty</th>
                            <th>Available Qty</th>
                            <th>Difference <br />(Formula[B])</th>
                            <th>Sale Price/<br />PCS</th>
                            <th>Sale Price/<br />Carton</th>
                            <th>Last Sale Price <br />(Carton)</th>
                            <th>Last Purchase<br />(Purchase By/<br />CTN)</th>
                            <th>Other Supplier Price</th>
                            <th>Order Type</th>
                            <th>Cartons <br />(PO)</th>
                            <th>PCS/Carton<br />(PO)</th>
                            <th>Total Quantity<br />(PO)</th>
                            <th>Purchase <br />Price</th>
                            <th>Total Item <br />Amount</th>
                            <th>GST TAX %</th>
                            <th>GST AMT</th>
                            <th>Total Amount<br />(With TAX)</th>
                            <th>Note</th>
                            <th>Inquiry (Cus <br />Count) - (Total <br />inq. Count)</th>
                            <th>Item Rating</th>
                            <th>Market Type</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr *ngFor="let item of [1,2,3,4,5]">
                            <td class="tbl-user">
                              <div class="tbl-user-checkbox-srno">
                                <div class="checkbox checkbox-primary checkbox-small">
                                  <input type="checkbox" id="tbl-checkbox2" class="material-inputs filled-in" />
                                  <label for="tbl-checkbox2"></label>
                                </div>
                                <span>01.</span>
                                <div class="tbl-user-wrapper">
                                  <div class="tbl-user-image">
                                    <img src="assets/images/avatar.jpg" alt="valamji">
                                  </div>
                                  <div class="tbl-user-text-action">
                                    <div class="tbl-user-text">
                                      <p>Ink Pen</p>
                                      <span>SKU #58545854585</span>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </td>
                            <td>458555</td>
                            <td><a class="btn-link text-primary" data-bs-toggle="modal"
                                data-bs-target="#poStockDetailsModal">20</a></td>
                            <td>25</td>
                            <td>20</td>
                            <td>10</td>
                            <td>100</td>
                            <td>105,106,104</td>
                            <td>
                              <p class="mb-0">Kavya, 50 </p>
                              <p class="mb-0">Nirmal,45</p>
                              <p class="mb-0">Ashopalav,45</p>
                              <div class="dropdown dropdown-with-tables">
                                <button class="btn btn-link text-primary" type="button" data-bs-toggle="dropdown"
                                  aria-expanded="false" data-bs-auto-close="outside"
                                  data-bs-popper-config='{"strategy":"fixed"}'>
                                  View more
                                </button>
                                <div class="dropdown-menu">
                                  <div class="table-responsive">
                                    <table class="table-theme table-hover table table-bordered table-sticky ">
                                      <thead class="border-less">
                                        <tr>
                                          <th>Supplier Name</th>
                                          <th>Carton Qty</th>
                                          <th>Qty/Carton</th>
                                          <th>Total Qty</th>
                                          <th>Price</th>
                                          <th>Dimension</th>
                                        </tr>
                                      </thead>
                                      <tbody>
                                        <tr *ngFor="let item of [1,2,3,4,5]">
                                          <td class="tbl-user">
                                            <div class="tbl-user-checkbox-srno">
                                              <div class="tbl-user-wrapper">
                                                <div class="tbl-user-image">
                                                  <i class="th th-outline-user"></i>
                                                </div>
                                                <div class="tbl-user-text">
                                                  <p>Alpeshbhai</p>
                                                  <span>alpeshbhai56&#64;gmail.com</span>
                                                </div>
                                              </div>
                                            </div>
                                          </td>
                                          <td>15</td>
                                          <td>20</td>
                                          <td>300</td>
                                          <td>85</td>
                                          <td>20 x 30 x 49</td>
                                        </tr>
                                      </tbody>
                                    </table>
                                  </div>
                                </div>
                              </div>
                            </td>
                            <td>
                              <div class="d-flex align-items-center">
                                <div class="dropdown dropdown-with-tables">
                                  <button class="btn btn-link text-primary" type="button" data-bs-toggle="dropdown"
                                    aria-expanded="false" data-bs-auto-close="outside"
                                    data-bs-popper-config='{"strategy":"fixed"}'>
                                    Amish, 45
                                  </button>
                                  <div class="dropdown-menu">
                                    <div class="table-responsive">
                                      <table class="table-theme table-hover table table-bordered table-sticky ">
                                        <thead class="border-less">
                                          <tr>
                                            <th>Supplier Name</th>
                                            <th>Qty/Carton</th>
                                            <th>Last Purchased on</th>
                                            <th>Price</th>
                                          </tr>
                                        </thead>
                                        <tbody>
                                          <tr *ngFor="let item of [1,2,3,4,5]">
                                            <td class="tbl-user">
                                              <div class="tbl-user-checkbox-srno">
                                                <div class="tbl-user-wrapper">
                                                  <div class="tbl-user-image">
                                                    <i class="th th-outline-user"></i>
                                                  </div>
                                                  <div class="tbl-user-text">
                                                    <p>Alpeshbhai</p>
                                                    <span>alpeshbhai56&#64;gmail.com</span>
                                                  </div>
                                                </div>
                                              </div>
                                            </td>
                                            <td>15</td>
                                            <td>12/05/2024</td>
                                            <td>85</td>
                                          </tr>
                                        </tbody>
                                      </table>
                                    </div>
                                  </div>
                                </div>
                                <i class="th th-outline-info-circle ms-auto text-danger"
                                  ngbTooltip="Other Supplier Price	" placement="bottom" container="body"
                                  triggers="hover"></i>
                              </div>
                            </td>
                            <td class="tbl-form-group-borderless">
                              <div class="form-group theme-ngselect">
                                <ng-select placeholder="Order Type" [multiple]="false" [clearable]="false"
                                  [items]="demo" bindLabel="name" bindValue="id" [(ngModel)]="selectedDemo1"
                                  appendTo="body">
                                </ng-select>
                              </div>
                            </td>
                            <td>5</td>
                            <td>10</td>
                            <td>50</td>
                            <td>100</td>
                            <td>5000</td>
                            <td>18</td>
                            <td>900</td>
                            <td>5900</td>
                            <td>kale mokalshe-gujrat transport ma</td>
                            <td>3 [15]</td>
                            <td>2.1</td>
                            <td class="tbl-form-group-borderless">
                              <div class="form-group theme-ngselect">
                                <ng-select placeholder="Market Type" [multiple]="false" [clearable]="false"
                                  [items]="demo" bindLabel="name" bindValue="id" [(ngModel)]="selectedDemo1"
                                  appendTo="body">
                                </ng-select>
                              </div>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
              <div class="accordion-item">
                <h2 class="accordion-header" id="accordionDoubleNormal_2_HeadingTwo">
                  <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#accordionDoubleNormal_2_CollapseTwo" aria-expanded="false"
                    aria-controls="accordionDoubleNormal_2_CollapseTwo">
                    <div class="accordion-header-left ">
                      <ul class="accordion-header-item accordion-header-item-normal">
                        <li>PO ID: #458555</li>
                        <li>Date: 5/10/2024</li>
                      </ul>
                    </div>
                    <div class="accordion-header-right">
                      <button class="btn btn-xs btn-light-primary btn-icon" data-bs-toggle="modal"
                        data-bs-target="#moveToTransitsModal"><i class="th th-outline-arrow-right-1"></i></button>
                      <div class="dropdown">
                        <button class="btn btn-xs btn-light-white btn-icon" id="actionDropDown"
                          data-bs-toggle="dropdown" aria-expanded="false">
                          <i class="th th-outline-more"></i>
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="actionDropDown">
                          <li><a class="dropdown-item" [routerLink]="['/users/purchases/new-po-local']"><i
                                class="th th-outline-edit"></i>Edit</a></li>
                          <li><a class="dropdown-item" href="#"><i class="th th-outline-add-circle"></i>Add Expense</a>
                          </li>
                          <li><a class="dropdown-item" href="#"><i class="bi bi-download"></i>Download PDF</a></li>
                          <li><a class="dropdown-item" href="#"><i class="th th-outline-whatsapp"></i>Send WA/Tel.</a>
                          </li>
                          <li><a class="dropdown-item" href="#"><i class="th th-outline-barcode"></i>Print Carton QR
                              Code</a>
                          </li>
                          <li><a class="dropdown-item" [routerLink]="['/users/purchases/po-local-carton-mapping']"><i
                                class="th th-outline-box"></i>Carton Mapping</a></li>
                          <li><a class="dropdown-item" href="#"><i class="th th-outline-box-1"></i>Audit Ticket</a></li>
                          <li><a class="dropdown-item" href="#"><i class="th th-outline-dollar-circle"></i>Make
                              Payment</a>
                          </li>
                          <li><a class="dropdown-item" href="#"><i class="th th-outline-tick-circle"></i>Mark As
                              Received</a>
                          </li>
                          <li><a class="dropdown-item" href="#"><i class="th th-outline-close-circle"></i>Cancel
                              Order</a>
                          </li>
                          <li>
                            <hr class="dropdown-divider">
                          </li>
                          <li><a class="dropdown-item text-danger" href="#"><i
                                class="th th-outline-trash"></i>Delete</a></li>
                        </ul>
                      </div>
                    </div>
                  </button>
                </h2>
                <div id="accordionDoubleNormal_2_CollapseTwo" class="accordion-collapse collapse"
                  aria-labelledby="accordionDoubleNormal_2_HeadingTwo" data-bs-parent="#accordionDoubleNormal_2">
                  <div class="accordion-body tbl-accordion-body">
                    <div class="table-responsive">
                      <table class="table-theme table-hover table table-bordered ">
                        <thead class="border-less">
                          <tr>
                            <th>Item Details</th>
                            <th>HSN Code</th>
                            <th>Purchase <br />Breach Qty</th>
                            <th>Available Qty</th>
                            <th>Difference <br />(Formula[B])</th>
                            <th>Sale Price/<br />PCS</th>
                            <th>Sale Price/<br />Carton</th>
                            <th>Last Sale Price <br />(Carton)</th>
                            <th>Last Purchase<br />(Purchase By/<br />CTN)</th>
                            <th>Other Supplier Price</th>
                            <th>Order Type</th>
                            <th>Cartons <br />(PO)</th>
                            <th>PCS/Carton<br />(PO)</th>
                            <th>Total Quantity<br />(PO)</th>
                            <th>Purchase <br />Price</th>
                            <th>Total Item <br />Amount</th>
                            <th>GST TAX %</th>
                            <th>GST AMT</th>
                            <th>Total Amount<br />(With TAX)</th>
                            <th>Note</th>
                            <th>Inquiry (Cus <br />Count) - (Total <br />inq. Count)</th>
                            <th>Item Rating</th>
                            <th>Market Type</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr *ngFor="let item of [1,2,3,4,5]">
                            <td class="tbl-user">
                              <div class="tbl-user-checkbox-srno">
                                <div class="checkbox checkbox-primary checkbox-small">
                                  <input type="checkbox" id="tbl-checkbox2" class="material-inputs filled-in" />
                                  <label for="tbl-checkbox2"></label>
                                </div>
                                <span>01.</span>
                                <div class="tbl-user-wrapper">
                                  <div class="tbl-user-image">
                                    <img src="assets/images/avatar.jpg" alt="valamji">
                                  </div>
                                  <div class="tbl-user-text-action">
                                    <div class="tbl-user-text">
                                      <p>Ink Pen</p>
                                      <span>SKU #58545854585</span>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </td>
                            <td>458555</td>
                            <td>
                              <a class="btn-link text-primary" data-bs-toggle="modal"
                                data-bs-target="#poStockDetailsModal">20</a>
                            </td>
                            <td>25</td>
                            <td>20</td>
                            <td>10</td>
                            <td>100</td>
                            <td>105,106,104</td>
                            <td>
                              <p class="mb-0">Kavya, 50 </p>
                              <p class="mb-0">Nirmal,45</p>
                              <p class="mb-0">Ashopalav,45</p>
                              <div class="dropdown dropdown-with-tables">
                                <button class="btn btn-link text-primary" type="button" data-bs-toggle="dropdown"
                                  aria-expanded="false" data-bs-auto-close="outside"
                                  data-bs-popper-config='{"strategy":"fixed"}'>
                                  View more
                                </button>
                                <div class="dropdown-menu">
                                  <div class="table-responsive">
                                    <table class="table-theme table-hover table table-bordered table-sticky ">
                                      <thead class="border-less">
                                        <tr>
                                          <th>Supplier Name</th>
                                          <th>Carton Qty</th>
                                          <th>Qty/Carton</th>
                                          <th>Total Qty</th>
                                          <th>Price</th>
                                          <th>Dimension</th>
                                        </tr>
                                      </thead>
                                      <tbody>
                                        <tr *ngFor="let item of [1,2,3,4,5]">
                                          <td class="tbl-user">
                                            <div class="tbl-user-checkbox-srno">
                                              <div class="tbl-user-wrapper">
                                                <div class="tbl-user-image">
                                                  <i class="th th-outline-user"></i>
                                                </div>
                                                <div class="tbl-user-text">
                                                  <p>Alpeshbhai</p>
                                                  <span>alpeshbhai56&#64;gmail.com</span>
                                                </div>
                                              </div>
                                            </div>
                                          </td>
                                          <td>15</td>
                                          <td>20</td>
                                          <td>300</td>
                                          <td>85</td>
                                          <td>20 x 30 x 49</td>
                                        </tr>
                                      </tbody>
                                    </table>
                                  </div>
                                </div>
                              </div>
                            </td>
                            <td>
                              <div class="d-flex align-items-center">
                                <div class="dropdown dropdown-with-tables">
                                  <button class="btn btn-link text-primary" type="button" data-bs-toggle="dropdown"
                                    aria-expanded="false" data-bs-auto-close="outside"
                                    data-bs-popper-config='{"strategy":"fixed"}'>
                                    Amish, 45
                                  </button>
                                  <div class="dropdown-menu">
                                    <div class="table-responsive">
                                      <table class="table-theme table-hover table table-bordered table-sticky ">
                                        <thead class="border-less">
                                          <tr>
                                            <th>Supplier Name</th>
                                            <th>Qty/Carton</th>
                                            <th>Last Purchased on</th>
                                            <th>Price</th>
                                          </tr>
                                        </thead>
                                        <tbody>
                                          <tr *ngFor="let item of [1,2,3,4,5]">
                                            <td class="tbl-user">
                                              <div class="tbl-user-checkbox-srno">
                                                <div class="tbl-user-wrapper">
                                                  <div class="tbl-user-image">
                                                    <i class="th th-outline-user"></i>
                                                  </div>
                                                  <div class="tbl-user-text">
                                                    <p>Alpeshbhai</p>
                                                    <span>alpeshbhai56&#64;gmail.com</span>
                                                  </div>
                                                </div>
                                              </div>
                                            </td>
                                            <td>15</td>
                                            <td>12/05/2024</td>
                                            <td>85</td>
                                          </tr>
                                        </tbody>
                                      </table>
                                    </div>
                                  </div>
                                </div>
                                <i class="th th-outline-info-circle ms-auto text-danger"
                                  ngbTooltip="Other Supplier Price	" placement="bottom" container="body"
                                  triggers="hover"></i>
                              </div>
                            </td>
                            <td class="tbl-form-group-borderless">
                              <div class="form-group theme-ngselect">
                                <ng-select placeholder="Order Type" [multiple]="false" [clearable]="false"
                                  [items]="demo" bindLabel="name" bindValue="id" [(ngModel)]="selectedDemo1"
                                  appendTo="body">
                                </ng-select>
                              </div>
                            </td>
                            <td>5</td>
                            <td>10</td>
                            <td>50</td>
                            <td>100</td>
                            <td>5000</td>
                            <td>18</td>
                            <td>900</td>
                            <td>5900</td>
                            <td>kale mokalshe-gujrat transport ma</td>
                            <td>3 [15]</td>
                            <td>2.1</td>
                            <td class="tbl-form-group-borderless">
                              <div class="form-group theme-ngselect">
                                <ng-select placeholder="Market Type" [multiple]="false" [clearable]="false"
                                  [items]="demo" bindLabel="name" bindValue="id" [(ngModel)]="selectedDemo1"
                                  appendTo="body">
                                </ng-select>
                              </div>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="paginationbox pagination-fixed">
  <app-pagination></app-pagination>
</div>

<!-- ----------------------------------------------------------------------- -->
<!--                 Move to Transits Forms Modal Start                 -->
<!-- ----------------------------------------------------------------------- -->
<div class="modal modal-theme fade" id="moveToTransitsModal" tabindex="-1" aria-labelledby="moveToTransitsModalLabel"
  aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="moveToTransitsModalLabel">Move to Transits</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
            class='th th-close'></i></button>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-12">
            <div class="form-group">
              <label class="form-label">Bus Name</label>
              <input type="text" class="form-control" placeholder="Enter Bus Name">
            </div>
          </div>
          <div class="col-6">
            <div class="form-group">
              <label class="form-label">Bus No</label>
              <input type="text" class="form-control" placeholder="Enter Bus No">
            </div>
          </div>
          <div class="col-6">
            <div class="form-group">
              <label class="form-label">Conductor No</label>
              <input type="text" class="form-control" placeholder="Enter Conductor No">
            </div>
          </div>
          <div class="col-6">
            <div class="form-group">
              <label class="form-label">LR No</label>
              <input type="text" class="form-control" placeholder="Enter LR No">
            </div>
          </div>
          <div class="col-6">
            <div class="form-group">
              <label class="form-label">No of carton</label>
              <input type="text" class="form-control" placeholder="Enter No of carton">
            </div>
          </div>
          <div class="col-6">
            <div class="form-group">
              <label class="form-label">Upload LR</label>
              <button class='btn btn-fileupload btn-fileupload-white'> <i class="bi bi-upload"></i>
                Upload LR
                <input type="file" ref={imageRef} accept="image/x-png,image/jpeg,image/jpg">
              </button>
            </div>
          </div>
          <div class="col-6">
            <div class="form-group">
              <label class="form-label">LR Copy Date</label>
              <div class="form-control-wrapper">
                <div class="form-group-icon-end">
                  <i class="th th-outline-calendar"></i>
                  <input type="text" class="form-control" placeholder="Select Date">
                </div>
              </div>
            </div>
          </div>
          <div class="col-12">
            <div class="form-group">
              <label class="form-label">No of delivery days</label>
              <input type="text" class="form-control" placeholder="Enter No of delivery days">
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <div class="modal-footer-group full-width-btn">
          <button type="button" class="btn btn-primary btn-icon-text"> <i class="th th-outline-tick-circle"></i>
            Save</button>
          <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                  Move to Transits Forms Modal End                  -->
<!-- ----------------------------------------------------------------------- -->