import { Component, OnInit, inject, ViewChild, Input, EventEmitter, Output } from '@angular/core';
import moment from 'moment';
import { DaterangepickerDirective } from 'ngx-daterangepicker-material';
import { POImportItem } from 'src/app/models/POImportItem';
import { POImportList } from 'src/app/models/POImportList';
import { POImportPagination } from 'src/app/models/request/POImportPagination';
import { UtilsService } from 'src/app/shared/services/utils.service';

@Component({
  selector: 'app-po-import-grn',
  templateUrl: './po-import-grn.component.html',
  styleUrls: ['./po-import-grn.component.scss']
})
export class PoImportGrnComponent implements OnInit {

  utilsService = inject(UtilsService);

  @ViewChild(DaterangepickerDirective) pickerDirective: DaterangepickerDirective;

  enumForContainerStatus = this.utilsService.poImportStatus

  @Input({ alias: 'selectedTab', required: true }) selectedTab: string;
  @Input({ alias: 'headerObj', required: true }) headerObj: any;
  @Input({ alias: 'columnArr', required: true }) columnArr: any;
  @Input({ alias: 'allHeaderArr', required: true }) allHeaderArr: any;

  @Input({ alias: 'poImportList', required: true }) poImportList: POImportList[];
  @Input({ alias: 'dropdown', required: true }) dropdown: any;
  @Input({ alias: 'paginationRequest', required: true }) paginationRequest: POImportPagination;

  @Output() getAllPOImports: EventEmitter<any> = new EventEmitter<any>();
  @Output() checkIfAllSelected: EventEmitter<any> = new EventEmitter<any>();
  @Output() saveCol: EventEmitter<any> = new EventEmitter<any>();
  @Output() onCollapse: EventEmitter<any> = new EventEmitter<any>();
  @Output() onClear: EventEmitter<any> = new EventEmitter<any>();
  @Output() redirectToDetails: EventEmitter<any> = new EventEmitter<any>();

  constructor() { }

  ngOnInit(): void {
  }

  onChangeImporter() {
    this.getAllPOImports.emit();
  }

  //track By/ Toggle Expand
  trackBy(index: number, name: POImportList): number {
    return name.id;
  }

  trackByChild(index: number, name: POImportItem): number {
    return name.id;
  }

  //Pagination
  addPageSizeData(event) {
    this.paginationRequest.pageNo = 1;
    this.paginationRequest.pageSize = event;
    this.getAllPOImports.emit();
  }

  pageNumber(event) {
    this.paginationRequest.pageNo = event
    this.getAllPOImports.emit();
  }

  //Date Ranges
  open(): void {
    if (!this.pickerDirective.picker.isShown) {
      this.pickerDirective.open();
    } else this.pickerDirective.hide()
  }

  //collapse
  toggleExpand(index: number): void {
    this.onCollapse.emit(index)
  }

  //Search
  onSearch = () => {
    this.getAllPOImports.emit();
  }

  //Date picker
  onChangeDate = (type: string) => {
    switch (type) {
      case 'delivery':
        let date = moment(`${this.paginationRequest.temp_expectedDeliveryDate.year}-${this.paginationRequest.temp_expectedDeliveryDate.month}-
          ${this.paginationRequest.temp_expectedDeliveryDate.day}`
          , 'YYYY-MM-DD');
        this.paginationRequest.expectedDeliveryDate = date.format('YYYY-MM-DD');
        this.getAllPOImports.emit()
        break;
      case 'loaded':
        let dateA = moment(`${this.paginationRequest.temp_loadedDate.year}-${this.paginationRequest.temp_loadedDate.month}-
          ${this.paginationRequest.temp_loadedDate.day}`
          , 'YYYY-MM-DD');
        this.paginationRequest.loadedDate = dateA.format('YYYY-MM-DD');
        this.getAllPOImports.emit()
        break;
      default:
        break;
    }
  }

  // Date range clear
  onClearDate() {
    this.pickerDirective?.clear()
  }


}
