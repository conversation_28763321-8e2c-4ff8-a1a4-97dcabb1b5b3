import { deserializeAs, serializeAs } from 'cerialize';

export class RegistrationOtherDetails {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('panNo')
    @deserializeAs('panNo')
    private _panNo: string;

    @serializeAs('businessRegNo')
    @deserializeAs('businessRegNo')
    private _businessRegNo: string;

    @serializeAs('paymentTermId')
    @deserializeAs('paymentTermId')
    private _paymentTermId: number;

    @serializeAs('websiteUrl')
    @deserializeAs('websiteUrl')
    private _websiteUrl: string;

    @serializeAs('facebook')
    @deserializeAs('facebook')
    private _facebook: string;

    @serializeAs('instagram')
    @deserializeAs('instagram')
    private _instagram: string;

    @serializeAs('twitter')
    @deserializeAs('twitter')
    private _twitter: string;

    constructor() {

    }


    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Getter panNo
     * @return {string}
     */
	public get panNo(): string {
		return this._panNo;
	}

    /**
     * Getter businessRegNo
     * @return {string}
     */
	public get businessRegNo(): string {
		return this._businessRegNo;
	}

    /**
     * Getter paymentTermId
     * @return {number}
     */
	public get paymentTermId(): number {
		return this._paymentTermId;
	}

    /**
     * Getter websiteUrl
     * @return {string}
     */
	public get websiteUrl(): string {
		return this._websiteUrl;
	}

    /**
     * Getter facebook
     * @return {string}
     */
	public get facebook(): string {
		return this._facebook;
	}

    /**
     * Getter instagram
     * @return {string}
     */
	public get instagram(): string {
		return this._instagram;
	}

    /**
     * Getter twitter
     * @return {string}
     */
	public get twitter(): string {
		return this._twitter;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Setter panNo
     * @param {string} value
     */
	public set panNo(value: string) {
		this._panNo = value;
	}

    /**
     * Setter businessRegNo
     * @param {string} value
     */
	public set businessRegNo(value: string) {
		this._businessRegNo = value;
	}

    /**
     * Setter paymentTermId
     * @param {number} value
     */
	public set paymentTermId(value: number) {
		this._paymentTermId = value;
	}

    /**
     * Setter websiteUrl
     * @param {string} value
     */
	public set websiteUrl(value: string) {
		this._websiteUrl = value;
	}

    /**
     * Setter facebook
     * @param {string} value
     */
	public set facebook(value: string) {
		this._facebook = value;
	}

    /**
     * Setter instagram
     * @param {string} value
     */
	public set instagram(value: string) {
		this._instagram = value;
	}

    /**
     * Setter twitter
     * @param {string} value
     */
	public set twitter(value: string) {
		this._twitter = value;
	}

}