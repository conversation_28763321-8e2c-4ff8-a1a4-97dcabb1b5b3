import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class ValidationService {

  /** Maximum length of 30 */
  MAX_30 = 30;
  /** Maximum length of 1000 */
  MAX_1000 = 1000;
  /** Maximum length of 2000 */
  MAX_2000 = 2000;

  /** Maximum length of 200 */
  MAX_150 = 150;

  /** Maximum length of 200 */
  MAX_200 = 200;
  MAX_250 = 250;

  /** Maximum length of 100 */
  MAX_100 = 100;

  /** Maximum length of 60 */
  MAX_60 = 60;

  /** Maximum length of 20 */
  MAX_20 = 20;

  /** Maximum length of 10 */
  MAX_10 = 10;

  /** Maximum length of 15 */
  MAX_15 = 15;

  /** Maximum length of 4 */
  MAX_4 = 4;

  /** Maximum length of 5 */
  MAX_5 = 5;

  /** Maximum length of 3 */
  MAX_3 = 3;

  /** Maximum length of 32 */
  MAX_32 = 32;

  /** Maximum length of 50 */
  MAX_50 = 50;

  /** Maximum length of 300 */
  MAX_300 = 300;

  /** Maximum length of 500 */
  MAX_500 = 500;

  /** Maximum length of 13 */
  MAX_13 = 13;

  /** Maximum length of 6 */
  MAX_6 = 6;

  /** Maximum length of 7 */
  MAX_7 = 7;

  /** Maximum length of 8 */
  MAX_8 = 8;

  /** Maximum length of 11 */
  MAX_11 = 11;

  /** Maximum length of 12 */
  MAX_12 = 12;

  /** Maximum length of 25 */
  MAX_25 = 25;

  /** Maximum length of 45 */
  MAX_45 = 45;

  /** Maximum length of 40 */
  MAX_40 = 40;

  /** Maximum length of 32 */
  PASSWORD = 32;

  /* Pattern use for Validation */

  NAME = /^([a-zA-Z][a-zA-Z\s]*)$/;
  STATE_NAME = /^([a-zA-Z][a-zA-Z\s]*)$/;
  CITY_NAME = /^([a-zA-Z][a-zA-Z\s]*)$/;
  LANGUAGE_NAME = /^([a-zA-Z][a-zA-Z\s]*)$/;
  ONLY_NUMBERS = '^[0-9]*$';
  // ONLY_NUMBERS_AND_DOT = /^[0-9.]{1,15}$/;
  ONLY_NUMBERS_AND_DOT = /^[0-9]+(?:\.[0-9]+)?$/;
  PATTERN_FOR_ALPHABATES_AND_SPACE_AND_DASH_DIGIT = '^[a-zA-Z0-9- ]*$';
  PATTERN_FOR_DASH_DIGIT = '^[0-9.-]*$';
  PATTERN_FOR_ALPHABATES_AND_SPACE_AND_DASH = '^[a-zA-Z- ]*$';
  // PATTERN_FOR_ALPHABATES_AND_SPACE_AND_ROUND_BRACKETS = '^[a-zA-Z() ]*$';
  PATTERN_FOR_ALPHABATES_AND_SPACE_AND_ROUND_BRACKETS = '^([a-zA-Z()][a-zA-Z() ]*)';
  LAB_MASTER_NAME = /.*\S.*/;
  // ONLY_SPACE_NOT_ALLOW = /^\S*$/;
  ONLY_SPACE_NOT_ALLOW = /.*\S.*/;
  ONLY_PHONE_CODE = /^\+(\d{1}\-)?(\d{1,10})$/
  ONLY_GREATER_THAN_ZERO = /^(?=.*[1-9])\d*\.?\d*$/

  MAX_365_NO_REQ = /^(0|[1-9][0-9]?|[1-2][0-9]{2}|3[0-5][0-9]|36[0-5])$/
  LEADING_SPACE_NOT_ALLOW = /^([a-zA-Z0-9][a-zA-Z0-9\s]*)/;
  PATTERN_FOR_USER_ROLE_NAME = /^([a-zA-Z][a-zA-Z[\_\]\s]*)$/; // added by Dhrumin
  PATTERN_FOR_PREFIX = '^([a-zA-Z0-9][a-zA-Z0-9\s!@#$%^&*()-_]*)';
  STARTING_WITH_ALPHABATES_AND_DIGIT = /^(\w|\d)([A-z\d_@.#$=!%^)(\]:\*;\?\/\,}{ '\|<>\[&\+-]*)$/; // added by ashish
  PATTERN_FOR_ALPHANUMERIC_DIGIT_SPECIAL_ESCAPE_CHARS = /.*\S.*/;
  PATTERN_FOR_ONLY_ALPHABATES_SPACE = '^[a-zA-Z ]*$';
  PATTERN_FOR_ONLY_ALPHABATES = '^[a-zA-Z]*$';
  PATTERN_FOR_ALPHABATES_AND_SPACE_AND_DIGIT = '^[a-zA-Z0-9 ]*$';
  PATTERN_FOR_ALPHABATES_AND_DIGIT = '^[a-zA-Z0-9]*$';
  PATTERN_FOR_ALPHABATES_AND_SPACE = '^([a-zA-Z][a-zA-Z ]*)$';
  PATTERN_FOR_ALPHABATES_AND_ORG_NAME = '^([a-zA-Z][a-zA-Z .&$@]*)$';
  PATTERN_FOR_ALPHABATES_NUMBER_AND_SPACE = '^([a-zA-Z0-9][a-zA-Z0-9 ]*)';
  PATTERN_FOR_ALPHABATES_NUMBER_AND_SPACE_DOT = '^([a-zA-Z0-9][a-zA-Z0-9 .]*)';
  PATTERN_FOR_ALPHABTETS_WITH_SPECIAL_CHAR = '^[a-zA-Z][a-zA-Z(_#@&) /-]*$'
  PATTERN_FOR_ALPHANUMERIC_WITH_SPECIAL_CHAR = "^(?![0-9]+$)[a-zA-Z0-9][a-zA-Z0-9(_#@&$!+):%.,' /-]*$"
  PATTERN_FOR_ALPHABTETS_WITH_CHARACTERS = '^[a-zA-Z0-9][a-zA-Z0-9_#@&., ]*$'
  PATTERN_FOR_DIGIT_SEMICOLON = '^[0-9.:]*$';
  PATTERN_FOR_ONLY_NUMBERS_NOT_ALLOW = '^(?![0-9]+$)[a-zA-Z0-9 ]{2,}$'

  PATTERN_FOR_EMAIL = '[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,50}'; // change by ashish
  PATTERN_FOR_WEBSITE_URL = /^(?:(?:https?|ftp):\/\/)?(?:www\.)?[a-z0-9-]+(?:\.[a-z0-9-]+)+[^\s]*$/i
  PATTERN_FOR_EMAIL_OR_PHONE_NO = '^(?:([a-zA-Z0-9._%+-]+@[A-Za-z0-9.-]+\.[a-z]{2,3})|([0-9]{10,13}))$';
  PATTERN_FOR_EMAIL_OR_USERNAME = '^(?:([a-zA-Z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,3})|([a-zA-Z0-9]*))$';
  PATTERN_FOR_PHONE_NO = '^[0-9]{8,15}$';
  PATTERN_FOR_PHONE_CODE = '^[0-9 +-]{7,15}$';
  PATTERN_FOR_LANDLINE_NEW = '^[a-zA-Z0-9 +-]{7,15}$';
  PATTERN_FOR_NUMBER = '^[0-9]*$';
  PATTERN_FOR_DECIMAL_NUMBER = '^[0-9.]*$';
  PATTERN_FOR_PINCODE = '^[0-9]{6}$';
  PATTERN_FOR_LANDLINE_NO = '^[0-9]{5,11}$';
  PATTERN_FOR_ADHARCARD_NO = '^[0-9]{12}$';
  PATTERN_FOR_HSN_CODE = '^[0-9]{8}$';
  PATTERN_FOR_SAC_CODE = '^[0-9]{6}$';
  PATTERN_FOR_PANCARD_NO = '^[A-Za-z]{5}[0-9]{4}[A-Za-z]{1}$';
  PATTERN_FOR_NOTARY_CERTIFICATE_NUMBER = /[A-Z]{2}\d{4}[\/]\d{4}/;
  PATTERN_FOR_DRIVING_LICENCE_NUMBER = /[A-Z]{2}\d{13}/;
  PATTERN_FOR_COLOR_HEX = /^#([0-9a-f]{3}){1,2}$/i;

  PATTERN_FOR_NAME_WITH_COMA_AND_SPACE = /^([a-zA-Z][a-zA-Z, ]*)$/;
  PATTERN_FOR_NAME_WITH_DASH_SLASH_AND_SPACE = /^([a-zA-Z][a-zA-Z-/ ]*)$/;
  PATTERN_FOR_NAME_WITH_COMA_CIRCLE_BRACKETS = /^([a-zA-Z][a-zA-Z,() ]*)$/;
  PATTERN_FOR_NAME_WITH_EMPERSON_AND_SPACE = /^([a-zA-Z][a-zA-Z& ]*)$/;

  PATTERN_FOR_PASSWORD_ONE_CHAR: RegExp = /^(?=[^A-Z]*[A-Z])(?=[^a-z]*[a-z])(?=\D*\d)(?=.*[$#@!%&*?]).{8,}$/;
  PATTERN_FOR_PASSWORD = /^(?=.*[A-Z])(?=.*\d)(?=.*[$@$!%*#?&])[A-Za-z\d$@$!%*#?&]{6,32}$/;
  PATTERN_FOR_BAR_COUNCIL_NO = /[a-zA-Z]{2}[\/]\d[0-9]{0,5}[\/][0-9]{4}/;
  PATTERN_FOR_GST_NO = /\d{2}[A-Z]{5}\d{4}[A-Z]{1}\d[Z]{1}[A-Z\d]{1}/;
  PATTERN_FOR_NUMBER_PLATE = '^[a-zA-Z]{2}[ -][0-9]{1,2}(?: [a-zA-Z])?(?: [a-zA-Z]*)? [0-9]{4}$';
  PATTERN_NOT_ALLOW_SPACE = /^\S*$/;
  PATTERN_FOR_DATE = /^(?:(?:31(\/|-|\.)(?:0?[13578]|1[02]))\1|(?:(?:29|30)(\/|-|\.)(?:0?[13-9]|1[0-2])\2))(?:(?:1[6-9]|[2-9]\d)?\d{2})$|^(?:29(\/|-|\.)0?2\3(?:(?:(?:1[6-9]|[2-9]\d)?(?:0[48]|[2468][048]|[13579][26])|(?:(?:16|[2468][048]|[3579][26])00))))$|^(?:0?[1-9]|1\d|2[0-8])(\/|-|\.)(?:(?:0?[1-9])|(?:1[0-2]))\4(?:(?:1[6-9]|[2-9]\d)?\d{2})$/;
  /* End pattern use for validation */

  /* Validation Messages */

  FILE_INFO = "File size should be less than 5 MB. Only JPG, JPEG, PNG and PDF formats are allowed.";
  PROFILE_INFO = "File size should be less than 5 MB. Only JPG, JPEG, and PNG formats are allowed.";
  DOC_INFO = "File size should be less than 5 MB. Only JPG, JPEG, PNG, Excel, PDF, and CSV formats are allowed.";
  DOC_INFO_IMG_VID = "File size should be less than 5 MB. Only JPG, JPEG, PNG, MP4 formats are allowed.";

  /** Indicates that an email address is necessary. */
  EMAIL_REQUIRED = 'Email is required!';

  /** Indicates that the entered email is not in a valid format. */
  EMAIL_INVALID = 'Please enter valid Email.';

  /** Indicates that a phone number is necessary. */
  PHONE_NUMBER_REQUIRED = 'Phone Number is required!';

  PHONE_NO_EXTENSION_REQ = 'Phone No. Extension is required!';

  /** Indicates that the entered phone number is not in a valid format. */
  PHONE_NUMBER_INVALID = 'Please enter valid Phone Number.';

  CONTACT_REQUIRED = 'Contact No. is required!';
  CONTACT_INVALID = 'Please enter valid Contact No.';

  /** Indicates that a mobile number is necessary. */
  MOBILE_NUMBER_REQUIRED = 'Mobile No. is required!';

  /** Indicates that the entered mobile number is not in a valid format. */
  MOBILE_NUMBER_INVALID = 'Please enter valid Mobile No.';

  /** Indicates that a password is necessary. */
  PASSWORD_REQUIRED = 'Password is required!';

  /** Indicates that the current password is necessary. */
  CURRENT_PASSWORD_REQUIRED = 'Current Password is required!';

  /** Indicates that the old password is necessary. */
  OLD_PASSWORD_REQUIRED = 'Old Password is required!';

  /** Indicates that a new password is necessary. */
  NEW_PASSWORD_REQUIRED = 'New Password is required!';

  /** Indicates that confirming the password is necessary. */
  CONFIRM_PASSWORD_REQUIRED = 'Confirm Password is required!';

  /** Indicates that confirming the new password is necessary. */
  CONFIRM_NEW_PASSWORD_REQUIRED = 'Confirm New Password is required!';

  /** Indicates that the password and confirmation don't match. */
  PASSWORD_NOT_MATCHED = 'Password and Confirm new password not equal.';

  /** Indicates that the password and confirmation don't match. */
  CONFIRM_PASSWORD_NOT_MATCHED = 'Entered password does not match.';

  /** Indicates the requirements for a valid password. */
  PASSWORD_INVALID = 'Your password must be at least 6 characters long and include a mix of uppercase letters, lowercase letters, numbers, and special characters like *, @, or $';

  /** Indicates that a country code is necessary. */
  COUNTRY_CODE_REQUIRED = 'Country Code is required!';

  /** Indicates that a first name is necessary. */
  FIRST_NAME_REQUIRED = 'First Name is required!';

  /** Indicates that a last name is necessary. */
  LAST_NAME_REQUIRED = 'Last Name is required!';

  /** Indicates that a document is required. */
  DOCUMENT_REQUIRED = 'Document is required!';

  EXCEL_REQUIRED = 'Excel File is required';
  EXCEL_MAX_FILE = 'Excel size must be less than 5 MB';
  UPLOAD_VALID_FILE = 'Please upload valid file.';

  /** Indicates that the document size must be less than a certain value. */
  DOCUMENT_MAX_FILE_SIZE = 'The Document size must be less than 5 MB';

  /** Indicates that the attachment size must be less than a certain value. */
  ATTACHMENT_MAX_FILE_SIZE = 'The Attachment size must be less than';

  /** Indicates that the selected document has an invalid extension. */
  DOCUMENT_INVALID_EXTENSION = 'Please select valid Document.';

  /** Indicates that the image size must be less than a certain value. */
  IMAGE_MAX_FILE_SIZE = 'The Image size must be less than 5 MB';

  /** Indicates that the selected image has an invalid extension. */
  IMAGE_INVALID_EXTENSION = 'Please select valid Image.';

  /** Indicates that the selected file has an invalid extension. */
  FILE_INVALID_EXTENSION = 'Please select valid File.';

  /** Indicates that the selected file has an invalid extension. */
  FILE_MAX_SIZE = 'The File size must be less than 5 MB.';

  //Validations
  EMAIL_MOBILE_INVALID = 'Entered Email / Mobile number is invalid';
  EMAIL_MOBILE_REQ = 'Email / Mobile number is required!';

  EMAIL_MOBILE_USER_REQ = 'Email / Mobile No / Username is required!';
  EMAIL_MOBILE_USER_INVALID = 'Entered Email / Mobile number / Username is invalid.';

  COLOR_NAME_REQ = 'Color Name is required!'
  COLOR_NAME_INVALID = 'Please enter valid Color Name.'
  COLOR_CHINESE_REQ = 'Chinese Value is required!'
  COLOR_SHORT_CODE_REQ = 'Short code is required!'

  UNIT_NAME_REQ = 'Unit Name is required!'
  UNIT_NAME_INVALID = 'Please enter valid Unit Name.'
  UNIT_SHORT_CODE_REQ = 'Short code is required!'
  UNIT_CATEGORY_REQUIRED = 'Category is required!'
  UNIT_FACTOR_REQUIRED = 'Factor is required!'

  SIZE_NAME_REQ = 'Size Name is required!'
  SIZE_NAME_INVALID = 'Please enter valid Size Name.'
  SIZE_SHORT_CODE_REQ = 'Short code is required!'

  HSN_NAME_REQ = 'HSN Code is required!';
  HSN_NAME_INVALID = 'Please enter valid HSN Code.';
  HSN_GST_SLAB_REQ = 'GST Slab is required!';

  PACKING_TYPE_REQ = 'Packing Type Name is required!';
  PACKING_TYPE_INVALID = 'Please enter valid Packing Type Name.';

  OTHER_TAX_REQ = 'Other Tax is required!';
  OTHER_TAX_INVALID = 'Please enter valid Other Tax Name.';
  GSTSLAB_TAX_REQ = 'GST Slab is required!';
  GSTSLAB_TAX_INVALID = 'Please enter valid GST Slab.';
  RATE_TAX_REQ = 'Rate is required!'
  RATE_TAX_INVALID = 'Please enter valid rate.'

  BANKGROUP_NAME_REQ = 'Bank Group Name is required!';
  BANKGROUP_NAME_INVALID = 'Please enter valid Bank Group Name.';
  GSTNO_REQ = 'GST No is required!';
  GSTNO_INVALID = 'Please enter valid GST No.';
  OPENING_BALANCE_REQ = 'Opening Balance is required!';
  OPENING_BALANCE_INVALID = 'Please enter valid Opening Balance.';
  DATE_REQ = 'Date is required!';

  ACC_HOLDER_REQ = 'Account Holder Name is required!'
  ACC_HOLDER_INVALID = 'Please enter valid Account Holder Name.'

  SETTINGS_MAX_DAYS = 'Maximum: 365 days'
  COUNTRY_NAME_REQ = 'Country Name is required!';
  COUNTRY_NAME_INVALID = 'Please enter valid Country Name.';
  COUNTRY_CODE_REQ = 'Country Code is required!';
  COUNTRY_CODE_INVALID = 'Please enter valid Country Code.';
  STATE_NAME_REQ = 'State Name is required!';
  STATE_NAME_INVALID = 'Please enter valid State Name.';
  STATE_CODE_REQ = 'State Code is required!';
  STATE_CODE_INVALID = 'Please enter valid State Code.';
  CITY_NAME_REQ = 'City Name is required!';
  CITY_NAME_INVALID = 'Please enter valid City Name.';
  CITY_CODE_REQ = 'City Code is required!';
  CITY_CODE_INVALID = 'Please enter valid City Code.';
  SHIPPINGDATE_CODE_REQ = 'Shipping Date is required!';
  SHIPPINGDATE_CODE_INVALID = 'Please enter valid Shipping Date.';
  PHONE_COUNTRY_EXTENSION_REQ = 'Phone No Extension is required.';
  PHONE_COUNTRY_EXTENSION_INVALID = 'Please enter valid Phone No Extension!';

  TICKET_SUB_NAME_REQ = 'Ticket Subject Name is required!';
  TICKET_SUB_NAME_INVALID = 'Please enter valid Ticket Subject Name.'

  MARKET_TYPE_NAME_REQ = 'Market Type Name is required!';
  MARKET_TYPE_NAME_INVALID = 'Please enter valid Market Type Name.'

  PAYMENT_TERMS_NAME_REQ = 'Payment Term Name is required!';
  PAYMENT_TERMS_NAME_INVALID = 'Please enter valid Payment Term Name.'
  NO_OF_DAYS_REQ = 'No. of days is required!';
  PAYMENT_TERMS_REQ = 'Payment Terms is required!';

  PAYMENT_TYPE_NAME_REQ = 'Payment Type Name is required!';
  PAYMENT_TYPE_NAME_INVALID = 'Please enter valid Payment Type Name.'

  CURRENT_NAME_REQ = 'Current Name is required!';
  CURRENT_NAME_INVALID = 'Please enter valid Currency Name.';
  CURRENT_SYMBOL_REQ = 'Current Symbol is required!';
  CURRENT_SYMBOL_INVALID = 'Please enter valid Currency Symbol.';
  EXCHANGE_RATE_REQ = 'Exchange Rate is required!';
  EXCHANGE_RATE_INVALID = 'Please enter valid Exchange Rate.';

  SEASON_NAME_REQ = 'Season Name is required!';
  SEASON_NAME_INVALID = 'Please enter valid Season Name.';
  SEASON_DATE_REQ = 'Season Date is required!';
  ADV_PO_DATE_REQ = 'Advance PO Date is required!';

  SOT_NAME_REQ = 'Sales Order Type Name is required!';
  SOT_NAME_INVALID = 'Please enter valid Sales Order Type Name.';

  DEPT_NAME_REQ = 'Department Name is required!';
  DEPT_NAME_INVALID = 'Please enter valid Department Name.'

  REASON_NAME_REQ = 'Reason is required!'
  REASON_NAME_INVALID = 'Please enter valid Reason.'
  REASON_SHORT_CODE_REQ = 'Reason Short code is required!'

  CC_NAME_REQ = 'Courier Company Name is required!'
  CC_NAME_INVALID = 'Please enter valid Courier Company Name.'
  CONTACT_PERSON_NAME_REQ = 'Contact Person Name is required!.'
  CONTACT_PERSON_NAME_INVALID = 'Please enter valid Contact Person Name.'
  PRICE_KG_REQ = 'Price/Kg is required!'
  PRICE_KG_INVALID = 'Please enter valid Price/Kg.'
  ADDRESS_REQ = 'Address is required!'
  ADDRESS_INVALID = 'Please enter valid address.'
  COUNTRY_REQ = 'Country is required!';
  STATE_REQ = 'State is required!';
  CITY_REQ = 'City is required!';
  ZIP_REQ = 'Zip is required!'
  ZIP_INVALID = 'Please enter valid Zip.'

  TRAVELLER_NAME_REQ = 'Traveller Name is required!';
  TRAVELLER_NAME_INVALID = 'Please enter valid Traveller Name.';
  CONDUCTOR_REQ = 'Conductor Name is required!';
  CONDUCTOR_INVALID = 'Please enter valid Conductor Name.';
  SHORT_CODE_REQ = 'Short Code is required!';
  SHORT_CODE_INVALID = 'Please enter valid Short Code.';
  CATEGORY_SHORT_CODE_REQ = 'Category Short Code is required!';
  CATEGORY_SHORT_CODE_INVALID = 'Please enter valid Category Short Code.';
  REPRESENTATIVE_REQUIRED = 'Representative is required.';

  FIRST_NAME_REQ = 'First Name is required!';
  FIRST_NAME_INVALID = 'Please enter valid First Name.';
  LAST_NAME_REQ = 'Last Name is required!';
  LAST_NAME_INVALID = 'Please enter valid Last Name.';

  CHECKLIST_NAME_REQ = 'Checklist Name is required!';
  CHECKLIST_NAME_INVALID = 'Please enter valid Checklist Name.';

  LEVEL_NAME_REQ = 'Level Name is required!'
  LEVEL_NAME_INVALID = 'Please enter valid Level Name.'
  AP_REQ = 'Assign Person is required!'

  BRANCH_NAME_REQ = 'Branch Name is required!';
  BRANCH_NAME_INVALID = 'Please enter valid Branch Name.';
  REP_NAME_REQ = 'Representative Name is required!';
  REP_NAME_INVALID = 'Please enter valid Representative Name.';
  TOC_REQ = 'Type of Company is required!';
  CURRENCY_REQ = 'Currency is required!';
  BANK_GROUP_REQ = 'Bank Group is required!';
  CONV_RATE_REQ = 'Conversion Rate is required!';
  CONV_RATE_NOT_ALLOW_ZERO = 'Conversion Rate should not be zero!';
  FINANCIAL_YR_REQ = 'Financial Year is required!';
  FINANCIAL_YR_EXCEED = 'Duration must be exactly 12 months for financial year'
  LANDMARK_INVALID = 'Please enter valid Landmark.'
  LOC_INVALID = 'Please enter valid Location Link.'

  NAME_REQ = 'Name is required!';
  NAME_INVALID = 'Please enter valid Name.'
  DEPT_REQ = 'Department is required!';
  ROLE_REQ = 'Role is required!';
  USERNAME_REQ = 'Username is required!';
  USERNAME_INVALID = 'Please enter valid Username.'
  GENDER_REQ = 'Gender is required!';
  AADHAR_REQ = 'Aadhaar No. is required!';
  AADHAR_INVALID = 'Please enter 12 digits of valid aadhaar no.'
  PAN_REQ = 'PAN No. is required!';
  PAN_INVALID = 'Please enter valid PAN No.';
  BRANCH_REQ = 'Branch is required!';
  WAREHOUSE_REQ = 'Warehouse is required!';
  SHIFT_START_END_SAME = "Shift Start Time and End Time should not be same!"

  TRANSPORT_NAME_REQ = 'Transport Name is required!'
  TRANSPORT_NAME_INVALID = 'Please enter valid Transport Name.'
  STORE_NAME_REQ = 'Store Name is required!'
  STORE_NAME_INVALID = 'Please enter valid Store Name.'
  MOT_REQ = 'Mode of Transport is required!'

  BRANCH_CODE_REQ = 'Branch Code is required!';
  BRANCH_CODE_INVALID = 'Please enter valid Branch Code.';
  LANDLINE_REQ = 'Landline No. is required!';
  LANDLINE_INVALID = 'Please enter correct Landline No.';
  PIN_CODE_REQ = 'PIN Code is required!';
  PIN_CODE_INVALID = 'Please enter valid PIN Code.'
  BOOKING_REQ = 'Booking is required!';
  DELIVERY_REQ = 'Delivery is required!';
  LOCATION_LINK_REQ = 'Location Link is required!';
  LOCATION_LINK_INVALID = 'Please enter valid Location Link.';

  ROLE_NAME_REQ = 'Role Name is required!';
  ROLE_NAME_INVALID = 'Please enter valid Role Name.';
  DES_INVALID = 'Please enter valid Description.';

  EXP_NAME_REQ = 'Expense Type Name is required!'
  EXP_NAME_INVALID = 'Please enter valid Expense Type Name.'

  PCODE_REQ = 'Parent Code is required!'
  CODE_NAME_REQ = 'Code Name is required!'
  CODE_NAME_INVALID = 'Please enter valid Code Name.'
  SCODE_NAME_REQ = 'Sub Code Name is required!'
  SCODE_NAME_INVALID = 'Please enter valid Sub Code Name.'

  CATEGORY_NAME_REQ = 'Category Name is required!'
  CATEGORY_NAME_INVALID = 'Please enter valid Category Name.'

  CATEGORY_REQ = 'Category is required!'
  SUBCATEGORY_NAME_REQ = 'Sub Category Name is required!'
  SUBCATEGORY_NAME_INVALID = 'Please enter valid Sub Category Name.'

  GRP_NAME_REQ = 'Group Name is required!';
  GRP_NAME_INVALID = 'Please enter valid Group Name.'

  WAREHOUSE_NAME_REQ = 'Warehouse Name is required!';
  WAREHOUSE_NAME_INVALID = 'Please enter valid Warehouse Name.'
  WAREHOUSE_MANAGER_REQ = 'Manager is required!';
  WAREHOUSE_MOBILE_REQ = 'Warehouse Mobile No is required!';
  WAREHOUSE_MOBILE_INVALID = 'Please enter valid Warehouse Mobile No.'
  WAREHOUSE_EMAIL_REQ = 'Warehouse Email is required!';
  WAREHOUSE_EMAIL_INVALID = 'Please enter valid Warehouse Email.'
  WAREHOUSE_ADDRESS_REQ = 'Warehouse Email is required!';
  WAREHOUSE_ADDRESS_INVALID = 'Please enter valid Warehouse Email.'
  ZIP_CODE_REQ = 'Zip Code is required!';
  ZIP_CODE_INVALID = 'Please enter valid Zip Code.';

  LOC_NAME_REQ = 'Location Name is required!';
  LOC_NAME_INVALID = 'Please enter valid Location Name.'

  AISLE_REQ = 'Aisle is required!';
  AISLE_NAME_REQ = 'Aisle Name is required!';
  AISLE_NAME_INVALID = 'Please enter valid Aisle Name.'
  RACK_NAME_REQ = 'Rack Name is required!';
  RACK_NAME_INVALID = 'Please enter valid Rack Name.'
  ADDITIONAL_NAME_INVALID = 'Please enter valid Additional Name.'


  ACCOUNT_TYPE_NAME_REQ = 'Account Type Name is required!'
  ACCOUNT_TYPE_NAME_INVALID = 'Please enter valid Account Type Name.'
  ACCOUNT_GROUP_REQUIRED = "Account Group is required!"

  ACCOUNT_TYPE_REQUIRED='Account Type is required!';
  ACCOUNT_NAME_REQUIRED  = "Account Name is required!";
  ACCOUNT_NAME_INVALID = 'Please enter valid Account Name.'
  SUB_ACCOUNT_NAME_REQUIRED  = "Sub Account Name is required!";
  SUB_ACCOUNT_NAME_INVALID = 'Please enter valid Sub Account Name.'
  ACCOUNT_CODE_REQUIRED  = "Account Code is required!";
  ACCOUNT_CODE_INVALID = 'Please enter valid Account Code.'
  SUB_ACCOUNT_CODE_REQUIRED  = "Sub Account Code is required!";
  SUB_ACCOUNT_CODE_INVALID = 'Please enter valid Sub Account Code.'
  PARENT_ACCOUNT_INVALID = 'Parent account is required!'


  // ITEMS
  MARKET_TYPE_REQ = 'Market Type is required!';
  ITEM_GROUP_REQ = 'Item Group is required!';
  ITEM_NAME_REQ = 'Item Name is required!';
  ITEM_NAME_INVALID = 'Please enter valid Item Name.';
  ITEM_DNAME_REQ = 'Item Display name is required!';
  ITEM_DNAME_INVALID = 'Please enter valid Item Display name.';
  UNIT_REQ = 'Unit is required!';
  DAY_REQ = 'For Perishable Alert, Day is required!';
  YT_LINK_INVALID = 'Please enter valid Youtube Link.';
  TITLE_REQ = 'Title is required!';
  TITLE_INVALID = 'Please enter valid Title.';
  SEARCH_KEYWORD_REQ = 'Search Keyword is required.';
  SEARCH_KEYWORD_INVALID = 'Please enter valid Search Keyword.';
  MATERIAL_INVALID = 'Please enter valid Material.';
  SUB_MATERIAL_INVALID = 'Please enter valid Sub Material.';
  SEASON_TYPE_REQ = 'Season Type is required!';
  DAYS_REQ = 'Days is required!';
  BP_REQ = 'Bullet Point is required!';
  BP_INVALID = 'Please enter valid Bullet Point.';
  LINK_INVALID = 'Please enter valid Link.'

  //
  NOTE_REQ = 'Note is required!';
  NOTE_INVALID = 'Please enter valid Note.';

  // REGISTRATION
  REG_TYPE_REQ = 'Registration Type is required.';
  F_NAME_REQ = 'First Name is required.';
  F_NAME_INVALID = 'Please enter valid First Name!';
  M_NAME_REQ = 'Middle Name is required.';
  M_NAME_INVALID = 'Please enter valid Middle Name!';
  L_NAME_REQ = 'Last Name is required.';
  L_NAME_INVALID = 'Please enter valid Last Name!';
  COMPANY_NAME_REQ = 'Company Name is required!';
  COMPANY_NAME_INVALID = 'Please enter valid Company Name.';
  DISPLAY_NAME_REQ = 'Display Name is required!';
  DISPLAY_NAME_INVALID = 'Please enter valid Display Name.';
  ASSIGN_SALES_PERSON_REQUIRED = "Assign Sales Person is requierd."

  WHATSAPP_NO_REQ = 'WhatsApp No. is required.'
  TELEGRAM_NO_REQ = 'Telegram No. is required.'
  WHATSAPP_NO_INVALID = 'Please enter valid Whatsapp No.';
  TELEGRAM_NO_INVALID = 'Please enter valid Telegram No.';
  REASON_TO_BLACHANGE_REQ = 'Blacklist Reason is required!'
  REASON_TO_BLOCHANGE_REQ = 'Blocklist Reason is required!'
  REASON_TO_CHANGE_REQ = 'Reason is required!';
  REASON_TO_CHANGE_INVALID = 'Please enter valid Reason!';
  REASON_TO_CHANGE_CL_REQ = 'Reason to Change (Credit Limit) is required!';
  REASON_TO_CHANGE_CL_INVALID = 'Please enter valid Reason to Change (Credit Limit)!';
  REASON_TO_CHANGE_CD_REQ = 'Reason to Change (Credit Days) is required!';
  REASON_TO_CHANGE_CD_INVALID = 'Please enter valid Reason to Change (Credit Days)!';
  BLACKLIST_REASON_INVALID = 'Please enter valid Blacklist Reason!';
  BLOCKLIST_REASON_INVALID = 'Please enter valid Blocklist Reason!';
  REF_PERSON_INVALID = 'Please enter valid Reference Person!';

  SUP_SHORT_CODE_REQ = 'Supplier Short Code is required!';
  SUP_TYPE_REQ = 'Supplier Type is required!';
  SUP_SHORT_CODE_INVALID = 'Please enter valid Supplier Short Code.';
  PAYMENT_PERSON_INVALID = 'Please enter valid Payment Person!';
  ACC_PAYMENT_BY_INVALID = 'Please enter valid Accept Payment By!';
  BUSINESS_REG_INVALID = 'Please enter valid Business Register No!';
  WEBSITE_URL_INVALID = 'Please enter valid Website URL!';
  FB_INVALID = 'Please enter valid Facebook Link/Name!';
  INSTAGRAM_INVALID = 'Please enter valid Instagram Link/Name!';
  TWITTER_INVALID = 'Please enter valid Twitter Link/Name!';
  REMARKS_INVALID = 'Please enter valid Remarks!';
  ACC_NO_NOT_MATCHED = 'Entered Account No. does not match.';
  BANK_NAME_INVALID = 'Please enter valid Bank Name!';
  ACC_HOLDER_NAME_INVALID = 'Please enter valid Account Holder Name.';

  SUPPLIER_REQ = 'Supplier is required!';
  CUS_REQ = 'Customer is required!';
  DA_REQ = 'Delivery Address is required!';

  IMPORTER_REQ = 'Importer is required!';
  CONTAINER_REQ = 'Container is required!';
  SHIPPING_TYPE_REQ = 'Shipping Type is required!';
  CBM_PRICE_REQ = 'CBM Price is required!';
  PRICE_REQ = 'Price is required!';
  AMOUNT_REQ = 'Amount is required!';
  PRICE_INVALID = 'Please enter valid Price.';

  VEH_NO_REQ = 'Vehicle No. is required!';
  CARTON_QTY_REQ = 'Carton Qty is required!';
  DATE_TIME_REQ = 'Date & Time is required!';

  STATUS_REQ = 'Status is required!';
  LOADED_QTY_REQ = 'Loaded Quantity is required!';
  REL_QTY_REQ = 'Released Quantity is required!';
  REC_QTY_REQ = 'Received Quantity is required!';

  TEMPO_NO_REQ = 'Tempo No. is required!';
  CHA_AGENT_REQ = 'CHA Agent is required!';
  CHA_CARRYING_TYPE_REQ = 'CHA Carrying Type is required!';

  GRP_CODE_REQ = 'Group Code is required!';
  GRP_CODE_INVALID = 'Please enter valid Group Code.';

  //STOCK BRANCH TRANSFER
  PICKER_PERSON_REQ = 'Pickup Person is required!'

}
