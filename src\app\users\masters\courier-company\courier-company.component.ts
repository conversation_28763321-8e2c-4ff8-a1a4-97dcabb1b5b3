import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Deserialize, Serialize } from 'cerialize';
import { debounceTime, distinctUntilChanged, Subject } from 'rxjs';
import { CourierCompany } from 'src/app/models/CourierCompany';
import { activeInactiveStatus, COURIER_COMPANY_MASTER } from 'src/app/shared/constants/constant';
import { UtilsService } from 'src/app/shared/services/utils.service';
import { saveAs } from 'file-saver';
declare var window: any;

@Component({
  selector: 'app-courier-company',
  templateUrl: './courier-company.component.html',
  styleUrls: ['./courier-company.component.scss']
})
export class CourierCompanyComponent implements OnInit {

  @ViewChild('logo') logo: ElementRef;
  filenameForLogo: string;
  flagForInvalidDocSize = false;
  flagForInvalidExtension = false;
  selectedLogo: File;
  fileLocal: any;

  activeInactiveStatus = activeInactiveStatus;
  courierMasterModal: any;
  deleteCourierMasterModal: any;

  dropdown: any;

  courierTH: any[] = []
  selectedIds: any[] = []
  courierList: CourierCompany[] = [];
  courierObj = new CourierCompany();
  activeFlag: boolean = true;
  courierGroup: FormGroup;
  flagForSelectAll: boolean = false;

  pageNo: number = 1;
  pageSize: string = '100';
  sortOrder: string;
  sortColumn: string;
  totalData: number;
  statusForModal: string;

  enumForSortOrder = this.utilsService.enumForSortOrder;
  searchSubject: Subject<string> = new Subject<string>();
  searchText: string;
  searchPriceSubject: Subject<any> = new Subject<any>();
  searchPrice: any;
  pagination: any;

  constructor(public utilsService: UtilsService, private fb: FormBuilder) {
    this.courierTH = COURIER_COMPANY_MASTER
  }

  ngOnInit() {

    this.searchSubject.pipe(debounceTime(300), distinctUntilChanged()).subscribe((res: string) => {
      this.pageNo = 1;
      // this.pageSize = '100';
      this.searchText = null;
      this.searchText = res;
      this.getCourierList();
    });


    this.searchPriceSubject.pipe(debounceTime(300), distinctUntilChanged()).subscribe((res: string) => {
      this.pageNo = 1;
      // this.pageSize = '100';
      this.searchPrice = null;
      this.searchPrice = res;
      this.getCourierList();
    });

    this.getCourierList();
    this.courierFormGroup();

    this.courierMasterModal = new window.bootstrap.Modal(
      document.getElementById('courierMasterModal')
    );

    this.deleteCourierMasterModal = new window.bootstrap.Modal(
      document.getElementById('deleteCourierMasterModal')
    );

    document.getElementById('courierMasterModal').addEventListener('shown.bs.modal', () => {
      document.getElementById('f').focus();
    });
  }

  getCourierList() {

    this.flagForSelectAll = false;
    this.totalData = 0;
    this.selectedIds = []

    const param = {
      pageNo: this.pageNo,
      pageSize: this.pageSize,
      sortOrder: this.sortOrder,
      sortColumn: this.sortColumn,
      searchText: this.searchText,
      priceKg: this.searchPrice,
      isActive: this.activeFlag
    }

    this.utilsService.postMethodAPI(false, this.utilsService.serverVariableService.COURIER_LISTING, param, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.courierList = Deserialize(response.content, CourierCompany);
        this.totalData = response.totalElements;
        this.pagination = response;
      } else {
        this.courierList = [];
      }
    })

  }

  courierFormGroup() {
    this.courierGroup = this.fb.group({
      name: ['', Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.PATTERN_FOR_ALPHANUMERIC_WITH_SPECIAL_CHAR)])],
      contact_person_name: ['', Validators.compose([Validators.pattern(this.utilsService.validationService.PATTERN_FOR_ALPHABATES_AND_SPACE)])],
      price: ['', Validators.compose([Validators.pattern(this.utilsService.validationService.ONLY_GREATER_THAN_ZERO)])],
      mobile: ['', Validators.compose([Validators.pattern(this.utilsService.validationService.PATTERN_FOR_PHONE_NO)])],
      email: ['', Validators.compose([Validators.pattern(this.utilsService.validationService.PATTERN_FOR_EMAIL)])],
      address: ['', Validators.compose([Validators.required])],
      country: ['', Validators.compose([Validators.required])],
      state: ['', Validators.compose([Validators.required])],
      city: ['', Validators.compose([Validators.required])],
      zip: ['', Validators.compose([Validators.required])],
      status: [true, Validators.compose([Validators.required])],
      countryExtension: [null]
    })
  }

  addPageSizeData(event) {
    this.pageNo = 1;
    this.pageSize = event;
    this.getCourierList();
  }

  pageNumber(event) {
    this.pageNo = event
    this.getCourierList();
  }

  trackBy(index: number, item: CourierCompany): number {
    return item.id;
  }

  onSaveCourier() {

    const formData = new FormData();

    if (this.courierGroup.invalid) {
      this.courierGroup.markAllAsTouched();
      return;
    }

    if (this.selectedLogo) {
      formData.set('logoImg', this.selectedLogo);
    }
    
    let param = this.utilsService.trimObjectValues(Serialize(this.courierObj));
    formData.set('saveCourierInfo', JSON.stringify(Serialize(param)));

    this.utilsService.postMethodAPI(true, this.utilsService.serverVariableService.COURIER_SAVE_EDIT_DELETE, formData, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.courierMasterModal.hide();
        this.getCourierList();

        setTimeout(() => {
          if (this.logo?.nativeElement) {
            this.logo.nativeElement.value = "";
          }
          this.flagForInvalidExtension = false;
          this.flagForInvalidDocSize = false;
          this.filenameForLogo = '';
          this.selectedLogo = null;
          this.fileLocal = null;
        }, 100);
      }
    })

  }

  openAddEditModal(obj: CourierCompany, status: string) {

    this.removeProfile(null);
    this.courierObj = new CourierCompany();
    this.courierGroup.reset();
    this.statusForModal = status;

    if (this.logo?.nativeElement) {
      this.logo.nativeElement.value = "";
    }
    this.flagForInvalidExtension = false;
    this.flagForInvalidDocSize = false;
    this.filenameForLogo = '';
    this.selectedLogo = null;

    if (this.statusForModal === 'Add') {
      setTimeout(() => {
        this.courierObj.isActive = true;
        this.getRequiredData();
      }, 100);
    }

    if (obj) {
      setTimeout(() => {
        this.getCourierCompanyByID(obj);
      }, 100);
    }
    this.courierMasterModal.show();
  }

  // Courier Company by ID
  getCourierCompanyByID(obj: CourierCompany) {
    this.utilsService.getMethodAPI(false, this.utilsService.serverVariableService.COURIER_DATA_BY_ID + `?id=${obj.id}`, null, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.courierObj = Deserialize(response, CourierCompany);
        if(obj.countryMaster) {
          this.courierObj.countryId = Serialize(obj.countryMaster.id)
        }
        if(obj.stateMaster) {
          this.courierObj.stateId = Serialize(obj.stateMaster.id)
        }
        if(obj.cityMaster) {
          this.courierObj.cityId = Serialize(obj.cityMaster.id)
        }
        if (obj.contactExtension) {
          this.courierObj.contactCountryExtensionId = Serialize(obj.contactExtension.id)
        }
        this.courierObj.originalName = response.originalName
        this.getRequiredData();
      }
    })
  }

  openDeleteCourierModal(obj: CourierCompany) {
    this.courierObj = Serialize(obj)
    this.deleteCourierMasterModal.show();
  }

  deleteCourierCompany() {

    this.utilsService.deleteMethodAPI(true, this.utilsService.serverVariableService.COURIER_SAVE_EDIT_DELETE + `?id=${this.courierObj.id}`, {}, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.deleteCourierMasterModal.hide();
        if(!this.pagination?.first && this.pagination?.last && this.pagination?.numberOfElements === 1) {
          this.pageNo = this.pageNo - 1
        }
        this.getCourierList();
      }
    })
  }

  // Select Deselect 

  selectAll() {
    if (this.flagForSelectAll === true) {
      this.selectedIds = new Array<string>();
    }
    const obj = this.courierList.filter((val, index) => {
      if (this.flagForSelectAll === true) {
        val['isSelected'] = true;
        this.selectedIds.push(val.id);
      } else {
        val['isSelected'] = false;
        this.selectedIds.splice(index, 1);
      }
    });
    if (this.flagForSelectAll === false) {
      this.selectedIds = new Array<string>();
    }
  }

  selectUnselect(id: number, index, value) {

    const isSelected = this.selectedIds.includes(id);

    if (value && !isSelected) {

      this.selectedIds.push(id);

    } else if (!value && isSelected) {

      const assetIndex = this.selectedIds.indexOf(id);
      this.selectedIds.splice(assetIndex, 1);
    }
    this.flagForSelectAll = this.checkIfAllSelected();
  }

  checkIfAllSelected() {
    let flag = true;
    this.courierList.filter((val, index) => {
      if (val['isSelected'] === false) {
        flag = false;
        return;
      }
    });
    return flag;
  }

  // sorting 

  onSortTH(key) {

    if (this.utilsService.isEmptyObjectOrNullUndefined(this.courierList)) {
      return;
    }

    if (key === this.sortColumn) {
      if (this.sortOrder === this.enumForSortOrder.A) {
        this.sortOrder = this.enumForSortOrder.D;
      } else if (this.sortOrder === this.enumForSortOrder.D) {
        this.sortOrder = this.enumForSortOrder.A;
      }
    } else {
      this.sortOrder = this.enumForSortOrder.D;
    }

    this.sortColumn = key;
    this.getCourierList();
  }

  //Search
  onSearch(event: any, type: boolean) {
    if (type) {
      this.searchSubject.next(event.target.value);
    }
    if (!type) {
      event.target.value = event.target.value.replace(/^\./, '');
      if(event.target.value !== "" && event.target.value !== ".") {
        this.searchPriceSubject.next(event.target.value);
      }
      else {
        this.searchPriceSubject.next(null);
      }
    }
  }

  // status 

  onChangeStatus(item: CourierCompany, value, index) {

    this.utilsService.putMethodAPI(true, this.utilsService.serverVariableService.COURIER_STATUS_CHANGE + `${item.id}/${value}`, {}, '', (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response.data)) {
        this.courierList[index].isActive = value
      } else {
        this.courierList[index].isActive = !value
      }
      if(!this.pagination?.first && this.pagination?.last && this.pagination?.numberOfElements === 1 && this.activeFlag) {
        this.pageNo = this.pageNo - 1
      }
      this.getCourierList()
    }, true);
  }

  // active/inactive

  onChangeActive() {
    this.pageNo = 1;
    // this.pageSize = '100';
    this.getCourierList();
  }

  // export 

  exportReport() {

    const param = {
      ids: this.selectedIds ? this.selectedIds : [],
      searchText: this.searchText,
      priceKg: this.searchPrice,
      isActive: this.activeFlag,
      sortOrder: this.sortOrder,
      sortColumn: this.sortColumn,
    }

    this.utilsService.exportReport(param, this.utilsService.serverVariableService.COURIER_EXPORT).subscribe((data: any) => {
      saveAs(new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' }), 'Courier Company Sheet');
    });
  }

  // dropdown

  onChangeCountryState(flag: boolean) {
    if(flag) {
      this.courierObj.stateId = null;
      this.courierObj.cityId = null;
      this.getRequiredData()
    }
    else {
      this.courierObj.cityId= null;
      this.getRequiredData()
    }
  }

  getRequiredData() {

    this.dropdown = null;
    const param = {
      countryIds: this.courierObj.countryId ? [this.courierObj.countryId] : [],
      stateIds: this.courierObj.stateId ? [this.courierObj.stateId] : [],
    }

    this.utilsService.postMethodAPI(false, this.utilsService.serverVariableService.COUNTRY_STATE_CITY_DROPDOWN, param, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.dropdown = response;
        this.dropdown.countryPh = Serialize(this.dropdown.country)
        this.dropdown.country = this.dropdown?.country ? this.utilsService.transformDropdownItems(this.dropdown?.country) : [];
        this.dropdown.countryPh = this.dropdown?.countryPh ? this.utilsService.transformDropdownItems(this.dropdown?.countryPh) : [];
        this.dropdown.state = this.dropdown?.state ? this.utilsService.transformDropdownItems(this.dropdown?.state) : [];
        this.dropdown.city = this.dropdown?.city ? this.utilsService.transformDropdownItems(this.dropdown?.city) : [];
        //
        this.dropdown.country = this.utilsService.filterIsActive(this.dropdown?.country, this.courierObj.countryId ? this.courierObj.countryId : null);
        this.dropdown.state = this.utilsService.filterIsActiveLV((this.dropdown?.state || []), this.courierObj.stateId ? this.courierObj.stateId : null);
        this.dropdown.city = this.utilsService.filterIsActiveLV((this.dropdown?.city || []), this.courierObj.cityId ? this.courierObj.cityId : null);
        this.dropdown.countryPh = this.utilsService.filterIsActive((this.dropdown?.countryPh || []), this.courierObj.contactCountryExtensionId ? this.courierObj.contactCountryExtensionId : null);
        //
      }
    })
  }

  onSelectLogo(event): void {

    if (event.target.files && event.target.files[0]) {
      this.flagForInvalidExtension = false;
      this.flagForInvalidDocSize = false;
      this.fileLocal = null;
      this.courierObj.originalName = null;
      this.courierObj.logoImg = null;
      this.filenameForLogo = null;
      const reader = new FileReader();
      const max_file_size = 5242880;
      reader.readAsDataURL(event.target.files[0]); // read file as data url
      const selectedFile = event.target.files[0];
      if (selectedFile) {
        const ext = selectedFile.name.substr(selectedFile.name.lastIndexOf('.') + 1);
        const ext1 = (ext).toLowerCase();

        if (ext1 === 'jpeg' || ext1 === 'png' || ext1 === 'jpg' || ext1 === 'jfif') {
          const fileUrl = URL.createObjectURL(event.target.files[0]);
          if (max_file_size < selectedFile.size) {
            this.flagForInvalidDocSize = true;
            this.filenameForLogo = '';
            this.utilsService.toasterService.error(this.utilsService.validationService.IMAGE_MAX_FILE_SIZE)
            this.selectedLogo = undefined;
          } else {
            this.filenameForLogo = event.target.files[0].name;
            this.selectedLogo = event.target.files[0];
            this.courierObj.logoImg = event.target.files[0].name
            this.fileLocal = fileUrl
          }
        } else {
          this.flagForInvalidExtension = true;
          this.filenameForLogo = '';
          this.selectedLogo = undefined;
          this.utilsService.toasterService.error(this.utilsService.validationService.IMAGE_INVALID_EXTENSION)
        }
      }

    }
  }

  openLink(link) {
    const filePreview = `${this.utilsService.imgPath}${link}`
    if (filePreview) {
      if (link) {
        window.open(filePreview, "_blank");
      }
    }
  }

  removeProfile(file: any) {
    if(this.logo?.nativeElement) {
      this.logo.nativeElement.value = "";
    }
    this.courierObj.logoImg = null;
    this.courierObj.originalName = null;
    this.flagForInvalidExtension = false;
    this.flagForInvalidDocSize = false;
    this.fileLocal = null;
    this.filenameForLogo = null;
  }

  onSearchCountryCode(event: any) {
    const searchTerm = event.term;
    let exactMatch = null;
    exactMatch = this.dropdown?.countryPh.find( item => item.countryExtension === searchTerm);
    if (exactMatch) {
      this.courierObj.contactCountryExtensionId = exactMatch.id;
    }
  }
}
