import { deserializeAs, serializeAs } from 'cerialize';

export class Branch {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('branchName')
    @deserializeAs('branchName')
    private _branchName: string;

    @serializeAs('shortCode')
    @deserializeAs('shortCode')
    private _shortCode: string;

    @serializeAs('representativeName')
    @deserializeAs('representativeName')
    private _representativeName: string;

    @serializeAs('branchEmail')
    @deserializeAs('branchEmail')
    private _branchEmail: string;

    @serializeAs('branchPhone')
    @deserializeAs('branchPhone')
    private _branchPhone: string;

    @serializeAs('zipCode')
    @deserializeAs('zipCode')
    private _zipCode: string;

    @serializeAs('address')
    @deserializeAs('address')
    private _address: string;

    @serializeAs('landmark')
    @deserializeAs('landmark')
    private _landmark: string;

    @serializeAs('locationLink')
    @deserializeAs('locationLink')
    private _locationLink: string;

    @serializeAs('isActive')
    @deserializeAs('isActive')
    private _isActive: boolean;

    // @serializeAs('isSelected')
    @deserializeAs('isSelected')
    private _isSelected: boolean;

    @serializeAs('isMainBranch')
    @deserializeAs('isMainBranch')
    private _isMainBranch: boolean;

    @serializeAs('isAllowLocalPurchase')
    @deserializeAs('isAllowLocalPurchase')
    private _isAllowLocalPurchase: boolean;

    @serializeAs('isAllowImportPurchase')
    @deserializeAs('isAllowImportPurchase')
    private _isAllowImportPurchase: boolean;

    @serializeAs('isConsiderGSTAvgPrice')
    @deserializeAs('isConsiderGSTAvgPrice')
    private _isConsiderGSTAvgPrice: boolean;

    @serializeAs('deletedDocsID')
    @deserializeAs('deletedDocsID')
    private _deletedDocsID: any[];

    @serializeAs('companyType')
    @deserializeAs('companyType')
    private _companyType: string;

    @serializeAs('idOfState')
    @deserializeAs('idOfState')
    private _idOfState: number;

    @serializeAs('idOfCountry')
    @deserializeAs('idOfCountry')
    private _idOfCountry: number;

    @serializeAs('idOfCity')
    @deserializeAs('idOfCity')
    private _idOfCity: number;

    @serializeAs('idOfCurrency')
    @deserializeAs('idOfCurrency')
    private _idOfCurrency: number;

    @serializeAs('idsOfBankGroup')
    @deserializeAs('idsOfBankGroup')
    private _idsOfBankGroup: number[];

    @deserializeAs('sortOrder')
    private _sortOrder: number;

    @deserializeAs('branchDoc')
    private _branchDoc: any[];

    @deserializeAs('stateName')
    private _stateName: string;

    @deserializeAs('cityName')
    private _cityName: string;

    @deserializeAs('countryName')
    private _countryName: string;

    // @serializeAs('gstNO')
    @deserializeAs('gstNO')
    private _gstNO: string[];

    @serializeAs('yearFromDate')
    @deserializeAs('yearFromDate')
    private _yearFromDate: any;

    @serializeAs('yearToDate')
    @deserializeAs('yearToDate')
    private _yearToDate: any;

    // @serializeAs('date')
    @deserializeAs('date')
    private _date: any;

    @serializeAs('gstNos')
    @deserializeAs('gstNos')
    private _gstNos: any;
   
    @serializeAs('idOfUser')
    @deserializeAs('idOfUser')
    private _idOfUser: any;

    constructor() {
        this.isActive = false;
        this.isAllowImportPurchase = false;
        this.isAllowLocalPurchase = false;
        this.isMainBranch = false;
        this.isConsiderGSTAvgPrice = false;
        this.isSelected = false;
        this.branchDoc = [];
        this.deletedDocsID = [];
        this.gstNO = [];
    }



    /**
     * Getter idsOfBankGroup
     * @return {number[]}
     */
	public get idsOfBankGroup(): number[] {
		return this._idsOfBankGroup;
	}

    /**
     * Setter idsOfBankGroup
     * @param {number[]} value
     */
	public set idsOfBankGroup(value: number[]) {
		this._idsOfBankGroup = value;
	}


    /**
     * Getter gstNos
     * @return {any}
     */
	public get gstNos(): any {
		return this._gstNos;
	}

    /**
     * Setter gstNos
     * @param {any} value
     */
	public set gstNos(value: any) {
		this._gstNos = value;
	}



    /**
     * Getter date
     * @return {any}
     */
	public get date(): any {
		return this._date;
	}

    /**
     * Setter date
     * @param {any} value
     */
	public set date(value: any) {
		this._date = value;
	}


    /**
     * Getter yearFromDate
     * @return {any}
     */
	public get yearFromDate(): any {
		return this._yearFromDate;
	}

    /**
     * Getter yearToDate
     * @return {any}
     */
	public get yearToDate(): any {
		return this._yearToDate;
	}

    /**
     * Setter yearFromDate
     * @param {any} value
     */
	public set yearFromDate(value: any) {
		this._yearFromDate = value;
	}

    /**
     * Setter yearToDate
     * @param {any} value
     */
	public set yearToDate(value: any) {
		this._yearToDate = value;
	}


    /**
     * Getter gstNO
     * @return {string[]}
     */
	public get gstNO(): string[] {
		return this._gstNO;
	}

    /**
     * Setter gstNO
     * @param {string[]} value
     */
	public set gstNO(value: string[]) {
		this._gstNO = value;
	}


    /**
     * Getter deletedDocsID
     * @return {any[]}
     */
	public get deletedDocsID(): any[] {
		return this._deletedDocsID;
	}

    /**
     * Setter deletedDocsID
     * @param {any[]} value
     */
	public set deletedDocsID(value: any[]) {
		this._deletedDocsID = value;
	}


    /**
     * Getter stateName
     * @return {string}
     */
	public get stateName(): string {
		return this._stateName;
	}

    /**
     * Getter cityName
     * @return {string}
     */
	public get cityName(): string {
		return this._cityName;
	}

    /**
     * Getter countryName
     * @return {string}
     */
	public get countryName(): string {
		return this._countryName;
	}

    /**
     * Setter stateName
     * @param {string} value
     */
	public set stateName(value: string) {
		this._stateName = value;
	}

    /**
     * Setter cityName
     * @param {string} value
     */
	public set cityName(value: string) {
		this._cityName = value;
	}

    /**
     * Setter countryName
     * @param {string} value
     */
	public set countryName(value: string) {
		this._countryName = value;
	}


    /**
     * Getter branchDoc
     * @return {any[]}
     */
	public get branchDoc(): any[] {
		return this._branchDoc;
	}

    /**
     * Setter branchDoc
     * @param {any[]} value
     */
	public set branchDoc(value: any[]) {
		this._branchDoc = value;
	}


    /**
     * Getter companyType
     * @return {string}
     */
	public get companyType(): string {
		return this._companyType;
	}

    /**
     * Setter companyType
     * @param {string} value
     */
	public set companyType(value: string) {
		this._companyType = value;
	}


    /**
     * Getter idOfCurrency
     * @return {number}
     */
	public get idOfCurrency(): number {
		return this._idOfCurrency;
	}

    /**
     * Setter idOfCurrency
     * @param {number} value
     */
	public set idOfCurrency(value: number) {
		this._idOfCurrency = value;
	}


    /**
     * Getter sortOrder
     * @return {number}
     */
	public get sortOrder(): number {
		return this._sortOrder;
	}

    /**
     * Setter sortOrder
     * @param {number} value
     */
	public set sortOrder(value: number) {
		this._sortOrder = value;
	}


    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Getter branchName
     * @return {string}
     */
	public get branchName(): string {
		return this._branchName;
	}

    /**
     * Getter shortCode
     * @return {string}
     */
	public get shortCode(): string {
		return this._shortCode;
	}

    /**
     * Getter representativeName
     * @return {string}
     */
	public get representativeName(): string {
		return this._representativeName;
	}

    /**
     * Getter branchEmail
     * @return {string}
     */
	public get branchEmail(): string {
		return this._branchEmail;
	}

    /**
     * Getter branchPhone
     * @return {string}
     */
	public get branchPhone(): string {
		return this._branchPhone;
	}

    /**
     * Getter zipCode
     * @return {string}
     */
	public get zipCode(): string {
		return this._zipCode;
	}

    /**
     * Getter address
     * @return {string}
     */
	public get address(): string {
		return this._address;
	}

    /**
     * Getter landmark
     * @return {string}
     */
	public get landmark(): string {
		return this._landmark;
	}

    /**
     * Getter locationLink
     * @return {string}
     */
	public get locationLink(): string {
		return this._locationLink;
	}

    /**
     * Getter isActive
     * @return {boolean}
     */
	public get isActive(): boolean {
		return this._isActive;
	}

    /**
     * Getter isSelected
     * @return {boolean}
     */
	public get isSelected(): boolean {
		return this._isSelected;
	}

    /**
     * Getter isMainBranch
     * @return {boolean}
     */
	public get isMainBranch(): boolean {
		return this._isMainBranch;
	}

    /**
     * Getter isAllowLocalPurchase
     * @return {boolean}
     */
	public get isAllowLocalPurchase(): boolean {
		return this._isAllowLocalPurchase;
	}

    /**
     * Getter isAllowImportPurchase
     * @return {boolean}
     */
	public get isAllowImportPurchase(): boolean {
		return this._isAllowImportPurchase;
	}

    /**
     * Getter isConsiderGSTAvgPrice
     * @return {boolean}
     */
	public get isConsiderGSTAvgPrice(): boolean {
		return this._isConsiderGSTAvgPrice;
	}

    /**
     * Getter idOfState
     * @return {number}
     */
	public get idOfState(): number {
		return this._idOfState;
	}

    /**
     * Getter idOfCountry
     * @return {number}
     */
	public get idOfCountry(): number {
		return this._idOfCountry;
	}

    /**
     * Getter idOfCity
     * @return {number}
     */
	public get idOfCity(): number {
		return this._idOfCity;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Setter branchName
     * @param {string} value
     */
	public set branchName(value: string) {
		this._branchName = value;
	}

    /**
     * Setter shortCode
     * @param {string} value
     */
	public set shortCode(value: string) {
		this._shortCode = value;
	}

    /**
     * Setter representativeName
     * @param {string} value
     */
	public set representativeName(value: string) {
		this._representativeName = value;
	}

    /**
     * Setter branchEmail
     * @param {string} value
     */
	public set branchEmail(value: string) {
		this._branchEmail = value;
	}

    /**
     * Setter branchPhone
     * @param {string} value
     */
	public set branchPhone(value: string) {
		this._branchPhone = value;
	}

    /**
     * Setter zipCode
     * @param {string} value
     */
	public set zipCode(value: string) {
		this._zipCode = value;
	}

    /**
     * Setter address
     * @param {string} value
     */
	public set address(value: string) {
		this._address = value;
	}

    /**
     * Setter landmark
     * @param {string} value
     */
	public set landmark(value: string) {
		this._landmark = value;
	}

    /**
     * Setter locationLink
     * @param {string} value
     */
	public set locationLink(value: string) {
		this._locationLink = value;
	}

    /**
     * Setter isActive
     * @param {boolean} value
     */
	public set isActive(value: boolean) {
		this._isActive = value;
	}

    /**
     * Setter isSelected
     * @param {boolean} value
     */
	public set isSelected(value: boolean) {
		this._isSelected = value;
	}

    /**
     * Setter isMainBranch
     * @param {boolean} value
     */
	public set isMainBranch(value: boolean) {
		this._isMainBranch = value;
	}

    /**
     * Setter isAllowLocalPurchase
     * @param {boolean} value
     */
	public set isAllowLocalPurchase(value: boolean) {
		this._isAllowLocalPurchase = value;
	}

    /**
     * Setter isAllowImportPurchase
     * @param {boolean} value
     */
	public set isAllowImportPurchase(value: boolean) {
		this._isAllowImportPurchase = value;
	}

    /**
     * Setter isConsiderGSTAvgPrice
     * @param {boolean} value
     */
	public set isConsiderGSTAvgPrice(value: boolean) {
		this._isConsiderGSTAvgPrice = value;
	}


    /**
     * Setter idOfState
     * @param {number} value
     */
	public set idOfState(value: number) {
		this._idOfState = value;
	}

    /**
     * Setter idOfCountry
     * @param {number} value
     */
	public set idOfCountry(value: number) {
		this._idOfCountry = value;
	}

    /**
     * Setter idOfCity
     * @param {number} value
     */
	public set idOfCity(value: number) {
		this._idOfCity = value;
	}


    /**
     * Getter idOfUser
     * @return {any}
     */
	public get idOfUser(): any {
		return this._idOfUser;
	}

    /**
     * Setter idOfUser
     * @param {any} value
     */
	public set idOfUser(value: any) {
		this._idOfUser = value;
	}


}