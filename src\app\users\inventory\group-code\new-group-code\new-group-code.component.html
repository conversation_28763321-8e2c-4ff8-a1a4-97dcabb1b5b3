<div class="page-content">
  <div class="page-title-wrapper">
    <div class="page-title-left">
      <h4>{{groupCodeId ? 'Edit' : 'Add New'}} Group Code</h4>
    </div>
    <div class="page-title-right">
      <Button class="btn btn-sm btn-icon btn-outline-white" [routerLink]="['/users/inventory/group-code']"
        ngbTooltip="Close" placement="left" container="body" triggers="hover">
        <i class="th th-close"></i>
      </Button>
    </div>
  </div>
  <div class="content-area">
    <div class="card card-theme card-forms" [formGroup]="groupCodeForm">
      <div class="card-body">
        <div class="row">
          <div class="col-lg-4 col-md-6 col-sm-12">
            <div class="form-group form-group-inline-control required">
              <label class="form-label">Group Code</label>
              <div class="form-control-wrapper">
                <input [maxlength]="utilsService.validationService.MAX_100" type="text" class="form-control"
                  placeholder="Enter Group Code" formControlName="groupName" [(ngModel)]="groupCodeObj.groupName">
                <div class="message error-message"
                  *ngIf="groupCodeForm.controls['groupName'].hasError('required') &&  groupCodeForm.controls['groupName'].touched">
                  {{utilsService.validationService.GRP_CODE_REQ}}
                </div>
                <div class="message error-message"
                  *ngIf="!groupCodeForm.controls['groupName'].hasError('required') && !groupCodeForm.controls['groupName'].valid && groupCodeForm.controls['groupName'].touched">
                  {{utilsService.validationService.GRP_CODE_INVALID}}
                </div>
              </div>
            </div>
            <div class="form-group form-group-inline-control" *ngIf="groupCodeId">
              <label class="form-label">Group Code ID</label>
              <div class="form-control-wrapper">
                <input type="text" class="form-control" placeholder="Enter Group Code" disabled
                  [ngModelOptions]="{standalone: true}" [(ngModel)]="groupCodeObj.groupCodeId">
              </div>
            </div>
            <div class="form-group theme-ngselect form-group-inline-control required theme-ngselect-group-list">
              <label class="form-label">Category</label>
              <div class="form-control-wrapper">
                <div class="form-group-button">
                  <ng-select placeholder="Select Category" [multiple]="false" [clearable]="false"
                    [items]="flattenedParentCategory" bindLabel="categoryName" bindValue="id"
                    formControlName="categoryId" [(ngModel)]="groupCodeObj.categoryId">
                    <ng-template ng-option-tmp let-item="item">
                      <span [title]="item.categoryName" [style.padding-left.px]="item.index * 25"
                        [ngClass]="{'ng-option-child-label' : item.isChild}" [class]="item.className">
                        {{ item.categoryName }}
                      </span>
                    </ng-template>
                  </ng-select>
                  <!-- <button class="btn btn-outline-white"><i class="th th-outline-add-circle text-primary"></i></button> -->
                </div>
                <div class="message error-message"
                  *ngIf="groupCodeForm.controls['categoryId'].hasError('required') &&  groupCodeForm.controls['categoryId'].touched">
                  {{utilsService.validationService.CATEGORY_REQ}}
                </div>
              </div>
            </div>
            <div class="form-group form-group-inline-control">
              <label class="form-label">Status</label>
              <div class="form-control-wrapper">
                <div class="switch-box">
                  <label class="switch" htmlFor="switch">
                    <input type="checkbox" id='switch' formControlName="isActive" [(ngModel)]="groupCodeObj.isActive" />
                    <div class="slider round"></div>
                  </label>
                </div>
              </div>
            </div>
          </div>
          <div class="col-lg-8 col-md-12" (paste)="onSelectAttachments($event);doc.value = ''"
            (dragover)="onSelectAttachments($event);doc.value = ''" (drop)="onSelectAttachments($event);doc.value = ''">
            <div class="row">
              <div class="col-lg-6">
                <div class="form-group required">
                  <div class="form-label">Upload Images & Videos<i class="th th-outline-info-circle ms-1"
                      [ngbTooltip]="utilsService.validationService.DOC_INFO_IMG_VID" placement="bottom" container="body"
                      triggers="hover"></i>
                  </div>
                  <div class='attachments-container h-100'>
                    <div class='attachments-content'>
                      <button class='btn btn-attachments'><i class="bi bi-upload"></i></button>
                      <p>Drag and Drop Images & Videos here or <span class='text-primary'>Choose file</span></p>
                    </div>
                    <input #doc type="file" ref={imageRef} multiple
                      (change)="onSelectAttachments($event);doc.value = ''"
                      accept="image/x-png,image/jpeg,image/jpg,video/mp4,video/x-m4v" />
                  </div>
                </div>
              </div>
              <div class="col-12">
                <div class="form-group">
                  <div class="attachments-wrapper">
                    <div class='attachments-upload-grid-container attachments-upload-grid-container2'
                      style="min-height: 10px;">
                      <div class='attachments-upload-row'>
                        <div class='attachments-upload-col' *ngFor="let item of groupCodeObj.docs; index as i; trackBy: trackByIndex;">
                          <div class='card-attachments-upload'>
                            <div class='attachments-image'>
                              <ng-container *ngIf="utilsService.isImage(item.originalName)">
                                <img (click)="openLink(item.formattedName, null)" loading="lazy" *ngIf="!item.file"
                                  [src]="item.formattedName ? (utilsService.imgPath + item.formattedName) : null"
                                  alt="valamji" />
                                <img (click)="openLink(null, item.formattedName)" loading="lazy" *ngIf="item.file"
                                  [src]="item.formattedName ? (item.formattedName) : null" alt="valamji" />
                              </ng-container>
                              <ng-container *ngIf="utilsService.isMedia(item.originalName)">
                                <img *ngIf="item.file" (click)="openLink(null, item.formattedName)"
                                  src="assets/images/files/file-video.svg" alt="valamji" />
                                <img *ngIf="!item.file" (click)="openLink(item.formattedName, null)"
                                  src="assets/images/files/file-video.svg" alt="valamji" />
                              </ng-container>
                            </div>
                            <div class="attachments-text"
                              [ngbTooltip]="item.fileName ? item.fileName : item.originalName" placement="bottom"
                              container="body" triggers="hover">
                              <h6 class="file-name">{{item.fileName ? item.fileName : item.originalName}}</h6>
                            </div>
                            <button (click)="removeAttachment(i, item)" class="btn-close" variant="close"><i
                                class='th th-close'></i></button>
                          </div>


                          <div class="radio radio-primary" *ngIf="utilsService.isImage(item.originalName)">
                            <input type="radio" [id]="'thumb-' + i" [(ngModel)]="item.isMarkDefault"
                              [ngModelOptions]="{standalone: true}" [value]="true" (change)="onPrimaryChange(i)"
                              [name]="'thumb-' + i">
                            <label [for]="'thumb-' + i">Mark default</label>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-lg-8 col-md-12 col-sm-12">
            <div class='nav-tabs-outer nav-tabs-style2'>
              <nav>
                <div class="nav nav-tabs" id="nav-tab" role="tablist">
                  <button class="nav-link active" id="nav-associates-items-tab" data-bs-toggle="tab"
                    data-bs-target="#nav-associates-items" type="button" role="tab" aria-controls="nav-associates-items"
                    aria-selected="true"> <i class="th th-outline-box"></i>Associated Items</button>
                </div>
              </nav>
              <div class="tab-content" id="nav-tabContent">
                <div class="tab-pane fade show active" id="nav-associates-items" role="tabpanel"
                  aria-labelledby="nav-associates-items-tab">
                  <div class="table-responsive">
                    <table class="table-theme table-hover table table-bordered" formArrayName="item">
                      <thead class="border-less">
                        <tr>
                          <th>Item Details</th>
                          <th>HSN Code</th>
                          <th>Action</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr class="tbl-add-row tbl-bg-white" *ngFor="let contact of items.controls; index as i" [formGroupName]="i">
                          <td class="tbl-user tbl-form-group w-50">
                            <div class="tbl-user-checkbox-srno">
                              <span>{{utilsService.padNumber(i + 1)}}.</span>
                              <div class="form-group theme-ngselect theme-ngselect-user-list form-border-less w-100"
                                *ngIf="!associatedItems[i].id">
                                <div class="d-flex align-items-center gap-2 justify-content-space-between">
                                  <ng-select (change)="onChangeItem(i)"
                                    (clear)="onClearItem(i)" class="new-ng-width" appendTo=".theme-ngselect"
                                    [ngClass]="{'required': contact.get('itemId').invalid && contact.get('itemId').touched}" formControlName="itemId"
                                    [items]="associatedItems[i].itemDropdown" placeholder="Select Item" [searchFn]="customSearchFn" bindValue="id"
                                    [(ngModel)]="associatedItems[i].itemId"
                                    [virtualScroll]="true">
                                    <ng-template ng-label-tmp let-item="item">
                                      <div class="tbl-user">
                                        <div class="tbl-user-checkbox-srno">
                                          <div class="tbl-user-wrapper">
                                            <div class="tbl-user-image">
                                              <img loading="lazy" [src]="item.formattedName ? (utilsService.imgPath + item.formattedName) : ''"
                                                alt="valamji">
                                            </div>
                                            <div class="tbl-user-text-action">
                                              <div class="tbl-user-text">
                                                <p>{{ item.displayName }} </p>
                                                <span>{{item.skuId}}</span>
                                              </div>
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                    </ng-template>
                                    <ng-template ng-option-tmp let-item="item">
                                      <div class="tbl-user">
                                        <div class="tbl-user-checkbox-srno">
                                          <div class="tbl-user-wrapper">
                                            <div class="tbl-user-image">
                                              <img [src]="item.formattedName ? (utilsService.imgPath + item.formattedName) : ''" alt="valamji">
                                            </div>
                                            <div class="tbl-user-text-action">
                                              <div class="tbl-user-text">
                                                <p>{{ item.displayName }} </p>
                                                <span>{{item.skuId}}</span>
                                              </div>
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                    </ng-template>
                                  </ng-select>
                                </div>
                              </div>
                              <div class="form-group theme-ngselect theme-ngselect-user-list form-border-less w-100"
                                *ngIf="associatedItems[i].id">
                                <div class="tbl-user-checkbox-srno">
                                  <div class="tbl-user-wrapper new-ng-width justify-content-space-between">
                                    <div
                                      *ngIf="!utilsService.isEmptyObjectOrNullUndefined(associatedItems[i]?.formattedName); else fallbackImage">
                                      <div class="tbl-user-image">
                                        <img [src]="utilsService.imgPath + associatedItems[i]?.formattedName"
                                          alt="valamji">
                                      </div>
                                    </div>
                                    <ng-template #fallbackImage>
                                      <div class="tbl-user-image">
                                        {{associatedItems[i]?.displayName?.charAt(0).toUpperCase()}}
                                      </div>
                                    </ng-template>
                                    <div class="tbl-user-text-action">
                                      <div class="tbl-user-text">
                                        <p>{{ associatedItems[i]?.displayName }} </p>
                                        <span>{{associatedItems[i]?.skuId}}</span>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </td>
                          <td class="w-50">{{associatedItems[i]?.hsnCode}}</td>
                          <td class="tbl-action w-25">
                            <div class="tbl-action-group">
                              <button *ngIf="items.controls.length > 1" class="btn btn-xs btn-light-danger btn-icon"
                                (click)="openRemoveAIModal(i, associatedItems[i])" ngbTooltip="Remove Item"
                                placement="bottom" container="body" triggers="hover"><i
                                  class="th th-outline-trash"></i></button>
                            </div>
                          </td>
                        </tr>
                        <tr *ngIf="utilsService.isEmptyObjectOrNullUndefined(associatedItems)">
                          <td colspan="20" class="text-center">
                            <span
                              class="truncate">{{utilsService.serverVariableService.STRING_WHEN_NO_RECORDS_FOUND}}</span>
                          </td>
                        </tr>
                      </tbody>
                      <tfoot>
                        <tr class="tbl-add-new">
                          <td colspan="100">
                            <button (click)="addItem()" class="btn btn-sm btn-link btn-icon-text text-primary">
                              <i class="th-bold-add-circle"></i> Add New Row </button>
                          </td>
                        </tr>
                      </tfoot>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class='bottombar-wrapper bottom-fixed'>
      <div class='bottombar-container'>
        <div class='bottombar-left'>
          <button (click)="onSave()" type="button" class="btn btn-primary btn-icon-text btn-sm"> <i
              class="th th-outline-tick-circle"></i>
            {{groupCodeId ? 'Update' : 'Save'}}</button>
          <!-- <button type="button" class="btn btn-outline-white btn-icon-text btn-sm"><i
              class="th th-outline-document-text"></i>Save as Draft</button> -->
          <button [routerLink]="['/users/inventory/group-code']" type="button"
            class="btn btn-outline-white btn-icon-text btn-sm"><i class="th th-outline-close-circle"></i>Cancel</button>
        </div>
        <div class='bottombar-right'>

        </div>
      </div>
    </div>
  </div>
</div>

<!-- Remove Associated Item Modal -->
<div class="modal modal-theme modal-confirmation modal-reject fade" id="groupCodeAssociateItemRemove" tabindex="-1"
  aria-labelledby="groupCodeAssociateItemRemoveLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
            class='th th-close'></i></button>
      </div>
      <div class="modal-body">
        <div class="modal-confirmation-container">
          <div class="modal-confirmation-icon">
            <i class="th th-bold-trash"></i>
          </div>
          <div class="modal-confirmation-content">
            <h5>Are You Sure?</h5>
            <p *ngIf="!itemObj.id">You want to Remove <b>{{"Selected Item"}}</b>.</p>
            <p *ngIf="itemObj.id">You want to Remove <b>{{itemObj?.displayName}}</b> Item.</p>
          </div>
        </div>
        <div class="modal-button-group modal-full-width-btn">
          <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
          <button (click)="removeItem()" type="button" class="btn btn-primary btn-icon-text"> <i
              class="th th-outline-tick-circle"></i>
            Remove</button>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- Remove Associated Item Modal End -->

<!-- Confirmation modal if new associated item added -->
<div class="modal modal-theme modal-confirmation modal-warning-two fade" id="newAssociatedItemModal" tabindex="-1"
  aria-labelledby="newAssociatedItemModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
            class='th th-close'></i></button>
      </div>
      <div class="modal-body">
        <div class="modal-confirmation-container">
          <div class="modal-confirmation-icon">
            <i class="th th-outline-info-circle"></i>
          </div>
          <div class="modal-confirmation-content">
            <h5>Are You Sure?</h5>
            <p>Do you want to {{groupCodeId ? 'Update' : 'Save'}} this Group Code?</p>
            <p><b>Note:</b> If new items are added, they will be assigned to this Group Code and removed from the previous one.</p>
          </div>                   
        </div>
        <div class="modal-button-group modal-full-width-btn">
          <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
          <button type="button" class="btn btn-primary btn-icon-text" (click)="onModalConfirmation()"> <i
              class="th th-outline-tick-circle"></i>{{groupCodeId ? 'Update' : 'Save'}}</button>
        </div>
      </div>
    </div>
  </div>
</div>