import { Component, OnInit } from '@angular/core';

@Component({
  selector: 'app-audit-ticket-deleted',
  templateUrl: './audit-ticket-deleted.component.html',
  styleUrls: ['./audit-ticket-deleted.component.scss']
})
export class AuditTicketDeletedComponent implements OnInit {

  Option = [
    { id: 1, name: 'Active' },
    { id: 2, name: 'Active 2' },
    { id: 3, name: 'Active 3' },
  ];

  demo = [
    { id: 1, name: 'Option 1' },
    { id: 2, name: 'Option 2' },
    { id: 3, name: 'Option 3' },
  ];

  demo2 = [
    { id: 1, name: 'Export' },
    { id: 2, name: 'Another action' },
    { id: 3, name: 'Something' },
  ];


  ItemGroup = [
    { id: 1, name: 'ItemGroup' },
    { id: 2, name: 'ItemGroup 2' },
    { id: 3, name: 'ItemGroup 3' },
  ];

  Active = [
    { id: 1, name: 'Active' },
    { id: 2, name: 'Active 2' },
    { id: 3, name: 'Active 3' },
  ];

  Export = [
    { id: 1, name: 'Export' },
    { id: 2, name: 'Export 2' },
    { id: 3, name: 'Export 3' },
  ];


  pagination = [
    { id: 1, name: '1' },
    { id: 2, name: '2' },
    { id: 3, name: '3' },
    { id: 3, name: '4' },
    { id: 3, name: '5' },
  ];

  selectedOption: number = 1;
  selectedDemo1: number = 1;
  selectedDemo2: number = 1;
  selectedPagination: number = 1;

  selectedItemGroup: number = 1;
  selectedActive: number = 1;
  selectedExport: number = 1;

  isExpanded: boolean = true;

  toggleExpand(): void {
    this.isExpanded = !this.isExpanded;
  }

  constructor() { }

  ngOnInit(): void {
  }

}
