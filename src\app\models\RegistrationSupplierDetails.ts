import { deserializeAs, serializeAs } from "cerialize";

export class RegistrationSupplierDetails {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('shortCode')
    @deserializeAs('shortCode')
    private _shortCode: string;

    @serializeAs('supplierType')
    @deserializeAs('supplierType')
    private _supplierType: string;

    @serializeAs('paymentPerson')
    @deserializeAs('paymentPerson')
    private _paymentPerson: string;

    @serializeAs('paymentBy')
    @deserializeAs('paymentBy')
    private _paymentBy: string;

    @deserializeAs('supplierDoc')
    private _supplierDoc: any[];

    constructor() {
        this.supplierDoc = [];
    }


    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Getter shortCode
     * @return {string}
     */
	public get shortCode(): string {
		return this._shortCode;
	}

    /**
     * Getter supplierType
     * @return {string}
     */
	public get supplierType(): string {
		return this._supplierType;
	}

    /**
     * Getter paymentPerson
     * @return {string}
     */
	public get paymentPerson(): string {
		return this._paymentPerson;
	}

    /**
     * Getter paymentBy
     * @return {string}
     */
	public get paymentBy(): string {
		return this._paymentBy;
	}

    /**
     * Getter supplierDoc
     * @return {any[]}
     */
	public get supplierDoc(): any[] {
		return this._supplierDoc;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Setter shortCode
     * @param {string} value
     */
	public set shortCode(value: string) {
		this._shortCode = value;
	}

    /**
     * Setter supplierType
     * @param {string} value
     */
	public set supplierType(value: string) {
		this._supplierType = value;
	}

    /**
     * Setter paymentPerson
     * @param {string} value
     */
	public set paymentPerson(value: string) {
		this._paymentPerson = value;
	}

    /**
     * Setter paymentBy
     * @param {string} value
     */
	public set paymentBy(value: string) {
		this._paymentBy = value;
	}

    /**
     * Setter supplierDoc
     * @param {any[]} value
     */
	public set supplierDoc(value: any[]) {
		this._supplierDoc = value;
	}

}