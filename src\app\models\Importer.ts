import { deserializeAs, serializeAs } from 'cerialize';

export class Importer {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('createdDate')
    @deserializeAs('createdDate')
    private _createdDate: string;

    @serializeAs('isActive')
    @deserializeAs('isActive')
    private _isActive: boolean;

    @serializeAs('isSelected')
    @deserializeAs('isSelected')
    private _isSelected: boolean;

    @serializeAs('countryMaster')
    @deserializeAs('countryMaster')
    private _countryMaster: any;

    @serializeAs('stateMaster')
    @deserializeAs('stateMaster')
    private _stateMaster: any;

    @serializeAs('cityMaster')
    @deserializeAs('cityMaster')
    private _cityMaster: any;

    @serializeAs('Address')
    @deserializeAs('Address')
    private _Address: string;

    @serializeAs('countryId')
    @deserializeAs('countryId')
    private _countryId: number;

    @serializeAs('stateId')
    @deserializeAs('stateId')
    private _stateId: number;

    @serializeAs('cityId')
    @deserializeAs('cityId')
    private _cityId: number;

    @serializeAs('zipCode')
    @deserializeAs('zipCode')
    private _zipCode: string;

    @serializeAs('email')
    @deserializeAs('email')
    private _email: string;

    @serializeAs('shortCode')
    @deserializeAs('shortCode')
    private _shortCode: string;

    @serializeAs('firstName')
    @deserializeAs('firstName')
    private _firstName: string;

    @serializeAs('lastName')
    @deserializeAs('lastName')
    private _lastName: string;

    @serializeAs('contactNo')
    @deserializeAs('contactNo')
    private _contactNo: string;

    @serializeAs('address')
    @deserializeAs('address')
    private _address: string;

    @serializeAs('lastModifiedDate')
    @deserializeAs('lastModifiedDate')
    private _lastModifiedDate: string;

    @serializeAs('contactCountryExtensionId')
    @deserializeAs('contactCountryExtensionId')
    private _contactCountryExtensionId: number;

    @deserializeAs('contactExtension')
    private _contactExtension: any;

    @deserializeAs('isDefault')
    private _isDefault: boolean;

    constructor() {
        this.isActive = false;
        this.isSelected = false;
        this.isDefault = false;
    }

    /**
     * Getter isDefault
     * @return {boolean}
     */
	public get isDefault(): boolean {
		return this._isDefault;
	}

    /**
     * Setter isDefault
     * @param {boolean} value
     */
	public set isDefault(value: boolean) {
		this._isDefault = value;
	}

    /**
     * Getter contactExtension
     * @return {any}
     */
	public get contactExtension(): any {
		return this._contactExtension;
	}

    /**
     * Setter contactExtension
     * @param {any} value
     */
	public set contactExtension(value: any) {
		this._contactExtension = value;
	}


    /**
     * Getter contactCountryExtensionId
     * @return {number}
     */
	public get contactCountryExtensionId(): number {
		return this._contactCountryExtensionId;
	}

    /**
     * Setter contactCountryExtensionId
     * @param {number} value
     */
	public set contactCountryExtensionId(value: number) {
		this._contactCountryExtensionId = value;
	}


    /**
     * Getter lastModifiedDate
     * @return {string}
     */
	public get lastModifiedDate(): string {
		return this._lastModifiedDate;
	}

    /**
     * Setter lastModifiedDate
     * @param {string} value
     */
	public set lastModifiedDate(value: string) {
		this._lastModifiedDate = value;
	}
    

    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Getter createdDate
     * @return {string}
     */
	public get createdDate(): string {
		return this._createdDate;
	}

    /**
     * Getter isActive
     * @return {boolean}
     */
	public get isActive(): boolean {
		return this._isActive;
	}

    /**
     * Getter isSelected
     * @return {boolean}
     */
	public get isSelected(): boolean {
		return this._isSelected;
	}

    /**
     * Getter countryMaster
     * @return {any}
     */
	public get countryMaster(): any {
		return this._countryMaster;
	}

    /**
     * Getter stateMaster
     * @return {any}
     */
	public get stateMaster(): any {
		return this._stateMaster;
	}

    /**
     * Getter cityMaster
     * @return {any}
     */
	public get cityMaster(): any {
		return this._cityMaster;
	}

    /**
     * Getter Address
     * @return {string}
     */
	public get Address(): string {
		return this._Address;
	}

    /**
     * Getter countryId
     * @return {number}
     */
	public get countryId(): number {
		return this._countryId;
	}

    /**
     * Getter stateId
     * @return {number}
     */
	public get stateId(): number {
		return this._stateId;
	}

    /**
     * Getter cityId
     * @return {number}
     */
	public get cityId(): number {
		return this._cityId;
	}

    /**
     * Getter zipCode
     * @return {string}
     */
	public get zipCode(): string {
		return this._zipCode;
	}

    /**
     * Getter email
     * @return {string}
     */
	public get email(): string {
		return this._email;
	}

    /**
     * Getter shortCode
     * @return {string}
     */
	public get shortCode(): string {
		return this._shortCode;
	}

    /**
     * Getter firstName
     * @return {string}
     */
	public get firstName(): string {
		return this._firstName;
	}

    /**
     * Getter lastName
     * @return {string}
     */
	public get lastName(): string {
		return this._lastName;
	}

    /**
     * Getter contactNo
     * @return {string}
     */
	public get contactNo(): string {
		return this._contactNo;
	}

    /**
     * Getter address
     * @return {string}
     */
	public get address(): string {
		return this._address;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Setter createdDate
     * @param {string} value
     */
	public set createdDate(value: string) {
		this._createdDate = value;
	}

    /**
     * Setter isActive
     * @param {boolean} value
     */
	public set isActive(value: boolean) {
		this._isActive = value;
	}

    /**
     * Setter isSelected
     * @param {boolean} value
     */
	public set isSelected(value: boolean) {
		this._isSelected = value;
	}

    /**
     * Setter countryMaster
     * @param {any} value
     */
	public set countryMaster(value: any) {
		this._countryMaster = value;
	}

    /**
     * Setter stateMaster
     * @param {any} value
     */
	public set stateMaster(value: any) {
		this._stateMaster = value;
	}

    /**
     * Setter cityMaster
     * @param {any} value
     */
	public set cityMaster(value: any) {
		this._cityMaster = value;
	}

    /**
     * Setter Address
     * @param {string} value
     */
	public set Address(value: string) {
		this._Address = value;
	}

    /**
     * Setter countryId
     * @param {number} value
     */
	public set countryId(value: number) {
		this._countryId = value;
	}

    /**
     * Setter stateId
     * @param {number} value
     */
	public set stateId(value: number) {
		this._stateId = value;
	}

    /**
     * Setter cityId
     * @param {number} value
     */
	public set cityId(value: number) {
		this._cityId = value;
	}

    /**
     * Setter zipCode
     * @param {string} value
     */
	public set zipCode(value: string) {
		this._zipCode = value;
	}

    /**
     * Setter email
     * @param {string} value
     */
	public set email(value: string) {
		this._email = value;
	}

    /**
     * Setter shortCode
     * @param {string} value
     */
	public set shortCode(value: string) {
		this._shortCode = value;
	}

    /**
     * Setter firstName
     * @param {string} value
     */
	public set firstName(value: string) {
		this._firstName = value;
	}

    /**
     * Setter lastName
     * @param {string} value
     */
	public set lastName(value: string) {
		this._lastName = value;
	}

    /**
     * Setter contactNo
     * @param {string} value
     */
	public set contactNo(value: string) {
		this._contactNo = value;
	}

    /**
     * Setter address
     * @param {string} value
     */
	public set address(value: string) {
		this._address = value;
	}

}