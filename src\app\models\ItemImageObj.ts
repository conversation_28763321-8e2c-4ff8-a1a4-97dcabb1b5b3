import { deserializeAs, serializeAs } from "cerialize";

export class ItemImageObj {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('formattedName')
    @deserializeAs('formattedName')
    private _formattedName: string;

    @serializeAs('originalName')
    @deserializeAs('originalName')
    private _originalName: string;

    @serializeAs('isMarkDefault')
    @deserializeAs('isMarkDefault')
    private _isMarkDefault: boolean;

    @serializeAs('isMediaLinks')
    @deserializeAs('isMediaLinks')
    private _isMediaLinks: boolean;

    @deserializeAs('linkUrl')
    private _linkUrl: string;

    @serializeAs('isLink')
    @deserializeAs('isLink')
    private _isLink: boolean;

    @serializeAs('grnDocGroupLinkId')
    @deserializeAs('grnDocGroupLinkId')
    private _grnDocGroupLinkId: number;

    @serializeAs('file')
    @deserializeAs('file')
    private _file: any;

    constructor() {
        this.isMediaLinks = false;
        this.isMarkDefault = false;
        this.isLink = false;
    }


    /**
     * Getter file
     * @return {any}
     */
	public get file(): any {
		return this._file;
	}

    /**
     * Setter file
     * @param {any} value
     */
	public set file(value: any) {
		this._file = value;
	}


    /**
     * Getter grnDocGroupLinkId
     * @return {number}
     */
	public get grnDocGroupLinkId(): number {
		return this._grnDocGroupLinkId;
	}

    /**
     * Setter grnDocGroupLinkId
     * @param {number} value
     */
	public set grnDocGroupLinkId(value: number) {
		this._grnDocGroupLinkId = value;
	}


    /**
     * Getter isLink
     * @return {boolean}
     */
	public get isLink(): boolean {
		return this._isLink;
	}

    /**
     * Setter isLink
     * @param {boolean} value
     */
	public set isLink(value: boolean) {
		this._isLink = value;
	}



    /**
     * Getter linkUrl
     * @return {string}
     */
	public get linkUrl(): string {
		return this._linkUrl;
	}

    /**
     * Setter linkUrl
     * @param {string} value
     */
	public set linkUrl(value: string) {
		this._linkUrl = value;
	}


    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Getter formattedName
     * @return {string}
     */
	public get formattedName(): string {
		return this._formattedName;
	}

    /**
     * Getter originalName
     * @return {string}
     */
	public get originalName(): string {
		return this._originalName;
	}

    /**
     * Getter isMarkDefault
     * @return {boolean}
     */
	public get isMarkDefault(): boolean {
		return this._isMarkDefault;
	}

    /**
     * Getter isMediaLinks
     * @return {boolean}
     */
	public get isMediaLinks(): boolean {
		return this._isMediaLinks;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Setter formattedName
     * @param {string} value
     */
	public set formattedName(value: string) {
		this._formattedName = value;
	}

    /**
     * Setter originalName
     * @param {string} value
     */
	public set originalName(value: string) {
		this._originalName = value;
	}

    /**
     * Setter isMarkDefault
     * @param {boolean} value
     */
	public set isMarkDefault(value: boolean) {
		this._isMarkDefault = value;
	}

    /**
     * Setter isMediaLinks
     * @param {boolean} value
     */
	public set isMediaLinks(value: boolean) {
		this._isMediaLinks = value;
	}

}