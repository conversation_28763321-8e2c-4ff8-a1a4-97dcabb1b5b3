export interface PaginationData {
    pageNo: number;
    pageSize: string;
    sortOrder: string;
    sortColumn: string;
    totalData: number;
    statusForModal: string;
    searchText: string;
    activeFlag: boolean;
    activeInactiveStatus: any;
    flagForSelectAll: boolean;
    country?: number;
    state?: number;
}

export const navItems: { name: string, url: string }[] = [
    { name: 'Color', url: '/users/masters/color' },
    { name: 'Unit', url: '/users/masters/unit' },
    { name: 'Size', url: '/users/masters/size' },
    { name: 'HSN Code', url: '/users/masters/hsn-code' },
    { name: 'Packing Type', url: '/users/masters/packing-type' },
    { name: 'Taxes & Compliance', url: '/users/masters/taxes-compliance' },
    { name: 'Bank Group', url: '/users/masters/bank-group' },
    { name: 'Location', url: '/users/masters/location' },
    { name: 'Courier Company', url: '/users/masters/courier-company' },
    { name: 'Ticket Subject', url: '/users/masters/ticket-subject' },
    { name: 'Level Master', url: '/users/masters/level' },
    { name: 'Market Type', url: '/users/masters/market-types' },
    { name: 'Payment Terms', url: '/users/masters/payment-terms' },
    { name: 'Payment Type', url: '/users/masters/payment-type' },
    { name: 'Currency', url: '/users/masters/currency' },
    { name: 'Transporter', url: '/users/masters/transporter' },
    { name: 'Travellers', url: '/users/masters/traveller' },
    { name: 'Reason', url: '/users/masters/reason' },
    { name: 'Importer', url: '/users/masters/importer' },
    { name: 'QC Checklist', url: '/users/masters/qc-checklist' },
    { name: 'Season Master', url: '/users/masters/season' },
    { name: 'Sales Order Type', url: '/users/masters/sales-order-type' },
    { name: 'Department', url: '/users/masters/department' },
    { name: 'Expense Type', url: '/users/masters/expense-type' },
    { name: 'Code Master', url: '/users/masters/code-sub-code' }
];

export const activeInactiveStatus = [
    { value: true, label: 'Active' },
    { value: false, label: 'Inactive' },
];

export const statusYesNo = [
    { value: true, label: 'Yes' },
    { value: false, label: 'No' },
];

export const BlackBlockList = [
    { value: 'block', label: 'Blocked Users' },
    { value: 'black', label: 'Blacklisted Users' },
];

export const genderList = [
    { value: 'MALE', label: 'Male', },
    { value: 'FEMALE', label: 'Female', },
];

export const ASC_ORDER = 'A';
export const DESC_ORDER = 'D';


export const COLOR_MASTER = [
    {
        'displayName': 'Color Name',
        'keyName': 'colorName',
        'class': 'd-flex align-items-center gap-2 tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'China Name',
        'keyName': 'chineseValue',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Short Code',
        'keyName': 'shortCode',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Date Created',
        'keyName': 'lastModifiedDate',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
]

export const UNIT_MASTER = [
    {
        'displayName': 'Unit Name',
        'keyName': 'unitName',
        'class': 'd-flex align-items-center gap-2 tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Short Code',
        'keyName': 'shortCode',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Category',
        'keyName': 'unitMasterCategory',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Base Unit',
        'keyName': '',
        'class': '',
        'iconClass': ''
    },
    {
        'displayName': 'Factor',
        'keyName': 'conversionToMeter',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Date Created',
        'keyName': 'lastModifiedDate',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
]

export const SIZE_MASTER = [
    {
        'displayName': 'Size Name',
        'keyName': 'sizeName',
        'class': 'd-flex align-items-center gap-2 tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Short Code',
        'keyName': 'shortCode',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Date Created',
        'keyName': 'lastModifiedDate',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
]

export const HSN_MASTER = [
    {
        'displayName': 'HSN Code',
        'keyName': 'h.hsnCode',
        'class': 'd-flex align-items-center gap-2 tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Description',
        'keyName': 'description',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'GST Slab',
        'keyName': 'taxesCompliance.taxName',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'CGST Rate(%)',
        'keyName': 'taxesCompliance.cGSTRate',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'SGST / UTGST Rate (%)',
        'keyName': 'taxesCompliance.sGSTRate',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'IGST Rate (%)',
        'keyName': 'taxesCompliance.rate',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
]

export const PACKING_MASTER = [
    {
        'displayName': 'Packing Type Name',
        'keyName': 'packingName',
        'class': 'd-flex align-items-center gap-2 tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Date Created',
        'keyName': 'lastModifiedDate',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
]

export const OTHER_TAX_MASTER = [
    {
        'displayName': 'Tax Name',
        'keyName': 'taxName',
        'class': 'd-flex align-items-center gap-2 tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Rate (%)',
        'keyName': 'rate',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Date Created',
        'keyName': 'lastModifiedDate',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
]

export const GST_SLAB_MASTER = [
    {
        'displayName': 'Slab Name',
        'keyName': 'taxName',
        'class': 'd-flex align-items-center gap-2 tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Rate (%)',
        'keyName': 'rate',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Date Created',
        'keyName': 'lastModifiedDate',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
]

export const BANK_GROUP_MASTER = [
    {
        'displayName': 'Name',
        'keyName': 'bankName',
        'class': 'd-flex align-items-center gap-2 tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'GST No',
        'keyName': 'gstNo',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Linked Banks',
        'keyName': '',
        'class': '',
        'iconClass': ''
    },
    {
        'displayName': 'Opening Balance',
        'keyName': 'openingBalance',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Note',
        'keyName': '',
        'class': '',
        'iconClass': ''
    },
]

export const COUNTRY_MASTER = [
    {
        'displayName': 'Country Name',
        'keyName': 'name',
        'class': 'd-flex align-items-center gap-2 tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Country Code',
        'keyName': 'code',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Phone No Extension',
        'keyName': '',
        'class': '',
        'iconClass': ''
    },
    {
        'displayName': 'Purchase Country',
        'keyName': 'isMarkAsPurchaseCountry',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Date Created',
        'keyName': 'lastModifiedDate',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
]

export const STATE_MASTER = [
    {
        'displayName': 'State Name',
        'keyName': 'name',
        'class': 'd-flex align-items-center gap-2 tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'State Code',
        'keyName': 'code',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Country Name',
        'keyName': 'countryMaster.id',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Purchase State',
        'keyName': 'markAsPurchaseState',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Date Created',
        'keyName': 'lastModifiedDate',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
]

export const CITY_MASTER = [
    {
        'displayName': 'City Name',
        'keyName': 'name',
        'class': 'd-flex align-items-center gap-2 tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'City Code',
        'keyName': 'code',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'State Name',
        'keyName': 'stateMaster.id',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Country Name',
        'keyName': 'countryMaster.id',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Shipping Days',
        'keyName': 'shippingDays',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Purchase City',
        'keyName': 'markAsPurchaseCity',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Date Created',
        'keyName': 'lastModifiedDate',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    // {
    //     'displayName': 'Purchase City',
    //     'keyName': '',
    //     'class': '',
    //     'iconClass': ''
    // },
]

export const TICKET_MASTER = [
    {
        'displayName': 'Subject Name',
        'keyName': 'subjectName',
        'class': 'd-flex align-items-center gap-2 tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Date Created',
        'keyName': 'lastModifiedDate',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
]

export const COURIER_COMPANY_MASTER = [
    {
        'displayName': 'Name',
        'keyName': 'courierName',
        'class': 'd-flex align-items-center gap-2 tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Mobile No',
        'keyName': 'mobileNo',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Address',
        'keyName': 'address',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Price/kg',
        'keyName': 'priceKg',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Date Created',
        'keyName': 'lastModifiedDate',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
]

export const MARKET_TYPE_MASTER = [
    {
        'displayName': 'Market Type',
        'keyName': 'marketName',
        'class': 'd-flex align-items-center gap-2 tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Date Created',
        'keyName': 'lastModifiedDate',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
]

export const PAYMENT_TERMS_MASTER = [
    {
        'displayName': 'Payment Term',
        'keyName': 'paymentTermName',
        'class': 'd-flex align-items-center gap-2 tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'No Of Days',
        'keyName': 'noOfDays',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Date Created',
        'keyName': 'lastModifiedDate',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
]

export const PAYMENT_TYPE_MASTER = [
    {
        'displayName': 'Payment Type',
        'keyName': 'paymentTypeName',
        'class': 'd-flex align-items-center gap-2 tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Date Created',
        'keyName': 'lastModifiedDate',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
]

export const CURRENCY_MASTER = [
    {
        'displayName': 'Currency Name',
        'keyName': 'currencyName',
        'class': 'd-flex align-items-center gap-2 tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Currency Symbol',
        'keyName': 'currencySymbol',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Exchange Rate',
        'keyName': 'exchangeRate',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Date Created',
        'keyName': 'lastModifiedDate',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
]

export const SEASON_MASTER = [
    {
        'displayName': 'Season Name',
        'keyName': 'seasonName',
        'class': 'd-flex align-items-center gap-2 tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Season Date',
        'keyName': 'fromDate',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Advance PO Date',
        'keyName': 'advanceDate',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
]

export const SALES_ORDER_TYPE_MASTER = [
    {
        'displayName': 'Sales Order Type Name',
        'keyName': 'name',
        'class': 'd-flex align-items-center gap-2 tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Date Created',
        'keyName': 'lastModifiedDate',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
]

export const DEPT_MASTER = [
    {
        'displayName': 'Department Name',
        'keyName': 'name',
        'class': 'd-flex align-items-center gap-2 tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Date Created',
        'keyName': 'lastModifiedDate',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
]

export const REASON_MASTER = [
    {
        'displayName': 'Reason Name',
        'keyName': 'reasonName',
        'class': 'd-flex align-items-center gap-2 tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Reason Short Code',
        'keyName': 'reasonShortCode',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Date Created',
        'keyName': 'lastModifiedDate',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
]

export const TRAVELLER_MASTER = [
    {
        'displayName': 'Traveller Name',
        'keyName': 'travellerName',
        'class': 'd-flex align-items-center gap-2 tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Contact No',
        'keyName': 'contactNo',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Short Code',
        'keyName': 'shortCode',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Service Cities',
        'keyName': '',
        'class': '',
        'iconClass': ''
    },
    {
        'displayName': 'E-way bill required?',
        'keyName': 'isEwayBill',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Need stamp with parcel?',
        'keyName': 'isStamp',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Date Created',
        'keyName': 'lastModifiedDate',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
]

export const IMPORTER_MASTER = [
    {
        'displayName': 'Importer Name',
        'keyName': 'firstName',
        'class': 'd-flex align-items-center gap-2 tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Contact No',
        'keyName': 'contactNo',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Short Code',
        'keyName': 'shortCode',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Address',
        'keyName': 'address',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Date Created',
        'keyName': 'lastModifiedDate',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
]

export const TRANSPORTER_MASTER = [
    {
        'displayName': 'Transport Name',
        'keyName': 'transporterName',
        'class': 'd-flex align-items-center gap-2 tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Transport ID',
        'keyName': 'transporterID',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Store Name',
        'keyName': 'storeName',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Mode Of Transport',
        'keyName': '',
        'class': '',
        'iconClass': ''
    },
    {
        'displayName': 'Available in Cities',
        'keyName': '',
        'class': '',
        'iconClass': ''
    },
    {
        'displayName': 'No of Branches',
        'keyName': '',
        'class': '',
        'iconClass': ''
    },
    {
        'displayName': 'Date Created',
        'keyName': 'lastModifiedDate',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
]

export const BRANCH_SHEET_TH = [
    {
        'displayName': 'BRANCH',
        'keyName': '',
        'class': '',
    },
    {
        'displayName': 'BRANCH CODE',
        'keyName': '',
        'class': '',
    },
    {
        'displayName': 'ADDRESS',
        'keyName': '',
        'class': '',
    },
    {
        'displayName': 'PHONE NO',
        'keyName': '',
        'class': '',
    },
    {
        'displayName': 'EMAIL',
        'keyName': '',
        'class': '',
    },
    {
        'displayName': 'BOOKING',
        'keyName': '',
        'class': '',
    },
    {
        'displayName': 'DELIVERY',
        'keyName': '',
        'class': '',
    },
]

export const QC_MASTER = [
    {
        'displayName': 'Checklist Name',
        'keyName': 'name',
        'class': 'd-flex align-items-center gap-2 tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Assigned items',
        'keyName': '',
        'class': '',
        'iconClass': ''
    },
    {
        'displayName': 'Date Created',
        'keyName': 'lastModifiedDate',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
]

export const LEVEL_MASTER = [
    {
        'displayName': 'Level Name',
        'keyName': 'levelName',
        'class': 'tbl-sorting sorting-asc sorting-desc w-50',
        'iconClass': ''
    },
    {
        'displayName': 'Short code',
        'keyName': 'shortCode',
        'class': 'tbl-sorting sorting-asc sorting-desc w-50',
        'iconClass': ''
    },
]

export const EXPENSE_TYPE_MASTER = [
    {
        'displayName': 'Expense Type Name',
        'keyName': 'expenseTypeName',
        'class': 'd-flex align-items-center gap-2 tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Date Created',
        'keyName': 'lastModifiedDate',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
]

export const USERS = [
    {
        'displayName': 'Name',
        'keyName': 'name',
        'class': 'd-flex align-items-center gap-2 tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Username',
        'keyName': 'userName',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Mobile Number',
        'keyName': 'mobileNo',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Branch',
        'keyName': '',
        'class': '',
        'iconClass': ''
    },
    {
        'displayName': 'Warehouse',
        'keyName': '',
        'class': '',
        'iconClass': ''
    },
    {
        'displayName': 'Department',
        'keyName': 'u.department.name',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Role',
        'keyName': 'u.userRole.name',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Availability',
        'keyName': 'isAbsent',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
]

export const ROLES = [
    {
        'displayName': 'Role Name',
        'keyName': 'name',
        'class': 'd-flex align-items-center gap-2 tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Department',
        'keyName': '',
        'class': '',
        'iconClass': ''
    },
    {
        'displayName': 'Description',
        'keyName': 'description',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Last Modify On',
        'keyName': 'lastModifiedDate',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
]

export const CODE_MASTER = [
    {
        'displayName': 'Code Name',
        'keyName': 'codeName',
        'class': 'd-flex align-items-center gap-2 tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Date Created',
        'keyName': 'lastModifiedDate',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
]

export const SUB_CODE_MASTER = [
    {
        'displayName': 'Code Name',
        'keyName': 'subCodeName',
        'class': 'd-flex align-items-center gap-2 tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Parent Code Name',
        'keyName': 'parentCodeName',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Date Created',
        'keyName': 'lastModifiedDate',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
]

export const CATEGORY = [
    {
        'displayName': 'Category Name',
        'keyName': 'categoryName',
        'class': 'd-flex align-items-center gap-2 tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Category Short Code',
        'keyName': 'categoryCode',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Date Created',
        'keyName': 'lastModifiedDate',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Note',
        'keyName': '',
        'class': '',
        'iconClass': ''
    },
]

export const ITEM_GROUP = [
    {
        'displayName': 'Sr. / Group Name',
        'keyName': 'groupName',
        'class': 'd-flex align-items-center gap-2 tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Group Code',
        'keyName': 'groupCode',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    // {
    //     'displayName': 'Category',
    //     'keyName': 'c.category.categoryName',
    //     'class': 'tbl-sorting sorting-asc sorting-desc',
    //     'iconClass': ''
    // },
    // {
    //     'displayName': 'Date Created',
    //     'keyName': 'lastModifiedDate',
    //     'class': 'tbl-sorting sorting-asc sorting-desc',
    //     'iconClass': ''
    // },
    {
        'displayName': 'HSN Code',
        'keyName': '',
        'class': '',
        'iconClass': ''
    },
]

export const DROPOFF_LOC = [
    {
        'displayName': 'Location Name',
        'keyName': '',
        'class': 'd-flex align-items-center gap-2 w-25',
        'iconClass': ''
    },
    {
        'displayName': 'Note',
        'keyName': '',
        'class': 'w-25',
        'iconClass': ''
    },
]

export const ACCOUNT_TYPE_MASTER_COLS = [
    {
        'displayName': 'Account Type',
        'keyName': 'typeName',
        'class': 'd-flex align-items-center gap-2 tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Account Group',
        'keyName': 'accountGroup',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Date Created',
        'keyName': 'lastModifiedDate',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Description',
        'keyName': 'description',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    }
]

export const CHART_OF_ACCOUNTS_MASTER_COLS = [
    {
        'displayName': 'Account Name',
        'keyName': 'ac.accountName',
        'class': 'd-flex align-items-center gap-2 tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Account Code',
        'keyName': 'ac.accountCode',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Account Type',
        'keyName': 'ac.accountType',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Date Created',
        'keyName': 'lastModifiedDate',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    }
]

export const ITEM_HEADER = [
    {
        'index': 0,
        'key': 'displayName',
        'show': true,
        'isSelected': true,
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'displayName': 'Sr. / Item Name',
        'keyName': 'displayName',
    },
    {
        'index': 1,
        'key': 'skuId',
        'show': true,
        'isSelected': true,
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'displayName': 'SKU',
        'keyName': 'skuId',
    },
    {
        'index': 2,
        'key': 'carton',
        'show': true,
        'isSelected': true,
        'class': '',
        'displayName': 'Carton',
        'keyName': '',
    },
    {
        'index': 3,
        'key': "looseQty",
        'show': true,
        'isSelected': true,
        'class': '',
        'displayName': 'Loose Qty',
        'keyName': '',
        'iconClass': '',
    },
    {
        'index': 4,
        'key': "TotalQty",
        'show': true,
        'isSelected': true,
        'class': '',
        'displayName': 'Total Qty',
        'keyName': '',
    },
    {
        'index': 5,
        'key': "categoryName",
        'show': true,
        'isSelected': true,
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'displayName': 'Category',
        'keyName': 'c.categoryName',
    },
    {
        'index': 6,
        'key': "levelItemMaps",
        'show': true,
        'isSelected': true,
        'class': '',
        'displayName': 'Level Qty',
        'keyName': '',
    },
    {
        'index': 7,
        'key': "itemsMarketTypes",
        'show': true,
        'isSelected': true,
        'class': '',
        'displayName': 'Market Types',
        'keyName': '',
    },
    {
        'index': 8,
        'key': "groupCodeName",
        'show': true,
        'isSelected': true,
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'displayName': 'Group Code',
        'keyName': 'gc.groupCodeName',
    },
    {
        'index': 9,
        'key': "AveragePrice",
        'show': true,
        'isSelected': true,
        'class': '',
        'displayName': 'Average Price',
        'keyName': '',
    },
    {
        'index': 10,
        'key': "AveragePriceG",
        'show': true,
        'isSelected': true,
        'class': '',
        'displayName': 'Average Price (GST)',
        'keyName': '',
    },
    {
        'index': 11,
        'key': "itemPrice",
        'show': true,
        'isSelected': true,
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'displayName': 'Sales Price/Piece',
        'keyName': 'itemPrice',
    },
    {
        'index': 12,
        'key': "itemCarton",
        'show': true,
        'isSelected': true,
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'displayName': 'Sales Price/Carton',
        'keyName': 'itemCarton',
    },
];

export const MJ_HEADER = [
    {
        'displayName': 'Date',
        'keyName': 'date',
        'class': 'd-flex align-items-center gap-2 tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Journal No',
        'keyName': 'journalNumber',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Amount',
        'keyName': '',
        'class': '',
        'iconClass': ''
    },
    {
        'displayName': 'Note',
        'keyName': 'note',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Created By',
        'keyName': 'u.name',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Date Created',
        'keyName': 'createdDate',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    }
]

export const REG_LIST = [
    {
        'displayName': 'Name',
        'keyName': 'r.firstName',
        'class': 'd-flex align-items-center gap-2 tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Display Name',
        'keyName': 'displayName',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Company Name',
        'keyName': 'companyName',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Phone No.',
        'keyName': 'phone',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Email',
        'keyName': 'email',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Registration Type',
        'keyName': 'r.registrationType',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Payables',
        'keyName': '',
        'class': '',
        'iconClass': ''
    },
    {
        'displayName': 'Receivables',
        'keyName': '',
        'class': '',
        'iconClass': ''
    },
]

export const CONTAINER_EXP = [
    {
        displayName: 'Expense Date',
        keyName: 'expenseDate',
        class: 'd-flex align-items-center gap-2 tbl-sorting sorting-asc sorting-desc',
        iconClass: ''
    },
    {
        displayName: 'Expense ID',
        keyName: 'expenseID',
        class: 'tbl-sorting sorting-asc sorting-desc',
        iconClass: ''
    },
    {
        displayName: 'PO Type',
        keyName: '',
        class: '',
        iconClass: ''
    },
    // {
    //     displayName: 'Expense Type',
    //     keyName: '',
    //     class: '',
    //     iconClass: ''
    // },
    {
        displayName: 'Container No',
        keyName: '',
        class: '',
        iconClass: ''
    },
    {
        displayName: 'Note',
        keyName: 'notes',
        class: 'tbl-sorting sorting-asc sorting-desc',
        iconClass: ''
    },
    {
        displayName: 'Attachments',
        keyName: '',
        class: '',
        iconClass: ''
    },
    {
        displayName: 'Amount',
        keyName: 'totalExpenseAmount',
        class: 'tbl-sorting sorting-asc sorting-desc',
        iconClass: ''
    },
]

export const TEMPO_EXP = [
    {
        displayName: 'Expense Date',
        keyName: 'expenseDate',
        class: 'd-flex align-items-center gap-2 tbl-sorting sorting-asc sorting-desc',
        iconClass: ''
    },
    {
        displayName: 'Expense ID',
        keyName: 'expenseID',
        class: 'tbl-sorting sorting-asc sorting-desc',
        iconClass: ''
    },
    {
        displayName: 'Tempo No.',
        keyName: 'poTempo.vehicleNo',
        class: 'tbl-sorting sorting-asc sorting-desc',
        iconClass: ''
    },
    {
        displayName: 'Containers',
        keyName: '',
        class: '',
        iconClass: ''
    },
    {
        displayName: 'Note',
        keyName: 'notes',
        class: 'tbl-sorting sorting-asc sorting-desc',
        iconClass: ''
    },
    {
        displayName: 'Attachments',
        keyName: '',
        class: '',
        iconClass: ''
    },
    {
        displayName: 'Amount',
        keyName: 'totalExpenseAmount',
        class: 'tbl-sorting sorting-asc sorting-desc',
        iconClass: ''
    },
]

export const GROUP_CODE = [
    {
        'displayName': 'Sr. / Group Code',
        'keyName': 'groupCodeName',
        'class': 'd-flex align-items-center gap-2 tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Group Code ID',
        'keyName': 'groupCodeID',
        'class': 'tbl-sorting sorting-asc sorting-desc',
        'iconClass': ''
    },
    {
        'displayName': 'Category',
        'keyName': '',
        'class': '',
        'iconClass': ''
    },
]