import { serializeAs } from "cerialize";
import moment from "moment";
import { Subject } from "rxjs";

export class StockBranchTransferPagination {

    @serializeAs('name')
    private _name: string;

    @serializeAs('toDate')
    private _toDate: string;

    @serializeAs('fromDate')
    private _fromDate: string;

    private _searchSubject: Subject<string>;
    private _dateRange: any;

    private _selectedWarehouseIndex: number;

    constructor() {
        this.fromDate = null;
        this.toDate = null;
        this.searchSubject = new Subject<string>();
        this.dateRange = {
            start: moment().startOf('month'),
            end: moment().endOf('month')
        };
    }


    /**
     * Getter selectedWarehouseIndex
     * @return {number}
     */
	public get selectedWarehouseIndex(): number {
		return this._selectedWarehouseIndex;
	}

    /**
     * Setter selectedWarehouseIndex
     * @param {number} value
     */
	public set selectedWarehouseIndex(value: number) {
		this._selectedWarehouseIndex = value;
	}


    /**
     * Getter name
     * @return {string}
     */
    public get name(): string {
        return this._name;
    }

    /**
     * Getter toDate
     * @return {string}
     */
    public get toDate(): string {
        return this._toDate;
    }

    /**
     * Getter fromDate
     * @return {string}
     */
    public get fromDate(): string {
        return this._fromDate;
    }

    /**
     * Getter searchSubject
     * @return {Subject<string>}
     */
    public get searchSubject(): Subject<string> {
        return this._searchSubject;
    }

    /**
     * Getter dateRange
     * @return {any}
     */
    public get dateRange(): any {
        return this._dateRange;
    }

    /**
     * Setter name
     * @param {string} value
     */
    public set name(value: string) {
        this._name = value;
    }

    /**
     * Setter toDate
     * @param {string} value
     */
    public set toDate(value: string) {
        this._toDate = value;
    }

    /**
     * Setter fromDate
     * @param {string} value
     */
    public set fromDate(value: string) {
        this._fromDate = value;
    }

    /**
     * Setter searchSubject
     * @param {Subject<string>} value
     */
    public set searchSubject(value: Subject<string>) {
        this._searchSubject = value;
    }

    /**
     * Setter dateRange
     * @param {any} value
     */
    public set dateRange(value: any) {
        this._dateRange = value;
    }

}