import { DatePipe } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormArray, FormGroup } from '@angular/forms';
import moment from 'moment';
import { Item } from 'src/app/models/Item';
import { statusYesNo } from 'src/app/shared/constants/constant';
import { ItemDropdown } from 'src/app/shared/constants/interface';
import { UtilsService } from 'src/app/shared/services/utils.service';

@Component({
  selector: 'app-item-other-details',
  templateUrl: './item-other-details.component.html',
  styleUrls: ['./item-other-details.component.scss']
})
export class ItemOtherDetailsComponent implements OnInit {

  @Input({ alias: 'itemForm', required: true }) itemForm: FormGroup;
  @Input({ alias: 'itemObj', required: true }) itemObj: Item;
  @Input({ alias: 'dropdown', required: true }) dropdown: ItemDropdown;

  @Output() removeBullet: EventEmitter<any> = new EventEmitter<any>();
  @Output() addBullet: EventEmitter<any> = new EventEmitter<any>();
  @Output() onChangeRegularSeason: EventEmitter<any> = new EventEmitter<any>();

  statusYesNo = statusYesNo
  
  constructor(public utilsService: UtilsService) { }

  ngOnInit(): void {
  }

  onChangeSeason(value: any) {
    if (this.dropdown?.seasonMaster) {
      let obj = this.dropdown?.seasonMaster.find(v => v.value == value)
      this.itemObj.tempAdvPODate = obj.advanceDate ? moment(obj.advanceDate).format('DD/MM/YYYY') : null;
    }
  }

  onChangeRS(value: boolean) {
    this.onChangeRegularSeason.emit(value)
  }

  //

  get bulletPts() {
    return (this.itemForm.get('bulletPts') as FormArray);
  }

  removeBulletPt(index: number) {
    this.removeBullet.emit(index)
  }

  //
  onClearSize = () => {
    this.itemObj.sizeMasterId = null;
  }
  onClearPackoff = () => {
    this.itemObj.packOfId = null;
  }
  onClearBatteryType = () => {
    this.itemObj.batteryTypeId = null;
  }
  onClearMType = () => {
    this.itemObj.mountingTypeId = null;
  }
}
