import { DatePipe } from '@angular/common';
import { Component, OnInit, OnD<PERSON>roy, ViewChild, inject } from '@angular/core';
import { FormGroup, FormControl, FormBuilder, Validators, AbstractControl } from '@angular/forms';
import { SafeResourceUrl } from '@angular/platform-browser';
import { EnumForRCtoLoadedSteps } from '@enums/EnumForRCtoLoadedSteps.enum';
import { EnumForRegUserType } from '@enums/EnumForRegUserType';
import { EnumForTempoStatus } from '@enums/EnumForTempoStatus.enum';
import { POImportCHtoLoaded } from '@modal/PoImportCHtoLoaded';
import { POImportHeader } from '@modal/POImportHeader';
import { POImportItem } from '@modal/POImportItem';
import { POImportList, POImportsSub } from '@modal/POImportList';
import { POImportTempo } from '@modal/POImportTempo';
import { POImportPagination } from '@modal/request/POImportPagination';
import { UtilsService } from '@service/utils.service';
import { Serialize, Deserialize } from 'cerialize';
import dayjs from 'dayjs';
import { saveAs } from 'file-saver';
import moment from 'moment';
import { DaterangepickerDirective } from 'ngx-daterangepicker-material';
import { Subscription, debounceTime, distinctUntilChanged } from 'rxjs';
import { DRAFT_HEADER, PO_CREATED_HEADER, RECEIVED_CHINA_TH, LOADED_TH, RELEASE_TH, GRN_TH, COMPLETED_TH } from 'src/app/shared/constants/POImportTH';
import { PoImportCreatedComponent } from './po-import-created/po-import-created.component';
import { PoImportDraftComponent } from './po-import-draft/po-import-draft.component';
import { PoImportGrnComponent } from './po-import-grn/po-import-grn.component';
import { PoImportLoadedComponent } from './po-import-loaded/po-import-loaded.component';
import { PoImportReceivedChinaComponent } from './po-import-received-china/po-import-received-china.component';
import { PoImportReleasedComponent } from './po-import-released/po-import-released.component';
import { PoImportTempoComponent } from './po-import-tempo/po-import-tempo.component';
import { PoImportCompletedComponent } from './po-import-completed/po-import-completed.component';
declare var window: any;

@Component({
  selector: 'app-purchase-order-import',
  templateUrl: './purchase-order-import.component.html',
  styleUrls: ['./purchase-order-import.component.scss'],
})
export class PurchaseOrderImportComponent implements OnInit, OnDestroy {

  demo = [
    { id: 1, name: 'Option 1' },
    { id: 2, name: 'Option 2' },
    { id: 3, name: 'Option 3' },
  ];

  selectedDemo1: number = 1;

  @ViewChild(DaterangepickerDirective, { static: true }) pickerDirective: DaterangepickerDirective;

  @ViewChild(PoImportLoadedComponent) PoImportLoadedComponent: PoImportLoadedComponent;
  @ViewChild(PoImportReleasedComponent) PoImportReleasedComponent: PoImportReleasedComponent;
  @ViewChild(PoImportReceivedChinaComponent) PoImportReceivedChinaComponent: PoImportReceivedChinaComponent;
  @ViewChild(PoImportTempoComponent) PoImportTempoComponent: PoImportTempoComponent;
  @ViewChild(PoImportCreatedComponent) PoImportCreatedComponent: PoImportCreatedComponent;
  @ViewChild(PoImportDraftComponent) PoImportDraftComponent: PoImportDraftComponent;
  @ViewChild(PoImportGrnComponent) PoImportGrnComponent: PoImportGrnComponent;
  @ViewChild(PoImportCompletedComponent) PoImportCompletedComponent: PoImportCompletedComponent;

  utilsService = inject(UtilsService);

  base64SourceSingle: SafeResourceUrl[];
  paginationRequest = new POImportPagination();
  poImportList: POImportList[] = [];
  poImportObj = new POImportList()
  selectedPO = new POImportsSub()
  selectedList = new POImportList()

  dateSub: Subscription;

  flagForAllExpand: boolean = false;
  isExpandedIDs: any[] = [];

  selectedTab: string;
  enumForStatus = this.utilsService.poImportStatus;

  dropdown: any;

  //Headers
  draggingColumnIndex: number = null;
  selectedId: number;
  headerObj = {
    optionsArray: null,
    columnArr: null,
    allHeaderArr: null,
    status: this.enumForStatus.DRAFT
  }

  //modals
  fromPoToDraft: any;
  deletePOModal: any;
  poToRecieveChina: any;
  ImportDetailsModalRecChina: any;
  loadedconfirmationModal: any;
  loadedEditContainerModal: any;

  // Created To RC China
  isPoToRecieveChina: boolean = false;
  isEditPotoRec: boolean = false;
  // RC China to Loaded
  minDateNGB: any;
  isRCtoLoaded: boolean = false;
  stageStep: string = null;
  enumForRcToLoadedStage = EnumForRCtoLoadedSteps
  importDetailsForm: FormGroup;
  importDropdown: any;
  importCHtoLoadedObj = new POImportCHtoLoaded()
  // Loaded - Container Edit
  loadedContainerEditGrp: FormGroup
  editContainerChanged: boolean = false;
  showEditContainerModal: boolean = false

  itemObj = new POImportItem();
  associatedItems: POImportItem[] = []
  totalCount = {
    totalPcsQty: 0,
    totalPoCarton: 0,
    receivedQtyTotal: 0,
    loadedQty: 0,
    overallRCQty: 0,
    overallLoadedQty: 0,
  }

  //Delete Item in PO
  deleteItemInPO: any;

  //Marka Search
  searchControl = new FormControl('');
  searchControlPOCreatedToRC = new FormControl('');
  searchSub: Subscription
  searchSubTwo: Subscription;
  oldItems: POImportItem[] = []

  //Add Edit Tempo
  addEditTempoModal: any;
  tempoToCompleted: any;
  deleteTempoModal: any;
  GRNStartConfirmModal: any;
  tempFormGroup: FormGroup;
  tempoObj = new POImportTempo();
  statusForModal: string = 'Add';
  enumForTempoStatus = EnumForTempoStatus;
  tempoContainerDropdown: any[] = []
  tempoMaxDate: any;

  //Change Status
  changeStatusItemModal: any;
  changeStatusId: string;
  changeStatusContainer: number;
  containerDropdown: any[] = []
  statusDropdownCS: any[] = []
  changeStatusItemModalShow: boolean = false;
  changeStatusFG: FormGroup;
  oldContainerId: number;

  deliveryDaySub: Subscription;

  // Assign to CHA
  CHAassignModal: any;
  assignCHAFormGroup: FormGroup;
  showCHAAssignModal: boolean = false;
  CHAdropdown: any[] = []

  // Move to Completed
  moveToCompletedModal: any;
  showMoveToCompletedModal: boolean = false;
  markAsCompletedList: POImportItem[] = [];
  completedConfirmationModal: any;

  constructor(private fb: FormBuilder) {
    this.selectedTab = this.enumForStatus.DRAFT;

    // this.paginationRequest.dateRangeControl.setValue({
    //   start: moment().startOf('month'),
    //   end: moment().endOf('month')
    // })

    this.paginationRequest.dateRange = undefined;
    this.getAllPOImports()

    this.minDateNGB = {
      year: moment().startOf('day').get('year'),
      month: moment().startOf('day').get('month') + 1,
      day: moment().startOf('day').get('date')
    };
    this.searchSub = new Subscription()
  }

  ngOnInit(): void {

    // Date Sub
    this.dateSub = new Subscription();
    this.deliveryDaySub = new Subscription();

    this.tempoMaxDate = dayjs().endOf('day').toDate().toISOString();
    this.dateSub = this.paginationRequest.dateRangeControl.valueChanges.subscribe(a => {
      switch (this.selectedTab) {
        case this.enumForStatus.DRAFT:
        case this.enumForStatus.PO_CREATED:
        case this.enumForStatus.RECEIVED_CHINA:
        case this.enumForStatus.LOADED:
        case this.enumForStatus.RELEASED:
        case this.enumForStatus.GRN:
        case this.enumForStatus.COMPLETED:
          if (a['start'] && a['end']) {
            this.paginationRequest.fromDate = dayjs(a['start']).format('YYYY-MM-DD');
            this.paginationRequest.toDate = dayjs(a['end']).format('YYYY-MM-DD');
            this.getAllPOImports()
          }
          break;
        case this.enumForStatus.TEMPO:
          if (a['start'] && a['end']) {
            this.paginationRequest.fromDate = dayjs(a['start']).format('YYYY-MM-DD');
            this.paginationRequest.toDate = dayjs(a['end']).format('YYYY-MM-DD');
            this.getAllPOImports()
          }
          break;
        default:
          break;
      }
    })

    this.searchSub = this.searchControl.valueChanges.pipe(debounceTime(50), distinctUntilChanged()).subscribe(searchTerm => {
      this.filterItems(searchTerm, false);
    });

    this.searchSubTwo = this.searchControlPOCreatedToRC.valueChanges.pipe(debounceTime(50), distinctUntilChanged()).subscribe(searchTerm => {
      this.filterItems(searchTerm, true);
    })

    this.fromPoToDraft = new window.bootstrap.Modal(
      document.getElementById('fromPoToDraft')
    );

    this.deletePOModal = new window.bootstrap.Modal(
      document.getElementById('deletePOModal')
    );

    this.poToRecieveChina = new window.bootstrap.Modal(
      document.getElementById('poToRecieveChina')
    );
    document.getElementById('poToRecieveChina').addEventListener('hidden.bs.modal', () => {
      this.isPoToRecieveChina = false;
    });

    this.ImportDetailsModalRecChina = new window.bootstrap.Modal(
      document.getElementById('ImportDetailsModalRecChina')
    );
    document.getElementById('ImportDetailsModalRecChina').addEventListener('hidden.bs.modal', () => {
      this.isRCtoLoaded = false;
    });
    this.importDetailsFormGroup();

    this.loadedconfirmationModal = new window.bootstrap.Modal(
      document.getElementById('loadedconfirmationModal')
    );

    this.loadedEditContainerModal = new window.bootstrap.Modal(
      document.getElementById('loadedEditContainerModal')
    );
    document.getElementById('loadedEditContainerModal').addEventListener('hidden.bs.modal', () => {
      this.showEditContainerModal = false;
    });
    this.loadedContainerEditForm();

    this.deleteItemInPO = new window.bootstrap.Modal(
      document.getElementById('deleteItemInPO')
    );

    //
    this.addEditTempoModal = new window.bootstrap.Modal(
      document.getElementById('addEditTempoModal')
    );
    this.tempoToCompleted = new window.bootstrap.Modal(
      document.getElementById('tempoToCompleted')
    );
    this.deleteTempoModal = new window.bootstrap.Modal(
      document.getElementById('deleteTempoModal')
    );
    this.GRNStartConfirmModal = new window.bootstrap.Modal(
      document.getElementById('GRNStartConfirmModal')
    );
    this.tempoForm()
    //

    //
    this.changeStatusItemModal = new window.bootstrap.Modal(
      document.getElementById('changeStatusItemModal')
    );
    document.getElementById('changeStatusItemModal').addEventListener('hidden.bs.modal', () => {
      this.changeStatusItemModalShow = false;
    });

    this.CHAassignModal = new window.bootstrap.Modal(
      document.getElementById('CHAassignModal')
    );

    document.getElementById('CHAassignModal').addEventListener('hidden.bs.modal', () => {
      this.showCHAAssignModal = false;
    });

    this.moveToCompletedModal = new window.bootstrap.Modal(
      document.getElementById('movetoCompletedModal')
    );

    document.getElementById('movetoCompletedModal').addEventListener('hidden.bs.modal', () => {
      this.showMoveToCompletedModal = false;
    });

    this.completedConfirmationModal = new window.bootstrap.Modal(
      document.getElementById('completedConfirmationModal')
    );

    this.headerObj.status = this.enumForStatus.DRAFT

    this.getHeader();
    this.getRequiredData();
    this.changeStatusForm();
    this.assignCHAForm();

    this.deliveryDaySub = this.importDetailsForm.get('date').valueChanges.subscribe(value => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(value)) {
        let date = new Date(value.year, value.month - 1, value.day);
        let today = moment().startOf('day').toDate();
        let diff = moment(date).diff(moment(today), 'days');
        this.importDetailsForm.get('exp_days').setValue(diff)
      }
    })
  }

  ngOnDestroy(): void {
    this.searchSub.unsubscribe();
    this.dateSub.unsubscribe();
    this.deliveryDaySub.unsubscribe();
  }

  //Search Marka
  filterItems(searchTerm: string, isPoToRC?: boolean): void {

    if (!searchTerm) {
      this.associatedItems = [...this.oldItems];
    }

    if (searchTerm) {
      searchTerm = searchTerm.toLowerCase().trim();
    }

    this.associatedItems = this.oldItems.filter(item => item.marka.toLowerCase().includes(searchTerm));
    setTimeout(() => {
      this.setFooterTotal(isPoToRC)
    }, 50);
  }

  setFooterTotal = (isPoToRC: boolean) => {
    let totalPoCarton = 0;
    let totalPcsQty = 0;

    this.associatedItems = this.associatedItems.map(item => {
      if (isPoToRC) {
        totalPoCarton += item.poCarton;
        totalPcsQty += item.totalPcsQty;
      } else {
        item.calcReceiveKey = Serialize(item.rcCarton ?? item.receivedQty);
        item.calcLoadedQty = Serialize(item.getSumLoadedQty ?? item.loadedQty);
        item.totalPcsQty = parseFloat((item.calcReceiveKey * item.pricePerCarton).toFixed(2));
        totalPoCarton += item.calcReceiveKey;
        totalPcsQty += item.totalPcsQty;
      }
      return item;
    });
    this.totalCount.totalPcsQty = totalPcsQty;
    this.totalCount.totalPoCarton = totalPoCarton;

    isPoToRC ? this.onChangeRecQty() : this.onChangeLoadQty()
  }


  getAllPOImports(isChangeTab?: boolean) {

    let ls_param = null
    ls_param = JSON.parse(localStorage.getItem('param'))

    if (!this.utilsService.isNullUndefinedOrBlank(ls_param)) {
      if (ls_param.pageName === 'po-import') {
        this.paginationRequest.pageNo = ls_param.pageNo,
          this.paginationRequest.pageSize = ls_param.pageSize,
          this.paginationRequest.customerId = ls_param.customerId,
          this.paginationRequest.supplierId = ls_param.supplierId,
          this.paginationRequest.fromDate = ls_param.fromDate,
          this.paginationRequest.toDate = ls_param.toDate,
          this.paginationRequest.purchaseOrderSearch = ls_param.purchaseOrderSearch,
          this.paginationRequest.searchPurchaseComments = ls_param.searchPurchaseComments,
          this.selectedTab = ls_param.selectedTab,
          this.isExpandedIDs = ls_param.isExpandedIDs,
          this.headerObj.status = ls_param.selectedTab
      }
      this.getHeader();
    }

    if (this.utilsService.checkPageAccess([this.utilsService.enumForPage.VIEW_RC_CHINA]) && !this.utilsService.checkPageAccess([this.utilsService.enumForPage.VIEW_PO])) {
      this.selectedTab = this.enumForStatus.RECEIVED_CHINA
    }

    if (this.utilsService.checkPageAccess([this.utilsService.enumForPage.VIEW_LOADED]) && !this.utilsService.checkPageAccess([this.utilsService.enumForPage.VIEW_PO])) {
      this.selectedTab = this.enumForStatus.LOADED
    }

    if (this.utilsService.checkPageAccess([this.utilsService.enumForPage.VIEW_RELEASED]) && !this.utilsService.checkPageAccess([this.utilsService.enumForPage.VIEW_PO])) {
      this.selectedTab = this.enumForStatus.RELEASED
    }

    this.paginationRequest.status = (this.selectedTab)

    let API = null;
    let key = null;
    let childKey = null;
    switch (this.selectedTab) {
      case this.enumForStatus.DRAFT:
        key = 'supplierId'
        childKey = 'poImports'
        API = this.utilsService.serverVariableService.PO_IMPORT_LISTING
        break;
      case this.enumForStatus.PO_CREATED:
        key = 'supplierId'
        childKey = 'poImportCreate'
        API = this.utilsService.serverVariableService.PO_CREATED_LISTING
        break;
      case this.enumForStatus.RECEIVED_CHINA:
        key = 'supplierId'
        childKey = 'poImportReceivedCh'
        API = this.utilsService.serverVariableService.PO_RECEIVE_CHINA_LISTING
        break;
      case this.enumForStatus.LOADED:
        key = 'id'
        API = this.utilsService.serverVariableService.PO_LOADED_LISTING
        break;
      case this.enumForStatus.RELEASED:
        key = 'id'
        API = this.utilsService.serverVariableService.PO_RELEASE_LISTING
        break;
      case this.enumForStatus.TEMPO:
        key = 'id'
        this.paginationRequest.status = null;
        API = this.utilsService.serverVariableService.PO_TEMPO_PAGE
        break;
      case this.enumForStatus.GRN:
        key = 'id'
        API = this.utilsService.serverVariableService.PO_GRN_LISTING
        break;
      case this.enumForStatus.COMPLETED:
        key = 'id'
        API = this.utilsService.serverVariableService.PO_COMPLETED_LISTING
        break;
      default:
        break;
    }

    if (!API) {
      return;
    }

    this.utilsService.postMethodAPI(false, API, Serialize(this.paginationRequest), (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.poImportList = Deserialize(response.content, POImportList);
        this.paginationRequest.totalData = response.totalElements;
        this.paginationRequest.pagination = response;

        if (!this.utilsService.isNullUndefinedOrBlank(this.isExpandedIDs)) {
          for (const obj of this.poImportList) {
            if (this.isExpandedIDs.includes(obj[key])) {
              obj.isExpand = true;
            } else {
              obj.isExpand = false;
            }
            if (obj[childKey]) {
              for (const group of obj[childKey] as POImportsSub[]) {
                if (group.poImportItemList) {
                  const ids = group.poImportItemList.map(item => item.id);
                  const allTargetIdsPresent = this.isExpandedIDs.some(id => ids.includes(id));
                  group.isExpand = allTargetIdsPresent;
                }
              }
            }
          }
        }

        if (!this.utilsService.isNullUndefinedOrBlank(this.paginationRequest.searchPurchaseComments) ||
          !this.utilsService.isNullUndefinedOrBlank(this.paginationRequest.searchText) ||
          !this.utilsService.isNullUndefinedOrBlank(this.paginationRequest.purchaseOrderSearch)) {
          if (this.poImportList) {
            for (const obj of this.poImportList) {
              obj.isExpand = true;
              if (obj[childKey]) {
                for (const group of obj[childKey] as POImportsSub[]) {
                  group.isExpand = true;
                }
              }
            }
          }
        }

        // if (isChangeTab) {
        //   if (this.poImportList.length > 0) {
        //     this.poImportList = this.poImportList.map((a, index) => {
        //       a.isExpand = index === 0;
        //       this.isExpandedIDs.push(a.id)
        //       if (index === 0 && a[childKey]) {
        //         (a[childKey] as POImportsSub[])?.forEach((group, subIndex) => {
        //           if (group.poImportItemList) {
        //             group.isExpand = subIndex == 0;
        //             this.isExpandedIDs.push(group.id)
        //           }
        //         });
        //       }
        //       return a;
        //     });
        //   }
        // }

        localStorage.removeItem('param')

        // setTimeout(() => {
        //   this.calculateAllValues()
        // }, 150);

      } else {
        this.poImportList = [];
      }
    })

  }

  onChangeTab(value: string) {

    if (this.selectedTab === value) return;

    this.poImportList = [];
    this.selectedTab = value;
    this.isExpandedIDs = [];

    ////
    this.emptyPageFilters();
    ////

    this.getHeader();
    this.getAllPOImports(true);
  }

  getRequiredData() {

    let API = null;
    API = this.utilsService.serverVariableService.PAGE_REQ_DATA

    this.utilsService.getMethodAPI(false, API, null, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.dropdown = response;
        this.dropdown.supplierDropdown = Serialize(this.dropdown.registration.filter(a => a.type !== EnumForRegUserType.CUSTOMER))
        this.dropdown.customerDropdown = Serialize(this.dropdown.registration.filter(a => a.type !== EnumForRegUserType.SUPPLIER))
        // this.containerDropdown = response.container
      }
    })
  }

  openDeletePOModal(obj: POImportsSub) {
    this.selectedPO = Serialize(obj)
    this.deletePOModal.show();
  }

  deletePO() {

    let API = null;
    switch (this.selectedTab) {
      case this.enumForStatus.DRAFT:
        API = this.utilsService.serverVariableService.PO_IMPORT_DEL
        break;
      case this.enumForStatus.PO_CREATED:
        API = this.utilsService.serverVariableService.PO_CREATED_DEL
        break;
      case this.enumForStatus.RECEIVED_CHINA:
        API = this.utilsService.serverVariableService.PO_REC_CHINA_DEL
        break;
      default:
        break;
    }


    this.utilsService.deleteMethodAPI(true, API + `?id=${this.selectedPO.id}`, {}, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        if (!this.paginationRequest.pagination?.first && this.paginationRequest.pagination?.last && this.paginationRequest.pagination?.numberOfElements === 1) {
          this.paginationRequest.pageNo = this.paginationRequest.pageNo - 1
        }
        this.getAllPOImports();
        this.deletePOModal.hide();
      }
    })
  }

  // Expand collapse
  onCollapse(index: number) {
    this.poImportList[index].isExpand = !this.poImportList[index].isExpand;

    let key = null;
    switch (this.selectedTab) {
      case this.enumForStatus.DRAFT:
        key = 'supplierId'
        break;
      case this.enumForStatus.PO_CREATED:
        key = 'supplierId'
        break;
      case this.enumForStatus.RECEIVED_CHINA:
        key = 'supplierId'
        break;
      case this.enumForStatus.LOADED:
        key = 'id'
        break;
      case this.enumForStatus.RELEASED:
        key = 'id'
        break;
      case this.enumForStatus.GRN:
        key = 'id'
        break;
      default:
        break;
    }
    //collapse
    this.poImportList.forEach(p => {
      if (p.isExpand) {
        if (!this.isExpandedIDs.includes(p[key])) {
          this.isExpandedIDs.push(p[key]);
        }
      } else {
        const index = this.isExpandedIDs?.indexOf(p[key]);
        if (index !== -1) {
          this.isExpandedIDs.splice(index, 1);
        }
      }
    });
  }

  checkIfAllExpand() {
    let flag = true;
    this.poImportList.filter((val, index) => {
      if (val['isExpand'] === false) {
        flag = false;
        return;
      }
    });
    return flag;
  }

  onCollapseChild(parent_index: number, i: number) {

    let key = null

    switch (this.selectedTab) {
      case this.enumForStatus.DRAFT:
        key = 'poImports'
        break;
      case this.enumForStatus.PO_CREATED:
        key = 'poImportCreate'
        break;
      case this.enumForStatus.RECEIVED_CHINA:
        key = 'poImportReceivedCh'
        break;
      default:
        break;
    }

    this.poImportList[parent_index][key][i].isExpand = !this.poImportList[parent_index][key][i].isExpand;

    //collapse
    this.poImportList[parent_index][key][i].poImportItemList?.forEach(p => {
      if (this.poImportList[parent_index][key][i].isExpand) {
        if (!this.isExpandedIDs.includes(p.id)) {
          this.isExpandedIDs.push(p.id);
        }
      } else {
        const index = this.isExpandedIDs.indexOf(p.id);
        if (index !== -1) {
          this.isExpandedIDs.splice(index, 1);
        }
      }
    });
  }

  //Headers
  getHeader() {

    let TH = []
    switch (this.selectedTab) {
      case this.enumForStatus.DRAFT:
        TH = DRAFT_HEADER
        break;
      case this.enumForStatus.PO_CREATED:
        TH = PO_CREATED_HEADER
        break;
      case this.enumForStatus.RECEIVED_CHINA:
        TH = RECEIVED_CHINA_TH
        break;
      case this.enumForStatus.LOADED:
        TH = LOADED_TH
        break;
      case this.enumForStatus.RELEASED:
        TH = RELEASE_TH
        break;
      case this.enumForStatus.GRN:
        TH = GRN_TH
        break;
        case this.enumForStatus.COMPLETED:
        TH = COMPLETED_TH
        break;
      default:
        break;
    }

    TH = TH.map((a, i) => { a.index = i; return a })

    // this.utilsService.getMethodAPI(false, this.utilsService.serverVariableService.HEADER_GET_SAVE_API + `?pageName=${this.selectedTab}`, null, (response) => {
    //   if (!this.utilsService.isEmptyObjectOrNullUndefined(response && (JSON.parse(response.data)).length === 0)) {
    //     this.headerObj.columnArr = Deserialize(TH, POImportHeader);
    //     const keys = (JSON.parse(response.data)).filter(v => !v.show).map(a => a.key)
    //     const indexArr = (JSON.parse(response.data)).map(a => a.index) as number[]
    //     const flag = indexArr.some(item => (item === null) || (item === undefined))
    //     this.headerObj.columnArr.forEach(item => {
    //       item.isSelected = !keys.includes(item.key);
    //       item.show = !keys.includes(item.key);
    //     });
    //     if (!flag) {
    //       this.headerObj.columnArr = indexArr.map(index => this.headerObj.columnArr[index]);
    //     }
    //     this.selectedId = response.id ? response.id : null
    //   }
    //   else {
    //     this.headerObj.columnArr = Deserialize(TH, POImportHeader)
    //   }
    //   //
    //   this.headerObj.optionsArray = Serialize(this.headerObj.columnArr.filter(v => v))
    //   this.headerObj.allHeaderArr = Serialize(this.headerObj.columnArr.filter(v => v))
    // }, false)

    this.headerObj.columnArr = Deserialize(TH, POImportHeader);

    this.headerObj.optionsArray = Serialize(this.headerObj.columnArr.filter(v => v))
    this.headerObj.allHeaderArr = Serialize(this.headerObj.columnArr.filter(v => v))
  }

  saveCol() {
    let newColArr = (this.headerObj.allHeaderArr) as POImportHeader[];
    const updatedData = newColArr.map((item, i) => ({
      ...item,
      show: item.isSelected,
    }));

    const sortedData = updatedData.sort((a, b) => {
      return (a.show === b.show) ? 0 : a.show ? -1 : 1;
    });

    const flag = newColArr.filter(v => v.isSelected).length < 4
    if (flag) {
      this.utilsService.toasterService.error('Please select atleast any 4 fields', '', { closeButton: true });
      return;
    }

    const param = {
      id: this.selectedId,
      pageName: this.selectedTab,
      data: JSON.stringify(sortedData)
      // data: JSON.stringify(newColArr)
    }
    this.utilsService.postMethodAPI(false, this.utilsService.serverVariableService.HEADER_GET_SAVE_API, param, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.getHeader();
        this.draggingColumnIndex = null;
      }
    }, null, true)
  }

  checkIfAllSelected() {
    let flag = true;
    this.headerObj.allHeaderArr.filter((val) => {
      if (val['isSelected'] === false) {
        flag = false;
        return;
      }
    });
    return flag;
  }

  //Math
  calculateAllValues() {
    let key = null
    switch (this.selectedTab) {
      case this.enumForStatus.DRAFT:
        key = 'poImports'
        // this.calcTabWise(key)
        break;
      case this.enumForStatus.PO_CREATED:
        key = 'poImportCreate'
        // this.calcTabWise(key)
        break;
      case this.enumForStatus.RECEIVED_CHINA:
        key = 'poImportReceivedCh'
        // this.calcTabWise(key)
        break;
      case this.enumForStatus.LOADED:
        // this.calcFromLoaded()
        break;
      case this.enumForStatus.RELEASED:
        // this.calcFromLoaded()
        break;
      default:
        break;
    }

  }

  calcTabWise(key: string) {
    this.poImportList.map(poImport => {
      const arr = poImport[key] as POImportsSub[]
      (arr).map(a => {
        if (a.poImportItemList) {
          this.setCalcAssociatedItems(a)
          a.totalCartons = Serialize(a.poImportItemList.reduce((sum, item) => sum + item.receivedCartonsCH, 0))
        }
      })
    });
  }

  calcFromLoaded() {
    this.poImportList.map(a => {
      if (a.poImportItemList) {

        this.setCalcAssociatedItems(a)
        a.totalCartons = Serialize(a.poImportItemList.reduce((sum, item) => sum + item.receivedCartonsCH, 0))
      }
    })
  }

  setCalcAssociatedItems(a: POImportList | POImportsSub) {
    a.poImportItemList.map(item => {

      let cbmPrice = 0;
      switch (this.selectedTab) {
        case this.enumForStatus.DRAFT:
          cbmPrice = Number(a.cbmPrice) || 0;
          break;
        case this.enumForStatus.PO_CREATED:
          cbmPrice = Number(a.cbmPrice) || 0;
          break;
        case this.enumForStatus.RECEIVED_CHINA:
          cbmPrice = Number(item.cbmPrice) || 0;
          break;
        case this.enumForStatus.LOADED:
          cbmPrice = Number(item.cbmPrice) || 0;
          break;
        case this.enumForStatus.RELEASED:
          cbmPrice = Number(item.cbmPrice) || 0;
          break;
        default:
          break;
      }

      const conv = Number(a?.conversationRate) || 0;
      const poCarton = Number(item?.poCarton) || 0;
      const pricePerCarton = Number(item?.pricePerCarton) || 0;
      const pricePerItem = Number(item?.pricePerItem) || 0;
      const expDeliveryCost = Number(item?.expDeliveryCost) || 0;
      const transportCharges = Number(item?.transportCharges) || 0;
      const craneExpense = Number(item?.craneExpense) || 0;
      const percentage = Number(item?.percentage) || 0;
      const expensePcs = Number(item?.expensePcs) || 0;

      const itemDimHeight = Number(item?.cartonHeight) / Number(item.unitMaster?.conversionToMeter) || 0;
      const itemDimWidth = Number(item?.cartonWidth) / Number(item.unitMaster?.conversionToMeter) || 0;
      const itemDimLength = Number(item?.cartonLength) / Number(item.unitMaster?.conversionToMeter) || 0;

      const cartonWeight = Number(item?.cartonWeight) || 0;
      const cartonWeightRu = Number(item?.cartonWeightRu) || 0;

      const receivedCartonsCH = Number(item?.receivedQty) || 0;

      // (C)
      item.totalPcsQty = parseFloat((poCarton * pricePerCarton).toFixed(6));
      const totalPcsQty = Number(item?.totalPcsQty) || 0;

      // (E)
      item.totalAmount = parseFloat((totalPcsQty * pricePerItem).toFixed(6));
      const totalAmount = Number(item?.totalAmount) || 0;

      //  (G)
      item.totalAmountWithExp = parseFloat((totalAmount + expDeliveryCost).toFixed(6));
      const totalAmountWithExp = Number(item?.totalAmountWithExp) || 0;

      //  (H)
      item.totalAmountWithExpInINR = parseFloat((totalAmountWithExp * conv).toFixed(6));
      const totalAmountWithExpInINR = Number(item?.totalAmountWithExpInINR) || 0;

      //(I)
      item.chinaFinalExpextedCode = parseFloat((totalAmountWithExpInINR / totalPcsQty).toFixed(6));
      const chinaFinalExpextedCode = Number(item?.chinaFinalExpextedCode) || 0;

      //  (P)
      item.totalTransportationChargesM2S = parseFloat((transportCharges * poCarton).toFixed(6));
      const totalTransportationChargesM2S = Number(item?.totalTransportationChargesM2S) || 0;

      //  (Q)
      item.transportationChargesM2SperPCS = parseFloat((totalTransportationChargesM2S / totalPcsQty).toFixed(6));
      const transportationChargesM2SperPCS = Number(item?.transportationChargesM2SperPCS) || 0;

      //  (R)
      item.totalInsurance = parseFloat((totalAmountWithExpInINR * 0.01).toFixed(6));
      const totalInsurance = Number(item?.totalInsurance) || 0;

      //  (S)
      item.insurancePerPcs = parseFloat((totalInsurance / totalPcsQty).toFixed(6));
      const insurancePerPcs = Number(item?.insurancePerPcs) || 0;

      //  (T)
      item.gstAmtPerPcs = parseFloat(((totalAmountWithExpInINR * 30 / 100) * (0.18 / totalPcsQty)).toFixed(6));
      const gstAmtPerPcs = Number(item?.gstAmtPerPcs) || 0;

      //  (V)
      item.craneExpPcs = parseFloat((craneExpense / totalPcsQty).toFixed(6));
      const craneExpPcs = Number(item?.craneExpPcs) || 0;

      //  (W)
      item.totalExp = parseFloat((transportationChargesM2SperPCS + insurancePerPcs + gstAmtPerPcs + craneExpPcs).toFixed(6));
      const totalExp = Number(item?.totalExp) || 0;

      // (K3)
      item.totalExpPCSper = parseFloat((chinaFinalExpextedCode * (percentage / 100)).toFixed(6));
      const totalExpPCSper = Number(item?.totalExpPCSper) || 0;

      //  (L3)
      item.totalFinalCostPCSper = parseFloat((chinaFinalExpextedCode + totalExp + totalExpPCSper).toFixed(6));

      //  (K5)
      item.totalItemAmt = parseFloat((chinaFinalExpextedCode + totalExp + expensePcs).toFixed(6));

      //  (J1)
      item.cbmPerCarton = parseFloat(((itemDimLength * itemDimWidth * itemDimHeight) / 1000000).toFixed(6));
      const cbmPerCarton = Number(item?.cbmPerCarton) || 0;

      //(K1)
      item.totalCbm = parseFloat((poCarton * cbmPerCarton).toFixed(6));
      const totalCbm = Number(item?.totalCbm) || 0;

      //  (M1)
      item.totalCBMExpenseINR = parseFloat((totalCbm * cbmPrice).toFixed(6));
      const totalCBMExpenseINR = Number(item?.totalCBMExpenseINR) || 0;

      //  (N1)
      item.shippingExpPerPCS = parseFloat((totalCBMExpenseINR / totalPcsQty).toFixed(6));
      const shippingExpPerPCS = Number(item?.shippingExpPerPCS) || 0;

      //  (X)
      item.chinaToSuratPadtar = parseFloat((chinaFinalExpextedCode + shippingExpPerPCS + totalExp).toFixed(6));

      // K2
      item.totalWeight = parseFloat((poCarton * cartonWeight).toFixed(6));
      const totalWeight = Number(item?.totalWeight) || 0;

      // M2
      item.totalLoadAmt = parseFloat((totalWeight * cartonWeightRu).toFixed(6));
      const totalLoadAmt = Number(item?.totalLoadAmt) || 0;

      // N2
      item.shippingCostperPieceINR = parseFloat((totalLoadAmt / totalPcsQty).toFixed(6));
      const shippingCostperPieceINR = Number(item?.shippingCostperPieceINR) || 0;

      // O2
      item.totalShippingExpWeight = parseFloat((chinaFinalExpextedCode + totalExp + shippingCostperPieceINR).toFixed(6));
      const totalShippingExpWeight = Number(item?.totalShippingExpWeight) || 0;

      //PENDING QTY
      item.pendingQtyCarton = parseFloat((poCarton - receivedCartonsCH).toFixed(6));
      const pendingQtyCarton = Number(item?.pendingQtyCarton) || 0;

      item.totalPendingQty = parseFloat((pendingQtyCarton * pricePerCarton).toFixed(6));
      const totalPendingQty = Number(item?.totalPendingQty) || 0;

    });
  }

  // Redirect
  redirectToDetails() {
    let param = null;
    param = {
      pageNo: this.paginationRequest.pageNo,
      pageSize: this.paginationRequest.pageSize,
      customerId: this.paginationRequest.customerId,
      supplierId: this.paginationRequest.supplierId,
      fromDate: this.paginationRequest.fromDate,
      toDate: this.paginationRequest.toDate,
      searchPurchaseComments: this.paginationRequest.searchPurchaseComments,
      purchaseOrderSearch: this.paginationRequest.purchaseOrderSearch,
      pageName: 'po-import',
      selectedTab: this.selectedTab,
      isExpandedIDs: this.isExpandedIDs,
    }
    localStorage.setItem('param', JSON.stringify(param))
  }

  //Search & Filters
  onSearchPO(event: any) {
    this.paginationRequest.purchaseOrderSearchSubject.next(event.target.value ? event.target.value : null);
  }

  // PO -> Move to Draft
  openPOtoMoveToDraftModal(item: POImportsSub) {
    this.selectedPO = Serialize(item)
    this.fromPoToDraft.show();
  }

  onMovetoDraft() {
    const param = {
      poCreateId: this.selectedPO.id
    }

    this.utilsService.putMethodAPI(true, this.utilsService.serverVariableService.PO_BACK_TO_DRAFT, param, null, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.getAllPOImports()
        this.fromPoToDraft.hide();
      }
    })
  }

  // Po Created to Received China

  resetTotalCount = () => {
    this.totalCount = {
      receivedQtyTotal: 0,
      totalPoCarton: 0,
      totalPcsQty: 0,
      loadedQty: 0,
      overallRCQty: 0,
      overallLoadedQty: 0,
    }
  }

  openPoToRecieveChina(item: POImportList, subItem: POImportsSub, isEdit: boolean) {

    this.isEditPotoRec = false;
    this.isPoToRecieveChina = true;
    this.searchControlPOCreatedToRC.reset();

    let items = []
    this.oldItems = []
    this.associatedItems = [];
    this.resetTotalCount();

    if (item) {
      let key = null;
      this.poImportObj = Serialize(item);
      switch (this.selectedTab) {
        case this.enumForStatus.DRAFT:
          key = 'poImports'
          break;
        case this.enumForStatus.PO_CREATED:
          key = 'poImportCreate'
          break;
        case this.enumForStatus.RECEIVED_CHINA:
          key = 'poImportReceivedCh'
          break;
        default:
          break;
      }
      items = (this.poImportObj[key] as POImportsSub[]).flatMap(a =>
        a.poImportItemList.map(item => ({
          ...item,
          poCreateIdF: a.id
        }))
      );

      if (subItem) {
        this.selectedPO = Serialize(subItem);
        items = Serialize(this.selectedPO.poImportItemList)
      }
    }

    setTimeout(() => {
      let totalPoCarton = 0;
      let totalPcsQty = 0;

      items = items.map(item => {
        totalPoCarton += item.poCarton;
        totalPcsQty += item.totalPcsQty;
        return item;
      });
      this.totalCount.totalPcsQty = totalPcsQty
      this.totalCount.totalPoCarton = totalPoCarton

      this.oldItems = [...items]
      this.associatedItems = [...this.oldItems];
      if (isEdit) {
        this.isEditPotoRec = true;
        for (const item of this.associatedItems) {
          this.onInitEditRcChina(item);
        }
      }
    }, 250)

    setTimeout(() => {
      this.poToRecieveChina.show();
    }, 150);
  }

  onInitEditRcChina = (item: POImportItem) => {

    if (item) {
      item.rcCartonField = item.rcCarton ? item.rcCarton : 0;
    }

    this.totalCount.receivedQtyTotal = this.associatedItems.reduce((total, item) => {
      const qty = Number(item.rcCartonField);
      return total + (isNaN(qty) ? 0 : qty);
    }, 0);

    this.totalCount.overallRCQty = this.oldItems.reduce((total, item) => {
      const qty = Number(item.rcCartonField);
      return total + (isNaN(qty) ? 0 : qty);
    }, 0);
  }

  onChangeRecQty(item?: POImportItem) {

    if (item && (item.rcCartonField as any) === '') {
      item.rcCartonField = null;
    }

    this.totalCount.receivedQtyTotal = this.associatedItems.reduce((total, item) => {
      const qty = Number(item.rcCartonField);
      return total + (isNaN(qty) ? 0 : qty);
    }, 0);

    this.totalCount.overallRCQty = this.oldItems.reduce((total, item) => {
      const qty = Number(item.rcCartonField);
      return total + (isNaN(qty) ? 0 : qty);
    }, 0);
  }

  onSaveUpdatePoToRC() {

    if (!this.isEditPotoRec) {
      const hasError = () => {
        return this.oldItems.some(item => item.poCarton < ((item.receivedQty ? item.receivedQty : 0) + item.rcCartonField));
      }
      if (hasError()) {
        return;
      }
    }

    const hasOnlyZero = this.oldItems.some(a => a.rcCartonField === 0);
    if (hasOnlyZero) {
      return;
    }

    const poItemId = this.oldItems.map(a => ({
      rcCarton: a.rcCartonField ? a.rcCartonField : null,
      poImportItemIds: a.id,
      id: this.isEditPotoRec ? a.poChinaMapId : null,
      poCreateId: this.selectedPO?.id ?? a.poCreateIdF ?? null
    }))

    let param = {
      supplierId: this.poImportObj.supplierId,
      poItemId: poItemId,
      id: this.isEditPotoRec ? this.selectedPO?.id : null
    }

    this.utilsService.postMethodAPI(true, this.utilsService.serverVariableService.PO_CREATED_TO_RC_CHINA, param, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.getAllPOImports();
        this.poToRecieveChina.hide();
      }
    })
  }


  // REC China to Loaded

  importDetailsFormGroup() {
    this.importDetailsForm = this.fb.group({
      importer: [null, Validators.compose([Validators.required])],
      containerId: [null, Validators.compose([Validators.required])],
      date: [null, Validators.compose([Validators.required])],
      exp_days: [null],
      shipmentType: [null, Validators.compose([Validators.required])],
      cbmPrice: [null, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.ONLY_GREATER_THAN_ZERO)])],
      trackingLink: [null],
      note: [null]
    })
  }

  openRecToLoaded(item: POImportList, subItem: POImportsSub, isEdit: boolean) {
    this.isRCtoLoaded = true;
    this.searchControl.reset();

    this.selectedPO = Serialize(subItem)

    this.importCHtoLoadedObj = new POImportCHtoLoaded();
    this.importDetailsForm.reset();
    this.oldItems = []
    this.associatedItems = []
    this.resetTotalCount()

    this.stageStep = this.enumForRcToLoadedStage.SHIPMENT_DETAILS;

    let API = null;
    if (item.supplierId) {
      API = this.utilsService.serverVariableService.PO_LOADING_CONTAINER_EDIT_ADD_NEW + `?supplierId=${item.supplierId}&isFlag=${true}`
    }
    if (item.supplierId && subItem) {
      API = this.utilsService.serverVariableService.PO_REC_CHINA_LOADED_REQ_DATA + `?receivedIds=${subItem.id}`
    }

    this.utilsService.getMethodAPI(false, API, null, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.importDropdown = response;
        this.importDropdown.container = this.importDropdown.container.map(item => {
          return {
            ...item,
            disabled: [this.enumForStatus.RELEASED, this.enumForStatus.LOADED, this.enumForStatus.WAY_TO_SURAT, this.enumForStatus.GRN].includes(item?.status?.value)
          };
        })

        setTimeout(() => {
          this.importDetailsForm.get('exp_days').disable();
          this.importDetailsForm.get('exp_days').updateValueAndValidity();
        }, 150);

        setTimeout(() => {
          this.oldItems = response.poImportItems
          this.associatedItems = [...this.oldItems];

          let totalPoCarton = 0;
          let totalPcsQty = 0;

          this.associatedItems = this.associatedItems.map(item => {
            // receive or rc carton set
            item.calcReceiveKey = Serialize(item.rcCarton ?? item.receivedQty);
            item.calcLoadedQty = Serialize(item.getSumLoadedQty ?? item.loadedQty);
            item.totalPcsQty = parseFloat((item.calcReceiveKey * item.pricePerCarton).toFixed(2));
            totalPoCarton += item.calcReceiveKey;
            totalPcsQty += item.totalPcsQty;
            return item;
          });
          this.totalCount.totalPcsQty = totalPcsQty
          this.totalCount.totalPoCarton = totalPoCarton

          if (isEdit) {
            this.onChangeLoadQty()
          }
        }, 250);
      }
    })

    this.ImportDetailsModalRecChina.show();
  }

  isValidLoadedReq(item: POImportItem): boolean {
    const hasOnlyZero = item.loadedQtyField === 0
    if (hasOnlyZero) {
      return true;
    } else {
      const sumLoaded = item.calcLoadedQty ?? item.getSumLoadedQty ?? 0;
      const loadedQty = Number(item.loadedQtyField || 0);
      return item.calcReceiveKey < (sumLoaded + loadedQty);
    }
  }

  onChangeLoadQty() {
    this.totalCount.loadedQty = this.associatedItems.reduce((total, item) => {
      return total + (Number(item.loadedQtyField) || 0);
    }, 0);

    this.totalCount.overallLoadedQty = this.oldItems.reduce((total, item) => {
      return total + (Number(item.loadedQtyField) || 0);
    }, 0);
  }

  onNextStep(value: string) {
    if (value == this.enumForRcToLoadedStage.SELECT_CARTONS) {
      if (this.importDetailsForm.invalid) {
        this.importDetailsForm.markAllAsTouched();
        return;
      }
    }
    this.stageStep = value;
  }

  onSaveCHtoLoaded() {

    const hasOnlyZero = this.oldItems.some(a => a.loadedQtyField === 0)
    if (hasOnlyZero) {
      return;
    }

    const hasError = () => { return this.oldItems.some(item => this.isValidLoadedReq(item)) }
    if (hasError()) {
      return;
    }

    let param = Serialize(this.importCHtoLoadedObj) as POImportCHtoLoaded
    param.status = this.enumForStatus.LOADED
    param.poLoadedItem = this.oldItems.map(a => {
      const obj = {
        loadedQty: a.loadedQtyField ? a.loadedQtyField : null,
        poImportItemIds: a.id,
        receivedChId: this.selectedPO ? a.receivedChId : null,
      }
      return obj;
    }).filter(v => v.loadedQty !== null)

    param.containerId = null
    param.containerName = null
    if (this.importCHtoLoadedObj.selectedContainer) {
      if (this.importCHtoLoadedObj.selectedContainer?.id) {
        param.containerId = Serialize(this.importCHtoLoadedObj.selectedContainer?.id)
      } else {
        param.containerName = Serialize(this.importCHtoLoadedObj.selectedContainer.containerName)
      }
    }

    if (this.importCHtoLoadedObj.t_expectedDeliveryDate) {
      let date = new Date(this.importCHtoLoadedObj.t_expectedDeliveryDate.year, this.importCHtoLoadedObj.t_expectedDeliveryDate.month - 1, this.importCHtoLoadedObj.t_expectedDeliveryDate.day)
      param.expectedDeliveryDate = new DatePipe('en-US').transform(date, 'yyyy-MM-dd')
    }

    this.utilsService.postMethodAPI(true, this.utilsService.serverVariableService.PO_REC_CHINA_LOADED_SAVE, Serialize(param), (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.getAllPOImports();
        this.ImportDetailsModalRecChina.hide();
      }
    })

  }

  disableIfNoItemOrEmpty = (isPoToRC: boolean): boolean => {
    if (!this.oldItems || this.oldItems.length === 0) {
      return true;
    }
    const isEmpty = isPoToRC ? this.totalCount?.overallRCQty > 0 : this.totalCount?.overallLoadedQty > 0
    return !isEmpty;
  }


  // LOADED 
  openConfirmToReleased(item: POImportList) {
    this.poImportObj = (item)
    this.loadedconfirmationModal.show();
  }

  onConfirmToRelease() {

    this.utilsService.getMethodAPI(true, this.utilsService.serverVariableService.LOADED_TO_RELEASE + `?containerId=${this.poImportObj.containerId}&id=${this.poImportObj.id}`, null, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.getAllPOImports();
        this.loadedconfirmationModal.hide();
      }
    })

  }

  // LOADED CONTAINER EDIT

  loadedContainerEditForm() {
    this.loadedContainerEditGrp = this.fb.group({
      containerId: [null, Validators.compose([Validators.required])],
      note: [null]
    })
  }

  openLoadedContainerEdit(item: POImportList) {

    this.showEditContainerModal = true;
    this.editContainerChanged = false;
    this.poImportObj = Serialize(item)
    this.poImportObj.selectedContainer = Serialize(item.containerName)
    this.poImportObj.containerId = item.containerId;
    this.poImportObj.note = item.note

    let API = this.utilsService.serverVariableService.PO_REC_CHINA_LOADED_REQ_DATA
    this.utilsService.getMethodAPI(false, API, null, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.importDropdown = response;
        if (this.importDropdown?.container) {
          this.importDropdown.container = (this.importDropdown.container || []).map(item => {
            return {
              ...item,
              disabled: [this.enumForStatus.RELEASED, this.enumForStatus.LOADED, this.enumForStatus.WAY_TO_SURAT, this.enumForStatus.GRN].includes(item?.status?.value)
            };
          })
        }
        this.loadedEditContainerModal.show();
      }
    })
  }

  onEditLoadedContainer() {
    let param = Serialize(this.poImportObj) as POImportList
    param.containerId = this.poImportObj.containerId
    param.containerName = this.poImportObj.containerName
    if (this.editContainerChanged) {
      param.containerId = null
      param.containerName = null
      if (this.poImportObj.selectedContainer) {
        if (this.poImportObj.selectedContainer?.id) {
          param.containerId = this.poImportObj.selectedContainer?.id
        } else {
          param.containerName = this.poImportObj.selectedContainer.containerName
        }
      }
    }

    const loadedEditReq = {
      id: param.id,
      containerId: param.containerId ? param.containerId : null,
      containerName: param.containerName ? param.containerName : null,
      note: param.note ? param.note : null,
      status: this.selectedTab,
    }

    let API = this.utilsService.serverVariableService.PO_EDIT_CONTAINER;
    this.utilsService.postMethodAPI(true, API, loadedEditReq, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.getAllPOImports();
        this.loadedEditContainerModal.hide();
      }
    })
  }

  onChangeEditContainer = () => {
    this.editContainerChanged = true;
  }

  // PO IMPORT ITEM DELETE ACTION

  openItemDeleteFrom3rdTab(item: POImportItem, subItem: POImportsSub) {
    this.itemObj = Serialize(item);
    this.selectedPO = Serialize(subItem);
    this.deleteItemInPO.show();
  }

  onDeletePOItem() {

    let API = null;

    switch (this.selectedTab) {
      case this.enumForStatus.DRAFT:
        API = this.utilsService.serverVariableService.PO_ITEM_DEL + `?id=${this.itemObj.id}&poDraftId=${this.selectedPO.id}`
        break;

      case this.enumForStatus.PO_CREATED:
        API = this.utilsService.serverVariableService.PO_ITEM_DEL + `?id=${this.itemObj.id}&poCreateId=${this.selectedPO.id}`
        break;

      case this.enumForStatus.RECEIVED_CHINA:
        API = this.utilsService.serverVariableService.PO_RC_ITEM_DEL + `?id=${this.selectedPO.id}&poImportId=${this.itemObj.id}`
        break;
      default:
        break;
    }

    this.utilsService.deleteMethodAPI(true, API, {}, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.getAllPOImports();
        this.deleteItemInPO.hide();
      }
    })

  }

  /// Tempo Tab functions

  getTempoContainer() {

    let API = null;
    API = this.utilsService.serverVariableService.PAGE_REQ_DATA

    this.tempoContainerDropdown = []

    this.utilsService.getMethodAPI(false, API, null, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.tempoContainerDropdown = response.container
        this.tempoContainerDropdown = (this.tempoContainerDropdown || []).filter(item => item?.status?.value == this.enumForStatus.RELEASED)
      }
    })
  }

  open(): void {
    if (!this.pickerDirective.picker.isShown) {
      this.pickerDirective.open();
    } else this.pickerDirective.hide()
  }

  tempoForm() {
    this.tempFormGroup = this.fb.group({
      vehicleNo: [null, Validators.compose([Validators.required])],
      date: [null, Validators.compose([Validators.required])],
      containerIDs: [null, Validators.compose([Validators.required])],
      cartonQtyVehicle: [null, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.ONLY_GREATER_THAN_ZERO)])]
    })
  }

  openTempoModal = (obj: POImportList, status: string) => {
    this.tempoObj = new POImportTempo();
    this.tempFormGroup.reset();
    this.tempoObj.tempDate = this.tempoObj.tempDate ?? {};
    this.tempoObj.tempDate.start = moment().toDate();
    this.statusForModal = status;
    this.getTempoContainer();

    // if (status == 'Add') {
    //   setTimeout(() => {
    //     this.getReqDataTempo(null)
    //   }, 100);
    // }

    if (obj) {
      setTimeout(() => {
        this.getReqDataTempo(obj.id)
      }, 100);
    }
    
    this.addEditTempoModal.show();
  }

  getReqDataTempo = (id: number) => {
    let API = this.utilsService.serverVariableService.PO_TEMPO_SAVE_DEL
    if (id) {
      API = this.utilsService.serverVariableService.PO_TEMPO_SAVE_DEL + `?id=${id}`;
    }

    this.utilsService.getMethodAPI(false, API, null, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.tempoObj.availableCarton = response.availableCarton + (response.cartonQtyVehicle ? response.cartonQtyVehicle : 0)
        this.tempoObj.releasedCarton = response.releasedCarton

        if (id) {
          this.tempoObj = Deserialize(response, POImportTempo);

          this.tempoObj.tempDate = this.tempoObj.tempDate ?? {};
          this.tempoObj.tempDate.start = moment(response.date).toDate();
        }
        this.addEditTempoModal.show();
      }
    })
  }

  onSaveTempo = () => {

    if (this.tempFormGroup.invalid) {
      this.tempFormGroup.markAllAsTouched();
      return;
    }

    if (this.utilsService.isEmptyObjectOrNullUndefined(this.tempoObj.tempDate)) {
      return;
    }

    // if (this.exceedsAvailableCartons()) {
    //   return;
    // }

    let date = null;
    if (this.tempoObj.tempDate) {
      date = dayjs(this.tempoObj.tempDate?.start).format('YYYY-MM-DD HH:mm:00');
    }
    this.tempoObj.date = Serialize(date)

    this.utilsService.postMethodAPI(true, this.utilsService.serverVariableService.PO_TEMPO_SAVE_DEL, Serialize(this.tempoObj), (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.getAllPOImports();
        this.addEditTempoModal.hide();
      }
    })

  }

  exceedsAvailableCartons(): boolean {
    const cartonQty = this.tempFormGroup.get('cartonQtyVehicle').value;
    return cartonQty && cartonQty > Number(this.tempoObj.availableCarton);
  }

  openTempoToCompleted = (obj: POImportList) => {
    this.poImportObj = (obj)
    this.tempoToCompleted.show();
  }

  onSaveTempoToCompleted = () => {
    this.utilsService.putMethodAPI(true, this.utilsService.serverVariableService.PO_TEMPO_SAVE_DEL + `?id=${this.poImportObj.id}`, null, null, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.getAllPOImports();
        this.tempoToCompleted.hide();
      }
    })

  }

  onTempoMarkAsCompleted = () => {
    this.utilsService.putMethodAPI(true, this.utilsService.serverVariableService.PO_TEMPO_MARK_AS_COMPLETED + `?id=${this.poImportObj.id}`, null, null, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.getAllPOImports();
        this.tempoToCompleted.hide();
      }
    })

  }

  openDeleteTempoModal = (obj: POImportList) => {
    this.poImportObj = (obj)
    this.deleteTempoModal.show();
  }

  onDelTempo = () => {
    this.utilsService.deleteMethodAPI(true, this.utilsService.serverVariableService.PO_TEMPO_SAVE_DEL + `?id=${this.poImportObj.id}`, null, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.getAllPOImports();
        this.deleteTempoModal.hide();
      }
    })
  }

  warningGRNStart = (item: POImportList) => {
    this.poImportObj = item;
    if (!item.isStartGrnFlag) {
      this.GRNStartConfirmModal.show();
    } else {
      this.utilsService.redirectTo(`/users/purchases/po-import/grn-start/` + `${item.id}`);
    }
  }

  redirectToCartonMapping = () => {
    this.redirectToDetails()
    this.storeContainerIDsLocalStorage();
    this.utilsService.redirectTo(`/users/purchases/carton-mapping/`)
    this.GRNStartConfirmModal.hide();
  }

  storeContainerIDsLocalStorage = () => {
    const param = {
      containerIDs: this.poImportObj.containerIDs
    }
    localStorage.setItem('containerIDs', JSON.stringify(param));
  }

  // Change Status Changes

  getChangeStatusContainer(item: POImportItem, poImportObj: POImportList, selectedPOSub: POImportsSub) {

    this.changeStatusFG.reset();
    this.poImportObj = null;
    this.itemObj = null;
    this.selectedPO = null;

    let API = null;
    API = this.utilsService.serverVariableService.PAGE_REQ_DATA

    this.containerDropdown = []

    this.utilsService.getMethodAPI(false, API, null, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.containerDropdown = response.container
        this.poImportObj = poImportObj
        this.itemObj = item
        this.selectedPO = selectedPOSub ? selectedPOSub : null

        setTimeout(() => {
          this.itemObj.calcReceiveKey = Serialize(this.itemObj.rcCarton ?? this.itemObj.receivedQty);
          this.itemObj.calcLoadedQty = Serialize(this.itemObj.getSumLoadedQty ?? this.itemObj.loadedQty);
        }, 150);

        this.changeStatusItemModal.show();
      }
    })
  }

  openStatusChangeModal = (item: POImportItem, poImportObj: POImportList, selectedPOSub: POImportsSub) => {
    this.changeStatusItemModalShow = true;
    this.changeStatusContainer = null;
    this.changeStatusId = null;
    this.statusDropdownCS = []
    this.oldContainerId = null;

    switch (this.selectedTab) {
      case this.enumForStatus.RECEIVED_CHINA:
        this.statusDropdownCS = (this.dropdown.status as { label: string, value: string }[])
          .filter(a => a.value === this.enumForStatus.LOADED);
        break;

      case this.enumForStatus.LOADED:
        this.statusDropdownCS = (this.dropdown.status as { label: string, value: string }[])
          .filter(a => a.value === this.enumForStatus.RECEIVED_CHINA || a.value === this.enumForStatus.RELEASED);
        break;

      case this.enumForStatus.RELEASED:
        this.statusDropdownCS = (this.dropdown.status as { label: string, value: string }[])
          .filter(a => a.value === this.enumForStatus.LOADED);
        break;

      default:
        this.statusDropdownCS = [];
        break;
    }

    this.getChangeStatusContainer(item, poImportObj, selectedPOSub)
  }

  onStatusChange = () => {

    let API = null;
    let param = null;

    if(this.changeStatusFG.invalid) {
      this.changeStatusFG.markAllAsTouched();
      return;
    }

    switch (this.selectedTab) {
      case this.enumForStatus.RECEIVED_CHINA:
        API = this.utilsService.serverVariableService.PO_RC_CHINA_CHANGE_STATUS;

        if (this.itemObj.loadedQtyField === 0) {
          return;
        }

        const hasError = () => { return this.isValidLoadedReq(this.itemObj) }
        if (hasError()) {
          return;
        }

        param = {
          containerId: this.changeStatusContainer,
          status: this.changeStatusId,
          receivedId: this.selectedPO.id,
          poItemId: this.itemObj.id,
          loadedQty: this.itemObj.loadedQtyField ? this.itemObj.loadedQtyField : null,
        }
        break;

      case this.enumForStatus.LOADED:
        API = this.utilsService.serverVariableService.PO_LOADED_CHANGE_STATUS

        if (this.changeStatusId === this.enumForStatus.RECEIVED_CHINA) {
          const hasErrorLoaded = () => { return this.isValidLoadedtoRCReq(this.itemObj) }
          if (hasErrorLoaded()) {
            return;
          }

          param = {
            receivedIds: this.itemObj.receivedChId,
            status: this.changeStatusId,
            receivedChQty: this.itemObj.rcCartonField,
            poItemId: this.itemObj.id,
            loadedMapId: this.itemObj.loadedMapId,
            oldContainerId: this.poImportObj.containerId
          }
        }

        if (this.changeStatusId === this.enumForStatus.RELEASED) {
          const hasErrorLoadedToRel = () => { return this.isValidLoadedToRelease(this.itemObj) }
          if (hasErrorLoadedToRel()) {
            return;
          }

          param = {
            status: this.changeStatusId,
            poItemId: this.itemObj.id,
            releasedQty: this.itemObj.releaseQtyField,
            containerId: this.changeStatusContainer,
            loadedId: this.poImportObj.id,
            loadedMapId: this.itemObj.loadedMapId,
            receivedIds: this.itemObj.receivedChId,
            oldContainerId: this.poImportObj.containerId
          }
        }

        break;

      case this.enumForStatus.RELEASED:
        API = this.utilsService.serverVariableService.PO_LOADED_CHANGE_STATUS

        if (this.changeStatusId === this.enumForStatus.LOADED) {
          const hasErrorRelToLoaded = () => { return this.isValidReleasedToLoaded(this.itemObj) }
          if (hasErrorRelToLoaded()) {
            return;
          }

          param = {
            status: this.changeStatusId,
            poItemId: this.itemObj.id,
            loadedQty: this.itemObj.loadedQtyField,
            containerId: this.changeStatusContainer,
            loadedId: this.poImportObj.id,
            loadedMapId: this.itemObj.loadedMapId,
            receivedIds: this.itemObj.receivedChId,
            oldContainerId: this.poImportObj.containerId
          }
        }
        break;
      default:
        break;
    }

    this.utilsService.postMethodAPI(true, API, param, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.getAllPOImports();
        this.changeStatusItemModal.hide();
      }
    })
  }

  changeStatusForm = () => {
    this.changeStatusFG = this.fb.group({
      changeStatusId: [null, Validators.required],
      changeStatusContainer: [null],
      loadedQtyField: [null],
      releaseQtyField: [null],
      rcCartonField: [null],
    })
  }

  isValidLoadedtoRCReq(item: POImportItem): boolean {
    const hasOnlyZero = item.rcCartonField === 0
    if (hasOnlyZero) {
      return true;
    } else {
      return item.rcCartonField > item.loadedQty;
    }
  }

  isValidLoadedToRelease = (item: POImportItem): boolean => {
    const hasOnlyZero = item.releaseQtyField === 0
    if (hasOnlyZero) {
      return true;
    } else {
      return item.releaseQtyField > item.loadedQty;
    }
  }

  isValidReleasedToLoaded = (item: POImportItem): boolean => {
    const hasOnlyZero = item.loadedQtyField === 0
    if (hasOnlyZero) {
      return true;
    } else {
      return item.loadedQtyField > item.releasedQty;
    }
  }

  onFilterContainer = () => {

    this.changeStatusContainer = null;

    let allFields = ['changeStatusContainer', 'loadedQtyField', 'releaseQtyField', 'rcCartonField'];
    allFields.map(a => {
      this.changeStatusFG.get(a).reset()
      this.changeStatusFG.get(a).clearValidators()
      this.changeStatusFG.get(a).updateValueAndValidity()
    })

    switch (this.changeStatusId) {
      case this.enumForStatus.LOADED:

        let fieldsL = ['changeStatusId', 'changeStatusContainer', 'loadedQtyField']
        fieldsL.map(a => {
          this.changeStatusFG.get(a).addValidators([Validators.compose([Validators.required])])
          this.changeStatusFG.get(a).updateValueAndValidity()
        })

        this.containerDropdown = (this.containerDropdown || []).filter(item => (item?.status?.value == this.enumForStatus.LOADED))
        break;
      case this.enumForStatus.RELEASED:

        let fieldsR = ['changeStatusId', 'changeStatusContainer', 'releaseQtyField']
        fieldsR.map(a => {
          this.changeStatusFG.get(a).addValidators([Validators.compose([Validators.required])])
          this.changeStatusFG.get(a).updateValueAndValidity()
        })

        this.containerDropdown = (this.containerDropdown || []).filter(item => (item?.status?.value == this.enumForStatus.RELEASED))
        break;
      case this.enumForStatus.RECEIVED_CHINA:
        let fieldsRC = ['changeStatusId', 'rcCartonField']
        fieldsRC.map(a => {
          this.changeStatusFG.get(a).addValidators([Validators.compose([Validators.required])])
          this.changeStatusFG.get(a).updateValueAndValidity()
        })
        break;
      default:
        break;
    }
  }

  // On Clear Filters

  onClear = () => {
    this.emptyPageFilters();
    this.getAllPOImports();
  }

  emptyPageFilters() {
    // this.paginationRequest.dateRangeControl.reset();
    this.paginationRequest.fromDate = null;
    this.paginationRequest.toDate = null;
    this.paginationRequest.supplierId = null;
    this.paginationRequest.customerId = null;
    this.paginationRequest.searchPurchaseComments = null;
    this.paginationRequest.purchaseOrderSearch = null;
    this.paginationRequest.temp_expectedDeliveryDate = null;
    this.paginationRequest.expectedDeliveryDate = null;
    this.paginationRequest.temp_loadedDate = null;
    this.paginationRequest.searchText = null;
    this.paginationRequest.loadedDate = null;
    this.paginationRequest.importerId = null;
    this.paginationRequest.searchByPoContainer = null;
    this.paginationRequest.fromDateTime = null;
    this.paginationRequest.toDateTime = null;
    this.paginationRequest.tempoStatus = null;

    switch (this.selectedTab) {
      case this.enumForStatus.DRAFT:
        this.PoImportDraftComponent?.onClearDate()
        break;
      case this.enumForStatus.PO_CREATED:
        this.PoImportCreatedComponent?.onClearDate()
        break;
      case this.enumForStatus.RECEIVED_CHINA:
        this.PoImportReceivedChinaComponent?.onClearDate()
        break;
      case this.enumForStatus.LOADED:
        this.PoImportLoadedComponent?.onClearDate()
        break;
      case this.enumForStatus.RELEASED:
        this.PoImportReleasedComponent?.onClearDate()
        break;
      case this.enumForStatus.TEMPO:
        this.PoImportTempoComponent?.onClearDate()
        break;
      case this.enumForStatus.GRN:
        this.PoImportGrnComponent?.onClearDate()
        break;
      case this.enumForStatus.COMPLETED:
        this.PoImportCompletedComponent?.onClearDate()
        break;
      default:
        break;
    }
  }

  // Track By
  trackBy(index: number, name: POImportItem): number {
    return name.id;
  }

  // Download Packing

  // printQR(obj: POImportList) {

  //   let API = this.utilsService.serverVariableService.AISLE_QR;
  //   if (obj) {
  //     API = this.utilsService.serverVariableService.AISLE_QR + `?id=${obj.id}`;
  //   }

  //   this.utilsService.getMethodAPI(false, API, null, (response) => {
  //     if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
  //       this.base64SourceSingle = Serialize(response)
  //       this.base64SourceSingle = this.base64SourceSingle.map(a => {
  //         return this.sanitizer.bypassSecurityTrustResourceUrl(`data:image/png;base64, ${a}`);
  //       });
  //       setTimeout(() => {
  //         const customPrintOptions: PrintOptions = new PrintOptions({
  //           printSectionId: 'dropOffSingle',
  //           printTitle: 'Packing'
  //         });
  //         // this.printService.printStyle = {'@page': { size: '3in 5in', margin: '2mm 2mm 2mm 2mm' }}
  //         // this.printService.printStyle = {'@page': { size: '4in 6in', margin: '3mm 3mm 3mm 3mm' }}
  //         this.printService.printStyle = { '@page': { size: 'A4', margin: '5mm 5mm 5mm 5mm' } }
  //         this.printService.print(customPrintOptions)
  //       }, 200);
  //     }
  //   })

  // }


  downloadPacking(id: number, status) {
    this.utilsService.exportReportGetApi(this.utilsService.serverVariableService.PO_RELEASE_LOADED_PACKING_DOWNLOAD + `?id=${id}&poStatus=${status}`).subscribe((data: any) => {
      saveAs(new Blob([data], { type: 'application/pdf' }), 'Packing PDF');
    });
  }

  downloadQR(id: number) {
    this.utilsService.exportReportGetApi(this.utilsService.serverVariableService.PO_QR_DOWNLOAD + `?id=${id}`).subscribe((data: any) => {
      saveAs(new Blob([data], { type: 'application/pdf' }), 'QR Code');
    });
  }

  // Rc to loaded change shipping type

  get shippingType(): string {
    const value = this.importDetailsForm.get('shipmentType').value
    return value ? value : null;
  }

  changeShipmentType = () => {
    this.importDetailsForm.get('cbmPrice').addValidators(Validators.compose([Validators.required]));
    if (this.shippingType == 'DONE') {
      this.importDetailsForm.get('cbmPrice').clearValidators();
    }
    this.importDetailsForm.get('cbmPrice').reset()
    this.importDetailsForm.get('cbmPrice').updateValueAndValidity()
  }

  // redirect to tempo expense
  redirectToTempoExpense(tempoId: number): void {
    this.utilsService.redirectTo(`/users/purchases/expenses/new-tempo-expense/${tempoId}`);
  }

  // redirect to container expense
  redirectToContainerExpense(containerId: number): void {
    this.utilsService.redirectTo(`/users/purchases/expenses/new-expense/${containerId}`);
  }

  // Assign to CHA

  assignCHAForm = () => {
    
    this.assignCHAFormGroup = this.fb.group({
      importerId: [null, Validators.compose([Validators.required])],
      cartonQty: [null, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.ONLY_GREATER_THAN_ZERO), this.assignToCHAQtyValidation])],
    })
  }

  openAssignToCHA = (item: POImportItem, poImportObj: POImportList) => {
    this.assignCHAFormGroup.reset();
    this.CHAassignModal.show();
    this.showCHAAssignModal = true;
    this.assignCHAFormGroup.get('importerId').disable();
    this.assignCHAFormGroup.get('importerId').updateValueAndValidity()
    setTimeout(() => {
      this.itemObj = item;
      this.poImportObj = poImportObj;
      this.getCHAImporterDropdown()
    }, 100);
  }

  getCHAImporterDropdown = () => {
    let API = this.utilsService.serverVariableService.PO_REQ_ASSIGN_TO_CHA
    this.utilsService.getMethodAPI(false, API, null, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.CHAdropdown = response.importer || [];
        this.assignCHAFormGroup.patchValue({
          importerId: this.poImportObj.importerId,
          cartonQty: this.itemObj.assignToCHAQty ? this.itemObj.assignToCHAQty : (this.itemObj.PendingGrnQty || 0),
        })
      }
    })
  }

  onAssignToCHA = () => {

    if(this.assignCHAFormGroup.invalid) {
      this.assignCHAFormGroup.markAllAsTouched();
      return;
    }

    const param = {
      importerId: +this.assignCHAFormGroup.get('importerId').value,
      assignToCHAQty: +this.assignCHAFormGroup.get('cartonQty').value,
      poLoadedMapId: this.itemObj.loadedMapId,
    }

    this.utilsService.postMethodAPI(true, this.utilsService.serverVariableService.PO_ASSIGN_TO_CHA, param, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.getAllPOImports();
        this.CHAassignModal.hide();
      }
    })

  }

  // validation for cartonQty control to not exceed PendingGrnQty
  assignToCHAQtyValidation = (control: AbstractControl) => {
    if (control.value > ((this.itemObj.PendingGrnQty || 0) + (this.itemObj.assignToCHAQty || 0))) {
      return { assignToCHAQty: true };
    }
    return null;
  }

  /// Move to completed

  openMoveToCompleted = (poImportObj: POImportList) => {
    this.moveToCompletedModal.show();
    this.showMoveToCompletedModal = true;
    setTimeout(() => {
      this.poImportObj = poImportObj;
      this.getMarkAsCompletedData()
    }, 100);
  }

  getMarkAsCompletedData = () => {
    this.markAsCompletedList = []
    let API = `${this.utilsService.serverVariableService.PO_GET_DATA_MARK_AS_COMPLETED}?loadedId=${this.poImportObj.id}`
    this.utilsService.getMethodAPI(false, API, null, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.markAsCompletedList = response;
      }
    })
  }

  onMoveToConfirmation = () => {
    this.moveToCompletedModal.hide();
    this.completedConfirmationModal.show();
  }

  moveToCompletedConfirm = () => {

    let API = `${this.utilsService.serverVariableService.PO_MOVE_TO_COMPLETED}?containerId=${this.poImportObj.containerId}&id=${this.poImportObj.id}` 

    this.utilsService.putMethodAPI(true, API, null, null, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.getAllPOImports();
        this.completedConfirmationModal.hide();
      }
    })
  }

  trackByMarkAsCompleted(index: number, name: POImportItem): number {
    return name.associatedIds;
  }

  onRefresh = () => {
    this.getAllPOImports();
  }

}
