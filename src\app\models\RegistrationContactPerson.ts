import { deserializeAs, serializeAs } from 'cerialize';

export class RegistrationCP {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    // @serializeAs('contactPersonId')
    @deserializeAs('contactPersonId')
    private _contactPersonId: number;

    @serializeAs('firstName')
    @deserializeAs('firstName')
    private _firstName: string;

    @serializeAs('lastName')
    @deserializeAs('lastName')
    private _lastName: string;

    @serializeAs('designation')
    @deserializeAs('designation')
    private _designation: string;

    @serializeAs('email')
    @deserializeAs('email')
    private _email: string;

    @serializeAs('phoneNo')
    @deserializeAs('phoneNo')
    private _phoneNo: string;

    @serializeAs('countryId')
    @deserializeAs('countryId')
    private _countryId: number;

    @deserializeAs('Country')
    private _Country: any[];

    @deserializeAs('country')
    private _country: any;


    constructor() {
        this.Country = [];
    }


    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Getter contactPersonId
     * @return {number}
     */
	public get contactPersonId(): number {
		return this._contactPersonId;
	}

    /**
     * Getter firstName
     * @return {string}
     */
	public get firstName(): string {
		return this._firstName;
	}

    /**
     * Getter lastName
     * @return {string}
     */
	public get lastName(): string {
		return this._lastName;
	}

    /**
     * Getter designation
     * @return {string}
     */
	public get designation(): string {
		return this._designation;
	}

    /**
     * Getter email
     * @return {string}
     */
	public get email(): string {
		return this._email;
	}

    /**
     * Getter phoneNo
     * @return {string}
     */
	public get phoneNo(): string {
		return this._phoneNo;
	}

    /**
     * Getter countryId
     * @return {number}
     */
	public get countryId(): number {
		return this._countryId;
	}

    /**
     * Getter Country
     * @return {any[]}
     */
	public get Country(): any[] {
		return this._Country;
	}

    /**
     * Getter country
     * @return {any}
     */
	public get country(): any {
		return this._country;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Setter contactPersonId
     * @param {number} value
     */
	public set contactPersonId(value: number) {
		this._contactPersonId = value;
	}

    /**
     * Setter firstName
     * @param {string} value
     */
	public set firstName(value: string) {
		this._firstName = value;
	}

    /**
     * Setter lastName
     * @param {string} value
     */
	public set lastName(value: string) {
		this._lastName = value;
	}

    /**
     * Setter designation
     * @param {string} value
     */
	public set designation(value: string) {
		this._designation = value;
	}

    /**
     * Setter email
     * @param {string} value
     */
	public set email(value: string) {
		this._email = value;
	}

    /**
     * Setter phoneNo
     * @param {string} value
     */
	public set phoneNo(value: string) {
		this._phoneNo = value;
	}

    /**
     * Setter countryId
     * @param {number} value
     */
	public set countryId(value: number) {
		this._countryId = value;
	}

    /**
     * Setter Country
     * @param {any[]} value
     */
	public set Country(value: any[]) {
		this._Country = value;
	}

    /**
     * Setter country
     * @param {any} value
     */
	public set country(value: any) {
		this._country = value;
	}

}