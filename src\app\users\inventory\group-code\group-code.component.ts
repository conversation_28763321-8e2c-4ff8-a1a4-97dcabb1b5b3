import { Component, OnInit, inject } from '@angular/core';
import { GroupCode } from '@modal/GroupCode';
import { GroupCodePagination } from '@modal/request/GroupCodePagination';
import { UtilsService } from '@service/utils.service';
import { activeInactiveStatus, GROUP_CODE } from 'src/app/shared/constants/constant';
import { Deserialize, Serialize } from 'cerialize';
import { saveAs } from 'file-saver';
import { Category } from '@modal/Category';
declare var window: any;

@Component({
  selector: 'app-group-code',
  templateUrl: './group-code.component.html',
  styleUrls: ['./group-code.component.css']
})
export class GroupCodeComponent implements OnInit {

  utilsService = inject(UtilsService);

  enumForSortOrder = this.utilsService.enumForSortOrder;
  activeInactiveStatus = activeInactiveStatus;
  groupCodeTH: any[] = []
  selectedIds: any[] = [];
  categoryList: any[] = [];
  flattenedParentCategory: any[] = [];
  paginationRequest = new GroupCodePagination();
  groupCodeList: GroupCode[] = [];
  groupCodeObj = new GroupCode();

  deleteGroupCodeModal: any;

  constructor() { }

  ngOnInit() {
    this.groupCodeTH = GROUP_CODE
    this.getAllGroupCode();
    this.getDropDown();

    this.deleteGroupCodeModal = new window.bootstrap.Modal(
      document.getElementById('deleteGroupCodeModal')
    );
  }

  getAllGroupCode = () => {

    let ls_param = null
    ls_param = JSON.parse(localStorage.getItem('param'))

    if (!this.utilsService.isNullUndefinedOrBlank(ls_param)) {
      if (ls_param.pageName === 'group-code') {
        this.paginationRequest.pageNo = ls_param.pageNo,
          this.paginationRequest.pageSize = ls_param.pageSize,
          this.paginationRequest.sortOrder = ls_param.sortOrder
        this.paginationRequest.sortColumn = ls_param.sortColumn,
          this.paginationRequest.searchText = ls_param.searchText,
          this.paginationRequest.isActive = ls_param.isActive,
          this.paginationRequest.categoryId = ls_param.categoryId
      }
    }

    this.paginationRequest.flagForSelectAll = false;
    this.selectedIds = []
    this.utilsService.postMethodAPI(false, this.utilsService.serverVariableService.GROUP_CODE_LISTING, Serialize(this.paginationRequest), (response: any) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.groupCodeList = Deserialize(response.content, GroupCode);
        this.paginationRequest.totalData = response.totalElements;
        this.paginationRequest.pagination = response;
        localStorage.removeItem('param')
      } else {
        this.groupCodeList = [];
      }
    })
  }

  getDropDown() {
    this.utilsService.getMethodAPI(false, this.utilsService.serverVariableService.GROUP_CODE_DROPDOWN, null, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.categoryList = response;
        setTimeout(() => {
          this.getParentCategory();
        }, 150);
      }
    })
  }

  //// category dropdown
  modifyParentResponse(category: any[]) {
    let flatList = [];
    for (let parent of (category || [])) {
      parent.className = "ng-select-option-" + (parent.index + 1);
      flatList.push(parent);
      if (parent.categoryList && parent.categoryList.length > 0) {
        let data = (parent.categoryList || []).filter(p => p.index < 4);
        for (let d of data) {
          d.className = "ng-select-option-" + (d.index + 1);
          d.isChild = true;
        }
        flatList = flatList.concat(this.modifyParentResponse(data));
      }
    }
    return flatList;
  }

  getParentCategory() {
    let data = this.assignCategoryIndex(this.categoryList)
    data = (data || []).filter(list => list.index == 0);
    this.flattenedParentCategory = this.modifyParentResponse(data);
  }

  assignCategoryIndex(categories: Category[], depth: number = 0): any[] {
    return categories.map((category) => {
      category.index = depth;
      if (category.categoryList && category.categoryList.length > 0) {
        category.categoryList = this.assignCategoryIndex(category.categoryList, depth + 1);
      }
      return category;
    });
  }

  // sorting 
  onSortTH = (key: string) => {

    if (this.utilsService.isEmptyObjectOrNullUndefined(this.groupCodeList)) {
      return;
    }

    if (key === this.paginationRequest.sortColumn) {
      if (this.paginationRequest.sortOrder === this.enumForSortOrder.A) {
        this.paginationRequest.sortOrder = this.enumForSortOrder.D;
      } else if (this.paginationRequest.sortOrder === this.enumForSortOrder.D) {
        this.paginationRequest.sortOrder = this.enumForSortOrder.A;
      }
    } else {
      this.paginationRequest.sortOrder = this.enumForSortOrder.D;
    }

    this.paginationRequest.sortColumn = key;
    this.getAllGroupCode();
  }

  selectAll = () => {
    this.selectedIds = [];

    if (this.paginationRequest.flagForSelectAll) {
      this.groupCodeList.forEach(val => {
        val.isSelected = true;
        this.selectedIds.push(val.id);
      });
    } else {
      this.groupCodeList.forEach(val => {
        val.isSelected = false;
      });
    }
  }

  selectUnselect = (id: number, value) => {
    const isSelected = this.selectedIds.includes(id);
    if (value && !isSelected) {
      this.selectedIds.push(id);
    } else if (!value && isSelected) {
      const assetIndex = this.selectedIds.indexOf(id);
      this.selectedIds.splice(assetIndex, 1);
    }
    this.paginationRequest.flagForSelectAll = this.checkIfAllSelected();
  }

  checkIfAllSelected = (): boolean => {
    return this.groupCodeList.every(val => val.isSelected);
  }

  // pagination 
  addPageSizeData = (event) => {
    this.paginationRequest.pageNo = 1;
    this.paginationRequest.pageSize = event;
    this.getAllGroupCode();
  }

  pageNumber = (event) => {
    this.paginationRequest.pageNo = event
    this.getAllGroupCode();
  }

  trackBy = (index: number, name: GroupCode): number => {
    return name.id;
  }

  // redirect
  redirectToDetails = () => {
    let param = null;
    param = {
      pageNo: this.paginationRequest.pageNo,
      pageSize: this.paginationRequest.pageSize,
      sortOrder: this.paginationRequest.sortOrder,
      sortColumn: this.paginationRequest.sortColumn,
      searchText: this.paginationRequest.searchText,
      isActive: this.paginationRequest.isActive,
      categoryId: this.paginationRequest.categoryId,
      pageName: 'group-code'
    }
    localStorage.setItem('param', JSON.stringify(param))
  }

  // export to excel
  exportReport() {

    const param = {
      ids: this.selectedIds ? this.selectedIds : [],
      isActive: this.paginationRequest.isActive,
      searchText: this.paginationRequest.searchText,
      sortOrder: this.paginationRequest.sortOrder,
      sortColumn: this.paginationRequest.sortColumn,
      categoryId: this.paginationRequest.categoryId,
    }

    this.utilsService.exportReport(param, this.utilsService.serverVariableService.GROUP_CODE_EXPORT).subscribe((data: any) => {
      saveAs(new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' }), 'Group Code Sheet');
    });
  }

  // change status
  onChangeStatus = (item: GroupCode, value, index) => {
    this.utilsService.putMethodAPI(true, this.utilsService.serverVariableService.GROUP_CODE_CHANGE_STATUS + `${item.id}`, {}, '', (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response.data)) {
        this.groupCodeList[index].isActive = value
      } else {
        this.groupCodeList[index].isActive = !value
      }
      if (!this.paginationRequest.pagination?.first && this.paginationRequest.pagination?.last && this.paginationRequest.pagination?.numberOfElements === 1 && this.paginationRequest.isActive) {
        this.paginationRequest.pageNo = this.paginationRequest.pageNo - 1
      }
      this.getAllGroupCode()
    }, true);
  }

  //Search
  onSearch = (event: any) => {
    this.paginationRequest.searchText = event.target.value;
    this.getAllGroupCode();
  }

  // active/inactive
  onChangeActive = () => {
    this.paginationRequest.pageNo = 1;
    this.getAllGroupCode();
  }

  // Delete

  openDeleteGroupCodeModal = (obj: GroupCode) => {
    this.groupCodeObj = Serialize(obj)
    this.deleteGroupCodeModal.show();
  }

  deleteGroupCode() {

    this.utilsService.deleteMethodAPI(true, this.utilsService.serverVariableService.GROUP_CODE_SAVE_EDIT_DELETE + `?id=${this.groupCodeObj.id}`, {}, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.deleteGroupCodeModal.hide();
        if (!this.paginationRequest.pagination?.first && this.paginationRequest.pagination?.last && this.paginationRequest.pagination?.numberOfElements === 1) {
          this.paginationRequest.pageNo = this.paginationRequest.pageNo - 1
        }
        this.getAllGroupCode();
      }
    })
  }
}
