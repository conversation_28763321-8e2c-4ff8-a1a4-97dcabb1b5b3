import { Directive, ElementRef, HostListener } from '@angular/core';

@Directive({
  selector: '[appNumberFloatOnly]'
})
export class NumberFloatOnlyDirective {

  constructor(private el: ElementRef) { }

  @HostListener('input', ['$event']) onInputChange(event: KeyboardEvent): void {
    const inputElement = this.el.nativeElement;
    inputElement.value = inputElement.value.replace(/[^0-9.]/g, '');
    if ((inputElement.value.match(/\./g) || []).length > 1) {
      inputElement.value = inputElement.value.replace(/\.+$/, '');
    }
    event.stopPropagation();
  }
}
