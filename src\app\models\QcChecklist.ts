import { deserializeAs, serializeAs } from 'cerialize';

export class QcChecklist {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('createdDate')
    @deserializeAs('createdDate')
    private _createdDate: string;

    @serializeAs('lastModifiedDate')
    @deserializeAs('lastModifiedDate')
    private _lastModifiedDate: string;

    @serializeAs('updatedDate')
    @deserializeAs('updatedDate')
    private _updatedDate: string;

    @serializeAs('name')
    @deserializeAs('name')
    private _name: string;

    @serializeAs('isActive')
    @deserializeAs('isActive')
    private _isActive: boolean;

    @serializeAs('isSelected')
    @deserializeAs('isSelected')
    private _isSelected: boolean;

    @serializeAs('checklists')
    @deserializeAs('checklists')
    private _checklists: Checklist[];

    @serializeAs('itemId')
    @deserializeAs('itemId')
    private _itemId: number[];

    @serializeAs('itemDeleteId')
    @deserializeAs('itemDeleteId')
    private _itemDeleteId: number[];

    @deserializeAs('countItem')
    private _countItem: number;

    @deserializeAs('isDefault')
    private _isDefault: boolean;

    constructor() {
        this.isActive = false;
        this.isSelected = false;
        this.checklists = [];
        this.itemId = [];
        this.itemDeleteId = [];
        this.isDefault = false;
    }

    /**
     * Getter isDefault
     * @return {boolean}
     */
	public get isDefault(): boolean {
		return this._isDefault;
	}

    /**
     * Setter isDefault
     * @param {boolean} value
     */
	public set isDefault(value: boolean) {
		this._isDefault = value;
	}

    /**
     * Getter countItem
     * @return {number}
     */
	public get countItem(): number {
		return this._countItem;
	}

    /**
     * Setter countItem
     * @param {number} value
     */
	public set countItem(value: number) {
		this._countItem = value;
	}


    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Getter createdDate
     * @return {string}
     */
	public get createdDate(): string {
		return this._createdDate;
	}

    /**
     * Getter lastModifiedDate
     * @return {string}
     */
	public get lastModifiedDate(): string {
		return this._lastModifiedDate;
	}

    /**
     * Getter name
     * @return {string}
     */
	public get name(): string {
		return this._name;
	}

    /**
     * Getter isActive
     * @return {boolean}
     */
	public get isActive(): boolean {
		return this._isActive;
	}

    /**
     * Getter isSelected
     * @return {boolean}
     */
	public get isSelected(): boolean {
		return this._isSelected;
	}

    /**
     * Getter checklists
     * @return {Checklist[]}
     */
	public get checklists(): Checklist[] {
		return this._checklists;
	}

    /**
     * Getter itemId
     * @return {number[]}
     */
	public get itemId(): number[] {
		return this._itemId;
	}

    /**
     * Getter itemDeleteId
     * @return {number[]}
     */
	public get itemDeleteId(): number[] {
		return this._itemDeleteId;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Setter createdDate
     * @param {string} value
     */
	public set createdDate(value: string) {
		this._createdDate = value;
	}

    /**
     * Setter lastModifiedDate
     * @param {string} value
     */
	public set lastModifiedDate(value: string) {
		this._lastModifiedDate = value;
	}

    /**
     * Setter name
     * @param {string} value
     */
	public set name(value: string) {
		this._name = value;
	}

    /**
     * Setter isActive
     * @param {boolean} value
     */
	public set isActive(value: boolean) {
		this._isActive = value;
	}

    /**
     * Setter isSelected
     * @param {boolean} value
     */
	public set isSelected(value: boolean) {
		this._isSelected = value;
	}

    /**
     * Setter checklists
     * @param {Checklist[]} value
     */
	public set checklists(value: Checklist[]) {
		this._checklists = value;
	}

    /**
     * Setter itemId
     * @param {number[]} value
     */
	public set itemId(value: number[]) {
		this._itemId = value;
	}

    /**
     * Setter itemDeleteId
     * @param {number[]} value
     */
	public set itemDeleteId(value: number[]) {
		this._itemDeleteId = value;
	}


    /**
     * Getter updatedDate
     * @return {string}
     */
	public get updatedDate(): string {
		return this._updatedDate;
	}

    /**
     * Setter updatedDate
     * @param {string} value
     */
	public set updatedDate(value: string) {
		this._updatedDate = value;
	}

}

export class Checklist {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('key')
    @deserializeAs('key')
    private _key: string;

    @serializeAs('type')
    @deserializeAs('type')
    private _type: string;

    @serializeAs('label')
    @deserializeAs('label')
    private _label: string;

    @serializeAs('index')
    @deserializeAs('index')
    private _index: number;

    @serializeAs('checkListChildList')
    @deserializeAs('checkListChildList')
    private _checkListChildList: ChecklistChild[];

    @serializeAs('isBodyVisible')
    @deserializeAs('isBodyVisible')
    private _isBodyVisible: boolean;

    @serializeAs('selectedField')
    @deserializeAs('selectedField')
    private _selectedField: string;

    @serializeAs('icon')
    @deserializeAs('icon')
    private _icon: string;

    constructor() {
        this.isBodyVisible = false;
        this.selectedField = null;
        this.checkListChildList = [];
    }


    /**
     * Getter icon
     * @return {string}
     */
	public get icon(): string {
		return this._icon;
	}

    /**
     * Setter icon
     * @param {string} value
     */
	public set icon(value: string) {
		this._icon = value;
	}
    

    /**
     * Getter selectedField
     * @return {string}
     */
	public get selectedField(): string {
		return this._selectedField;
	}

    /**
     * Setter selectedField
     * @param {string} value
     */
	public set selectedField(value: string) {
		this._selectedField = value;
	}


    /**
     * Getter isBodyVisible
     * @return {boolean}
     */
	public get isBodyVisible(): boolean {
		return this._isBodyVisible;
	}

    /**
     * Setter isBodyVisible
     * @param {boolean} value
     */
	public set isBodyVisible(value: boolean) {
		this._isBodyVisible = value;
	}


    /**
     * Getter id
     * @return {number}
     */
    public get id(): number {
        return this._id;
    }

    /**
     * Setter id
     * @param {number} value
     */
    public set id(value: number) {
        this._id = value;
    }

    /**
     * Getter key
     * @return {string}
     */
    public get key(): string {
        return this._key;
    }

    /**
     * Setter key
     * @param {string} value
     */
    public set key(value: string) {
        this._key = value;
    }

    /**
     * Getter type
     * @return {string}
     */
    public get type(): string {
        return this._type;
    }

    /**
     * Setter type
     * @param {string} value
     */
    public set type(value: string) {
        this._type = value;
    }

    /**
     * Getter label
     * @return {string}
     */
    public get label(): string {
        return this._label;
    }

    /**
     * Setter label
     * @param {string} value
     */
    public set label(value: string) {
        this._label = value;
    }

    /**
     * Getter index
     * @return {number}
     */
    public get index(): number {
        return this._index;
    }

    /**
     * Setter index
     * @param {number} value
     */
    public set index(value: number) {
        this._index = value;
    }

    /**
     * Getter checkListChildList
     * @return {ChecklistChild[]}
     */
    public get checkListChildList(): ChecklistChild[] {
        return this._checkListChildList;
    }

    /**
     * Setter checkListChildList
     * @param {ChecklistChild[]} value
     */
    public set checkListChildList(value: ChecklistChild[]) {
        this._checkListChildList = value;
    }

}

export class ChecklistChild {

    @serializeAs('value')
    @deserializeAs('value')
    private _value: string;

    @serializeAs('index')
    @deserializeAs('index')
    private _index: number;

    constructor() { }


    /**
     * Getter value
     * @return {string}
     */
    public get value(): string {
        return this._value;
    }

    /**
     * Setter value
     * @param {string} value
     */
    public set value(value: string) {
        this._value = value;
    }


    /**
     * Getter index
     * @return {number}
     */
    public get index(): number {
        return this._index;
    }

    /**
     * Setter index
     * @param {number} value
     */
    public set index(value: number) {
        this._index = value;
    }


}