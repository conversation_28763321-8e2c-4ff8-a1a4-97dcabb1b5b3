<div class="modal-body">
  <div class="row" [formGroup]="itemGroupForm">
    <div class="col-12">
      <div class="form-group required">
        <label class="form-label">Group Name</label>
        <input [maxlength]="utilsService.validationService.MAX_50" type="text" class="form-control"
          placeholder="Enter Group Name" formControlName="name" [(ngModel)]="itemGroupObj.groupName">
        <div class="message error-message"
          *ngIf="itemGroupForm.controls['name'].hasError('required') &&  itemGroupForm.controls['name'].touched">
          {{utilsService.validationService.GRP_NAME_REQ}}
        </div>
        <div class="message error-message"
          *ngIf="!itemGroupForm.controls['name'].hasError('required') && !itemGroupForm.controls['name'].valid && itemGroupForm.controls['name'].touched">
          {{utilsService.validationService.GRP_NAME_INVALID}}
        </div>
      </div>
    </div>
    <div class="col-12">
      <div class="form-group theme-ngselect  required theme-ngselect-group-list">
        <div class="form-label">Category</div>
        <ng-select placeholder="Select Category" [multiple]="false" [clearable]="false"
          [items]="flattenedParentCategory" bindLabel="categoryName" bindValue="id"
          [(ngModel)]="itemGroupObj.categoryId" formControlName="category">
          <ng-template ng-option-tmp let-item="item">
            <span [style.padding-left.px]="item.index * 25" [ngClass]="{'ng-option-child-label' : item.isChild}"
              [class]="item.className">
              {{ item.categoryName }}
            </span>
          </ng-template>
        </ng-select>
        <div class="message error-message"
          *ngIf="itemGroupForm.controls['category'].hasError('required') &&  itemGroupForm.controls['category'].touched">
          {{utilsService.validationService.CATEGORY_REQ}}
        </div>
      </div>
    </div>
    <div class="col-12">
      <div class="form-group theme-ngselect">
        <div class="form-label">HSN Code</div>
        <ng-select (clear)="clearAll()" placeholder="HSN Code" [multiple]="true" [clearable]="true"
          [items]="hsnCodeDropdown" bindLabel="label" bindValue="value" [(ngModel)]="itemGroupObj.hsnCodeId"
          formControlName="hsn" [closeOnSelect]="false" [virtualScroll]="true">
        </ng-select>
        <div class="message error-message"
          *ngIf="itemGroupForm.controls['hsn'].hasError('required') &&  itemGroupForm.controls['hsn'].touched">
          {{utilsService.validationService.HSN_NAME_REQ}}
        </div>
      </div>
    </div>
  </div>
</div>
<div class="modal-footer">
  <div class="modal-footer-group full-width-btn">
    <button (click)="onSave()" type="button" class="btn btn-primary btn-icon-text"> <i
        class="th th-outline-tick-circle"></i>Save</button>
    <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
  </div>
</div>