import { deserializeAs, serializeAs } from 'cerialize';

export class Traveller {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('travellerName')
    @deserializeAs('travellerName')
    private _travellerName: string;

    @serializeAs('conductorName')
    @deserializeAs('conductorName')
    private _conductorName: string;

    @serializeAs('contactNo')
    @deserializeAs('contactNo')
    private _contactNo: string;

    @serializeAs('isActive')
    @deserializeAs('isActive')
    private _isActive: boolean;

    @serializeAs('isSelected')
    @deserializeAs('isSelected')
    private _isSelected: boolean;

    @serializeAs('isStamp')
    @deserializeAs('isStamp')
    private _isStamp: boolean;

    @serializeAs('isEwayBill')
    @deserializeAs('isEwayBill')
    private _isEwayBill: boolean;

    @serializeAs('shortCode')
    @deserializeAs('shortCode')
    private _shortCode: string;

    @serializeAs('countryIds')
    @deserializeAs('countryIds')
    private _countryIds: number[];

    @serializeAs('stateIds')
    @deserializeAs('stateIds')
    private _stateIds: number[];

    @serializeAs('cityIds')
    @deserializeAs('cityIds')
    private _cityIds: number[];

    @serializeAs('cityNamesString')
    @deserializeAs('cityNamesString')
    private _cityNamesString: string;

    @serializeAs('createdDate')
    @deserializeAs('createdDate')
    private _createdDate: string;

    @serializeAs('cityNames')
    @deserializeAs('cityNames')
    private _cityNames: string[];

    @serializeAs('lastModifiedDate')
    @deserializeAs('lastModifiedDate')
    private _lastModifiedDate: string;

    @serializeAs('contactCountryExtensionId')
    @deserializeAs('contactCountryExtensionId')
    private _contactCountryExtensionId: number;

    @deserializeAs('contactExtension')
    private _contactExtension: any;

    @deserializeAs('isDefault')
    private _isDefault: boolean;


    constructor() {
        this.isActive = false;
        this.isSelected = false;
        this.isStamp = false;
        this.isEwayBill = false;
        this.isDefault = false;
    }

    /**
     * Getter isDefault
     * @return {boolean}
     */
	public get isDefault(): boolean {
		return this._isDefault;
	}

    /**
     * Setter isDefault
     * @param {boolean} value
     */
	public set isDefault(value: boolean) {
		this._isDefault = value;
	}

    /**
     * Getter contactExtension
     * @return {any}
     */
	public get contactExtension(): any {
		return this._contactExtension;
	}

    /**
     * Setter contactExtension
     * @param {any} value
     */
	public set contactExtension(value: any) {
		this._contactExtension = value;
	}


    /**
     * Getter contactCountryExtensionId
     * @return {number}
     */
	public get contactCountryExtensionId(): number {
		return this._contactCountryExtensionId;
	}

    /**
     * Setter contactCountryExtensionId
     * @param {number} value
     */
	public set contactCountryExtensionId(value: number) {
		this._contactCountryExtensionId = value;
	}



    /**
     * Getter isStamp
     * @return {boolean}
     */
	public get isStamp(): boolean {
		return this._isStamp;
	}

    /**
     * Getter isEwayBill
     * @return {boolean}
     */
	public get isEwayBill(): boolean {
		return this._isEwayBill;
	}

    /**
     * Setter isStamp
     * @param {boolean} value
     */
	public set isStamp(value: boolean) {
		this._isStamp = value;
	}

    /**
     * Setter isEwayBill
     * @param {boolean} value
     */
	public set isEwayBill(value: boolean) {
		this._isEwayBill = value;
	}


    /**
     * Getter lastModifiedDate
     * @return {string}
     */
	public get lastModifiedDate(): string {
		return this._lastModifiedDate;
	}

    /**
     * Setter lastModifiedDate
     * @param {string} value
     */
	public set lastModifiedDate(value: string) {
		this._lastModifiedDate = value;
	}


    /**
     * Getter createdDate
     * @return {string}
     */
	public get createdDate(): string {
		return this._createdDate;
	}

    /**
     * Setter createdDate
     * @param {string} value
     */
	public set createdDate(value: string) {
		this._createdDate = value;
	}


    /**
     * Getter cityNamesString
     * @return {string}
     */
	public get cityNamesString(): string {
		return this._cityNamesString;
	}

    /**
     * Getter cityNames
     * @return {string[]}
     */
	public get cityNames(): string[] {
		return this._cityNames;
	}

    /**
     * Setter cityNamesString
     * @param {string} value
     */
	public set cityNamesString(value: string) {
		this._cityNamesString = value;
	}

    /**
     * Setter cityNames
     * @param {string[]} value
     */
	public set cityNames(value: string[]) {
		this._cityNames = value;
	}


    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Getter travellerName
     * @return {string}
     */
	public get travellerName(): string {
		return this._travellerName;
	}

    /**
     * Getter conductorName
     * @return {string}
     */
	public get conductorName(): string {
		return this._conductorName;
	}

    /**
     * Getter contactNo
     * @return {string}
     */
	public get contactNo(): string {
		return this._contactNo;
	}

    /**
     * Getter isActive
     * @return {boolean}
     */
	public get isActive(): boolean {
		return this._isActive;
	}

    /**
     * Getter isSelected
     * @return {boolean}
     */
	public get isSelected(): boolean {
		return this._isSelected;
	}

    /**
     * Getter shortCode
     * @return {string}
     */
	public get shortCode(): string {
		return this._shortCode;
	}

    /**
     * Getter countryIds
     * @return {number[]}
     */
	public get countryIds(): number[] {
		return this._countryIds;
	}

    /**
     * Getter stateIds
     * @return {number[]}
     */
	public get stateIds(): number[] {
		return this._stateIds;
	}

    /**
     * Getter cityIds
     * @return {number[]}
     */
	public get cityIds(): number[] {
		return this._cityIds;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Setter travellerName
     * @param {string} value
     */
	public set travellerName(value: string) {
		this._travellerName = value;
	}

    /**
     * Setter conductorName
     * @param {string} value
     */
	public set conductorName(value: string) {
		this._conductorName = value;
	}

    /**
     * Setter contactNo
     * @param {string} value
     */
	public set contactNo(value: string) {
		this._contactNo = value;
	}

    /**
     * Setter isActive
     * @param {boolean} value
     */
	public set isActive(value: boolean) {
		this._isActive = value;
	}

    /**
     * Setter isSelected
     * @param {boolean} value
     */
	public set isSelected(value: boolean) {
		this._isSelected = value;
	}

    /**
     * Setter shortCode
     * @param {string} value
     */
	public set shortCode(value: string) {
		this._shortCode = value;
	}

    /**
     * Setter countryIds
     * @param {number[]} value
     */
	public set countryIds(value: number[]) {
		this._countryIds = value;
	}

    /**
     * Setter stateIds
     * @param {number[]} value
     */
	public set stateIds(value: number[]) {
		this._stateIds = value;
	}

    /**
     * Setter cityIds
     * @param {number[]} value
     */
	public set cityIds(value: number[]) {
		this._cityIds = value;
	}


}