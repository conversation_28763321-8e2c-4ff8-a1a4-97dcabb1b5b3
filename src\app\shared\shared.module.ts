import { A11yModule } from "@angular/cdk/a11y";
import { DragDropModule } from "@angular/cdk/drag-drop";
import { ScrollingModule } from "@angular/cdk/scrolling";
import { CommonModule, NgOptimizedImage } from "@angular/common";
import { NgModule, ModuleWithProviders } from "@angular/core";
import { ReactiveFormsModule, FormsModule } from "@angular/forms";
import { RouterModule } from "@angular/router";
import { AccessDeniedComponent } from "@common/access-denied/access-denied.component";
import { AttachmentDownloadDropdownComponent } from "@common/attachment-download-dropdown/attachment-download-dropdown.component";
import { DateRangeSelectionComponent } from "@common/date-range-selection/date-range-selection.component";
import { ImageViewModalComponent } from "@common/image-view-modal/image-view-modal.component";
import { MarkaImagePreviewComponent } from "@common/marka-image-preview/marka-image-preview.component";
import { CategoryModalComponent } from "@common/masters-modal/category-modal/category-modal.component";
import { HsnMasterComponent } from "@common/masters-modal/hsn-master/hsn-master.component";
import { ItemGrpMasterComponent } from "@common/masters-modal/item-grp-master/item-grp-master.component";
import { UnitMasterComponent } from "@common/masters-modal/unit-master/unit-master.component";
import { NavbarComponent } from "@common/navbar/navbar.component";
import { NoRecordComponent } from "@common/no-record/no-record.component";
import { NotFoundComponent } from "@common/not-found/not-found.component";
import { PaginationComponent } from "@common/pagination/pagination.component";
import { SessionExpiredComponent } from "@common/session-expired/session-expired.component";
import { SidebarComponent } from "@common/sidebar/sidebar.component";
import { TableColumnFilterDropdownComponentNew } from "@common/table-column-filter-dropdown-new/table-column-filter-dropdown.component";
import { TableColumnFilterDropdownComponent } from "@common/table-column-filter-dropdown/table-column-filter-dropdown.component";
import { UnderMaintenanceComponent } from "@common/under-maintenance/under-maintenance.component";
import { OwlDateTimeModule, OwlNativeDateTimeModule } from "@danielmoncada/angular-datetime-picker";
import { CopyTextDirective } from "@directives/copy-text.directive";
import { NumberOnlyDirective } from "@directives/number.directive";
import { NumberFloatOnlyDirective } from "@directives/numberFloat.directive";
import { RoleDirective } from "@directives/role.directive";
import { NgbTooltipModule, NgbDropdownModule, NgbDatepickerModule, NgbTimepicker } from "@ng-bootstrap/ng-bootstrap";
import { NgSelectModule } from "@ng-select/ng-select";
import { AsterickPipe } from "@pipes/asterick.pipe";
import { BranchFilterPipe } from "@pipes/branch-filter.pipe";
import { FilterShippingTypePipe } from "@pipes/filterShippingType.pipe";
import { IndianCurrencyPipe } from "@pipes/indianCurrency.pipe";
import { PadNumPipe } from "@pipes/padNum.pipe";
import { SafePipe } from "@pipes/safe.pipe";
import { UnderscorePipe } from "@pipes/underscore.pipe";
import { NgOtpInputModule } from "ng-otp-input";
import { AutosizeModule } from "ngx-autosize";
import { CountdownModule } from "ngx-countdown";
import { NgxDaterangepickerMd } from "ngx-daterangepicker-material";
import { NgxMaskDirective, NgxMaskPipe, provideEnvironmentNgxMask } from "ngx-mask";
import { NgxPrintModule } from "ngx-print";
import { SlickCarouselModule } from "ngx-slick-carousel";
import { SimplebarAngularModule } from "simplebar-angular";
import { ControlDemoComponent } from "../control-demo/control-demo.component";
import { DateTimePickerComponent } from "@common/date-time-picker/date-time-picker.component";

@NgModule({
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    RouterModule,
    NgSelectModule,
    SimplebarAngularModule,
    NgOtpInputModule,
    NgbTooltipModule,
    NgbDropdownModule,
    NgbDatepickerModule,
    NgxDaterangepickerMd.forRoot({
      format: 'DD/MM/YYYY',
      firstDay: 1,
    }),
    NgxPrintModule,
    SlickCarouselModule,
    DragDropModule,
    A11yModule,
    NgbTimepicker,
    NgxMaskDirective,
    NgxMaskPipe,
    OwlDateTimeModule,
    OwlNativeDateTimeModule,
    NgOptimizedImage,
    ScrollingModule,
    AutosizeModule,
  ],
  exports: [
    CommonModule,
    ScrollingModule,
    AutosizeModule,
    NgOptimizedImage,
    ReactiveFormsModule,
    FormsModule,
    NgbDropdownModule,
    NgSelectModule,
    NavbarComponent,
    NotFoundComponent,
    SessionExpiredComponent,
    MarkaImagePreviewComponent,
    UnderMaintenanceComponent,
    NoRecordComponent,
    SidebarComponent,
    PaginationComponent,
    NgOtpInputModule,
    NgxMaskDirective,
    NgxMaskPipe,
    UnderscorePipe,
    NgbTooltipModule,
    OwlDateTimeModule,
    OwlNativeDateTimeModule,
    SlickCarouselModule,
    NgxDaterangepickerMd,
    ItemGrpMasterComponent,
    DateRangeSelectionComponent,
    AsterickPipe,
    IndianCurrencyPipe,
    NgxPrintModule,
    CountdownModule,
    HsnMasterComponent,
    UnitMasterComponent,
    CategoryModalComponent,
    NgbDatepickerModule,
    NumberOnlyDirective,
    NumberFloatOnlyDirective,
    DragDropModule,
    A11yModule,
    NgbTimepicker,
    TableColumnFilterDropdownComponent,
    TableColumnFilterDropdownComponentNew,
    AttachmentDownloadDropdownComponent,
    ImageViewModalComponent,
    RoleDirective,
    BranchFilterPipe,
    CopyTextDirective,
    FilterShippingTypePipe,
    PadNumPipe,
    SafePipe,
    ControlDemoComponent,
    DateTimePickerComponent
  ],
  declarations: [AccessDeniedComponent, NavbarComponent, NotFoundComponent, SessionExpiredComponent, MarkaImagePreviewComponent, 
    SidebarComponent, 
    PaginationComponent, 
    AsterickPipe, 
    NumberOnlyDirective, 
    NumberFloatOnlyDirective, 
    NumberFloatOnlyDirective, 
    DateRangeSelectionComponent, 
    TableColumnFilterDropdownComponent,
    UnderMaintenanceComponent,
    NoRecordComponent,
    TableColumnFilterDropdownComponentNew,
    ItemGrpMasterComponent,
    AttachmentDownloadDropdownComponent,
    ImageViewModalComponent,
    HsnMasterComponent,
    UnitMasterComponent,
    CategoryModalComponent,
    RoleDirective,
    BranchFilterPipe,
    CopyTextDirective,
    UnderscorePipe,
    FilterShippingTypePipe,
    IndianCurrencyPipe,
    PadNumPipe,
    SafePipe,
    ControlDemoComponent,
    DateTimePickerComponent
  ]
})

export class SharedModule {

  /*** This static forRoot block (provides and configures services) is
  * used in case of when we want use some services in one or more components.
  */
  static forRoot(): ModuleWithProviders<any> {
    return {
      ngModule: SharedModule,
      providers: [provideEnvironmentNgxMask({ decimalMarker: '.' })]
    };
  }
}

