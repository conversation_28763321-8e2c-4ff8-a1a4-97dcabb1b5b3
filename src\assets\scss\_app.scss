/* -------------------------------------------------------------------------- */
/*                               Page Title End                               */
/* -------------------------------------------------------------------------- */
.content-area {
    .page-filters {
        padding: 10px;
        gap: 8px;
        margin: 0;
        // overflow-x: auto;
        // overflow-y: hidden;
    }
}

/* -------------------------------------------------------------------------- */
/*                             Bottombar CSS Start                            */
/* -------------------------------------------------------------------------- */
.bottombar-wrapper {
    &.bottom-fixed {
        z-index: 9;
        position: fixed;
        left: 250px;
        right: 0;
        bottom: 0;
    }

    .bottombar-container {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        background-color: $white_color;
        border-top: 1px solid #E6EAEE;
        padding: 7px 10px;

        .bottombar-left {
            display: flex;
            flex-direction: row;
            gap: 8px;
        }

        .bottombar-right {
            display: flex;
            flex-direction: row;
            gap: 8px;
        }
    }
}

[data-layout=vertical][data-sidebar-size=sm] .bottombar-wrapper {
    &.bottom-fixed {
        left: 70px;
    }
}

/* -------------------------------------------------------------------------- */
/*                              Bottombar CSS End                             */
/* -------------------------------------------------------------------------- */

/* -------------------------------------------------------------------------- */
/*                             Settings CSS Start                             */
/* -------------------------------------------------------------------------- */
.settings-row-wrapper {
    .settings-row {
        display: flex;
        flex-direction: row;
        align-items: center;
        margin-bottom: 8px;

        .setting-col-left {
            flex: 0 0 300px;
            max-width: 300px;
            padding-right: 10px;

            p {
                color: $text_color;
                font-size: 13px;
            }
        }

        .setting-col-right {
            flex: 0 0 calc(100% - 300px);
            max-width: calc(100% - 300px);
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: 10px;

            p {
                color: $text_color;
                font-size: 13px;
            }

            .form-group {
                margin-bottom: 0;
                max-width: 200px;
                width: 100%;

                .input-group {
                    .input-group-text {
                        min-width: 52px;
                        justify-content: center;
                    }
                }
            }
        }

        .settings-info {
            font-size: 13px;
            color: $black_color;
        }

        &:nth-last-child(1) {
            margin-bottom: 0;
        }
    }
}

/* -------------------------------------------------------------------------- */
/*                              Settings CSS End                              */
/* -------------------------------------------------------------------------- */

/* -------------------------------------------------------------------------- */
/*                      Account Reconciliation CSS Start                      */
/* -------------------------------------------------------------------------- */
.account-reconciliation {
    display: flex;
    flex-direction: row;
    height: 100%;

    .account-reconciliation-left {
        flex: 0 0 calc(100% - 550px);
        max-width: calc(100% - 550px);

        .content-area-sub {
            padding-bottom: 80px;
        }
    }

    .account-reconciliation-right {
        flex: 0 0 550px;
        max-width: 550px;
        border-left: 1px solid #E1E6EB;
    }

    .content-area-sub {
        padding: 10px;
        position: relative;
        height: calc(100vh - 190px);
        overflow-y: auto;
        overflow-x: hidden;

        .attachments-container {
            min-height: 230px;
        }
    }

    .bank-account-amout {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        position: relative;
        overflow: hidden;
        background-color: #F9FAFC;
        border: 1px solid #E6EAEE;
        border-radius: 6px;
        padding: 10px 10px 10px 40px;
        width: 100%;

        &.without-bank-label {
            padding: 10px;
        }

        .bank-account-label {
            position: absolute;
            left: 0px;
            top: 0;
            bottom: 0;
            background-color: $primary_color;
            color: $white_color;
            padding: 5px;
            font-size: 12px;
            font-weight: 600;
            width: 30px;
            display: flex;
            align-items: center;
            justify-content: center;

            span {
                transform: rotate(-90deg);
            }
        }

        .bank-account-amout-list {
            ul {
                list-style-type: none;
                padding: 0;
                margin: 0;
                display: flex;
                flex-direction: row;
                gap: 20px;

                li {
                    p {
                        color: $text_color;
                        font-size: 12px;
                        font-weight: 600;
                        margin-bottom: 2px;
                    }

                    h6 {
                        color: $text_black_color;
                        font-size: 16px;
                        font-weight: 600;
                        margin-bottom: 0;
                    }

                    span {
                        color: $text_black_color;
                        font-size: 16px;
                        font-weight: 400;
                    }
                }
            }
        }

        &.bottom-fixed {
            position: fixed;
            bottom: 42px;
            width: calc(100% - 830px);
            z-index: 1;
        }
    }

    .account-reconciliation-right {
        .card-table-sticky {
            .table-responsive {
                max-height: calc(100vh - 248px);
            }
        }

        .table-theme {
            .tbl-action {
                position: relative;
                min-width: 200px;
                overflow: hidden;

                .tbl-action-group {
                    margin-bottom: 20px;
                }

                .badge {
                    position: absolute;
                    right: -2px;
                    font-weight: 400;
                    bottom: -2px;
                    margin-top: 15px;
                }
            }
        }
    }
}

[data-layout=vertical][data-sidebar-size=sm] {
    .account-reconciliation {
        .bank-account-amout {
            &.bottom-fixed {
                width: calc(100% - 650px);
            }
        }
    }
}

/* -------------------------------------------------------------------------- */
/*                       Account Reconciliation CSS End                       */
/* -------------------------------------------------------------------------- */



/* -------------------------------------------------------------------------- */
/*                              Branch Management                             */
/* -------------------------------------------------------------------------- */

.card {
    &.card-grid {
        background-color: $white_color;
        box-shadow: 0px 10px 20px 0px #0000001A;
        border-color: $stock_light;
        border-radius: 8px;

        &.without-shadow-card-grid {
            box-shadow: none;
            border: 0;
            border-radius: 0;
        }

        .card-header {
            background-color: transparent;
            border: 0px;
            padding: 17px;
            padding-bottom: 0px;

            .card-grid-icon {
                width: 50px;
                height: 50px;
                min-width: 50px;
                border-radius: 10px;
                color: $primary_color;
                background-color: $primary_light_color;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 24px;
                font-weight: 600;
                line-height: 31.2px;
            }

            .card-grid-title {
                h6 {
                    font-size: 18px;
                    font-weight: 600;
                    line-height: 23.4px;
                    color: $text_black_color;
                    margin-bottom: 2px;

                }

                p {
                    font-size: 12px;
                    font-weight: 400;
                    line-height: 20.4px;
                    color: $text_color;
                    margin-bottom: 0px;
                }

            }
        }

        .card-body {
            padding: 17px;

            h6 {
                font-size: 14px;
                font-weight: 600;
                line-height: 18px;
                color: $text_black_color;
                margin-bottom: 10px;

            }

            p {
                font-size: 12px;
                font-weight: 400;
                line-height: 20.4px;
                color: $text_color;
            }
        }

        .card-footer {
            border: 0px;
            background-color: transparent;
            padding: 0px;

            .card-grid-footer-wrapper {
                display: flex;
                align-items: center;
                gap: 0 10px;

                .badge {
                    border-radius: 10px 10px 0px 0px !important;
                    font-size: 12px;
                    font-weight: 600;
                    line-height: 15.6px;
                    gap: 0 7px;

                    &:first-child {
                        border-radius: 0px 10px 0px 0px !important;

                    }

                    i {
                        font-size: 17px;
                        line-height: 17px;
                    }

                }
            }


        }

        .card-grid-details {
            ul {
                list-style: none;
                padding: 0px;
                margin: 0px;

                li {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    font-size: 12px;
                    font-weight: 400;
                    line-height: 20.4px;
                    color: $text_color;
                    margin-bottom: 6px;

                    &:last-child {
                        margin-bottom: 0px;
                    }

                    span {
                        font-weight: 600;
                        color: $text_black_color;
                        text-align: right;
                    }

                }
            }
        }
    }
}

/* -------------------------------------------------------------------------- */
/*                              Branch Management                             */
/* -------------------------------------------------------------------------- */




/* ------------------------------- .warehouse ------------------------------- */
.details-page {
    display: flex;
    align-items: center;
    height: 100%;

    .details-page-left {
        width: 330px;
        background-color: $white_color;
        border-right: 1px solid $stock_light;
        height: 100%;

        .details-page-left-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 22px 17px;
            border-bottom: 1px solid $stock_light;

            .form-group {
                margin-bottom: 0px;

                &.theme-ngselect .ng-select-container {
                    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%230b78ff' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e");

                    .ng-value-container {
                        padding-left: 0;
                        color: $primary_color;
                    }
                }
            }


            .details-page-header-right,
            .details-page-header-left {
                display: flex;
                align-items: center;
                gap: 0 10px;
            }
        }

        .details-page-left-header-body {
            height: calc(100vh - 126px);
            overflow-y: auto;
        }
    }

    .details-page-right {
        width: calc(100% - 330px);
        background-color: $white_color;
        border-right: 1px solid $stock_light;
        height: 100%;

        .details-page-right-body {
            height: calc(100vh - 102px);
            overflow-y: auto;

            &.details-page-with-tab {
                .tab-content {
                    height: calc(100vh - 137px);
                    overflow-y: auto;
                }
            }
        }
    }
}


.warehouse-details-list {
    .warehouse-details-list-wrapper {
        list-style: none;
        padding: 0px;
        margin: 0px;

        .warehouse-details-list-item {
            display: flex;
            align-items: center;
            gap: 0 8px;
            border-bottom: 1px solid $stock_light;
            padding: 10px;
            cursor: pointer;

            .details-list-item-icon {
                width: 32px;
                height: 32px;
                min-width: 32px;
                display: flex;
                align-items: center;
                justify-content: center;
                color: $text_color;
                background-color: $bg_light;
                border: 1px solid $stock_light;
                border-radius: 7px;
            }

            .details-list-item-content {
                width: 100%;

                h6 {
                    font-size: 14px;
                    font-weight: 600;
                    line-height: 18.2px;
                    margin-bottom: 0px;
                    color: $text_black_color;
                    word-wrap: break-word;
                    word-break: break-word;
                    white-space: normal;
                    -webkit-line-clamp: 1;
                    -webkit-box-orient: vertical;
                    display: -webkit-box;
                    white-space: normal;
                    overflow: hidden;

                    i {
                        color: $primary_color;
                    }
                }

                p {
                    font-size: 12px;
                    font-weight: 400;
                    line-height: 20.4px;
                    color: $text_color;
                    margin-bottom: 0px;
                    word-wrap: break-word;
                    word-break: break-word;
                    white-space: normal;
                }
            }

            .details-list-item-status {
                p {

                    font-size: 14px;
                    font-weight: 400;
                    line-height: 23.8px;
                    margin-bottom: 0px;
                    color: $text_color;


                }
            }

            &.active {
                background-color: $primary_bg_color;
                position: relative;

                &::before {
                    position: absolute;
                    content: "";
                    left: 0;
                    top: 50%;
                    transform: translateY(-50%);
                    width: 3px;
                    height: 100%;
                    background-color: $primary_color;
                }

            }
        }
    }
}

/* ------------------------------- .warehouse ------------------------------- */

/* ---------------------------------- items start --------------------------------- */
.item-grid-view {
    padding: 0 10px;

    .item-grid-row {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        column-count: 4;
        margin-left: -10px;
        margin-right: -10px;

        .item-grid-col {
            padding-left: 10px;
            padding-right: 10px;
            max-width: 25%;
            width: 25%;
        }
    }

    .product-card {
        margin: 10px 0;
        padding: 10px;
        border-radius: 6px;
        border: 1px solid $stock_light;
        overflow: hidden;

        .card-image {
            border-radius: 4px;
            overflow: hidden;
            height: 171px;
            background-color: #F9F9F9;
            position: relative;

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                z-index: 0;
            }

            .product-checkbox {
                position: absolute;
                left: 7px;
                top: 7px;
                bottom: auto;
                right: auto;
                z-index: 1;
            }
        }

        .card-image-marka {
            border-radius: 4px;
            overflow: hidden;
            height: 300px;
            background-color: #F9F9F9;
            position: relative;

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                z-index: 0;
            }
        }

        .card-body {
            padding: 11px 0 0 0;

            .title {
                margin-bottom: 11px;

                h6 {
                    font-size: 14px;
                    font-weight: 600;
                    line-height: 18.2px;
                    color: $text_black_color;
                    margin-bottom: 4px;
                }

                p {
                    font-size: 10px;
                    font-weight: 400;
                    line-height: 17px;
                    color: $text_color;
                    margin-bottom: 0;

                }
            }

            .details {
                ul {
                    margin: 0;

                    li {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        gap: 8px;
                        margin-bottom: 8px;

                        &:last-child {
                            margin-bottom: 0;
                        }

                        label,
                        span {
                            font-size: 12px;
                            font-weight: 400;
                            line-height: 20.4px;
                            margin: 0;
                        }

                        label {
                            color: $text_color;
                        }

                        span {
                            display: inline-block;
                            font-weight: 600;
                            color: $text_black_color;
                        }

                    }
                }
            }
        }
    }
}

/* ---------------------------------- items end --------------------------------- */

/* ---------------------- product-modal-slider-wrapper start ---------------------- */
.product-modal-slider-wrapper {
    .product-modal-slider {
        .product-modal-item {
            // border: 1px solid red;

            .product-image {
                width: 100%;
                height: 550px;

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }

                video {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }

        }
    }

    .product-modal-counter {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        color: $text_color;
        font-weight: 600;
        margin-top: 8px;

        span {
            display: inline-block;
            margin: 0 5px;
        }
    }
}

/* ---------------------- product-modal-slider-wrapper end ---------------------- */

/* ---------------------- product-slider-wrapper Start ---------------------- */
.product-slider-wrapper {
    .product-image-slider {
        padding: 10px;
        border-radius: 7px;
        overflow: hidden;
        background-color: $white_color;
        border: 1px solid $stock_light;
        margin-bottom: 18px;

        .product-slider-item {
            .stock-details-slider-image {
                width: 100%;
                height: 300px;
                overflow: hidden;
                border-radius: 7px;
                background-color: $bg_grey;

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }

                video {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }

            .stock-details-content {
                display: flex;
                align-items: baseline;
                gap: 10px;
                justify-content: space-between;
                padding-top: 10px;

                .product-primary {
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    gap: 0 7px;
                    font-size: 10px;
                    font-weight: 600;
                    padding: 4px 8px;
                    border-radius: 7px;
                    overflow: hidden;
                    background-color: $success_light_color;
                    color: $text_black_color;
                }
            }
        }
    }

    .product-thumb-slider-wrapper {
        // display: flex;
        align-items: center;
        flex-direction: row;
        justify-content: space-between;
        margin: 0 -10px;

        .product-thumb-slider {
            // max-width: 75%;
            // flex: 0 0 75%;
            overflow: hidden;
            padding: 0 20px;

            .slick-track {
                padding: 0;
                margin: 0 -10px;
            }

            .slick-slide {
                margin: 0 10px;
            }

            .slick-prev {
                left: 0;
            }

            .slick-next {
                right: 0;
            }

            .product-thumb-item {
                &.slick-current {
                    .stock-details-slider-image {
                        border-color: $primary_color;
                    }
                }

                .stock-details-slider-image {
                    width: auto;
                    height: 75px;
                    overflow: hidden;
                    border-radius: 7px;
                    background-color: $bg_grey;
                    border: 1px solid $stock_light;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;

                    img {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                    }
                }
            }
        }


        .product-thumb-add-wrapper {
            max-width: 85px;
            flex: 0 0 85px;
            padding: 0 10px;

            .product-thumb-add-item {
                width: 100%;

                .stock-details-box {
                    width: 100%;
                    height: 75px;
                    overflow: hidden;
                    border-radius: 7px;
                    background-color: $white_color;
                    border: 1px solid $stock_light;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;

                    i {
                        font-size: 24px;
                    }
                }
            }
        }

    }
}

/* ---------------------- product-slider-wrapper End ---------------------- */

/* ------------------------ items-details-list start ------------------------ */
.items-details-list {
    .page-filter {
        padding: 10px;
    }

    .items-details-list-wrapper {
        list-style: none;
        padding: 0px;
        margin: 0px;

        .items-details-list-item {
            display: flex;
            align-items: center;
            gap: 0 8px;
            border-bottom: 1px solid $stock_light;
            padding: 10px;
            cursor: pointer;

            .details-list-item-icon {
                width: 32px;
                height: 32px;
                min-width: 32px;
                display: flex;
                align-items: center;
                justify-content: center;
                color: $text_color;
                background-color: $bg_light;
                border: 1px solid $stock_light;
                border-radius: 7px;
            }

            .details-list-item-content {
                width: 100%;

                h6 {
                    font-size: 14px;
                    font-weight: 600;
                    line-height: 18.2px;
                    margin-bottom: 0px;
                    color: $text_black_color;

                    i {
                        color: $primary_color;
                    }
                }

                p {
                    font-size: 12px;
                    font-weight: 400;
                    line-height: 20.4px;
                    color: $text_color;
                    margin-bottom: 0px;
                }
            }

            .details-list-item-status {
                width: 100%;
                text-align: end;

                p {
                    font-size: 14px;
                    font-weight: 400;
                    line-height: 23.8px;
                    margin-bottom: 0px;
                    color: $text_color;
                }
            }

            &.active {
                background-color: $primary_light_color;
                position: relative;

                &::before {
                    position: absolute;
                    content: "";
                    left: 0;
                    top: 50%;
                    transform: translateY(-50%);
                    width: 3px;
                    height: 100%;
                    background-color: $primary_color;
                }

            }
        }
    }
}

.item-details-overview {
    .card {
        &.card-grid {
            .card-grid-details {
                ul {
                    margin-bottom: 26px;

                    &:last-child {
                        margin-bottom: 0;
                    }
                }
            }
        }
    }

    .stock-details-wrapper {
        display: flex;
        flex-direction: column;
        gap: 16px;

        .stock-details-item {
            .stock-details-card {
                padding: 14px;
                border-radius: 9px;
                background-color: $primary_light_color;
                border: 0;
                box-shadow: none;
                overflow: hidden;
                display: flex;
                gap: 8px;

                .stock-title {

                    h5 {
                        font-size: 21px;
                        font-weight: 600;
                        line-height: 27px;
                        color: $black_color;
                        margin-bottom: 0;
                        display: inline-flex;
                        align-items: baseline;
                        gap: 5px;

                        span {
                            display: inline-block;
                            font-size: 14px;
                            font-weight: 400;
                            line-height: 23.8px;
                            color: $text_color;
                        }
                    }
                }

                .stock-content {
                    p {
                        font-size: 14px;
                        font-weight: 400;
                        line-height: 23.8px;
                        color: $black_color;
                        margin-bottom: 0;
                    }
                }
            }

        }
    }

}

/* ------------------------ items-details-list end----------------------- */


/* ---------------------- import-items-container start ---------------------- */
.import-items-container {
    .import-items-wrapper {
        padding: 7px;
        border-bottom: 1px solid $stock_light;

        .import-items-nav {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 12px;

            .import-items-list {
                width: 75%;
                margin: 0 auto;
                display: flex;
                flex-direction: column;
                align-items: center;

                .import-items-title {
                    h3 {
                        font-size: 16px;
                        font-weight: 600;
                        line-height: 20px;
                        color: $text_black_color;
                        margin-bottom: 10px;
                        text-align: center;
                    }
                }

                ul {
                    margin: 0;
                    display: flex;
                    align-items: center;
                    flex-direction: row;

                    li {
                        display: flex;
                        align-items: center;
                        cursor: pointer;
                        position: relative;

                        &:last-child {
                            .step-boxs {
                                padding-right: 0;
                                margin-right: 0;
                            }

                            &::after {
                                content: none;
                            }
                        }

                        &::after {
                            content: "";
                            position: absolute;
                            top: 50%;
                            right: 16px;
                            left: auto;
                            border-top: 3px solid #E6EAEE;
                            border-radius: 20px;
                            z-index: 0;
                            width: 27px;
                        }

                        &.active {
                            .step-boxs {
                                .step-number {
                                    color: $primary_color;
                                    border: 1px solid #B1DDFC;
                                }

                                h6 {
                                    color: $primary_color;
                                }
                            }
                        }

                        &.done {
                            .step-boxs {
                                .step-number {
                                    display: none;
                                }

                                .step-done {
                                    display: flex;
                                }

                                h6 {
                                    color: $success_color;
                                }
                            }
                        }

                        .step-boxs {
                            display: flex;
                            align-items: center;
                            flex-wrap: nowrap;
                            position: relative;
                            z-index: 9;
                            padding-right: 46px;
                            margin-right: 16px;
                            gap: 10px;

                            .step-number {
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                width: 40px;
                                height: 40px;
                                border-radius: 8px;
                                font-size: 16px;
                                font-weight: 600;
                                line-height: 20.8px;
                                text-align: center;
                                color: $text_color;
                                border: 1px solid $primary_light_color;
                                background-color: $primary_light_color;
                                overflow: hidden;
                            }

                            .step-done {
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                width: 40px;
                                height: 40px;
                                border-radius: 8px;
                                border: 1px solid $success_bg_color;
                                background-color: $success_bg_color;
                                overflow: hidden;
                                display: none;

                                img {
                                    width: 21px;
                                    height: 21px;
                                    object-fit: contain;
                                }

                                i {
                                    font-size: 20px;
                                }
                            }

                            h6 {
                                font-size: 14px;
                                font-weight: 600;
                                line-height: 18px;
                                color: $text_color;
                                margin-bottom: 0;
                            }
                        }
                    }
                }
            }
        }
    }

    .import-items-details-wrapper {
        &.import-items-responsive {
            overflow-y: auto;
            position: relative;
            height: calc(100vh - 181px);
        }

        .import-items-card {
            width: 600px;
            padding: 40px 10px;
            margin: 0 auto;
            border: 0;
            box-shadow: none;
            background-color: transparent;


            .card-body {
                padding: 0;
            }

            .card-footer {
                padding: 30px 0 5px 0;
                border-top: 1px solid $stock_light;
                background-color: transparent;

                .import-items-footer-group {
                    display: flex;
                    align-items: stretch;
                    justify-content: flex-end;
                    gap: 7px;

                    .btn {
                        min-width: 100px;
                    }
                }
            }
        }

        .import-items-details {
            .group-container {
                margin-bottom: 22px;
            }

            .import-items-file {
                .attachments-container {
                    height: 220px;
                }

                .file-upload-details {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    gap: 12px;

                    p {
                        flex: 0 0 65%;
                        max-width: 65%;
                        font-size: 14px;
                        font-weight: 400;
                        line-height: 23px;
                        color: $text_color;
                        margin-bottom: 0;
                    }
                }

                .file-upload-type {
                    display: flex;
                    flex-direction: row;
                    gap: 10px;

                    .file-type-box {
                        width: 100%;
                        border: 2px solid $stock_light;
                        border-radius: 9px;
                        padding: 10px 12px;
                        display: inline-block;
                        background-color: $white_color;

                        &.active {
                            border-color: $primary_color;
                        }

                        &.active {
                            border-color: $primary_color;

                            label {
                                color: $primary_color;
                                padding-left: 21px;
                            }
                        }

                        label {
                            padding-left: 21px;
                        }

                        p {
                            font-size: 12px;
                            font-weight: 400;
                            line-height: 20px;
                            color: $text_color;
                            margin-bottom: 0;
                            margin-left: 21px;
                        }
                    }
                }
            }
        }
    }

}

/* ---------------------- import-items-container end ---------------------- */

/* ------------------------------ Registration ------------------------------ */
.gst-card {
    padding: 15px;
    background-color: $bg_grey;
}

.reg-overview-company-details {
    display: flex;
    align-items: flex-start;
    gap: 0 12px;

    .reg-overview-company-image {
        width: 52px;
        height: 52px;
        min-width: 52px;
        border: 1px solid $stock_light;
        border-radius: 4px;
        overflow: hidden;

        img {
            width: 100%;
            height: 100%;
            background-size: cover;
        }
    }

    .reg-overview-company-content {
        .reg-overview-company-title {
            h6 {
                font-size: 14px;
                font-weight: 600;
                line-height: 18.2px;
                color: $primary_color;
                margin-bottom: 0px;

            }

            p {
                font-size: 12px;
                font-weight: 400;
                line-height: 20.4px;
                color: $text_color;

                span {
                    padding: 0 5px;
                    border-right: 1px solid $stock_light;

                    &:last-child {
                        padding-right: 0px;
                        border-right: 0px;
                    }

                    &:first-child {
                        padding-left: 0px;
                    }
                }
            }
        }
    }


    .reg-overview-company-genral-details {
        margin-top: 10px;

        p {
            display: flex;
            align-items: center;
            gap: 0 10px;
            margin-bottom: 3px;

            label {
                min-width: 100px;
                font-size: 12px;
                font-weight: 400;
                line-height: 20.4px;
                margin-bottom: 0px;
                color: $text_color;
            }

            span {

                font-size: 12px;
                font-weight: 600;
                line-height: 15.6px;
                text-align: right;
                margin-bottom: 0px;
                color: $text_black_color;

            }
        }
    }
}


.reg-overview-contact-person {

    .reg-overview-contact-person-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 10px;

        .reg-overview-contact-person-title-right,
        .reg-overview-contact-person-title-left {
            display: flex;
            align-items: center;
            gap: 0 10px;
        }

        h6 {
            font-size: 14px;
            font-weight: 600;
            line-height: 18.2px;
            color: $text_black_color;
            margin-bottom: 0px;
        }
    }

    .reg-overview-contact-person-card {
        padding: 12px 14px;
        border-radius: 8px;
        border: 1px solid $stock_light;
        display: flex;
        align-items: flex-start;
        gap: 0 12px;
        margin-bottom: 10px;

        &:last-child {
            margin-bottom: 0px;
        }

    }

    .reg-overview-contact-image {
        width: 40px;
        height: 40px;
        min-width: 40px;
        border: 1px solid $stock_light;
        border-radius: 4px;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;

        img {
            width: 100%;
            height: 100%;
            background-size: cover;

        }

        i {
            font-size: 17px;
            line-height: 17px;
            color: $text_color;
        }
    }

    .reg-overview-contact-content {
        .reg-overview-contact-title {
            h6 {
                font-size: 14px;
                font-weight: 600;
                line-height: 18.2px;
                color: $text_black_color;
                margin-bottom: 0px;
                word-wrap: break-word;
                word-break: break-word;
                white-space: normal;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
                display: -webkit-box;
                white-space: normal;
                overflow: hidden;
            }

            p {
                font-size: 12px;
                font-weight: 400;
                line-height: 20.4px;
                color: $text_color;
                word-wrap: break-word;
                word-break: break-word;
                white-space: normal;

                span {
                    padding: 0 5px;
                    border-right: 1px solid $stock_light;
                    max-width: 50%;
                    width: 50%;
                    display: inline-block;
                    vertical-align: text-top;
                    word-wrap: break-word;
                    word-break: break-word;
                    white-space: normal;

                    &:last-child {
                        padding-right: 0px;
                        border-right: 0px;
                    }

                    &:first-child {
                        padding-left: 0px;
                    }
                }
            }

            &.reg-overview-contact-title-inline {
                display: flex;
                align-items: center;
                justify-content: space-between;
                gap: 0 10px;
            }
        }
    }

    .reg-overview-contact-genral-details {
        margin-top: 10px;

        p {
            display: flex;
            align-items: center;
            gap: 0 10px;
            margin-bottom: 3px;

            label {
                min-width: 100px;
                font-size: 12px;
                font-weight: 400;
                line-height: 20.4px;
                margin-bottom: 0px;
                color: $text_color;
            }

            span {

                font-size: 12px;
                font-weight: 600;
                line-height: 15.6px;
                text-align: start;
                margin-bottom: 0px;
                word-wrap: break-word;
                word-break: break-word;
                white-space: normal;
                color: $text_black_color;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
                display: -webkit-box;
                white-space: normal;
                overflow: hidden;
            }
        }
    }

}



.counter {
    margin-bottom: 20px;

    .counter-wrapper {
        display: flex;
        align-items: center;
        gap: 0 16px;
        width: 100%;

        .counter-card {
            background-color: $primary_light_color;
            padding: 15px;
            border-radius: 10px;
            width: 100%;

            .counter-card-text {
                h3 {
                    font-size: 21px;
                    font-weight: 600;
                    line-height: 27.3px;
                    color: $text_black_color;
                    margin-bottom: 10px;

                }

                p {
                    font-size: 14px;
                    font-weight: 400;
                    line-height: 23.8px;
                    margin-bottom: 0px;
                    color: $text_black_color;

                }
            }
        }
    }
}

/* ------------------------------ Registration ------------------------------ */

.card-forms-total {
    background-color: #F4F5F6;
    padding: 8px;
    border-radius: 5px;

    .table {
        tbody {

            td,
            th {
                background-color: transparent;
                border-bottom: 0;
            }
        }

        tfoot {
            tr {

                td,
                th {
                    background-color: transparent;
                    border-top: 1px solid #E1E6EB;
                    border-bottom: 0;
                }
            }
        }
    }
}




/* ------------------------ Inventory > Print QR Code ----------------------- */
.print-qr-container {
    max-width: 1040px;
    margin: 0 auto;
    padding-top: 40px;
}


.print-qr-card {
    background-color: #F0F3F5;
    border-color: #F0F3F5;
}

.location-card {
    background-color: #F9FAFC;
    border: 1px solid $stock_light;
    padding: 12px;
    border-radius: 6px;
    margin-bottom: 25px;

    p {
        font-size: 12px;
        font-weight: 600;
        line-height: 15.6px;
        margin-bottom: 5px;
        gap: 0 8px;
        color: $text_color;
        display: flex;
        align-items: center;

        i {
            font-size: 14px;
            line-height: 14px;

        }
    }

    h5 {
        font-size: 18px;
        font-weight: 600;
        line-height: 23.4px;
        margin-bottom: 0px;
        padding-left: 22px;
    }
}

/* ------------------------ Inventory > Print QR Code ----------------------- */
/* ------------------------ audit-tickets-details > update-history card start ----------------------- */
.update-history-wrapper {
    display: flex;
    flex-direction: column;
    gap: 10px;

    .update-history-item {
        padding: 14px;
        border-radius: 8px;
        border: 1px solid $stock_light;
        overflow: hidden;
        display: flex;
        justify-content: space-between;
        gap: 10px;

        .card-details-left {
            display: flex;
            align-items: center;
            gap: 0 8px;

            .card-user-image {
                width: 32px;
                height: 32px;
                flex: 0 0 32px;
                border-radius: 50%;
                overflow: hidden;

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }

            .card-details {
                p {
                    font-size: 14px;
                    font-weight: 500;
                    line-height: 20px;
                    margin: 0;
                    color: $text_black_color;
                }

                span {
                    font-size: 14px;
                    font-weight: 400;
                    line-height: 20px;
                    margin: 0;
                    color: $gray_color;
                }
            }
        }

        .card-details-right {
            .card-date {
                font-size: 14px;
                font-weight: 400;
                line-height: 20px;
                margin: 0;
                color: $gray_color;
            }
        }
    }
}

/* ------------------------ audit-tickets-details > update-history card end ----------------------- */


/* --------------------------------- Inquiry -------------------------------- */
.timer {
    h2 {
        font-size: 24.38px;
        font-weight: 600;
        line-height: 31.7px;
        color: $text_black_color;
        margin-bottom: 0;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 0 10px;
    }
}



.image-inquiry-scroll {
    max-height: calc(100vh - 254px);
    overflow: hidden;
    overflow-y: auto;
}

/* --------------------------------- Inquiry -------------------------------- */


.tbl-user {

    .tbl-user-wrapper {
        display: flex;
        align-items: center;
        gap: 0 8px;
        width: 100%;

        &.tbl-courier-wrapper {
            .tbl-user-image {
                img {
                    width: 17px;
                    height: 17px;
                    object-fit: contain;
                }
            }
        }

        .tbl-user-image {
            width: 40px;
            height: 40px;
            min-width: 40px;
            border-radius: 4px;
            overflow: hidden;
            background-color: #F9F9F9;
            border: 1px solid $stock_light;
            display: flex;
            align-items: center;
            justify-content: center;

            img {
                width: 100%;
                height: 100%;
                background-size: cover;
            }

            i {
                font-size: 18px;
            }
        }

        .tbl-user-text {
            width: 100%;
            display: flex;
            flex-direction: column;

            p {
                font-weight: 600;
                color: $text_black_color;
                font-size: 12px;
                margin-bottom: 0px;
            }

            span {
                color: $text_color;
                font-size: 12px;
            }

            .tbl-user-counter {
                background-color: #F0F3F5;
                height: 24px;
                width: 24px;
                display: inline-flex;
                flex-direction: row;
                align-items: center;
                justify-content: center;
                color: $text_black_color;
                font-size: 13px;
                font-weight: 600;
                border-radius: 4px;
                font-size: 13px;
            }
        }

        .user-action {
            display: flex;
            align-items: center;
            gap: 0 3px;

            .btn-user-action {
                background-color: transparent;
                border: 0px;
                color: $text_color;
                font-size: 14px;
                line-height: 14px;
                padding: 0px;
            }
        }


        .tbl-user-text-action {
            display: flex;
            align-items: stretch;
            justify-content: flex-start;
            gap: 0 5px;
            width: 100%;

            .user-action,
            .tbl-user-text {
                height: 100%;
            }
        }
    }

    .tbl-user-checkbox-srno {
        display: flex;
        align-items: center;
        gap: 0 7px;
        width: 100%;
    }

    .tbl-color-checkbox {
        display: flex;
        align-items: center;
        gap: 0 6px;
        width: 100%;

        .tbl-color-box {
            width: 25px;
            height: 25px;
            overflow: hidden;
            border-radius: 4px;
            background-color: $bg_grey;
        }
    }

    .tbl-user-srno {
        color: $text_color;
        font-weight: 400;
    }


    &.tbl-user-item {
        padding: 10px;
        border-radius: 10px;
        border-bottom: 1px solid $stock_light;

        &:hover {
            background-color: $primary_color;

            .tbl-user-wrapper {
                .tbl-user-text {

                    p,
                    span {
                        color: $white_color !important;
                    }

                }
            }
        }

        &:last-child {
            border-bottom: 0px solid $stock_light;
        }

    }

    .cdk-drag-animating {
        transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
    }

}


// purchases-import-scan start
.purchases-import-scan-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;

    .purchases-import-scan-inner {
        max-width: 300px;
        margin: 0 auto;

        .scan-image {
            width: 140px;
            height: 140px;
            margin-bottom: 30px;
            margin-left: auto;
            margin-right: auto;

            img {
                width: 100%;
                height: 100%;
                object-fit: contain;
            }
        }

        .scan-details {
            p {
                font-weight: 600;
                font-size: 18px;
                line-height: 23.4px;
                letter-spacing: 0%;
                text-align: center;
                color: $black_color;
            }
        }
    }
}

// purchases-import-scan end

.image-name {
    width: 400px;
    word-wrap: break-word;
    word-break: break-word;
    white-space: normal;

    div {
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        display: -webkit-box;
        white-space: normal;
        overflow: hidden;
    }
}

// drag opacity
.cdk-drag-placeholder {
    opacity: 0;
}

// cdk animation
.cdk-drag-animating {
    transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}
