import { Component, inject, OnDestroy, OnInit } from '@angular/core';
import { UtilsService } from '@service/utils.service';
import { Subscription } from 'rxjs';
import { RxStompService } from '../../socket_config/rx-stomp.service';
declare var window: any;

@Component({
  selector: 'app-navbar',
  templateUrl: './navbar.component.html',
  styleUrls: ['./navbar.component.css']
})
export class NavbarComponent implements OnInit, OnDestroy {

  utilsService = inject(UtilsService);
  rxStompService = inject(RxStompService);

  sidebarSize = false;
  isSidebarToggleIcon: boolean = true;

  isFullscreen: boolean = false;
  logoutModal: any;

  socketSub: Subscription;
  profileSub: Subscription;

  constructor() { }

  ngOnInit() {

    this.socketSub = new Subscription();
    this.profileSub = new Subscription();

    this.logoutModal = new window.bootstrap.Modal(
      document.getElementById('logoutModal')
    );


    const userData = JSON.parse(localStorage.getItem('userData'));
    const token = this.utilsService.decodeToken(this.utilsService.getToken())
    // console.log(token)

    if(!this.utilsService.isEmptyObjectOrNullUndefined(userData)) {
      this.utilsService.username = `${token?.user?.name}`;
      this.utilsService.userId = Number(`${token?.user?.id}`);
      this.utilsService.roleName = `${userData?.roleName}`;
      this.utilsService.userProfilePicture = userData?.profileImageUrl ? `${userData?.profileImageUrl}` : null;
      ///
      this.socketSub = this.rxStompService.watch(`/topic/updateStatus/` + `${this.utilsService.userId}`).subscribe((message) => {
        const msg = JSON.parse(message.body) 
        if (!this.utilsService.isEmptyObjectOrNullUndefined(msg)) {
          if (msg) {
            this.utilsService.logout(false);
            this.utilsService.toasterService.info('User configuration changed by system administrator, Please re-login to continue.', '', {
              positionClass: 'toast-top-right',
              closeButton: true,
              timeOut: 10000
            });
          }
        }
      });

      this.profileSub = this.rxStompService.watch(`/topic/profile/` + `${this.utilsService.userId}`).subscribe((message) => {
        const msg = JSON.parse(message.body)
        if (!this.utilsService.isEmptyObjectOrNullUndefined(msg)) {
          if (msg.profileUrl) {
            this.utilsService.userProfilePicture = msg.profileUrl as string;
          }

          if (msg.id == 0) {
            this.utilsService.userProfilePicture = null;
          }
          let data = JSON.parse(localStorage.getItem('userData'));
          data.profileImageUrl = this.utilsService.userProfilePicture
          this.utilsService.storeDataLocally('userData', JSON.stringify(data));
        }
      })
      ///
    }
  }

  ngOnDestroy(): void {
    this.socketSub.unsubscribe();
    this.profileSub.unsubscribe();
  }

  sidebarToggle() {
    this.isSidebarToggleIcon = !this.isSidebarToggleIcon;

    // Method 1
    this.sidebarSize = !this.sidebarSize;
    if (this.sidebarSize === false) {
      document.documentElement.setAttribute('data-sidebar-size', 'lg');
    } else if (this.sidebarSize === true) {
      document.documentElement.setAttribute('data-sidebar-size', 'sm');
    }
  }

  openLogoutModal() {
    this.logoutModal.show();
  }

  confirmLogout() {
    this.logoutModal.hide();
    this.utilsService.logout(true);
  }
  
  toggleFullscreen(): void {
    const doc: any = document;
    const elem: any = document.documentElement;

    const isInFullScreen =
      doc.fullscreenElement ||
      doc.webkitFullscreenElement ||
      doc.mozFullScreenElement ||
      doc.msFullscreenElement;

    if (!isInFullScreen) {
      // Enter fullscreen
      switch (true) {
        case !!elem.requestFullscreen:
          elem.requestFullscreen();
          break;
        case !!elem.webkitRequestFullscreen:
          elem.webkitRequestFullscreen();
          break;
        case !!elem.mozRequestFullScreen:
          elem.mozRequestFullScreen();
          break;
        case !!elem.msRequestFullscreen:
          elem.msRequestFullscreen();
          break;
      }
      this.isFullscreen = true;
    } else {
      // Exit fullscreen (call only if active)
      switch (true) {
        case !!doc.exitFullscreen:
          doc.exitFullscreen();
          break;
        case !!doc.webkitExitFullscreen:
          doc.webkitExitFullscreen();
          break;
        case !!doc.mozCancelFullScreen:
          doc.mozCancelFullScreen();
          break;
        case !!doc.msExitFullscreen:
          doc.msExitFullscreen();
          break;
      }
      this.isFullscreen = false;
    }
  }
}
