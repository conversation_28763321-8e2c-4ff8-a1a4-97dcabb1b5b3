import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { HttpErrorResponse } from '@angular/common/http';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { environment } from '@env/environment';
import { ResponseWrapperDTO } from '@modal/response/ResponseWrapperDTO';
import { catchError, finalize, of, Subject, takeUntil, tap } from 'rxjs';
import { DropOffLocation } from 'src/app/models/DropoffLocation';
import { UtilsService } from 'src/app/shared/services/utils.service';

@Component({
  selector: 'app-warehouse-drop-off-locations',
  templateUrl: './warehouse-drop-off-locations.component.html',
  styleUrls: ['./warehouse-drop-off-locations.component.css']
})
export class WarehouseDropOffLocationsComponent implements OnInit {

  @Input({ alias: 'dropOffLocationList', required: true }) dropOffLocationList: DropOffLocation[];
  @Input({ alias: 'dropOffLocObj', required: true }) dropOffLocObj: DropOffLocation;
  @Input({ alias: 'dropOffFormGroup', required: true }) dropOffFormGroup: FormGroup;
  @Input({ alias: 'dropOffTH', required: true }) dropOffTH: any[];
  @Input({ alias: 'searchText', required: true }) searchText: any;
  @Input({ alias: 'destroy', required: true }) destroy$: Subject<any>;
  
  @Output() getAllWarehouseDetails: EventEmitter<any> = new EventEmitter<any>();
  @Output() openAddEditDropOffLoc: EventEmitter<any> = new EventEmitter<any>();
  @Output() openDeleteDropLocationModal: EventEmitter<any> = new EventEmitter<any>();
  @Output() onSearchDO: EventEmitter<any> = new EventEmitter<any>();
  @Output() getDropOffQR: EventEmitter<any> = new EventEmitter<any>();

  constructor(public utilsService: UtilsService) { }

  ngOnInit() {
  }

  onChangeStatus(item: DropOffLocation, value: boolean, index: number) {

    const API =  `${environment.API_URL}${this.utilsService.serverVariableService.DROPOFF_STATUS}${item.id}`
    const headers = this.utilsService.setHeaders(this.utilsService.serverVariableService.DROPOFF_STATUS);
    
    this.utilsService.showLoader++;

    return this.utilsService.http.put<ResponseWrapperDTO>(API, {}, { headers }).pipe(
      tap((response) => {
        this.dropOffLocationList[index].isActive = value
        this.utilsService.successToaster(response.message)
      }),
      catchError((error: HttpErrorResponse) => {
        this.dropOffLocationList[index].isActive = !value
        this.utilsService.errorHandler(error);
        return of(null);
      }),
      finalize(() => {
        if (this.utilsService.showLoader > 0) {
          this.utilsService.showLoader--;
        }
      }),
      takeUntil(this.destroy$),
    ).subscribe();
  }

  //drag drop
  drop(event: CdkDragDrop<any>) {

    this.dropOffLocationList[event.previousContainer.data.index] = event.container.data.item;
    this.dropOffLocationList[event.container.data.index] = event.previousContainer.data.item;
    moveItemInArray(this.dropOffLocationList, event.previousIndex, event.currentIndex);

    let seqArr = this.dropOffLocationList.map((v, i) => ({
      id: v.id,
      sortOrder: i + 1
    }));
    const param = {
      dropLocations: seqArr
    }

    this.utilsService.postMethodAPI(false, this.utilsService.serverVariableService.DROPOFF_SORT_ORDER, param, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.getAllWarehouseDetails.emit();
      }
    })
  }

  // AddEditDelete
  openAddEditModal(obj: DropOffLocation, status: string) {
    this.openAddEditDropOffLoc.emit({ obj: obj, status: status })
  }

  openDeleteModal(obj: DropOffLocation) {
    this.openDeleteDropLocationModal.emit({obj: obj})
  }

  //Search
  onSearch(event) {
    this.onSearchDO.emit(event)
  }

  //QR
  getQR(item: DropOffLocation) {
    this.getDropOffQR.emit(item)
  }
}
