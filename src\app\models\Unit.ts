import { deserializeAs, serializeAs } from 'cerialize';

export class Unit {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('createdDate')
    @deserializeAs('createdDate')
    private _createdDate: string;

    @serializeAs('unitName')
    @deserializeAs('unitName')
    private _unitName: string;

    @serializeAs('symbol')
    @deserializeAs('symbol')
    private _symbol: string;

    @serializeAs('isActive')
    @deserializeAs('isActive')
    private _isActive: boolean;

    @serializeAs('isSelected')
    @deserializeAs('isSelected')
    private _isSelected: boolean;
    
    @serializeAs('isDefault')
    @deserializeAs('isDefault')
    private _isDefault: boolean;

    @serializeAs('shortCode')
    @deserializeAs('shortCode')
    private _shortCode: string;

    @serializeAs('lastModifiedDate')
    @deserializeAs('lastModifiedDate')
    private _lastModifiedDate: string;
   
    @serializeAs('unitMasterCategory')
    @deserializeAs('unitMasterCategory')
    private _unitMasterCategory: any;
   
    @serializeAs('conversionToMeter')
    @deserializeAs('conversionToMeter')
    private _conversionToMeter: any;
   
    @deserializeAs('baseUnit')
    private _baseUnit: string;

    constructor() {
        this.isActive = false;
        this.isSelected = false;
        this.baseUnit = "";
    }


    /**
     * Getter lastModifiedDate
     * @return {string}
     */
	public get lastModifiedDate(): string {
		return this._lastModifiedDate;
	}

    /**
     * Setter lastModifiedDate
     * @param {string} value
     */
	public set lastModifiedDate(value: string) {
		this._lastModifiedDate = value;
	}


    /**
     * Getter shortCode
     * @return {string}
     */
	public get shortCode(): string {
		return this._shortCode;
	}

    /**
     * Setter shortCode
     * @param {string} value
     */
	public set shortCode(value: string) {
		this._shortCode = value;
	}


    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Getter createdDate
     * @return {string}
     */
	public get createdDate(): string {
		return this._createdDate;
	}

    /**
     * Getter unitName
     * @return {string}
     */
	public get unitName(): string {
		return this._unitName;
	}

    /**
     * Getter symbol
     * @return {string}
     */
	public get symbol(): string {
		return this._symbol;
	}

    /**
     * Getter isActive
     * @return {boolean}
     */
	public get isActive(): boolean {
		return this._isActive;
	}

    /**
     * Getter isSelected
     * @return {boolean}
     */
	public get isSelected(): boolean {
		return this._isSelected;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Setter createdDate
     * @param {string} value
     */
	public set createdDate(value: string) {
		this._createdDate = value;
	}

    /**
     * Setter unitName
     * @param {string} value
     */
	public set unitName(value: string) {
		this._unitName = value;
	}

    /**
     * Setter symbol
     * @param {string} value
     */
	public set symbol(value: string) {
		this._symbol = value;
	}

    /**
     * Setter isActive
     * @param {boolean} value
     */
	public set isActive(value: boolean) {
		this._isActive = value;
	}

    /**
     * Setter isSelected
     * @param {boolean} value
     */
	public set isSelected(value: boolean) {
		this._isSelected = value;
	}



    /**
     * Getter isDefault
     * @return {boolean}
     */
	public get isDefault(): boolean {
		return this._isDefault;
	}

    /**
     * Setter isDefault
     * @param {boolean} value
     */
	public set isDefault(value: boolean) {
		this._isDefault = value;
	}


    /**
     * Getter unitMasterCategory
     * @return {any}
     */
	public get unitMasterCategory(): any {
		return this._unitMasterCategory;
	}

    /**
     * Setter unitMasterCategory
     * @param {any} value
     */
	public set unitMasterCategory(value: any) {
		this._unitMasterCategory = value;
	}


    /**
     * Getter conversionToMeter
     * @return {any}
     */
	public get conversionToMeter(): any {
		return this._conversionToMeter;
	}

    /**
     * Setter conversionToMeter
     * @param {any} value
     */
	public set conversionToMeter(value: any) {
		this._conversionToMeter = value;
	}



    /**
     * Getter baseUnit
     * @return {string}
     */
	public get baseUnit(): string {
		return this._baseUnit;
	}

    /**
     * Setter baseUnit
     * @param {string} value
     */
	public set baseUnit(value: string) {
		this._baseUnit = value;
	}

}