<div class="authentication">
  <div class="authentication-wrapper">
    <div class="authentication-inner-wrapper">
      <div class="auth-logo">
        <img src="assets/images/auth-logo.png" alt="valamji">
      </div>
      <div class="auth-card">
        <div class="auth-card-icon">
          <i class="th th-outline-user"></i>
        </div>
        <div class="auth-card-title">
          <h2>Login to Valamji</h2>
        </div>

        <div class="auth-form" [formGroup]="loginFormGroup">
          <div class="form-group">
            <div class="form-label">Email / Mobile No / Username</div>
            <!-- <div class="form-label">Username</div> -->
            <div class="form-group-icon-start">
              <i class="th th-outline-user"></i>
              <input [maxlength]="utilsService.validationService.MAX_50" (keyup.enter)="onLogin()" formControlName="email" type="text" placeholder="Enter Email / Mobile No / Username"
                class="form-control">
            </div>
            <div class="message error-message"
              *ngIf="loginFormGroup.controls['email'].hasError('required') &&  loginFormGroup.controls['email'].touched">
              {{utilsService.validationService.EMAIL_MOBILE_USER_REQ}}
            </div>
            <div class="message error-message"
              *ngIf="!loginFormGroup.controls['email'].hasError('required') && !loginFormGroup.controls['email'].valid && loginFormGroup.controls['email'].touched">
              {{utilsService.validationService.EMAIL_MOBILE_USER_INVALID}}
            </div>
          </div>
          <div class="form-group">
            <div class="form-label">Password</div>
            <div class="form-group-icon-start form-group-password">
              <i class="th th-outline-lock"></i>
              <input (keyup.enter)="onLogin()" [type]="flagForPasswordHideShow? 'password' : 'text'"
                formControlName="password" type="password" class="form-control" placeholder="Enter Password">
              <button (click)="flagForPasswordHideShow = !flagForPasswordHideShow" class="btn-password">
                <i class="th th th-outline-eye"
                  [ngClass]="{'th th-outline-eye': !flagForPasswordHideShow, 'th th-outline-eye-slash': flagForPasswordHideShow}"></i>
              </button>
            </div>
            <div class="message error-message"
              *ngIf="loginFormGroup.controls['password'].hasError('required') &&  loginFormGroup.controls['password'].touched">
              {{utilsService.validationService.PASSWORD_REQUIRED}}
            </div>
          </div>

        </div>



        <div class="auth-btn-group">
          <button type="submit" (click)="onLogin()" class="btn btn-primary">Login</button>
        </div>


        <div class="auth-form-link">
          <a [routerLink]="['/auth/forgot-password']">Forgot Password?</a>
        </div>


      </div>
    </div>

    <div class="authentication-footer">
      <p>{{utilsService.version}}</p>
    </div>
  </div>
</div>
