import { RouterModule, Routes } from "@angular/router";
import { SettingsComponent } from "./settings.component";
import { CommonModule } from "@angular/common";
import { SharedModule } from "src/app/shared/shared.module";
import { NgModule } from "@angular/core";
import { AlertsRemindersComponent } from "./alerts-reminders/alerts-reminders.component";
import { CoinsSettingsComponent } from "./coins-settings/coins-settings.component";
import { GeneralComponent } from "./general/general.component";
import { SystemComponent } from "./system/system.component";

const routes: Routes = [
    { path: '', component: SettingsComponent }
]

@NgModule({
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    SharedModule.forRoot()
  ],
  declarations: [SettingsComponent, AlertsRemindersComponent, CoinsSettingsComponent, GeneralComponent, SystemComponent]
})
export class SettingsModule { }