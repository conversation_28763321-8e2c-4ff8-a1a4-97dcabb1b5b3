import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Deserialize, Serialize } from 'cerialize';
import { debounceTime, distinctUntilChanged, Subject } from 'rxjs';
import { Transport, TransportGrp } from 'src/app/models/Transport';
import { activeInactiveStatus, statusYesNo, TRANSPORTER_MASTER } from 'src/app/shared/constants/constant';
import { UtilsService } from 'src/app/shared/services/utils.service';
import { saveAs } from 'file-saver';
import { TransportBranchSheet } from 'src/app/models/TransportBranchSheet';
import { FileError } from 'src/app/models/file-error/fileError';
import { environment } from 'src/environments/environment';
declare var window: any;

@Component({
  selector: 'app-transport',
  templateUrl: './transport.component.html',
  styleUrls: ['./transport.component.scss']
})
export class TransportComponent implements OnInit {

  transportAddModal: any;
  deleteTransportModal: any;
  branchdeleteModal: any;

  @ViewChild('excel') excel: ElementRef;
  flagForInvalidDocSize = false;
  flagForInvalidExtension = false;

  transportTH: any[] = []
  selectedIds: any[] = []
  transportList: Transport[] = [];
  transportObj = new Transport();
  transportBranchObj = new TransportBranchSheet();
  activeFlag: boolean = true;
  transportGroup: FormGroup;
  flagForSelectAll: boolean = false;

  pageNo: number = 1;
  pageSize: string = '100';
  sortOrder: string;
  sortColumn: string;
  totalData: number;
  statusForModal: string;
  storeName: string;
  transporterName: string;
  isBooking: boolean;
  isDelivery: boolean;
  pagination: any;

  enumForSortOrder = this.utilsService.enumForSortOrder;
  searchSubject: Subject<string> = new Subject<string>();
  searchText: string;
  searchBcode: Subject<string> = new Subject<string>();
  branchCodeSearch: string;

  dropdown: any;
  bookingdeliveryDropdown = statusYesNo;
  activeInactiveStatus = activeInactiveStatus;
  isExpandedIDs: any[] = [];

  transportFileErrorModal: any;
  errorsList: FileError[] = []

  blob: Blob;

  constructor(public utilsService: UtilsService, private fb: FormBuilder) {
    this.transportTH = TRANSPORTER_MASTER
  }

  ngOnInit() {

    this.searchSubject.pipe(debounceTime(300), distinctUntilChanged()).subscribe((res: string) => {
      this.pageNo = 1;
      // this.pageSize = '100';
      this.searchText = null;
      this.searchText = res;
      this.getTransportList();
    });

    this.searchBcode.pipe(debounceTime(300), distinctUntilChanged()).subscribe((res: string) => {
      this.pageNo = 1;
      // this.pageSize = '100';
      this.branchCodeSearch = null;
      this.branchCodeSearch = res;
      this.getTransportList();
    });

    this.getRequiredData();
    this.getTransportList();
    this.transportFormGroup();

    this.transportAddModal = new window.bootstrap.Modal(
      document.getElementById('transportAddModal')
    );

    this.deleteTransportModal = new window.bootstrap.Modal(
      document.getElementById('deleteTransportModal')
    );

    this.branchdeleteModal = new window.bootstrap.Modal(
      document.getElementById('branchdeleteModal')
    );

    this.transportFileErrorModal = new window.bootstrap.Modal(
      document.getElementById('transportFileErrorModal')
    );

    document.getElementById('transportAddModal').addEventListener('shown.bs.modal', () => {
      document.getElementById('f').focus();
    });
  }

  getTransportList() {

    this.flagForSelectAll = false;
    this.totalData = 0;
    this.selectedIds = []

    let ls_param = null
    ls_param = JSON.parse(localStorage.getItem('param'))

    if (!this.utilsService.isNullUndefinedOrBlank(ls_param)) {
      if (ls_param.pageName === 'transport') {
        this.isExpandedIDs = ls_param.id
        this.pageNo = ls_param.pageNo,
          this.pageSize = ls_param.pageSize,
          this.sortOrder = ls_param.sortOrder
          this.sortColumn = ls_param.sortColumn,
          this.searchText = ls_param.searchText,
          this.activeFlag = ls_param.isActive,
          this.storeName = ls_param.storeName,
          this.transporterName = ls_param.transporterName,
          this.isBooking = ls_param.isBooking,
          this.isDelivery = ls_param.isDelivery,
          this.branchCodeSearch =  ls_param.branchCodeSearch
      }
    }

    const param = {
      pageNo: this.pageNo,
      pageSize: this.pageSize,
      sortOrder: this.sortOrder,
      sortColumn: this.sortColumn,
      searchText: this.searchText,
      isActive: this.activeFlag,
      storeName: this.storeName,
      transporterName: this.transporterName,
      isBooking: this.isBooking,
      isDelivery: this.isDelivery,
      branchCodeSearch: this.branchCodeSearch
    }

    this.utilsService.postMethodAPI(false, this.utilsService.serverVariableService.TRANSPORT_LISTING, param, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.transportList = Deserialize(this.convertBranchDataByCity(response.content), Transport);
        this.transportList.forEach(v => {
          v.branchTransportsGroupedByCity = Deserialize(v.branchTransportsGroupedByCity, TransportGrp)
        })
        this.pagination = response;
        this.totalData = response.totalElements;

        if (!this.utilsService.isNullUndefinedOrBlank(this.isExpandedIDs)) {
          this.transportList?.forEach(obj => {
            if (this.isExpandedIDs.includes(obj.id)) {
              obj.isExpand = true;
            } else {
              obj.isExpand = false;
            }
            if (obj.branchTransportsGroupedByCity)
              obj.branchTransportsGroupedByCity?.forEach(group => {
                const branchIds = group.branches.map(branch => branch.id);
                const allTargetIdsPresent = this.isExpandedIDs.some(id => branchIds.includes(id));
                group.isExpand = allTargetIdsPresent;
              });
          });
        }

        if (!this.utilsService.isNullUndefinedOrBlank(this.branchCodeSearch) || !this.utilsService.isNullUndefinedOrBlank(this.searchText) || this.isDelivery !== null || this.isBooking !== null) {
          if(this.transportList) {
            this.transportList?.forEach(obj => {
              obj.isExpand = true;
              if (obj.branchTransportsGroupedByCity)
                obj.branchTransportsGroupedByCity?.forEach(group => {
                  group.isExpand = true;
                });
            });
          }
        }
        localStorage.removeItem('param')
      } else {
        this.transportList = [];
      }
    })

  }

  transportFormGroup() {
    this.transportGroup = this.fb.group({
      transporterName: ['', Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.PATTERN_FOR_ALPHANUMERIC_WITH_SPECIAL_CHAR)])],
      storeName: ['', Validators.compose([Validators.required])],
      mode_of_transport: [null, Validators.compose([Validators.required])],
      status: [null, Validators.required]
    })
  }

  openAddEditModal(obj: Transport, status: string) {
    this.transportObj = new Transport();
    this.transportGroup.reset();
    this.statusForModal = status;
    if (this.excel?.nativeElement) {
      this.excel.nativeElement.value = "";
    }
    if (this.statusForModal === 'Add') {
      setTimeout(() => {
        this.transportObj.isActive = true;
      }, 100);
    }

    if (obj) {
      setTimeout(() => {
        this.transportObj = Serialize(obj);
        this.transportObj.mot = (obj.modeOfTransport).split(",").map(item => item.trim());
      }, 100);
    }
    this.transportAddModal.show();
  }

  openDeleteTransportModal(obj: Transport) {
    this.transportObj = Serialize(obj)
    this.deleteTransportModal.show();
  }

  deleteTransport() {

    this.utilsService.deleteMethodAPI(true, this.utilsService.serverVariableService.TRANSPORT_SAVE_EDIT_DELETE + `?id=${this.transportObj.id}`, {}, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.deleteTransportModal.hide();
        if (!this.pagination?.first && this.pagination?.last && this.pagination?.numberOfElements === 1) {
          this.pageNo = this.pageNo - 1
        }
        this.getTransportList();
      }
    })
  }

  // Select Deselect 
  selectAll() {
    if (this.flagForSelectAll === true) {
      this.selectedIds = new Array<string>();
    }
    const obj = this.transportList.filter((val, index) => {
      if (this.flagForSelectAll === true) {
        val['isSelected'] = true;
        this.selectedIds.push(val.id);
      } else {
        val['isSelected'] = false;
        this.selectedIds.splice(index, 1);
      }
    });
    if (this.flagForSelectAll === false) {
      this.selectedIds = new Array<string>();
    }
  }

  selectUnselect(id: number, index, value) {

    const isSelected = this.selectedIds.includes(id);

    if (value && !isSelected) {

      this.selectedIds.push(id);

    } else if (!value && isSelected) {

      const assetIndex = this.selectedIds.indexOf(id);
      this.selectedIds.splice(assetIndex, 1);
    }
    this.flagForSelectAll = this.checkIfAllSelected();
  }

  checkIfAllSelected() {
    let flag = true;
    this.transportList.filter((val, index) => {
      if (val['isSelected'] === false) {
        flag = false;
        return;
      }
    });
    return flag;
  }

  //required data

  getRequiredData() {

    this.dropdown = []
    this.utilsService.getMethodAPI(false, this.utilsService.serverVariableService.TRANSPORT_DROPDOWN, null, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.dropdown = response;
      }
    })
  }

  // sorting 

  onSortTH(key) {

    if (this.utilsService.isEmptyObjectOrNullUndefined(this.transportList)) {
      return;
    }

    if (key === this.sortColumn) {
      if (this.sortOrder === this.enumForSortOrder.A) {
        this.sortOrder = this.enumForSortOrder.D;
      } else if (this.sortOrder === this.enumForSortOrder.D) {
        this.sortOrder = this.enumForSortOrder.A;
      }
    } else {
      this.sortOrder = this.enumForSortOrder.D;
    }

    this.sortColumn = key;
    this.getTransportList();
  }

  // status 

  onChangeStatus(item: Transport, value, index) {

    this.utilsService.putMethodAPI(true, this.utilsService.serverVariableService.TRANSPORT_STATUS_CHANGE + `${item.id}/${value}`, {}, '', (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response.data)) {
        this.transportList[index].isActive = value
      } else {
        this.transportList[index].isActive = !value
      }
      if(!this.pagination?.first && this.pagination?.last && this.pagination?.numberOfElements === 1 && this.activeFlag) {
        this.pageNo = this.pageNo - 1
      }
      this.getTransportList()
    }, true);
  }

  // branch status
  onChangeBranchStatus(item: TransportBranchSheet, value, index, p_index, c_index) {

    this.utilsService.putMethodAPI(true, this.utilsService.serverVariableService.BRANCH_TRANSPORT_STATUS + `${item.id}/${value}`, {}, '', (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response.data)) {
        this.transportList[p_index].branchTransportsGroupedByCity[index].branches[c_index].isActive = value
      } else {
        this.transportList[p_index].branchTransportsGroupedByCity[index].branches[c_index].isActive = !value
      }
      if(!this.pagination?.first && this.pagination?.last && this.pagination?.numberOfElements === 1 && this.activeFlag) {
        this.pageNo = this.pageNo - 1
      }
      this.getTransportList()
    }, true);
  }

  //Search
  onSearch(event: any, isBranch: boolean) {
    if(!isBranch) {
      this.searchSubject.next(event.target.value);
    }
    if(isBranch) {
      this.searchBcode.next(event.target.value);
    }
  }

  // active/inactive

  onChangeActive() {
    this.pageNo = 1;
    // this.pageSize = '100';
    this.getTransportList();
  }

  trackBy(index: number, item: Transport): number {
    return item.id;
  }

  addPageSizeData(event) {
    this.pageNo = 1;
    this.pageSize = event;
    this.getTransportList();
  }

  pageNumber(event) {
    this.pageNo = event
    this.getTransportList();
  }

  // Expand collapse
  onCollapse(index: number) {
    this.transportList[index].isExpand = !this.transportList[index].isExpand;

    if (this.transportList[index].isExpand) {
      this.transportList[index].branchTransportsGroupedByCity.forEach(v => v.isExpand = true)
    }
    else {
      this.transportList[index].branchTransportsGroupedByCity.forEach(v => v.isExpand = false)
    }

    //collapse
    this.transportList.forEach(p => {
      if (p.isExpand) {
        if (!this.isExpandedIDs.includes(p.id)) {
          this.isExpandedIDs.push(p.id);
        }
      } else {
        const index = this.isExpandedIDs?.indexOf(p.id);
        if (index !== -1) {
          this.isExpandedIDs.splice(index, 1);
        }
      }
    });
  }

  checkIfAllExpand() {
    let flag = true;
    this.transportList.filter((val, index) => {
      if (val['isExpand'] === false) {
        flag = false;
        return;
      }
    });
    return flag;
  }

  onCollapseChild(parent_index: number, i: number) {
    this.transportList[parent_index].branchTransportsGroupedByCity[i].isExpand = !this.transportList[parent_index].branchTransportsGroupedByCity[i].isExpand;
    
    //collapse
    this.transportList[parent_index]?.branchTransportsGroupedByCity[i].branches?.forEach(p => {
      if (this.transportList[parent_index].branchTransportsGroupedByCity[i].isExpand) {
        if (!this.isExpandedIDs.includes(p.id)) {
          this.isExpandedIDs.push(p.id);
        }
      } else {
        const index = this.isExpandedIDs.indexOf(p.id);
        if (index !== -1) {
          this.isExpandedIDs.splice(index, 1);
        }
      }
    });
  }

  // response format change
  convertToGroupedByCity(input: any): any[] {
    const result: any[] = [];
    Object.keys(input).forEach(city => {
      const cityData = input[city];
      const groupedCity: any = {
        city: city,
        branches: cityData
      };
      result.push(groupedCity);
    });

    return result;
  }

  convertBranchDataByCity(input: any[]): any[] {
    return input?.map(transporter => {
      const transformedTransporter = {
        ...transporter,
        branchTransportsGroupedByCity: transporter.branchTransportsGroupedByCity ? this.convertToGroupedByCity(transporter.branchTransportsGroupedByCity) : null
      };
      return transformedTransporter;
    });
  }

  getContacts(i: number, j: number, k: number): string[] {
    return this.transportList[i].branchTransportsGroupedByCity[j].branches[k].contacts?.map(v => {
      if (v?.name && v?.mobileNo) {
        return `${v.name} : ${v.mobileNo}`;
      } else if (v?.name) {
        return `${v.name}`;
      } else if (v?.mobileNo) {
        return `${v.mobileNo}`;
      }
      return '';
    });
  }

  getEmails(i: number, j: number, k: number): string[] {
    return this.transportList[i].branchTransportsGroupedByCity[j].branches[k].emails?.map(v => `${v?.email ? v?.email : ''}`);
  }

  getLandlines(i: number, j: number, k: number): string[] {
    return this.transportList[i].branchTransportsGroupedByCity[j].branches[k].landlineNos?.map(v => `${v?.landlineNo ? v?.landlineNo : ''}`);
  }

  // select excel

  onSelectExcel(event): void {

    if (event.target.files && event.target.files[0]) {
      let fileData = null;
      this.flagForInvalidExtension = false;
      this.flagForInvalidDocSize = false;
      const reader = new FileReader();
      const max_file_size = 5242880;
      reader.readAsDataURL(event.target.files[0]); // read file as data url
      const selectedFile = event.target.files[0];
      if (selectedFile) {
        const ext = selectedFile.name.substr(selectedFile.name.lastIndexOf('.') + 1);
        const ext1 = (ext).toLowerCase();

        if (ext1 === 'xlsx' || ext1 === 'xls') {
          if (max_file_size < selectedFile.size) {
            this.flagForInvalidDocSize = true;
            this.utilsService.toasterService.error(this.utilsService.validationService.EXCEL_MAX_FILE)
          } else {
            // this.userObj.profileUrl = event.target.files[0].name
            fileData = {
              fileName: selectedFile.name,
              selectedFile
            };
            this.transportObj.excel = fileData;
          }
        } else {
          this.flagForInvalidExtension = true;
          this.utilsService.toasterService.error(this.utilsService.validationService.EXCEL_REQUIRED)
        }
      }

    }
  }

  //Save Transport
  onSaveTransport() {

    const formData = new FormData();
    if (this.transportGroup.invalid) {
      this.transportGroup.markAllAsTouched();
      return;
    }

    if (this.transportObj?.excel) {
      formData.set('file', this.transportObj?.excel?.selectedFile);
    } else {
      // if (this.statusForModal === 'Add') {
      //   this.utilsService.toasterService.error('Branch Sheet is required!', '', {
      //     positionClass: 'toast-top-right',
      //     closeButton: true,
      //     timeOut: 10000
      //   });
      //   return;
      // }
    }

    this.transportObj.modeOfTransports = Serialize(this.transportObj.mot)

    let param = {
      id: this.transportObj.id ? this.transportObj.id : null,
      transporterName: this.transportObj.transporterName,
      storeName: this.transportObj.storeName,
      isActive: this.transportObj.isActive,
      modeOfTransports: this.transportObj.modeOfTransports,
    }

    if(this.statusForModal === 'Edit') {
      formData.set('isEdit', JSON.stringify(true));
    }

    if(this.statusForModal === 'Add') {
      formData.set('isEdit', JSON.stringify(false));
    }
    
    formData.set('transportInfo', JSON.stringify(Serialize(param)));

    this.utilsService.postMethodAPI(false, this.utilsService.serverVariableService.TRANSPORT_SAVE_EDIT_DELETE, formData, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response.data)) {

        //Error

        if (!response.data.isSuccess) {
          this.utilsService.toasterService.error(response.message, '', {
            positionClass: 'toast-top-right',
            closeButton: true,
            timeOut: 10000
          });
          this.openTransportErrorModal(response.data.errors);
          this.removeFile();
          return;
        }

        //Valid

        if (response.data.isSuccess) {
          this.utilsService.toasterService.success(response.message, '', {
            positionClass: 'toast-top-right',
            closeButton: true,
            timeOut: 10000
          });
          this.transportAddModal.hide();
          this.getTransportList();
        }
      }
    }, true)
    setTimeout(() => {
      this.removeFile();
    }, 150);
  }

  removeFile(){
    if(!this.utilsService.isNullUndefinedOrBlank(this.transportObj.excel)){
      if(this.transportObj?.excel?.fileName) {
        this.transportObj.excel.fileName = null;
        this.transportObj.excel.selectedFile = null;
      }
      if(this.excel) {
        this.excel.nativeElement.value = null;
      }
    }
  }

  exportReport() {

    const param = {
      ids: this.selectedIds ? this.selectedIds : [],
      searchText: this.searchText,
      isActive: this.activeFlag,
      storeName: this.storeName,
      transporterName: this.transporterName,
      isBooking: this.isBooking,
      isDelivery: this.isDelivery,
      sortOrder: this.sortOrder,
      sortColumn: this.sortColumn,
      branchCodeSearch: this.branchCodeSearch,
    }

    this.utilsService.exportReport(param, this.utilsService.serverVariableService.TRANSPORT_EXPORT).subscribe((data: any) => {
      saveAs(new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' }), 'Transport Sheet');
    });
  }

  //delete branch

  openDeleteTransportBModal(obj: TransportBranchSheet) {
    this.transportBranchObj = Serialize(obj)
    this.branchdeleteModal.show();
  }

  deleteTransportB() {

    this.utilsService.deleteMethodAPI(true, this.utilsService.serverVariableService.BRANCH_TRANSPORT_SAVE_EDIT_DELETE + `?id=${this.transportBranchObj.id}`, {}, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.branchdeleteModal.hide();
        // if (!this.pagination?.first && this.pagination?.last && this.pagination?.numberOfElements === 1) {
        //   this.pageNo = this.pageNo - 1
        // }
        this.getTransportList();
      }
    })
  }


  // Error Modal

  openTransportErrorModal(errorList: FileError[]) {
    this.errorsList = [...errorList]
    this.transportFileErrorModal.show();
  }

  //
  redirectToDetails() {
    let param = null;
    param = {
      id: this.isExpandedIDs,
      pageNo: this.pageNo,
      pageSize: this.pageSize,
      sortOrder: this.sortOrder,
      sortColumn: this.sortColumn,
      searchText: this.searchText,
      isActive: this.activeFlag,
      storeName: this.storeName,
      transporterName: this.transporterName,
      isBooking: this.isBooking,
      isDelivery: this.isDelivery,
      pageName: 'transport'
    }
    localStorage.setItem('param', JSON.stringify(param))
  }

  // download sample file
  downloadSampleFile() {

    let data = null
    let name = null

    data = environment.FILE_PATH + environment.TRANSPORT_SAMPLE_FILE
    name = 'transport_sample_file'

    this.blob = new Blob([data], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
    saveAs(data, `${name}`);
  }
}
