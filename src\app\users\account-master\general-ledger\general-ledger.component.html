<div class="page-content">
  <div class="page-title-wrapper">
    <div class="page-title-left">
      <h4>General Ledger Report</h4>
    </div>
    <div class="page-title-right">
    </div>
  </div>
  <div class="content-area">
    <div class="page-filters">
      <div class="page-filters-left">
        <div class="form-group form-group-sm">
          <div class="form-group-icon-end">
            <i class="th th-outline-calendar"></i>
            <input type="text" class="form-control" placeholder="Select Start Date">
          </div>
        </div>
        <div class="form-group form-group-sm">
          <div class="form-group-icon-end">
            <i class="th th-outline-calendar"></i>
            <input type="text" class="form-control" placeholder="Select End Date">
          </div>
        </div>
        <div class="form-group theme-ngselect form-group-sm">
          <ng-select placeholder="Vendor" [multiple]="false" [clearable]="false" [items]="demo" bindLabel="name"
            bindValue="id" [(ngModel)]="selectedDemo1">
          </ng-select>
        </div>
        <div class="form-group theme-ngselect form-group-sm">
          <button class="btn btn-sm btn-primary">Search</button>
        </div>
      </div>
      <div class="page-filters-right">
        <div class="form-group theme-ngselect form-group-export form-group-sm">
          <ng-select placeholder="Export" [multiple]="false" [clearable]="false" [items]="demo" bindLabel="name"
            bindValue="id" [(ngModel)]="selectedDemo2">
          </ng-select>
        </div>
      </div>
    </div>
    <div class="card card-theme">
      <div class="card-body p-0">
        <div class="table-responsive">
          <table class="table-theme table-hover table table-bordered tbl-collapse table-general-ledger-reports">
            <thead class="border-less">
              <tr>
                <th class="d-flex align-items-center gap-2">
                  <div class="checkbox checkbox-primary checkbox-small">
                    <input type="checkbox" id="tbl-checkbox" class="material-inputs filled-in" />
                    <label for="tbl-checkbox"></label>
                  </div>
                  Account Name
                </th>
              </tr>
            </thead>
            <tbody *ngFor="let item of [1,2,3,4,5,6]">
              <tr class="tbl-row-collapse-title" [ngClass]="{'tbl-bg-secondary': isExpanded}">
                <td class="tbl-user tbl-bold">
                  <div class="tbl-user-checkbox-srno">
                    <div class="checkbox checkbox-primary checkbox-small">
                      <input type="checkbox" id="tbl-checkbox2" class="material-inputs filled-in" />
                      <label for="tbl-checkbox2"></label>
                    </div>
                    <div class="tbl-user-wrapper">
                      <div class="tbl-user-image">
                        <img src="assets/images/icons/box.svg" alt="valamji">
                      </div>
                      <div class="tbl-user-text">
                        <p>Prepaid Expenses</p>
                      </div>
                    </div>
                  </div>
                  <button class="btn btn-xs text-color btn-icon btn-link collapse-arrow collapsed" type="button"
                    (click)="toggleExpand()" [attr.aria-expanded]="isExpanded" data-bs-toggle="collapse"
                    data-bs-target="#collapseGeneralLedgerReport" aria-expanded="false"
                    aria-controls="collapseGeneralLedgerReport">
                    <i class="th th-outline-arrow-right-3"></i>
                  </button>
                </td>
              </tr>
              <tr class="collapse" id="collapseGeneralLedgerReport">
                <td colspan="30" class="p-0 tbl-collapse-child">
                  <div class="general-ledger-reports-row">
                    <div class="general-ledger-reports-col">
                      <table class="table-theme table-hover table table-bordered table-credit-debit">
                        <thead>
                          <tr>
                            <th>Particular</th>
                            <th class="tbl-credit-amount">Credit Amount</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr *ngFor="let item of [1,2,3,4,5,6]">
                            <td>Advanced Tax</td>
                            <td class="tbl-credit-amount">₹150.00</td>
                          </tr>
                        </tbody>
                        <tfoot>
                          <tr class="tbl-total-row">
                            <th>Total</th>
                            <th class="tbl-credit-amount">₹150.00</th>
                          </tr>
                        </tfoot>
                      </table>
                    </div>
                    <div class="general-ledger-reports-col">
                      <table class="table-theme table-hover table table-bordered table-credit-debit">
                        <thead>
                          <tr>
                            <th>Particular</th>
                            <th class="tbl-debit-amount">Debit Amount</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr *ngFor="let item of [1,2,3,4,5,6]">
                            <td>Advanced Tax</td>
                            <td class="tbl-debit-amount">₹150.00</td>
                          </tr>
                        </tbody>
                        <tfoot>
                          <tr class="tbl-total-row">
                            <th>Total</th>
                            <th class="tbl-debit-amount">₹150.00</th>
                          </tr>
                        </tfoot>
                      </table>
                    </div>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
    <div class="paginationbox pagination-fixed">
      <app-pagination></app-pagination>
    </div>
  </div>
</div>