import { deserializeAs, serializeAs } from 'cerialize';
import { Registration } from './Registration';
import { POImportItem } from './POImportItem';
import { StockDetails, StockDetailsList } from './StockDetails';

export class Item {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    // @serializeAs('lastModifiedDate')
    @deserializeAs('lastModifiedDate')
    private _lastModifiedDate: string;

    @serializeAs('displayName')
    @deserializeAs('displayName')
    private _displayName: string;

    @serializeAs('groupCodeId')
    @deserializeAs('groupCodeId')
    private _groupCodeId: number;

    @serializeAs('skuId')
    @deserializeAs('skuId')
    private _skuId: string;

    @serializeAs('isActive')
    @deserializeAs('isActive')
    private _isActive: boolean;

    // @serializeAs('isSelected')
    @deserializeAs('isSelected')
    private _isSelected: boolean;

    @serializeAs('itemPrice')
    @deserializeAs('itemPrice')
    private _itemPrice: number;

    @serializeAs('itemCarton')
    @deserializeAs('itemCarton')
    private _itemCarton: number;

    @serializeAs('image')
    @deserializeAs('image')
    private _image: string;

    @serializeAs('itemName')
    @deserializeAs('itemName')
    private _itemName: string;

    @serializeAs('marketTypeIds')
    @deserializeAs('marketTypeIds')
    private _marketTypeIds: number[];

    @serializeAs('isSellCarton')
    @deserializeAs('isSellCarton')
    private _isSellCarton: boolean;

    @serializeAs('isVisible')
    @deserializeAs('isVisible')
    private _isVisible: boolean;

    @serializeAs('isAlert')
    @deserializeAs('isAlert')
    private _isAlert: boolean;

    @serializeAs('days')
    @deserializeAs('days')
    private _days: number;

    @serializeAs('itemGroupId')
    @deserializeAs('itemGroupId')
    private _itemGroupId: number;

    @serializeAs('unitId')
    @deserializeAs('unitId')
    private _unitId: number;

    @serializeAs('hsnCodeId')
    @deserializeAs('hsnCodeId')
    private _hsnCodeId: number;

    @serializeAs('qcChecklistId')
    @deserializeAs('qcChecklistId')
    private _qcChecklistId: number;

    @serializeAs('itemLength')
    @deserializeAs('itemLength')
    private _itemLength: number;

    @serializeAs('itemWidth')
    @deserializeAs('itemWidth')
    private _itemWidth: number;

    @serializeAs('itemHeight')
    @deserializeAs('itemHeight')
    private _itemHeight: number;

    // @serializeAs('itemsDim')
    @deserializeAs('itemsDim')
    private _itemsDim: any;

    // @serializeAs('boxDim')
    @deserializeAs('boxDim')
    private _boxDim: any;

    @serializeAs('boxLength')
    @deserializeAs('boxLength')
    private _boxLength: number;

    @serializeAs('boxWidth')
    @deserializeAs('boxWidth')
    private _boxWidth: number;

    @serializeAs('boxHeight')
    @deserializeAs('boxHeight')
    private _boxHeight: number;

    @serializeAs('youtubeLink')
    @deserializeAs('youtubeLink')
    private _youtubeLink: string;

    @serializeAs('itemsBelongTo')
    @deserializeAs('itemsBelongTo')
    private _itemsBelongTo: string;

    @serializeAs('itemsWeight')
    @deserializeAs('itemsWeight')
    private _itemsWeight: number;

    @serializeAs('boxWeight')
    @deserializeAs('boxWeight')
    private _boxWeight: number;

    // @serializeAs('itemsWeightDim')
    @deserializeAs('itemsWeightDim')
    private _itemsWeightDim: any;

    // @serializeAs('boxWeightDim')
    @deserializeAs('boxWeightDim')
    private _boxWeightDim: any;

    @serializeAs('purchaseRation')
    @deserializeAs('purchaseRation')
    private _purchaseRation: number;

    @serializeAs('gstItemPrice')
    @deserializeAs('gstItemPrice')
    private _gstItemPrice: number;

    @serializeAs('gstItemCarton')
    @deserializeAs('gstItemCarton')
    private _gstItemCarton: number;

    @serializeAs('discounterCoins')
    @deserializeAs('discounterCoins')
    private _discounterCoins: number;

    @serializeAs('loosesGoodsCoins')
    @deserializeAs('loosesGoodsCoins')
    private _loosesGoodsCoins: number;

    @serializeAs('seasonMasterId')
    @deserializeAs('seasonMasterId')
    private _seasonMasterId: number;

    @serializeAs('itemsSeason')
    @deserializeAs('itemsSeason')
    private _itemsSeason: any;

    private _advPODate: string;
    private _tempAdvPODate: string;
    private _marketType: any;

    @serializeAs('title')
    @deserializeAs('title')
    private _title: string;

    // @serializeAs('breachQty')
    @deserializeAs('breachQty')
    private _breachQty: BreachQty;

    @serializeAs('searchKeyWords')
    @deserializeAs('searchKeyWords')
    private _searchKeyWords: string;

    @serializeAs('material')
    @deserializeAs('material')
    private _material: string;
    
    @serializeAs('subMaterial')
    @deserializeAs('subMaterial')
    private _subMaterial: string;

    @serializeAs('sizeMasterId')
    @deserializeAs('sizeMasterId')
    private _sizeMasterId: number;

    @serializeAs('accessories')
    @deserializeAs('accessories')
    private _accessories: string;

    @deserializeAs('categoryName')
    private _categoryName: string;

    @serializeAs('capacity')
    @deserializeAs('capacity')
    private _capacity: number;

    @serializeAs('numberOfBattery')
    @deserializeAs('numberOfBattery')
    private _numberOfBattery: number;

    @serializeAs('isRemoteControl')
    @deserializeAs('isRemoteControl')
    private _isRemoteControl: boolean;

    @serializeAs('wattage')
    @deserializeAs('wattage')
    private _wattage: string;

    @serializeAs('categoryId')
    @deserializeAs('categoryId')
    private _categoryId: number;

    @serializeAs('voltage')
    @deserializeAs('voltage')
    private _voltage: string;

    @serializeAs('mountingTypeId')
    @deserializeAs('mountingTypeId')
    private _mountingTypeId: any;

    @serializeAs('bulletPoint')
    @deserializeAs('bulletPoint')
    private _bulletPoint: any;

    @serializeAs('packOfId')
    @deserializeAs('packOfId')
    private _packOfId: any;

    @serializeAs('packQty')
    @deserializeAs('packQty')
    private _packQty: string;

    @serializeAs('itemsDescription')
    @deserializeAs('itemsDescription')
    private _itemsDescription: string;

    @serializeAs('singlePieceVolume')
    @deserializeAs('singlePieceVolume')
    private _singlePieceVolume: any;

    @serializeAs('specialFeatures')
    @deserializeAs('specialFeatures')
    private _specialFeatures: string;

    @serializeAs('deletePoImageImageId')
    @deserializeAs('deletePoImageImageId')
    private _deletePoImageImageId: any[];

    @serializeAs('deletePoImageId')
    @deserializeAs('deletePoImageId')
    private _deletePoImageId: any[];

    @serializeAs('deletedDocsID')
    @deserializeAs('deletedDocsID')
    private _deletedDocsID: any[];

    @serializeAs('deletedMediaLinksID')
    @deserializeAs('deletedMediaLinksID')
    private _deletedMediaLinksID: number[];

    @serializeAs('mediaLinks')
    @deserializeAs('mediaLinks')
    private _mediaLinks: ItemMediaLink[];

    @serializeAs('batteryTypeId')
    @deserializeAs('batteryTypeId')
    private _batteryTypeId: any;

    // @serializeAs('itemDocs')
    @deserializeAs('itemDocs')
    private _itemDocs: any[];

    @deserializeAs('itemGroup')
    private _itemGroup: any;

    @deserializeAs('qcChecklist')
    private _qcChecklist: any;

    @deserializeAs('unitMaster')
    private _unitMaster: any;

    @deserializeAs('hsnCodeMaster')
    private _hsnCodeMaster: any;

    @deserializeAs('seasonMaster')
    private _seasonMaster: any;

    @deserializeAs('sizeMaster')
    private _sizeMaster: any;

    @deserializeAs('itemsMarketTypes')
    private _itemsMarketTypes: any;

    @deserializeAs('batteryType')
    private _batteryType: any;

    @deserializeAs('mountingType')
    private _mountingType: any;

    @deserializeAs('packOf')
    private _packOf: any;

    @deserializeAs('tempBullet')
    private _tempBullet: any[];

    @deserializeAs('poColors')
    private _poColors: any;

    @deserializeAs('colorList')
    private _colorList: any;

    @serializeAs('itemsDimId')
    @deserializeAs('itemsDimId')
    private _itemsDimId: number;

    @serializeAs('boxDimId')
    @deserializeAs('boxDimId')
    private _boxDimId: number;

    @serializeAs('itemsWeightDimId')
    @deserializeAs('itemsWeightDimId')
    private _itemsWeightDimId: number;

    @serializeAs('boxWeightDimId')
    @deserializeAs('boxWeightDimId')
    private _boxWeightDimId: number;

    @serializeAs('isSaveAsDraft')
    @deserializeAs('isSaveAsDraft')
    private _isSaveAsDraft: boolean;

    @deserializeAs('color')
    private _color: any;

    @deserializeAs('levelItemMaps')
    private _levelItemMaps: any[];

    @deserializeAs('levelQty')
    private _levelQty: string;

    @deserializeAs('supplier')
    private _supplier: Registration[];

    @deserializeAs('joinsLevels')
    private _joinsLevels: string;

    @deserializeAs('category')
    private _category: any;

    @deserializeAs('purchaseHistory')
    private _purchaseHistory: POImportItem[];

    @deserializeAs('stockDetails')
    private _stockDetails: StockDetails[];

    @deserializeAs('itemStock')
    private _itemStock: ItemStockList[];

    @deserializeAs('itemGroupCode')
    private _itemGroupCode: any;

    @deserializeAs('groupCodeName')
    private _groupCodeName: string;

    constructor() {
        this.isActive = false;
        this.isSelected = false;
        this.isRemoteControl = false;
        this.itemsSeason = false;
        this.isAlert = false;
        this.isVisible = false;
        this.isSellCarton = false;
        this.itemDocs = [];
        this.deletePoImageImageId = [];
        this.deletePoImageId = [];
        this.deletedDocsID = []
        this.tempBullet = [];
        this.breachQty = new BreachQty();
        this.isSaveAsDraft = false;
        this.mediaLinks = []
        this.deletedMediaLinksID = []
        this.purchaseHistory = []
        this.stockDetails = []
        this.itemStock = []
    }

    /**
     * Getter groupCodeName
     * @return {string}
     */
	public get groupCodeName(): string {
		return this._groupCodeName;
	}

    /**
     * Setter groupCodeName
     * @param {string} value
     */
	public set groupCodeName(value: string) {
		this._groupCodeName = value;
	}


    /**
     * Getter itemGroupCode
     * @return {any}
     */
	public get itemGroupCode(): any {
		return this._itemGroupCode;
	}

    /**
     * Setter itemGroupCode
     * @param {any} value
     */
	public set itemGroupCode(value: any) {
		this._itemGroupCode = value;
	}


    /**
     * Getter groupCodeId
     * @return {number}
     */
	public get groupCodeId(): number {
		return this._groupCodeId;
	}

    /**
     * Setter groupCodeId
     * @param {number} value
     */
	public set groupCodeId(value: number) {
		this._groupCodeId = value;
	}


    /**
     * Getter itemStock
     * @return {ItemStockList[]}
     */
	public get itemStock(): ItemStockList[] {
		return this._itemStock;
	}

    /**
     * Setter itemStock
     * @param {ItemStockList[]} value
     */
	public set itemStock(value: ItemStockList[]) {
		this._itemStock = value;
	}

  

    /**
     * Getter stockDetails
     * @return {StockDetails[]}
     */
	public get stockDetails(): StockDetails[] {
		return this._stockDetails;
	}

    /**
     * Setter stockDetails
     * @param {StockDetails[]} value
     */
	public set stockDetails(value: StockDetails[]) {
		this._stockDetails = value;
	}


    /**
     * Getter purchaseHistory
     * @return {POImportItem[]}
     */
	public get purchaseHistory(): POImportItem[] {
		return this._purchaseHistory;
	}

    /**
     * Setter purchaseHistory
     * @param {POImportItem[]} value
     */
	public set purchaseHistory(value: POImportItem[]) {
		this._purchaseHistory = value;
	}
    

    /**
     * Getter categoryName
     * @return {string}
     */
	public get categoryName(): string {
		return this._categoryName;
	}

    /**
     * Setter categoryName
     * @param {string} value
     */
	public set categoryName(value: string) {
		this._categoryName = value;
	}


    /**
     * Getter category
     * @return {any}
     */
	public get category(): any {
		return this._category;
	}

    /**
     * Setter category
     * @param {any} value
     */
	public set category(value: any) {
		this._category = value;
	}


    /**
     * Getter categoryId
     * @return {number}
     */
	public get categoryId(): number {
		return this._categoryId;
	}

    /**
     * Setter categoryId
     * @param {number} value
     */
	public set categoryId(value: number) {
		this._categoryId = value;
	}
    
    /**
     * Getter deletedMediaLinksID
     * @return {number[]}
     */
	public get deletedMediaLinksID(): number[] {
		return this._deletedMediaLinksID;
	}

    /**
     * Getter mediaLinks
     * @return {ItemMediaLink[]}
     */
	public get mediaLinks(): ItemMediaLink[] {
		return this._mediaLinks;
	}

    /**
     * Setter deletedMediaLinksID
     * @param {number[]} value
     */
	public set deletedMediaLinksID(value: number[]) {
		this._deletedMediaLinksID = value;
	}

    /**
     * Setter mediaLinks
     * @param {ItemMediaLink[]} value
     */
	public set mediaLinks(value: ItemMediaLink[]) {
		this._mediaLinks = value;
	}


    /**
     * Getter isSaveAsDraft
     * @return {boolean}
     */
	public get isSaveAsDraft(): boolean {
		return this._isSaveAsDraft;
	}

    /**
     * Setter isSaveAsDraft
     * @param {boolean} value
     */
	public set isSaveAsDraft(value: boolean) {
		this._isSaveAsDraft = value;
	}


    /**
     * Getter supplier
     * @return {Registration[]}
     */
	public get supplier(): Registration[] {
		return this._supplier;
	}

    /**
     * Setter supplier
     * @param {Registration[]} value
     */
	public set supplier(value: Registration[]) {
		this._supplier = value;
	}


    /**
     * Getter levelQty
     * @return {string}
     */
	public get levelQty(): string {
		return this._levelQty;
	}

    /**
     * Setter levelQty
     * @param {string} value
     */
	public set levelQty(value: string) {
		this._levelQty = value;
	}


    /**
     * Getter levelItemMaps
     * @return {any[]}
     */
	public get levelItemMaps(): any[] {
		return this._levelItemMaps;
	}

    /**
     * Setter levelItemMaps
     * @param {any[]} value
     */
	public set levelItemMaps(value: any[]) {
		this._levelItemMaps = value;
	}


    /**
     * Getter color
     * @return {any}
     */
	public get color(): any {
		return this._color;
	}

    /**
     * Setter color
     * @param {any} value
     */
	public set color(value: any) {
		this._color = value;
	}


    /**
     * Getter itemsDimId
     * @return {number}
     */
	public get itemsDimId(): number {
		return this._itemsDimId;
	}

    /**
     * Getter boxDimId
     * @return {number}
     */
	public get boxDimId(): number {
		return this._boxDimId;
	}

    /**
     * Getter itemsWeightDimId
     * @return {number}
     */
	public get itemsWeightDimId(): number {
		return this._itemsWeightDimId;
	}

    /**
     * Getter boxWeightDimId
     * @return {number}
     */
	public get boxWeightDimId(): number {
		return this._boxWeightDimId;
	}

    /**
     * Setter itemsDimId
     * @param {number} value
     */
	public set itemsDimId(value: number) {
		this._itemsDimId = value;
	}

    /**
     * Setter boxDimId
     * @param {number} value
     */
	public set boxDimId(value: number) {
		this._boxDimId = value;
	}

    /**
     * Setter itemsWeightDimId
     * @param {number} value
     */
	public set itemsWeightDimId(value: number) {
		this._itemsWeightDimId = value;
	}

    /**
     * Setter boxWeightDimId
     * @param {number} value
     */
	public set boxWeightDimId(value: number) {
		this._boxWeightDimId = value;
	}


    /**
     * Getter packQty
     * @return {string}
     */
	public get packQty(): string {
		return this._packQty;
	}

    /**
     * Setter packQty
     * @param {string} value
     */
	public set packQty(value: string) {
		this._packQty = value;
	}


    /**
     * Getter colorList
     * @return {any}
     */
	public get colorList(): any {
		return this._colorList;
	}

    /**
     * Setter colorList
     * @param {any} value
     */
	public set colorList(value: any) {
		this._colorList = value;
	}


    /**
     * Getter poColors
     * @return {any}
     */
	public get poColors(): any {
		return this._poColors;
	}

    /**
     * Setter poColors
     * @param {any} value
     */
	public set poColors(value: any) {
		this._poColors = value;
	}


    /**
     * Getter deletePoImageImageId
     * @return {any[]}
     */
	public get deletePoImageImageId(): any[] {
		return this._deletePoImageImageId;
	}

    /**
     * Getter deletePoImageId
     * @return {any[]}
     */
	public get deletePoImageId(): any[] {
		return this._deletePoImageId;
	}

    /**
     * Setter deletePoImageImageId
     * @param {any[]} value
     */
	public set deletePoImageImageId(value: any[]) {
		this._deletePoImageImageId = value;
	}

    /**
     * Setter deletePoImageId
     * @param {any[]} value
     */
	public set deletePoImageId(value: any[]) {
		this._deletePoImageId = value;
	}


    /**
     * Getter breachQty
     * @return {BreachQty}
     */
	public get breachQty(): BreachQty {
		return this._breachQty;
	}

    /**
     * Setter breachQty
     * @param {BreachQty} value
     */
	public set breachQty(value: BreachQty) {
		this._breachQty = value;
	}


    /**
     * Getter mountingType
     * @return {any}
     */
	public get mountingType(): any {
		return this._mountingType;
	}

    /**
     * Setter mountingType
     * @param {any} value
     */
	public set mountingType(value: any) {
		this._mountingType = value;
	}


    /**
     * Getter mountingTypeId
     * @return {any}
     */
	public get mountingTypeId(): any {
		return this._mountingTypeId;
	}

    /**
     * Setter mountingTypeId
     * @param {any} value
     */
	public set mountingTypeId(value: any) {
		this._mountingTypeId = value;
	}


    /**
     * Getter batteryType
     * @return {any}
     */
	public get batteryType(): any {
		return this._batteryType;
	}

    /**
     * Getter packOf
     * @return {any}
     */
	public get packOf(): any {
		return this._packOf;
	}

    /**
     * Setter batteryType
     * @param {any} value
     */
	public set batteryType(value: any) {
		this._batteryType = value;
	}

    /**
     * Setter packOf
     * @param {any} value
     */
	public set packOf(value: any) {
		this._packOf = value;
	}


    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Getter lastModifiedDate
     * @return {string}
     */
	public get lastModifiedDate(): string {
		return this._lastModifiedDate;
	}

    /**
     * Getter displayName
     * @return {string}
     */
	public get displayName(): string {
		return this._displayName;
	}

    /**
     * Getter skuId
     * @return {string}
     */
	public get skuId(): string {
		return this._skuId;
	}

    /**
     * Getter isActive
     * @return {boolean}
     */
	public get isActive(): boolean {
		return this._isActive;
	}

    /**
     * Getter isSelected
     * @return {boolean}
     */
	public get isSelected(): boolean {
		return this._isSelected;
	}

    /**
     * Getter itemPrice
     * @return {number}
     */
	public get itemPrice(): number {
		return this._itemPrice;
	}

    /**
     * Getter itemCarton
     * @return {number}
     */
	public get itemCarton(): number {
		return this._itemCarton;
	}

    /**
     * Getter image
     * @return {string}
     */
	public get image(): string {
		return this._image;
	}

    /**
     * Getter itemName
     * @return {string}
     */
	public get itemName(): string {
		return this._itemName;
	}

    /**
     * Getter marketTypeIds
     * @return {number[]}
     */
	public get marketTypeIds(): number[] {
		return this._marketTypeIds;
	}

    /**
     * Getter isSellCarton
     * @return {boolean}
     */
	public get isSellCarton(): boolean {
		return this._isSellCarton;
	}

    /**
     * Getter isVisible
     * @return {boolean}
     */
	public get isVisible(): boolean {
		return this._isVisible;
	}

    /**
     * Getter isAlert
     * @return {boolean}
     */
	public get isAlert(): boolean {
		return this._isAlert;
	}

    /**
     * Getter days
     * @return {number}
     */
	public get days(): number {
		return this._days;
	}

    /**
     * Getter itemGroupId
     * @return {number}
     */
	public get itemGroupId(): number {
		return this._itemGroupId;
	}

    /**
     * Getter unitId
     * @return {number}
     */
	public get unitId(): number {
		return this._unitId;
	}

    /**
     * Getter hsnCodeId
     * @return {number}
     */
	public get hsnCodeId(): number {
		return this._hsnCodeId;
	}

    /**
     * Getter qcChecklistId
     * @return {number}
     */
	public get qcChecklistId(): number {
		return this._qcChecklistId;
	}

    /**
     * Getter itemLength
     * @return {number}
     */
	public get itemLength(): number {
		return this._itemLength;
	}

    /**
     * Getter itemWidth
     * @return {number}
     */
	public get itemWidth(): number {
		return this._itemWidth;
	}

    /**
     * Getter itemHeight
     * @return {number}
     */
	public get itemHeight(): number {
		return this._itemHeight;
	}

    /**
     * Getter itemsDim
     * @return {any}
     */
	public get itemsDim(): any {
		return this._itemsDim;
	}

    /**
     * Getter boxDim
     * @return {any}
     */
	public get boxDim(): any {
		return this._boxDim;
	}

    /**
     * Getter boxLength
     * @return {number}
     */
	public get boxLength(): number {
		return this._boxLength;
	}

    /**
     * Getter boxWidth
     * @return {number}
     */
	public get boxWidth(): number {
		return this._boxWidth;
	}

    /**
     * Getter boxHeight
     * @return {number}
     */
	public get boxHeight(): number {
		return this._boxHeight;
	}

    /**
     * Getter youtubeLink
     * @return {string}
     */
	public get youtubeLink(): string {
		return this._youtubeLink;
	}

    /**
     * Getter itemsBelongTo
     * @return {string}
     */
	public get itemsBelongTo(): string {
		return this._itemsBelongTo;
	}

    /**
     * Getter itemsWeight
     * @return {number}
     */
	public get itemsWeight(): number {
		return this._itemsWeight;
	}

    /**
     * Getter boxWeight
     * @return {number}
     */
	public get boxWeight(): number {
		return this._boxWeight;
	}

    /**
     * Getter itemsWeightDim
     * @return {any}
     */
	public get itemsWeightDim(): any {
		return this._itemsWeightDim;
	}

    /**
     * Getter boxWeightDim
     * @return {any}
     */
	public get boxWeightDim(): any {
		return this._boxWeightDim;
	}

    /**
     * Getter purchaseRation
     * @return {number}
     */
	public get purchaseRation(): number {
		return this._purchaseRation;
	}

    /**
     * Getter gstItemPrice
     * @return {number}
     */
	public get gstItemPrice(): number {
		return this._gstItemPrice;
	}

    /**
     * Getter gstItemCarton
     * @return {number}
     */
	public get gstItemCarton(): number {
		return this._gstItemCarton;
	}

    /**
     * Getter discounterCoins
     * @return {number}
     */
	public get discounterCoins(): number {
		return this._discounterCoins;
	}

    /**
     * Getter loosesGoodsCoins
     * @return {number}
     */
	public get loosesGoodsCoins(): number {
		return this._loosesGoodsCoins;
	}

    /**
     * Getter seasonMasterId
     * @return {number}
     */
	public get seasonMasterId(): number {
		return this._seasonMasterId;
	}

    /**
     * Getter itemsSeason
     * @return {any}
     */
	public get itemsSeason(): any {
		return this._itemsSeason;
	}

    /**
     * Getter advPODate
     * @return {string}
     */
	public get advPODate(): string {
		return this._advPODate;
	}

    /**
     * Getter tempAdvPODate
     * @return {string}
     */
	public get tempAdvPODate(): string {
		return this._tempAdvPODate;
	}

    /**
     * Getter marketType
     * @return {any}
     */
	public get marketType(): any {
		return this._marketType;
	}

    /**
     * Getter title
     * @return {string}
     */
	public get title(): string {
		return this._title;
	}

    /**
     * Getter searchKeyWords
     * @return {string}
     */
	public get searchKeyWords(): string {
		return this._searchKeyWords;
	}

    /**
     * Getter material
     * @return {string}
     */
	public get material(): string {
		return this._material;
	}

    /**
     * Getter subMaterial
     * @return {string}
     */
	public get subMaterial(): string {
		return this._subMaterial;
	}

    /**
     * Getter sizeMasterId
     * @return {number}
     */
	public get sizeMasterId(): number {
		return this._sizeMasterId;
	}

    /**
     * Getter accessories
     * @return {string}
     */
	public get accessories(): string {
		return this._accessories;
	}

    /**
     * Getter capacity
     * @return {number}
     */
	public get capacity(): number {
		return this._capacity;
	}

    /**
     * Getter numberOfBattery
     * @return {number}
     */
	public get numberOfBattery(): number {
		return this._numberOfBattery;
	}

    /**
     * Getter isRemoteControl
     * @return {boolean}
     */
	public get isRemoteControl(): boolean {
		return this._isRemoteControl;
	}

    /**
     * Getter wattage
     * @return {string}
     */
	public get wattage(): string {
		return this._wattage;
	}

    /**
     * Getter voltage
     * @return {string}
     */
	public get voltage(): string {
		return this._voltage;
	}

    /**
     * Getter bulletPoint
     * @return {any}
     */
	public get bulletPoint(): any {
		return this._bulletPoint;
	}

    /**
     * Getter packOfId
     * @return {any}
     */
	public get packOfId(): any {
		return this._packOfId;
	}


    /**
     * Getter itemsDescription
     * @return {string}
     */
	public get itemsDescription(): string {
		return this._itemsDescription;
	}

    /**
     * Getter singlePieceVolume
     * @return {any}
     */
	public get singlePieceVolume(): any {
		return this._singlePieceVolume;
	}

    /**
     * Getter specialFeatures
     * @return {string}
     */
	public get specialFeatures(): string {
		return this._specialFeatures;
	}

    /**
     * Getter deletedDocsID
     * @return {any[]}
     */
	public get deletedDocsID(): any[] {
		return this._deletedDocsID;
	}

    /**
     * Getter batteryTypeId
     * @return {any}
     */
	public get batteryTypeId(): any {
		return this._batteryTypeId;
	}

    /**
     * Getter itemDocs
     * @return {any[]}
     */
	public get itemDocs(): any[] {
		return this._itemDocs;
	}

    /**
     * Getter itemGroup
     * @return {any}
     */
	public get itemGroup(): any {
		return this._itemGroup;
	}

    /**
     * Getter qcChecklist
     * @return {any}
     */
	public get qcChecklist(): any {
		return this._qcChecklist;
	}

    /**
     * Getter unitMaster
     * @return {any}
     */
	public get unitMaster(): any {
		return this._unitMaster;
	}

    /**
     * Getter hsnCodeMaster
     * @return {any}
     */
	public get hsnCodeMaster(): any {
		return this._hsnCodeMaster;
	}

    /**
     * Getter seasonMaster
     * @return {any}
     */
	public get seasonMaster(): any {
		return this._seasonMaster;
	}

    /**
     * Getter sizeMaster
     * @return {any}
     */
	public get sizeMaster(): any {
		return this._sizeMaster;
	}

    /**
     * Getter itemsMarketTypes
     * @return {any}
     */
	public get itemsMarketTypes(): any {
		return this._itemsMarketTypes;
	}

    /**
     * Getter tempBullet
     * @return {any[]}
     */
	public get tempBullet(): any[] {
		return this._tempBullet;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Setter lastModifiedDate
     * @param {string} value
     */
	public set lastModifiedDate(value: string) {
		this._lastModifiedDate = value;
	}

    /**
     * Setter displayName
     * @param {string} value
     */
	public set displayName(value: string) {
		this._displayName = value;
	}

    /**
     * Setter skuId
     * @param {string} value
     */
	public set skuId(value: string) {
		this._skuId = value;
	}

    /**
     * Setter isActive
     * @param {boolean} value
     */
	public set isActive(value: boolean) {
		this._isActive = value;
	}

    /**
     * Setter isSelected
     * @param {boolean} value
     */
	public set isSelected(value: boolean) {
		this._isSelected = value;
	}

    /**
     * Setter itemPrice
     * @param {number} value
     */
	public set itemPrice(value: number) {
		this._itemPrice = value;
	}

    /**
     * Setter itemCarton
     * @param {number} value
     */
	public set itemCarton(value: number) {
		this._itemCarton = value;
	}

    /**
     * Setter image
     * @param {string} value
     */
	public set image(value: string) {
		this._image = value;
	}

    /**
     * Setter itemName
     * @param {string} value
     */
	public set itemName(value: string) {
		this._itemName = value;
	}

    /**
     * Setter marketTypeIds
     * @param {number[]} value
     */
	public set marketTypeIds(value: number[]) {
		this._marketTypeIds = value;
	}

    /**
     * Setter isSellCarton
     * @param {boolean} value
     */
	public set isSellCarton(value: boolean) {
		this._isSellCarton = value;
	}

    /**
     * Setter isVisible
     * @param {boolean} value
     */
	public set isVisible(value: boolean) {
		this._isVisible = value;
	}

    /**
     * Setter isAlert
     * @param {boolean} value
     */
	public set isAlert(value: boolean) {
		this._isAlert = value;
	}

    /**
     * Setter days
     * @param {number} value
     */
	public set days(value: number) {
		this._days = value;
	}

    /**
     * Setter itemGroupId
     * @param {number} value
     */
	public set itemGroupId(value: number) {
		this._itemGroupId = value;
	}

    /**
     * Setter unitId
     * @param {number} value
     */
	public set unitId(value: number) {
		this._unitId = value;
	}

    /**
     * Setter hsnCodeId
     * @param {number} value
     */
	public set hsnCodeId(value: number) {
		this._hsnCodeId = value;
	}

    /**
     * Setter qcChecklistId
     * @param {number} value
     */
	public set qcChecklistId(value: number) {
		this._qcChecklistId = value;
	}

    /**
     * Setter itemLength
     * @param {number} value
     */
	public set itemLength(value: number) {
		this._itemLength = value;
	}

    /**
     * Setter itemWidth
     * @param {number} value
     */
	public set itemWidth(value: number) {
		this._itemWidth = value;
	}

    /**
     * Setter itemHeight
     * @param {number} value
     */
	public set itemHeight(value: number) {
		this._itemHeight = value;
	}

    /**
     * Setter itemsDim
     * @param {any} value
     */
	public set itemsDim(value: any) {
		this._itemsDim = value;
	}

    /**
     * Setter boxDim
     * @param {any} value
     */
	public set boxDim(value: any) {
		this._boxDim = value;
	}

    /**
     * Setter boxLength
     * @param {number} value
     */
	public set boxLength(value: number) {
		this._boxLength = value;
	}

    /**
     * Setter boxWidth
     * @param {number} value
     */
	public set boxWidth(value: number) {
		this._boxWidth = value;
	}

    /**
     * Setter boxHeight
     * @param {number} value
     */
	public set boxHeight(value: number) {
		this._boxHeight = value;
	}

    /**
     * Setter youtubeLink
     * @param {string} value
     */
	public set youtubeLink(value: string) {
		this._youtubeLink = value;
	}

    /**
     * Setter itemsBelongTo
     * @param {string} value
     */
	public set itemsBelongTo(value: string) {
		this._itemsBelongTo = value;
	}

    /**
     * Setter itemsWeight
     * @param {number} value
     */
	public set itemsWeight(value: number) {
		this._itemsWeight = value;
	}

    /**
     * Setter boxWeight
     * @param {number} value
     */
	public set boxWeight(value: number) {
		this._boxWeight = value;
	}

    /**
     * Setter itemsWeightDim
     * @param {any} value
     */
	public set itemsWeightDim(value: any) {
		this._itemsWeightDim = value;
	}

    /**
     * Setter boxWeightDim
     * @param {any} value
     */
	public set boxWeightDim(value: any) {
		this._boxWeightDim = value;
	}

    /**
     * Setter purchaseRation
     * @param {number} value
     */
	public set purchaseRation(value: number) {
		this._purchaseRation = value;
	}

    /**
     * Setter gstItemPrice
     * @param {number} value
     */
	public set gstItemPrice(value: number) {
		this._gstItemPrice = value;
	}

    /**
     * Setter gstItemCarton
     * @param {number} value
     */
	public set gstItemCarton(value: number) {
		this._gstItemCarton = value;
	}

    /**
     * Setter discounterCoins
     * @param {number} value
     */
	public set discounterCoins(value: number) {
		this._discounterCoins = value;
	}

    /**
     * Setter loosesGoodsCoins
     * @param {number} value
     */
	public set loosesGoodsCoins(value: number) {
		this._loosesGoodsCoins = value;
	}

    /**
     * Setter seasonMasterId
     * @param {number} value
     */
	public set seasonMasterId(value: number) {
		this._seasonMasterId = value;
	}

    /**
     * Setter itemsSeason
     * @param {any} value
     */
	public set itemsSeason(value: any) {
		this._itemsSeason = value;
	}

    /**
     * Setter advPODate
     * @param {string} value
     */
	public set advPODate(value: string) {
		this._advPODate = value;
	}

    /**
     * Setter tempAdvPODate
     * @param {string} value
     */
	public set tempAdvPODate(value: string) {
		this._tempAdvPODate = value;
	}

    /**
     * Setter marketType
     * @param {any} value
     */
	public set marketType(value: any) {
		this._marketType = value;
	}

    /**
     * Setter title
     * @param {string} value
     */
	public set title(value: string) {
		this._title = value;
	}

    /**
     * Setter searchKeyWords
     * @param {string} value
     */
	public set searchKeyWords(value: string) {
		this._searchKeyWords = value;
	}

    /**
     * Setter material
     * @param {string} value
     */
	public set material(value: string) {
		this._material = value;
	}

    /**
     * Setter subMaterial
     * @param {string} value
     */
	public set subMaterial(value: string) {
		this._subMaterial = value;
	}

    /**
     * Setter sizeMasterId
     * @param {number} value
     */
	public set sizeMasterId(value: number) {
		this._sizeMasterId = value;
	}

    /**
     * Setter accessories
     * @param {string} value
     */
	public set accessories(value: string) {
		this._accessories = value;
	}

    /**
     * Setter capacity
     * @param {number} value
     */
	public set capacity(value: number) {
		this._capacity = value;
	}

    /**
     * Setter numberOfBattery
     * @param {number} value
     */
	public set numberOfBattery(value: number) {
		this._numberOfBattery = value;
	}

    /**
     * Setter isRemoteControl
     * @param {boolean} value
     */
	public set isRemoteControl(value: boolean) {
		this._isRemoteControl = value;
	}

    /**
     * Setter wattage
     * @param {string} value
     */
	public set wattage(value: string) {
		this._wattage = value;
	}

    /**
     * Setter voltage
     * @param {string} value
     */
	public set voltage(value: string) {
		this._voltage = value;
	}

    /**
     * Setter bulletPoint
     * @param {any} value
     */
	public set bulletPoint(value: any) {
		this._bulletPoint = value;
	}

    /**
     * Setter packOfId
     * @param {any} value
     */
	public set packOfId(value: any) {
		this._packOfId = value;
	}


    /**
     * Setter itemsDescription
     * @param {string} value
     */
	public set itemsDescription(value: string) {
		this._itemsDescription = value;
	}

    /**
     * Setter singlePieceVolume
     * @param {any} value
     */
	public set singlePieceVolume(value: any) {
		this._singlePieceVolume = value;
	}

    /**
     * Setter specialFeatures
     * @param {string} value
     */
	public set specialFeatures(value: string) {
		this._specialFeatures = value;
	}


    /**
     * Setter deletedDocsID
     * @param {any[]} value
     */
	public set deletedDocsID(value: any[]) {
		this._deletedDocsID = value;
	}

    /**
     * Setter batteryTypeId
     * @param {any} value
     */
	public set batteryTypeId(value: any) {
		this._batteryTypeId = value;
	}

    /**
     * Setter itemDocs
     * @param {any[]} value
     */
	public set itemDocs(value: any[]) {
		this._itemDocs = value;
	}

    /**
     * Setter itemGroup
     * @param {any} value
     */
	public set itemGroup(value: any) {
		this._itemGroup = value;
	}

    /**
     * Setter qcChecklist
     * @param {any} value
     */
	public set qcChecklist(value: any) {
		this._qcChecklist = value;
	}

    /**
     * Setter unitMaster
     * @param {any} value
     */
	public set unitMaster(value: any) {
		this._unitMaster = value;
	}

    /**
     * Setter hsnCodeMaster
     * @param {any} value
     */
	public set hsnCodeMaster(value: any) {
		this._hsnCodeMaster = value;
	}

    /**
     * Setter seasonMaster
     * @param {any} value
     */
	public set seasonMaster(value: any) {
		this._seasonMaster = value;
	}

    /**
     * Setter sizeMaster
     * @param {any} value
     */
	public set sizeMaster(value: any) {
		this._sizeMaster = value;
	}

    /**
     * Setter itemsMarketTypes
     * @param {any} value
     */
	public set itemsMarketTypes(value: any) {
		this._itemsMarketTypes = value;
	}

    /**
     * Setter tempBullet
     * @param {any[]} value
     */
	public set tempBullet(value: any[]) {
		this._tempBullet = value;
	}


    /**
     * Getter joinsLevels
     * @return {string}
     */
	public get joinsLevels(): string {
		return this._joinsLevels;
	}

    /**
     * Setter joinsLevels
     * @param {string} value
     */
	public set joinsLevels(value: string) {
		this._joinsLevels = value;
	}

    
}

export class BreachQty {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('branchTransferAlert')
    @deserializeAs('branchTransferAlert')
    private _branchTransferAlert: number;
    
    @serializeAs('branchReorderQty')
    @deserializeAs('branchReorderQty')
    private _branchReorderQty: number;

    @serializeAs('blockBranchTransferQty')
    @deserializeAs('blockBranchTransferQty')
    private _blockBranchTransferQty: number;

    @serializeAs('blockSalesQty')
    @deserializeAs('blockSalesQty')
    private _blockSalesQty: number;

    @serializeAs('mainWarehouseAlert')
    @deserializeAs('mainWarehouseAlert')
    private _mainWarehouseAlert: number;

    @serializeAs('warehouseReorderQty')
    @deserializeAs('warehouseReorderQty')
    private _warehouseReorderQty: number;

    @serializeAs('rackBreachQty')
    @deserializeAs('rackBreachQty')
    private _rackBreachQty: number;

    @serializeAs('rackRefillQty')
    @deserializeAs('rackRefillQty')
    private _rackRefillQty: number;

    constructor() {
        this.id = null;
    }


    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}


    /**
     * Getter branchTransferAlert
     * @return {number}
     */
	public get branchTransferAlert(): number {
		return this._branchTransferAlert;
	}

    /**
     * Setter branchTransferAlert
     * @param {number} value
     */
	public set branchTransferAlert(value: number) {
		this._branchTransferAlert = value;
	}

    /**
     * Getter branchReorderQty
     * @return {number}
     */
	public get branchReorderQty(): number {
		return this._branchReorderQty;
	}

    /**
     * Setter branchReorderQty
     * @param {number} value
     */
	public set branchReorderQty(value: number) {
		this._branchReorderQty = value;
	}

    /**
     * Getter blockBranchTransferQty
     * @return {number}
     */
	public get blockBranchTransferQty(): number {
		return this._blockBranchTransferQty;
	}

    /**
     * Setter blockBranchTransferQty
     * @param {number} value
     */
	public set blockBranchTransferQty(value: number) {
		this._blockBranchTransferQty = value;
	}

    /**
     * Getter blockSalesQty
     * @return {number}
     */
	public get blockSalesQty(): number {
		return this._blockSalesQty;
	}

    /**
     * Setter blockSalesQty
     * @param {number} value
     */
	public set blockSalesQty(value: number) {
		this._blockSalesQty = value;
	}

    /**
     * Getter mainWarehouseAlert
     * @return {number}
     */
	public get mainWarehouseAlert(): number {
		return this._mainWarehouseAlert;
	}

    /**
     * Setter mainWarehouseAlert
     * @param {number} value
     */
	public set mainWarehouseAlert(value: number) {
		this._mainWarehouseAlert = value;
	}

    /**
     * Getter warehouseReorderQty
     * @return {number}
     */
	public get warehouseReorderQty(): number {
		return this._warehouseReorderQty;
	}

    /**
     * Setter warehouseReorderQty
     * @param {number} value
     */
	public set warehouseReorderQty(value: number) {
		this._warehouseReorderQty = value;
	}

    /**
     * Getter rackBreachQty
     * @return {number}
     */
	public get rackBreachQty(): number {
		return this._rackBreachQty;
	}

    /**
     * Setter rackBreachQty
     * @param {number} value
     */
	public set rackBreachQty(value: number) {
		this._rackBreachQty = value;
	}

    /**
     * Getter rackRefillQty
     * @return {number}
     */
	public get rackRefillQty(): number {
		return this._rackRefillQty;
	}

    /**
     * Setter rackRefillQty
     * @param {number} value
     */
	public set rackRefillQty(value: number) {
		this._rackRefillQty = value;
	}
    
}

export class DiscountSetting {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('discount')
    @deserializeAs('discount')
    private _discount: number;

    @serializeAs('orderQtyTo')
    @deserializeAs('orderQtyTo')
    private _orderQtyTo: number;

    @serializeAs('orderQtyFrom')
    @deserializeAs('orderQtyFrom')
    private _orderQtyFrom: number;

    constructor() {
        this.discount = null;
        this.orderQtyFrom = null;
        this.orderQtyTo = null;
    }


    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}


    /**
     * Getter discount
     * @return {number}
     */
	public get discount(): number {
		return this._discount;
	}

    /**
     * Setter discount
     * @param {number} value
     */
	public set discount(value: number) {
		this._discount = value;
	}

    /**
     * Getter orderQtyTo
     * @return {number}
     */
	public get orderQtyTo(): number {
		return this._orderQtyTo;
	}

    /**
     * Setter orderQtyTo
     * @param {number} value
     */
	public set orderQtyTo(value: number) {
		this._orderQtyTo = value;
	}

    /**
     * Getter orderQtyFrom
     * @return {number}
     */
	public get orderQtyFrom(): number {
		return this._orderQtyFrom;
	}

    /**
     * Setter orderQtyFrom
     * @param {number} value
     */
	public set orderQtyFrom(value: number) {
		this._orderQtyFrom = value;
	}


}

export class PackingInfo {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('name')
    @deserializeAs('name')
    private _name: string;

    @serializeAs('qtyRangeFrom')
    @deserializeAs('qtyRangeFrom')
    private _qtyRangeFrom: number;

    @serializeAs('qtyRangeTo')
    @deserializeAs('qtyRangeTo')
    private _qtyRangeTo: number;

    @serializeAs('packingTypeId')
    @deserializeAs('packingTypeId')
    private _packingTypeId: number;

    // @serializeAs('packingMaster')
    @deserializeAs('packingMaster')
    private _packingMaster: any;

    @deserializeAs('packingDropdown')
    private _packingDropdown: any[];

    constructor() {
        this.packingDropdown = []
    }


    /**
     * Getter packingDropdown
     * @return {any[]}
     */
	public get packingDropdown(): any[] {
		return this._packingDropdown;
	}

    /**
     * Setter packingDropdown
     * @param {any[]} value
     */
	public set packingDropdown(value: any[]) {
		this._packingDropdown = value;
	}
    

    /**
     * Getter packingMaster
     * @return {any}
     */
	public get packingMaster(): any {
		return this._packingMaster;
	}

    /**
     * Setter packingMaster
     * @param {any} value
     */
	public set packingMaster(value: any) {
		this._packingMaster = value;
	}


    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Getter name
     * @return {string}
     */
	public get name(): string {
		return this._name;
	}

    /**
     * Setter name
     * @param {string} value
     */
	public set name(value: string) {
		this._name = value;
	}

    /**
     * Getter qtyRangeFrom
     * @return {number}
     */
	public get qtyRangeFrom(): number {
		return this._qtyRangeFrom;
	}

    /**
     * Setter qtyRangeFrom
     * @param {number} value
     */
	public set qtyRangeFrom(value: number) {
		this._qtyRangeFrom = value;
	}

    /**
     * Getter qtyRangeTo
     * @return {number}
     */
	public get qtyRangeTo(): number {
		return this._qtyRangeTo;
	}

    /**
     * Setter qtyRangeTo
     * @param {number} value
     */
	public set qtyRangeTo(value: number) {
		this._qtyRangeTo = value;
	}

    /**
     * Getter packingTypeId
     * @return {number}
     */
	public get packingTypeId(): number {
		return this._packingTypeId;
	}

    /**
     * Setter packingTypeId
     * @param {number} value
     */
	public set packingTypeId(value: number) {
		this._packingTypeId = value;
	}

    

}

export class ItemMediaLink {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('link')
    @deserializeAs('link')
    private _link: string;

    constructor() {}


    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}
    

    /**
     * Getter link
     * @return {string}
     */
	public get link(): string {
		return this._link;
	}

    /**
     * Setter link
     * @param {string} value
     */
	public set link(value: string) {
		this._link = value;
	}

}

export class ItemStockList {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('warehouseName')
    @deserializeAs('warehouseName')
    private _warehouseName: string;

    @serializeAs('isExpand')
    @deserializeAs('isExpand')
    private _isExpand: boolean;

    @deserializeAs('stockItems')
    private _stockItems: StockDetails[];

    constructor() {
        this.isExpand = false;
        this.stockItems = []
    }


    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Getter warehouseName
     * @return {string}
     */
	public get warehouseName(): string {
		return this._warehouseName;
	}

    /**
     * Setter warehouseName
     * @param {string} value
     */
	public set warehouseName(value: string) {
		this._warehouseName = value;
	}

    /**
     * Getter isExpand
     * @return {boolean}
     */
	public get isExpand(): boolean {
		return this._isExpand;
	}

    /**
     * Setter isExpand
     * @param {boolean} value
     */
	public set isExpand(value: boolean) {
		this._isExpand = value;
	}

    /**
     * Getter stockItems
     * @return {StockDetails[]}
     */
	public get stockItems(): StockDetails[] {
		return this._stockItems;
	}

    /**
     * Setter stockItems
     * @param {StockDetails[]} value
     */
	public set stockItems(value: StockDetails[]) {
		this._stockItems = value;
	}

  
    
}


