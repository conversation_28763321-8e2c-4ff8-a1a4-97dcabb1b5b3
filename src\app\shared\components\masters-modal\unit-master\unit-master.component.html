<div class="modal-body">
  <div class="row" [formGroup]="unitGroup">
    <div class="col-12">
      <div class="form-group required">
        <label class="form-label">Unit Name</label>
        <input id="f" formControlName="name" type="text" class="form-control"
          [maxlength]="utilsService.validationService.MAX_50" placeholder="Enter Unit Name"
          [(ngModel)]="unitObj.unitName">
        <div class="message error-message"
          *ngIf="unitGroup.controls['name'].hasError('required') &&  unitGroup.controls['name'].touched">
          {{utilsService.validationService.UNIT_NAME_REQ}}
        </div>
        <div class="message error-message"
          *ngIf="!unitGroup.controls['name'].hasError('required') && !unitGroup.controls['name'].valid && unitGroup.controls['name'].touched">
          {{utilsService.validationService.UNIT_NAME_INVALID}}
        </div>
      </div>
    </div>
    <div class="col-12">
      <div class="form-group required">
        <label class="form-label">Short Code </label>
        <input formControlName="short_code" type="text" class="form-control"
          [maxlength]="utilsService.validationService.MAX_10" placeholder="Enter Short Code"
          [(ngModel)]="unitObj.shortCode">
        <div class="message error-message"
          *ngIf="unitGroup.controls['short_code'].hasError('required') && unitGroup.controls['short_code'].touched">
          {{utilsService.validationService.UNIT_SHORT_CODE_REQ}}
        </div>
      </div>
    </div>
    <div class="col-6">
      <div class="form-group theme-ngselect  required">
        <div class="form-label">Category</div>
        <ng-select class="" placeholder="Select Category" formControlName="category" [multiple]="false"
          [clearable]="false" [items]="allCategories" bindLabel="label" bindValue="value"
          [(ngModel)]="unitObj.unitMasterCategory" (change)="onChangeCategory()">
        </ng-select>
        <div class="message error-message"
          *ngIf="unitGroup.controls['category'].hasError('required') && unitGroup.controls['category'].touched">
          {{utilsService.validationService.UNIT_CATEGORY_REQUIRED}}
        </div>
      </div>
    </div>
    <div class="col-6">
      <div class="form-group">
        <label class="form-label">Base Unit </label>
        <input type="text" class="form-control" placeholder="Enter Short Code" disabled [value]="unitObj.baseUnit">
      </div>
    </div>
    <div class="col-12">
      <div class="form-group required">
        <label class="form-label">Factor</label>
        <input mask="separator.4" thousandSeparator="" formControlName="conversionToMeter"
          type="text" class="form-control" [maxlength]="utilsService.validationService.MAX_12"
          placeholder="Enter Factor value" [(ngModel)]="unitObj.conversionToMeter">
        <div class="message error-message"
          *ngIf="unitGroup.controls['conversionToMeter'].hasError('required') && unitGroup.controls['conversionToMeter'].touched">
          {{utilsService.validationService.UNIT_FACTOR_REQUIRED}}
        </div>
      </div>
    </div>
  </div>
</div>
<div class="modal-footer">
  <div class="modal-footer-group full-width-btn">
    <button (click)="onSaveUnit.emit()" type="button" class="btn btn-primary btn-icon-text"> <i
        class="th th-outline-tick-circle"></i>Save</button>
    <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
  </div>
</div>