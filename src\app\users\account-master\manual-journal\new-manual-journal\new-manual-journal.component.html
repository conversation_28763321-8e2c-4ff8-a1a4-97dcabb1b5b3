<div class="page-content"
  [pageAccess]="{page: utilsService.enumForPage.ACCOUNTS, action: utilsService.enumForPage.VIEW_ACCOUNTS, view: true}">
  <div class="page-title-wrapper">
    <div class="page-title-left">
      <h4>{{mjId ? 'Edit' : 'Add New'}} Manual Journal</h4>
    </div>
    <div class="page-title-right">
      <button class="btn btn-sm btn-icon btn-outline-white" [routerLink]="['/users/accounts/manual-journal']"
        ngbTooltip="Close" placement="left" container="body" triggers="hover">
        <i class="th th-close"></i>
      </button>
    </div>
  </div>
  <div class="content-area">
    <div class="card card-theme card-forms">
      <div class="card-body" [formGroup]="mjFormGroup">
        <div class="row">
          <div class="col-lg-4 col-md-6 col-sm-12">
            <div class="form-group form-group-inline-control required">
              <label class="form-label">Date</label>
              <div class="form-control-wrapper">
                <!-- <div class="form-group-icon-end"> -->
                  <!-- <i class="th th-outline-calendar-1"></i> -->
                  <app-date-time-picker formControlName="date" [(ngModel)]="mjObj.tempDate" 
                    [placeholder]="'Select Date'" [timer]="false" [displayFormat]="'DD/MM/YYYY'" 
                    [outputDateFormat]="'DD-MM-YYYY'"
                    [minDate]="minDateNGB" [maxDate]="maxDateNGB"/>
                  <!-- <input (click)="e.toggle()" (keydown.space)="e.toggle()" [(ngModel)]="mjObj.tempDate" [minDate]="minDateNGB"
                    [maxDate]="maxDateNGB" formControlName="date" readonly type="text" class="form-control" placeholder="dd/mm/yyyy"
                    ngbDatepicker #e="ngbDatepicker"> -->
                <!-- </div> -->
                <div class="message error-message"
                  *ngIf="mjFormGroup.controls['date'].hasError('required') &&  mjFormGroup.controls['date'].touched">
                  {{utilsService.validationService.DATE_REQ}}
                </div>
              </div>
            </div>
            <div class="form-group form-group-inline-control">
              <label class="form-label">Note</label>
              <div class="form-control-wrapper">
                <textarea [maxlength]="utilsService.validationService.MAX_150" [(ngModel)]="mjObj.note"
                  formControlName="note" class="form-control" placeholder="Enter note"></textarea>
                <div class="message error-message"
                  *ngIf="mjFormGroup.controls['note'].hasError('required') &&  mjFormGroup.controls['note'].touched">
                  {{utilsService.validationService.NOTE_REQ}}
                </div>
                <div class="message error-message"
                  *ngIf="!mjFormGroup.controls['note'].hasError('required') && !mjFormGroup.controls['note'].valid && mjFormGroup.controls['note'].touched">
                  {{utilsService.validationService.NOTE_INVALID}}
                </div>
              </div>
            </div>
            <!-- <div class="form-group form-group-inline-control">
                        <label class="form-label"></label>
                        <div class="form-control-wrapper">
                          <div class="checkbox checkbox-primary checkbox-small">
                            <input type="checkbox" id="cashbasedjournal" class="material-inputs filled-in" />
                            <label for="cashbasedjournal">Cash based Journal</label>
                          </div>
                        </div>
                      </div> -->
          </div>
        </div>
        <div class="form-group required" formArrayName="accountInfo">
          <label class="form-label">Accounts<i class="th th-outline-info-circle ms-1"
              ngbTooltip="Please ensure that the Debits and Credits are equal" placement="bottom" container="body"
              triggers="hover"></i>
          </label>
          <div class="table-responsive">
            <table class="table-theme table-hover table table-bordered ">
              <thead class="border-less">
                <tr>
                  <th>Account Name</th>
                  <th>Debit</th>
                  <th>Credit</th>
                  <th class="text-center">Action</th>
                </tr>
              </thead>
              <tbody>
                <!-- <tr *ngFor="let item of [1,2,3,4,5,6]">
                  <td class="tbl-form-group-borderless">
                    <div class="form-group theme-ngselect">
                      <ng-select placeholder="Frequency" [multiple]="false" [clearable]="false" [items]="demo"
                        bindLabel="name" bindValue="id" [(ngModel)]="selectedDemo1" appendTo="body">
                      </ng-select>
                    </div>
                  </td>
                  <td>5000</td>
                  <td>500</td>
                </tr> -->

                <tr class="tbl-add-row" *ngFor="let account of accountInfo.controls; index as i" [formGroupName]="i">
                  <td class="tbl-form-group">
                    <div class="form-group theme-ngselect">
                      <ng-select class="" placeholder="Select Account" [multiple]="false" [clearable]="false"
                        [ngClass]="{'required': account.get('accountId').invalid && account.get('accountId').touched}" [items]="accountDropdown"
                        bindLabel="label" [(ngModel)]="mjObj.accountRequestList[i].accountId" bindValue="value"
                        formControlName="accountId" appendTo="body">
                      </ng-select>
                    </div>
                  </td>
                  <td class="tbl-form-group">
                    <div class="form-group"
                      [ngClass]="{'form-error': account.get('debit').invalid && account.get('debit').touched}">
                      <input (change)="onDebitInput(i)" (input)="onInputChange()" mask="separator.3"
                        thousandSeparator="" [maxlength]="utilsService.validationService.MAX_10"
                        [(ngModel)]="mjObj.accountRequestList[i].debit" formControlName="debit" type="text"
                        class="form-control" placeholder="Enter Debit">
                    </div>
                  </td>
                  <td class="tbl-form-group">
                    <div class="form-group"
                      [ngClass]="{'form-error': account.get('credit').invalid && account.get('credit').touched}">
                      <input (change)="onCreditInput(i)" (input)="onInputChange()" mask="separator.3"
                        thousandSeparator="" [maxlength]="utilsService.validationService.MAX_10"
                        [(ngModel)]="mjObj.accountRequestList[i].credit" formControlName="credit" type="text"
                        class="form-control" placeholder="Enter Credit">
                    </div>
                  </td>
                  <td class="tbl-action">
                    <div class="tbl-action-group">
                      <button (click)="openRemoveModal(i)" class="btn btn-xs btn-light-danger btn-icon"
                        ngbTooltip="Remove Account" placement="left" container="body" triggers="hover">
                        <i class="th th-outline-trash"></i>
                      </button>
                    </div>
                  </td>
                </tr>
                <tr *ngIf="utilsService.isEmptyObjectOrNullUndefined(mjObj.accountRequestList)">
                  <td colspan="20" class="text-center">
                    <span class="truncate">{{utilsService.serverVariableService.STRING_WHEN_NO_RECORDS_FOUND}}</span>
                  </td>
                </tr>
              </tbody>
              <tfoot>
                <tr class="tbl-total-row" *ngIf="mjObj.accountRequestList.length > 0">
                  <th>Total</th>
                  <th>{{debitTotal | number}}</th>
                  <th colspan="2">{{creditTotal | number}}</th>
                </tr>
                <tr class="tbl-add-new">
                  <td colspan="100">
                    <button (click)="addAccount()" class="btn btn-sm btn-link btn-icon-text text-primary"> <i
                        class="th-bold-add-circle"></i>
                      Add New Row
                    </button>
                  </td>
                </tr>
              </tfoot>
            </table>
          </div>
        </div>
      </div>
    </div>
    <div class='bottombar-wrapper bottom-fixed'>
      <div class='bottombar-container'>
        <div class='bottombar-left'>
          <button (click)="onSave()" type="button" class="btn btn-primary btn-icon-text btn-sm"> <i
              class="th th-outline-tick-circle"></i>
            {{mjId ? 'Update' : 'Save'}}</button>
          <button [routerLink]="['/users/accounts/manual-journal']" type="button"
            class="btn btn-outline-white btn-icon-text btn-sm"><i class="th th-outline-close-circle"></i>Cancel</button>
        </div>
        <div class='bottombar-right'>

        </div>
      </div>
    </div>
  </div>
</div>

<!-- ----------------------------------------------------------------------- -->
<!--                           Delete Modal Start                            -->
<!-- ----------------------------------------------------------------------- -->
<div class="modal modal-theme modal-confirmation modal-reject fade" id="deleteMJAccount" tabindex="-1"
  aria-labelledby="deleteMJAccountLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
            class='th th-close'></i></button>
      </div>
      <div class="modal-body">
        <div class="modal-confirmation-container">
          <div class="modal-confirmation-icon">
            <i class="th th-bold-trash"></i>
          </div>
          <div class="modal-confirmation-content">
            <h5>Are You Sure?</h5>
            <p>You want to remove the Account from Manual Journal.</p>
          </div>
        </div>
        <div class="modal-button-group modal-full-width-btn">
          <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
          <button (click)="removeAccount()" type="button" class="btn btn-primary btn-icon-text"> <i
              class="th th-outline-tick-circle"></i>
            Remove</button>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                           Delete Modal End                            -->
<!-- ----------------------------------------------------------------------- -->