.dropdown {
    .dropdown-toggle {
        padding-right: 30px;
        position: relative;

        &::after {
            content: '\F282';
            font-family: bootstrap-icons !important;
            border: 0;
            margin: 0;
            -webkit-text-stroke: 0.5px;
            right: 8px;
            position: absolute;
            font-size: 12px;
        }
    }

    .dropdown-menu {
        -webkit-box-shadow: 0 5px 10px rgba(30, 32, 37, .12);
        box-shadow: 0 5px 10px rgba(30, 32, 37, .12);
        -webkit-animation-name: DropDownSlide;
        animation-name: DropDownSlide;
        -webkit-animation-duration: .3s;
        animation-duration: .3s;
        -webkit-animation-fill-mode: both;
        animation-fill-mode: both;
        position: absolute;
        z-index: 1000;
        border: 1px solid #E6EAEE;
        border-radius: 10px;
        width: 220px;
        max-width: 220px;
        padding: 10px;


        ul {
            list-style: none;
            padding-left: 0px;
            height: calc(100% - 33px);
        }

        .dropdown-header {
            font-size: 14px;
            font-weight: 500;
            padding: 4px 6px;
            color: $text_color;
        }

        .dropdown-footer {
            text-align: center;
            font-size: 14px;
            cursor: pointer;
            font-weight: 500;
            padding: 6px 12px;
            color: #777F90;
            border-radius: 7px;
            background-color: #2372F42e;
        }

        .dropdown-item {
            font-size: 14px;
            cursor: pointer;
            font-weight: 500;
            padding: 7px 12px;
            color: #777F90;
            border-radius: 7px;
            display: flex;
            align-items: center;
            white-space: normal;
            gap: 0 6px;

            &:hover {
                background-color: #F4F5F6;
                color: $primary_color;
            }

            i {
                font-size: 16px;
                vertical-align: middle;
                margin-right: 6px;
            }

        }



    }


}

@-webkit-keyframes DropDownSlide {
    100% {
        margin-top: -1px
    }

    0% {
        margin-top: 8px
    }
}

@keyframes DropDownSlide {
    100% {
        margin-top: -1px
    }

    0% {
        margin-top: 8px
    }
}

@-webkit-keyframes DropDownSlideDown {
    100% {
        margin-bottom: 0
    }

    0% {
        margin-bottom: 8px
    }
}

@keyframes DropDownSlideDown {
    100% {
        margin-bottom: 0
    }

    0% {
        margin-bottom: 8px
    }
}

.more-dropdown {
    .btn-more {
        height: 24px;
        width: 24px;
        min-width: 24px;
        font-size: 16px;
        background-color: #F4F5F6;
        border: 1px solid #F4F5F6;
        color: #777F90;

        &.btn-xs {
            height: 28px;
            width: 28px;
            font-size: 14px;
        }

        &.btn-light-success {
            background-color: #D2EDE0;
            border: 1px solid #D2EDE0;
            color: $black_color;

            &:hover,
            &.btn.active,
            &.btn:active,
            &.btn.disabled,
            &.btn.show,
            &:disabled,
            &:focus-visible,
            &:first-child:active {
                background-color: $success_color;
                border: 1px solid $success_color;
                color: $black_color;
            }
        }
    }
}

.export-dropdown {
    .dropdown-toggle {
        padding-right: 34px;
    }

    .btn {
        min-height: 31px;
        font-weight: 400;
        color: $text_color;

        &:hover,
        &:focus-visible,
        &:focus {
            &::after {
                border-left-color: $stock_dark;
            }
        }

        &::after {
            border-left: 1px solid $stock_light;
            padding-left: 6px;
        }
    }
}

/* -------------------------------------------------------------------------- */
/*                             Team Dropdown Start                            */
/* -------------------------------------------------------------------------- */
.dropdown-teams {
    .btn-team {
        background-color: transparent !important;
        border: 0;
        padding: 0;
    }

    .dropdown-menu {
        min-width: 220px;
        max-height: 210px;
        overflow-y: auto;
    }



    &.dropup {
        &:hover {
            .dropdown-menu {
                display: inline-masonry;
            }
        }
    }

    &:hover {
        .dropdown-menu {
            display: block
        }
    }

    .team-profile-container {
        display: flex;
        flex-direction: row;
        align-items: center;

        .team-profile-image {
            margin-right: 8px;
            height: 25px;
            width: 25px;
            overflow: hidden;
            border-radius: 50%;
            background-color: #B9C4E1;

            img {
                height: 25px;
                width: 25px;
                border-radius: 50%;
            }
        }

        .team-profile-content {
            h6 {
                font-size: 13px;
                margin-bottom: 0px;
                color: $black_color;
                font-weight: 500;
            }

            p {
                margin-bottom: 0;
                font-size: 12px;
                font-weight: 500;
                color: $text_color;
            }
        }
    }
}

/* -------------------------------------------------------------------------- */
/*                              Team Dropdown End                             */
/* -------------------------------------------------------------------------- */



/* -------------------------- dropdown-notification ------------------------- */
.dropdown-notification {
    .dropdown-menu {
        width: 450px;
        max-width: 450px;
        padding: 0px;
        overflow: hidden;
    }


    .dropdown-notification-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 14px;
        border-bottom: 1px solid $stock_light;

        .notification-header-right,
        .notification-header-left {
            display: flex;
            align-items: center;
            gap: 0 10px;
        }

        .notification-header-link {
            font-size: 14px;
            font-weight: 600;
            line-height: 18.2px;
            color: $primary_color;
            margin-bottom: 0px;

            &:hover {
                text-decoration: underline;
            }

        }

        .notification-header-title {
            h3 {
                font-size: 14px;
                font-weight: 600;
                line-height: 18.2px;
                color: $text_color;
                margin-bottom: 0px;
            }
        }
    }

    .dropdown-notification-body {
        max-height: 560px;
        overflow-y: auto;
    }


    .dropdown-notification-footer {
        background-color: $primary_light_color;
        padding: 12px;

        a {
            color: $primary_color;
            width: 100%;
            font-size: 16px;
            font-weight: 500;
            line-height: 19.2px;
            display: block;
            text-align: center;

        }
    }

    .nav-tabs-outer {
        .tab-pane {
            padding: 20px;
        }
    }


    .notification-list {


        .notification-list-title {
            margin-bottom: 15px;

            h6 {
                font-size: 12px;
                font-weight: 600;
                line-height: 15.6px;
                text-align: left;
                color: $text_color;
                margin-bottom: 0px;
            }


        }

        .notification-list-item {
            display: flex;
            align-items: center;
            gap: 0 10px;
            padding-bottom: 15px;
            margin-bottom: 15px;
            position: relative;


            .notification-list-icon {
                width: 37px;
                min-width: 37px;
                height: 37px;
                background-color: $primary_light_color;
                color: $primary_color;
                border-radius: 8px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 14px;
                font-weight: 600;
                line-height: 18.2px;
                text-align: center;

            }

            .notification-list-content {
                .notification-list-text {
                    p {
                        font-size: 14px;
                        font-weight: 400;
                        line-height: 23.8px;
                        text-align: left;
                        margin-bottom: 0px;

                        b {
                            font-weight: 700;
                        }
                    }
                }
            }


            .notification-list-action {
                display: flex;
                align-items: center;
                gap: 0 5px;

                .notification-time {
                    font-size: 12px;
                    font-weight: 400;
                    line-height: 20.4px;
                    margin-bottom: 0px;
                    text-wrap: nowrap;

                }
            }

            &::before {
                content: "";
                position: absolute;
                left: 50%;
                transform: translateX(-50%);
                bottom: 0px;
                width: 100%;
                max-width: 329px;
                height: 1px;
                background-color: $stock_light;
            }
        }
    }

}

/* -------------------------- dropdown-notification ------------------------- */

/* -------------------------- table-filter dropdown start ------------------------- */
.table-filter {
    &.tbl-category-dropdown {
        .table-filter-dialog {
            min-width: 500px;
            padding: 0;

            @media only screen and (max-width: 767px) {
                max-width: max-content;
            }
        }

        .table-filter-content {
            border-radius: 9px;

            .table-filter-header,
            .table-filter-body,
            .table-filter-footer {
                padding: 15px;
            }

            .table-filter-body {
                padding-bottom: 15px;
            }
        }

        .table-filter-header {
            gap: 8px;

            .filter-title {
                font-size: 12px;
                font-weight: 500;
                margin-bottom: 2px;

                i {
                    margin-right: 2px;
                }
            }

            .filter-search {
                width: 100%;
                max-width: 250px;
            }
        }

        .btn-close {
            background: 0;
            font-size: 17px;
            color: $text_color;
            opacity: 1;
            box-shadow: none;
            border: 0;
            padding: 0;
            height: 31px;
            width: 31px;

            i {
                display: inline-block;
                transition: 0.70s;
                -webkit-text-stroke: 0.5px;
            }

            &:hover {
                background-color: #F4F5F6;
                color: $black_color;

                i {
                    transform: rotate(90deg);
                }
            }
        }



        .dropdown {
            .dropdown-menu {
                .dropdown-item {
                    border-radius: 0;

                    &:hover {
                        background-color: $primary_light_color;
                        color: $primary_color;
                    }

                    &.dropdown-toggle {
                        padding-right: 30px;

                        &::after {
                            transform: rotate(270deg);
                        }
                    }
                }


                ul {
                    box-shadow: 0 5px 10px rgba(30, 32, 37, 0.12);
                    animation-name: inherit;
                    // position: absolute;
                    z-index: 1000;
                    border: 1px solid #E6EAEE;
                    border-radius: 10px;
                    max-width: 220px;
                    padding: 0;
                    height: auto;
                    margin: 0;

                    &:nth-last-child(1n+1) {
                        ul {
                            inset: auto 0 0 0 !important;
                            border-color: #ad2d0d;
                        }
                    }

                    // &:nth-last-child(1n+2) {
                    //     ul {
                    //         // inset: auto 0 0 0 !important;
                    //         border-color: #0e40c9;
                    //         top: -100% !important;
                    //         bottom: auto !important;
                    //     }
                    // }

                    // &:nth-last-child(1n+3) {
                    //     ul {
                    //         // inset: auto 0 0 0 !important;
                    //         border-color: #c90eaa;
                    //         top: -100% !important;
                    //         bottom: auto !important;
                    //     }
                    // }
                }



                // &:nth-child(2) {
                //     ul {
                //         top: -100% !important;
                //         bottom: auto !important;
                //     }
                // }

                // &:nth-child(3) {
                //     ul {
                //         // inset: auto 0 0 0 !important;
                //         top: -100% !important;
                //         bottom: auto !important;
                //     }
                // }
            }
        }





    }

    .table-filter-dialog {
        min-width: 510px;
        padding: 0;

        @media only screen and (max-width: 767px) {
            max-width: max-content;
        }
    }

    .table-filter-content {
        border: 0;
        border-radius: 15px;

        .table-filter-header,
        .table-filter-body,
        .table-filter-footer {
            padding: 15px;
        }

        .table-filter-header {
            padding-bottom: 0;
        }

        .table-filter-body {
            overflow-y: auto;
            max-height: calc(100vh - 350px);
            margin-bottom: 15px;

            ul {
                height: inherit;
            }
        }

        .table-filter-footer {
            padding-top: 0;
        }
    }

    .table-filter-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 8px;

        .filter-title {
            font-size: 18px;
            color: $text_black_color;
            font-weight: 600;
            display: inline-flex;
            align-items: center;

            i {
                margin-right: 4px;
            }
        }
    }

    .table-filter-footer {
        .btn {
            min-width: 90px;
        }

        .table-filter-footer-group {
            margin-top: 0;
            display: flex;
            justify-content: flex-start;
            gap: 8px;
            width: 100%;

            &.modal-full-width-btn {
                .btn {
                    width: 100%;
                }
            }

            &.modal-btn-end {
                justify-content: flex-end;
            }
        }
    }

    .btn-close {
        background: 0;
        font-size: 20px;
        color: $text_color;
        opacity: 1;
        box-shadow: none;
        border: 0;
        padding: 0;
        height: 31px;
        width: 31px;

        i {
            display: inline-block;
            transition: 0.70s;
            -webkit-text-stroke: 0.5px;
        }

        &:hover {
            background-color: #F4F5F6;
            color: $black_color;

            i {
                transform: rotate(90deg);
            }
        }
    }
}

/* -------------------------- table-filter dropdown end ------------------------- */


/* -------------------------- table-filter-tab dropdown start ------------------------- */
.table-filter-tab {
    .dropdown-menu {
        min-width: 650px;
        padding: 0;
        border-radius: 7px;
    }
}

/* -------------------------- table-filter-tab dropdown end ------------------------- */
/* -------------------------- table-attachments dropdown start ------------------------- */
.table-attachments {
    .dropdown-menu {
        min-width: 350px;
    }
}

.dropdown-attachments {

    .dropdown-menu {
        min-width: 350px;
        max-width: 100%;
        padding: 0;
        overflow: hidden;
        border-radius: 8px;

        .attachments-card-wrapper {
            display: flex;
            flex-direction: column;

            &.attachments-sticky {
                >.attachments-card-header {
                    position: -webkit-sticky;
                    position: sticky;
                    top: -1px;
                    z-index: 9;
                }
            }

            .attachments-card-header {
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: space-between;
                gap: 6px;
                padding: 6px 10px;
                background-color: $primary_light_color;

                h6 {
                    font-size: 12px;
                    font-weight: 600;
                    line-height: 15.6px;
                    color: $text_color;
                    margin: 0;
                }
            }

            .attachments-card-body {
                overflow-y: auto;
                max-height: 210px;

                .attachments-items {
                    display: flex;
                    align-items: center;
                    padding: 5px;
                    gap: 0 8px;
                    width: 100%;
                    border-bottom: 1px solid $stock_light;

                    &:last-child {
                        border-color: transparent;
                    }

                    &:hover {
                        background-color: $bg_blue;

                        .user-action {
                            opacity: 1;
                        }
                    }

                    .attachments-user-image {
                        width: 32px;
                        height: 32px;
                        min-width: 32px;
                        border-radius: 4px;
                        overflow: hidden;
                        background-color: #F9F9F9;
                        border: 1px solid $stock_light;
                        display: flex;
                        align-items: center;
                        justify-content: center;

                        img {
                            width: 100%;
                            height: 100%;
                            background-size: cover;
                        }

                        i {
                            font-size: 18px;
                        }

                        &.small-image {
                            img {
                                width: 17px;
                                height: 17px;
                                object-fit: contain;
                            }
                        }
                    }

                    .attachments-user-text {
                        width: 100%;
                        max-width: 230px;

                        p {
                            font-weight: 600;
                            color: $text_black_color;
                            font-size: 12px;
                            margin-bottom: 0px;
                            -webkit-line-clamp: 1;
                            -webkit-box-orient: vertical;
                            display: -webkit-box;
                            white-space: normal;
                            overflow: hidden;
                        }

                        span {
                            color: $text_color;
                            font-size: 12px;
                        }

                        .attachments-user-counter {
                            background-color: #F0F3F5;
                            height: 24px;
                            width: 24px;
                            display: inline-flex;
                            flex-direction: row;
                            align-items: center;
                            justify-content: center;
                            color: $text_black_color;
                            font-size: 13px;
                            font-weight: 600;
                            border-radius: 4px;
                            font-size: 13px;
                        }
                    }

                    .user-action {
                        display: flex;
                        align-items: center;
                        gap: 0 3px;
                        opacity: 0;
                        transition: all 0.3s;

                        .btn-user-action {
                            background-color: transparent;
                            border: 0px;
                            color: $text_color;
                            font-size: 14px;
                            line-height: 14px;
                            padding: 0px;
                        }
                    }


                    .attachments-user-text-action {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        gap: 0 5px;
                        width: 100%;

                        .user-action,
                        .attachments-user-text {
                            height: 100%;
                        }
                    }

                }
            }

        }

    }
}

/* -------------------------- table-attachments dropdown end ------------------------- */

/* ----------------------- dropdown-multiple-category start ----------------------- */
.dropdown-multiple-category {
    position: relative;

    .btn-multiple-category {
        background-color: $white_color;
        color: #636D83;
        border-color: #E6EAEE;
        font-weight: 400;
        width: 100%;
        justify-content: flex-start;

        &:hover {
            border-color: $primary_color;
        }
    }

    &.multiple-category-layer1 {
        .dropdown-menu-wrapper {
            width: 240px;
            min-width: 240px;
        }

        .card-dropdown-menu-header {
            .dropdown-menu-header-filter {
                .form-search {
                    min-width: auto;
                }
            }
        }

    }

    &.multiple-category-layer2 {
        .dropdown-menu-wrapper {
            width: 460px;
            min-width: 460px;
        }
    }

    &.multiple-category-layer3 {
        .dropdown-menu-wrapper {
            width: 680px;
            min-width: 680px;
        }
    }

    &.multiple-category-layer4 {
        .dropdown-menu-wrapper {
            width: 900px;
            min-width: 900px;
        }
    }

    .dropdown-menu-wrapper {
        box-shadow: 0px 10px 20px 0px #0000001A;

        .dropdown-menu-list,
        .dropdown-menu {
            border: 1px solid #E6EAEE;
            border-radius: 10px;
            margin: 0;
            max-width: 220px;
        }
    }

    .card-dropdown-menu {
        .card-dropdown-menu-header {
            padding: 10px;

            .dropdown-menu-header-filter {
                display: flex;
                flex-direction: row;
                justify-content: space-between;
                align-items: center;
                gap: 8px;
            }
        }

        .card-dropdown-menu-body {
            padding: 10px;
        }
    }

    .dropdown-menu {
        padding: 0;

        .dropdown-item {
            margin-bottom: 3px;

            &:last-child {
                margin-bottom: 0;
            }

            &:hover,
            &.active {
                background-color: #EDF9FF;
                color: $primary_color;
            }
        }
    }


    .card-dropdown-menu-header {
        .dropdown-menu-header-filter {
            .form-search {
                min-width: 345px;
            }
        }
    }

    .card-dropdown-menu-body {
        .dropdown-menu-list {
            position: relative;

            li {

                position: relative;
                // &:hover> {
                //     .dropdown-menu {
                //         display: block;
                //     }
                // }

                .dropdown-toggle {
                    &::after {
                        transform: rotate(270deg);
                    }
                }

                .dropdown-menu {
                    background-color: $white_color;

                    .dropdown-item {
                        &.dropdown-toggle {
                            padding-right: 25px;
                        }
                    }
                }
            }
        }

        .dropdown-menu {
            top: 0;
            left: 100%;
            background-color: $white_color;
            // display: none;
            display: block;
            position: absolute;
            height: auto;
        }
    }
}

/* ----------------------- dropdown-multiple-category end ----------------------- */

.tbale-lg-dropdown {
    background-color: $white_color;

    .tbale-lg-dropdown-header {
        background-color: $bg_grey;
        padding: 16px 10px;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .tbale-lg-dropdown-header-left,
        .tbale-lg-dropdown-header-right {
            display: flex;
            align-items: center;
            gap: 0 10px;
        }

        .dropdown-title {
            h6 {
                font-size: 14px;
                font-weight: 600;
                line-height: 18.2px;
                margin-bottom: 0px;
                color: $text_black_color;
            }
        }
    }

    .nav-tabs-outer {
        .tab-content {
            .tab-pane {
                height: auto !important;
            }
        }
    }
}

.dropdown-with-card {
    .btn-link {
        font-weight: 400;
    }

    .dropdown-menu {
        padding: 0;
        min-width: 330px;
        overflow: hidden;
    }
}

.dropdown-with-card2 {
    .btn-link {
        font-weight: 400;
    }

    .dropdown-menu {
        padding: 0;
        min-width: 250px;
        overflow: hidden;
    }

    .card-theme4 {
        .card-body {
            padding: 7px;
            max-height: 300px;
            overflow-x: auto;

            ul {
                li {
                    display: flex;
                    flex-direction: column;
                    align-items: flex-start;
                    padding: 7px 10px;
                    background-color: $white_color;
                    margin-bottom: 3px;
                    border-radius: 7px;

                    &:last-child {
                        margin-bottom: 0;
                    }

                    &:hover,
                    &.active {
                        background-color: $primary_light_color;
                    }

                    label {
                        font-size: 16px;
                        line-height: 20px;
                        font-weight: 600;
                        color: $text_color;
                    }

                    span {
                        font-size: 14px;
                        line-height: 18px;
                        font-weight: 400;
                        color: $text_color;
                    }
                }
            }
        }
    }
}

.dropdown-with-tables {
    .table-responsive {
        max-height: 350px !important;
    }

    .btn-link {
        font-weight: 400;
        font-size: 12px;
    }

    .dropdown-menu {
        padding: 0;
        min-width: 550px;
    }
}

.dropdown-with-tabs-tables {
    .table-responsive {
        max-height: 350px !important;

        @media only screen and (max-width: 1440px) {
            max-height: 300px !important;
        }
    }

    .btn-link {
        font-weight: 400;
        font-size: 12px;
    }

    .dropdown-menu {
        padding: 0;
        min-width: 650px;
    }

    .dropdown-with-tabs-tables-close {
        display: flex;
        align-items: center;
        gap: 0 2px;
    }

    .card-dropdown-with-tabs-tables {
        .card-header {
            padding: 0;
            background-color: transparent;
            border: 0;

            .nav-tabs-outer {
                .nav-tabs {
                    border-bottom: 0;
                }
            }

            .dropdown-with-tabs-tables-close {
                .btn {
                    border: 0;
                }
            }
        }

        .card-body {
            padding: 0;

            .nav-tabs-outer {
                .nav-tabs {
                    border-bottom: 0;
                }

                .tab-pane {
                    padding: 0;
                    height: auto !important;
                }

            }
        }

        .card-footer {
            background-color: transparent;
            border: 0;


        }
    }


}

// dropdown-branch
.dropdown-branch {
    .dropdown-menu {
        padding: 0;
        min-width: 320px;
        overflow: hidden;
        margin-bottom: 10px !important;
    }

    .card-header,
    .card-body,
    .card-footer {
        padding: 14px;
    }

    .card-header {
        padding-bottom: 5px;
    }

    .card-body {
        padding-top: 10px;
        max-height: 250px;
        overflow-x: auto;

        .branch-details-group {
            margin-bottom: 10px;

            &:nth-last-child(1) {
                margin-bottom: 0;
            }

            .branch-details-list {
                .branch-title {
                    font-weight: 400;
                    font-size: 12px;
                    line-height: 20.4px;
                    margin-bottom: 7px;
                    color: $text_color;
                }

                .branch-item {
                    padding: 7px 10px;
                    overflow: hidden;
                    border: 1px solid $stock_light;
                    border-radius: 7px;
                    margin-bottom: 7px;
                    display: flex;
                    align-items: center;
                    flex-wrap: nowrap;
                    justify-content: space-between;
                    gap: 10px;

                    &:nth-last-child(1) {
                        margin-bottom: 0;
                    }

                    &.secondary {
                        border-color: $secondary_color;
                    }

                    .branch-info {
                        display: flex;
                        align-items: center;
                        gap: 6px;
                        flex-wrap: nowrap;

                        .branch-icon {
                            width: 32px;
                            height: 32px;
                            flex: 0 0 32px;
                            overflow: hidden;
                            border-radius: 6px;
                            overflow: hidden;
                            background-color: $primary_light_color;
                            display: flex;
                            align-items: center;
                            justify-content: center;

                            img {
                                width: 17px;
                                height: auto;
                            }
                        }

                        .branch-content {
                            h5 {

                                font-weight: 600;
                                font-size: 12px;
                                line-height: 15.6px;
                                margin-bottom: 2px;
                                color: $text_black_color;
                                word-wrap: break-word;
                                word-break: break-word;
                                white-space: normal;
                            }

                            p {
                                font-weight: 400;
                                font-size: 10px;
                                line-height: 17px;
                                margin-bottom: 0;
                                color: $text_color;
                                word-wrap: break-word;
                                word-break: break-word;
                                white-space: normal;
                            }
                        }
                    }

                    .branch-tag {}
                }
            }
        }
    }

    .card-footer {
        padding-top: 10px;
        border-top: 1px solid $stock_light;

        .card-button-group {
            justify-content: space-between;

            .btn {
                // width: 100%;
            }
        }
    }
}

// Dropdown For Attachments (body)
.dropdown-menu-attachment  {
    min-width: 350px;
    max-width: 100%;
    padding: 0;
    overflow: hidden;
    border-radius: 8px;

    .attachments-card-wrapper {
        display: flex;
        flex-direction: column;

        &.attachments-sticky {
            >.attachments-card-header {
                position: -webkit-sticky;
                position: sticky;
                top: -1px;
                z-index: 9;
            }
        }

        .attachments-card-header {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
            gap: 6px;
            padding: 6px 10px;
            background-color: $primary_light_color;

            h6 {
                font-size: 12px;
                font-weight: 600;
                line-height: 15.6px;
                color: $text_color;
                margin: 0;
            }
        }

        .attachments-card-body {
            overflow-y: auto;
            max-height: 210px;

            .attachments-items {
                display: flex;
                align-items: center;
                padding: 5px;
                gap: 0 8px;
                width: 100%;
                border-bottom: 1px solid $stock_light;

                &:last-child {
                    border-color: transparent;
                }

                &:hover {
                    background-color: $bg_blue;

                    .user-action {
                        opacity: 1;
                    }
                }

                .attachments-user-image {
                    width: 32px;
                    height: 32px;
                    min-width: 32px;
                    border-radius: 4px;
                    overflow: hidden;
                    background-color: #F9F9F9;
                    border: 1px solid $stock_light;
                    display: flex;
                    align-items: center;
                    justify-content: center;

                    img {
                        width: 100%;
                        height: 100%;
                        background-size: cover;
                    }

                    i {
                        font-size: 18px;
                    }

                    &.small-image {
                        img {
                            width: 17px;
                            height: 17px;
                            object-fit: contain;
                        }
                    }
                }

                .attachments-user-text {
                    width: 100%;
                    max-width: 230px;

                    p {
                        font-weight: 600;
                        color: $text_black_color;
                        font-size: 12px;
                        margin-bottom: 0px;
                        -webkit-line-clamp: 1;
                        -webkit-box-orient: vertical;
                        display: -webkit-box;
                        white-space: normal;
                        overflow: hidden;
                    }

                    span {
                        color: $text_color;
                        font-size: 12px;
                    }

                    .attachments-user-counter {
                        background-color: #F0F3F5;
                        height: 24px;
                        width: 24px;
                        display: inline-flex;
                        flex-direction: row;
                        align-items: center;
                        justify-content: center;
                        color: $text_black_color;
                        font-size: 13px;
                        font-weight: 600;
                        border-radius: 4px;
                        font-size: 13px;
                    }
                }

                .user-action {
                    display: flex;
                    align-items: center;
                    gap: 0 3px;
                    opacity: 0;
                    transition: all 0.3s;

                    .btn-user-action {
                        background-color: transparent;
                        border: 0px;
                        color: $text_color;
                        font-size: 14px;
                        line-height: 14px;
                        padding: 0px;
                    }
                }


                .attachments-user-text-action {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    gap: 0 5px;
                    width: 100%;

                    .user-action,
                    .attachments-user-text {
                        height: 100%;
                    }
                }

            }
        }

    }

}

.dropdown-no-arrow [ngbDropdownToggle]::after {
    display: none !important;
}

.wide-dropdown {
    min-width: 450px;
    max-width: 100%;
    max-height: 300px;
    overflow-y: auto;
}