<div class="page-content">
  <div class="page-title-wrapper">
    <div class="page-title-left">
      <h4>{{regId ? 'Edit' : 'Add New'}} Registration </h4>
    </div>
    <div class="page-title-right">
      <Button class="btn btn-sm btn-icon btn-outline-white" [routerLink]="['/users/registration/']" ngbTooltip="Close"
        placement="left" container="body" triggers="hover">
        <i class="th th-close"></i>
      </Button>
    </div>
  </div>
  <div class="content-area">
    <div class="card card-theme card-forms" [formGroup]="regForm">
      <div class="card-body">
        <div class="row">
          <div class="col-lg-4 col-md-4 col-sm-12">
            <div class="form-group form-group-inline-control required">
              <label class="form-label">Registration Type</label>
              <div class="form-control-wrapper">
                <div class="radio radio-primary form-check-inline"
                  *ngFor="let item of dropdown?.RegistrationType; index as i">
                  <input (click)="captureOldValue()" (change)="onChangeRegType()" type="radio" id="regType-{{i}}"
                    [name]="'registrationType'" [(ngModel)]="regObj.registrationType" [value]="item.value"
                    formControlName="registrationType">
                  <label for="regType-{{i}}">{{item.label}}</label>
                </div>
                <div class="message error-message"
                  *ngIf="regForm.controls['registrationType'].hasError('required') &&  regForm.controls['registrationType'].touched">
                  {{utilsService.validationService.REG_TYPE_REQ}}
                </div>
                <!-- <div class="radio radio-primary form-check-inline">
                  <input type="radio" id="customer" name="type" checked="">
                  <label for="customer">Customer</label>
                </div>
                <div class="radio radio-primary form-check-inline">
                  <input type="radio" id="supplier" name="type">
                  <label for="supplier">Supplier</label>
                </div>
                <div class="radio radio-primary form-check-inline">
                  <input type="radio" id="both" name="type" disabled="">
                  <label for="both">Both</label>
                </div> -->
              </div>
            </div>
            <div class="form-group  form-group-inline-control required">
              <label class="form-label">Full Name</label>
              <div class="form-control-wrapper d-flex gap-2 flex-wrap">

                <input [maxlength]="utilsService.validationService.MAX_30" formControlName="firstName"
                  [(ngModel)]="regObj.firstName" type="text" class="form-control" placeholder="First name">
                <div class="message error-message"
                  *ngIf="regForm.controls['firstName'].hasError('required') &&  regForm.controls['firstName'].touched">
                  {{utilsService.validationService.F_NAME_REQ}}
                </div>
                <div class="message error-message"
                  *ngIf="!regForm.controls['firstName'].hasError('required') && !regForm.controls['firstName'].valid && regForm.controls['firstName'].touched">
                  {{utilsService.validationService.F_NAME_INVALID}}
                </div>

                <input [maxlength]="utilsService.validationService.MAX_30" formControlName="middleName"
                  [(ngModel)]="regObj.middleName" type="text" class="form-control" placeholder="Middle name">
                <div class="message error-message"
                  *ngIf="regForm.controls['middleName'].hasError('required') &&  regForm.controls['middleName'].touched">
                  {{utilsService.validationService.M_NAME_REQ}}
                </div>
                <div class="message error-message"
                  *ngIf="!regForm.controls['middleName'].hasError('required') && !regForm.controls['middleName'].valid && regForm.controls['middleName'].touched">
                  {{utilsService.validationService.M_NAME_INVALID}}
                </div>

                <input [maxlength]="utilsService.validationService.MAX_30" formControlName="lastName"
                  [(ngModel)]="regObj.lastName" type="text" class="form-control" placeholder="Last name">
                <div class="message error-message"
                  *ngIf="regForm.controls['lastName'].hasError('required') &&  regForm.controls['lastName'].touched">
                  {{utilsService.validationService.L_NAME_REQ}}
                </div>
                <div class="message error-message"
                  *ngIf="!regForm.controls['lastName'].hasError('required') && !regForm.controls['lastName'].valid && regForm.controls['lastName'].touched">
                  {{utilsService.validationService.L_NAME_INVALID}}
                </div>
              </div>
            </div>
            <div class="form-group  form-group-inline-control">
              <label class="form-label">Company Name</label>
              <div class="form-control-wrapper ">
                <input type="text" class="form-control" placeholder="Enter company name"
                  [maxlength]="utilsService.validationService.MAX_30" formControlName="companyName"
                  [(ngModel)]="regObj.companyName">
                <div class="message error-message"
                  *ngIf="regForm.controls['companyName'].hasError('required') &&  regForm.controls['companyName'].touched">
                  {{utilsService.validationService.COMPANY_NAME_REQ}}
                </div>
                <div class="message error-message"
                  *ngIf="!regForm.controls['companyName'].hasError('required') && !regForm.controls['companyName'].valid && regForm.controls['companyName'].touched">
                  {{utilsService.validationService.COMPANY_NAME_INVALID}}
                </div>
              </div>
            </div>
            <div class="form-group  form-group-inline-control required">
              <label class="form-label">Display Name</label>
              <div class="form-control-wrapper ">
                <input type="text" class="form-control" placeholder="Enter display name"
                  [maxlength]="utilsService.validationService.MAX_30" formControlName="displayName"
                  [(ngModel)]="regObj.displayName">
                <div class="message error-message"
                  *ngIf="regForm.controls['displayName'].hasError('required') &&  regForm.controls['displayName'].touched">
                  {{utilsService.validationService.DISPLAY_NAME_REQ}}
                </div>
                <div class="message error-message"
                  *ngIf="!regForm.controls['displayName'].hasError('required') && !regForm.controls['displayName'].valid && regForm.controls['displayName'].touched">
                  {{utilsService.validationService.DISPLAY_NAME_INVALID}}
                </div>
              </div>
            </div>
            <div class="form-group  form-group-inline-control">
              <label class="form-label">Email Address</label>
              <div class="form-control-wrapper ">
                <input [maxlength]="utilsService.validationService.MAX_30" formControlName="email"
                  [(ngModel)]="regObj.email" type="text" class="form-control" placeholder="Enter email address">
                <div class="message error-message"
                  *ngIf="!regForm.controls['email'].hasError('required') && !regForm.controls['email'].valid && regForm.controls['email'].touched">
                  {{utilsService.validationService.EMAIL_INVALID}}
                </div>
              </div>
            </div>

            <div class="form-group theme-ngselect form-group-inline-control">
              <div class="form-label">Phone</div>
              <div class="form-control-wrapper ">
                <div class="input-group input-group-select">
                  <ng-select class="" placeholder="Ph." [multiple]="false" [clearable]="false" [items]="dropdown?.phone"
                    bindLabel="countryExtension" bindValue="id" [(ngModel)]="regObj.countryId"
                    formControlName="countryId" (search)="onSearch($event, 'p')">
                  </ng-select>
                  <input [(ngModel)]="regObj.phone" formControlName="phone" type="text" placeholder="Enter Phone No."
                    class="form-control">
                </div>
                <div class="message error-message"
                  *ngIf="regForm.controls['countryId'].hasError('required') && regForm.controls['countryId'].touched">
                  {{utilsService.validationService.PHONE_NO_EXTENSION_REQ}}
                </div>
                <div class="message error-message"
                  *ngIf="regForm.controls['phone'].hasError('required') && regForm.controls['phone'].touched && regForm.controls['countryId'].valid">
                  {{utilsService.validationService.PHONE_NUMBER_REQUIRED}}
                </div>
                <div class="message error-message"
                  *ngIf="!regForm.controls['phone'].hasError('required') && !regForm.controls['phone'].valid && regForm.controls['phone'].touched && regForm.controls['countryId'].valid">
                  {{utilsService.validationService.PHONE_NUMBER_INVALID}}
                </div>
                <div class="checkbox checkbox-primary checkbox-small form-check-inline">
                  <input (change)="onChangeWhatspp(regObj.isWhatsAppNo)" [(ngModel)]="regObj.isWhatsAppNo"
                    formControlName="isWhatsAppNo" type="checkbox" id="whatsApp" class="material-inputs filled-in">
                  <label for="whatsApp"> Other WhatsApp No </label>
                </div>

                <div class="input-group input-group-select" *ngIf="regObj.isWhatsAppNo">
                  <ng-select class="" placeholder="Ph." [multiple]="false" [clearable]="false"
                    [items]="dropdown?.phoneWhatsApp" bindLabel="countryExtension" bindValue="id"
                    [(ngModel)]="regObj.countyForWhatsAppId" formControlName="countryIdForWhatsApp"
                    (search)="onSearch($event, 'w')">
                  </ng-select>
                  <input formControlName="whatsAppNo" [(ngModel)]="regObj.whatsAppNo" type="text"
                    placeholder="Enter WhatsApp No." class="form-control">
                </div>
                <div class="message error-message"
                  *ngIf="regForm.controls['countryIdForWhatsApp'].hasError('required') && regForm.controls['countryIdForWhatsApp'].touched">
                  {{utilsService.validationService.PHONE_NO_EXTENSION_REQ}}
                </div>
                <div class="message error-message"
                  *ngIf="regForm.controls['whatsAppNo'].hasError('required') && regForm.controls['whatsAppNo'].touched && regForm.controls['countryIdForWhatsApp'].valid">
                  {{utilsService.validationService.WHATSAPP_NO_REQ}}
                </div>
                <div class="message error-message"
                  *ngIf="!regForm.controls['whatsAppNo'].hasError('required') && !regForm.controls['whatsAppNo'].valid && regForm.controls['whatsAppNo'].touched && regForm.controls['countryIdForWhatsApp'].valid">
                  {{utilsService.validationService.WHATSAPP_NO_INVALID}}
                </div>
                <div class="checkbox checkbox-primary checkbox-small form-check-inline">
                  <input (change)="onChangeTelegram(regObj.isTelegramNo)" [(ngModel)]="regObj.isTelegramNo"
                    formControlName="isTelegramNo" type="checkbox" id="telegram" class="material-inputs filled-in">
                  <label for="telegram"> Other Telegram No </label>
                </div>

                <div class="input-group input-group-select" *ngIf="regObj.isTelegramNo">
                  <ng-select class="" placeholder="Ph." [multiple]="false" [clearable]="false"
                    [items]="dropdown?.phoneTelegram" bindLabel="countryExtension" bindValue="id"
                    [(ngModel)]="regObj.countyForTelegramId" formControlName="countryIdForTelegram"
                    (search)="onSearch($event, 't')">
                  </ng-select>
                  <input formControlName="telegramNo" [(ngModel)]="regObj.telegramNo" type="text"
                    placeholder="Enter Telegram No." class="form-control">
                </div>
                <div class="message error-message"
                  *ngIf="regForm.controls['countryIdForTelegram'].hasError('required') && regForm.controls['countryIdForTelegram'].touched">
                  {{utilsService.validationService.PHONE_NO_EXTENSION_REQ}}
                </div>
                <div class="message error-message"
                  *ngIf="regForm.controls['telegramNo'].hasError('required') && regForm.controls['telegramNo'].touched && regForm.controls['countryIdForTelegram'].valid">
                  {{utilsService.validationService.TELEGRAM_NO_REQ}}
                </div>
                <div class="message error-message"
                  *ngIf="!regForm.controls['telegramNo'].hasError('required') && !regForm.controls['telegramNo'].valid && regForm.controls['telegramNo'].touched && regForm.controls['countryIdForTelegram'].valid">
                  {{utilsService.validationService.TELEGRAM_NO_INVALID}}
                </div>
              </div>
            </div>

            <div class="form-group form-group-inline-control">
              <div class="form-label">Status</div>
              <div class="form-control-wrapper ">
                <div class="switch-box">
                  <label class="switch" htmlFor="switch">
                    <input type="checkbox" id='switch' formControlName="isActive" [(ngModel)]="regObj.isActive" />
                    <div class="slider round"></div>
                  </label>
                </div>
              </div>
            </div>

          </div>
          <div class="col-lg-4 col-md-4 col-sm-12" (dragover)="onSelectProfile($event);file.value = ''"
            (drop)="onSelectProfile($event);file.value = ''">
            <div class="form-group">
              <div class="form-label">Upload Profile Photo<i class="th th-outline-info-circle ms-1"
                  [ngbTooltip]="utilsService.validationService.PROFILE_INFO" placement="bottom" container="body"
                  triggers="hover"></i>
              </div>
              <div class="attachments-wrapper">
                <div class='attachments-container h-100'>
                  <div class='attachments-content'>
                    <button class='btn btn-attachments'><i class="bi bi-upload"></i></button>
                    <p>Upload Profile Photo <span class='text-primary'>Choose
                        file</span></p>
                  </div>
                  <input type="file" ref={imageRef} accept="image/x-png,image/jpeg,image/jpg" #file
                    (change)="onSelectProfile($event); file.value=''" />
                </div>
                <div class='attachments-upload-grid-container attachments-upload-grid-container2' style="min-height: 200px;"
                  *ngIf="regObj.profileUrl">
                  <div class='attachments-upload-row'>
                    <div class='attachments-upload-col'>
                      <div class='card-attachments-upload'>
                        <div class='attachments-image'>
                          <img
                            [src]="regObj.profileUrl?.fileName ? regObj.profileUrl.fileName : (utilsService.imgPath + regObj.profileUrl?.formattedName)"
                            alt="valamji" />
                        </div>
                        <div class="attachments-text" placement="bottom" container="body" triggers="hover"
                          [ngbTooltip]="regObj.profileUrl?.originalName ? regObj.profileUrl?.originalName : regObj.profileUrl?.name">
                          <h6 class="file-name">{{regObj.profileUrl?.originalName ? regObj.profileUrl?.originalName
                            :regObj.profileUrl?.name}}
                          </h6>
                        </div>
                        <button (click)="onRemoveProfile()" class="btn-close" variant="close"><i
                            class='th th-close'></i></button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="row mt-3">
          <div class="col-12 ">
            <div class='nav-tabs-outer nav-tabs-style2'>
              <ul ngbNav #nav="ngbNav" class="nav-tabs" [destroyOnHide]="true" [(activeId)]="selectedTab"
                (activeIdChange)="onChangeTab($event)">
                <li [ngbNavItem]="enumForTab.CUSTOMER_DETAILS" *ngIf="regObj.registrationType !== enumForRegType.SUPPLIER">
                  <button ngbNavLink class="nav-link" type="button"> <i class="th th-outline-profile-2user"></i>Customer Details
                  </button>
                  <ng-template ngbNavContent>
                    <app-registration-customer-details [regForm]="regForm" [dropdown]="dropdown" [obj]="customerDetailsObj"
                      (onSelectAadharC)="onSelectAadharC($event)" (onRemoveAadharC)="onRemoveAadharC()"
                      (removeAttachmentCus)="removeAttachmentCus($event.i, $event.file)"
                      (onSelectAttachmentsCus)="onSelectAttachmentsCus($event)" [regId]="regId" [showDaysReason]="showDaysReason"
                      [showCreditLimitReason]="showCreditLimitReason" />
                  </ng-template>
                </li>
                <li [ngbNavItem]="enumForTab.SUPPLIER_DETAILS" *ngIf="regObj.registrationType !== enumForRegType.CUSTOMER">
                  <button ngbNavLink class="nav-link" type="button"> <i class="th th-outline-profile-2user"></i>Supplier Details
                  </button>
                  <ng-template ngbNavContent>
                    <app-registration-supplier [regForm]="regForm" [dropdown]="dropdown" [obj]="supplierDetailsObj"
                      (onSelectAadharS)="onSelectAadharS($event)" (onRemoveAadharS)="onRemoveAadharS($event.i, $event.file)" />
                  </ng-template>
                </li>
                <li [ngbNavItem]="enumForTab.ASSOCIATE_ITEMS" *ngIf="regObj.registrationType !== enumForRegType.CUSTOMER">
                  <button ngbNavLink class="nav-link" type="button"> <i class="th th-outline-box"></i>Associated Items
                  </button>
                  <ng-template ngbNavContent>
                    <app-registration-associated-items [regForm]="regForm" [dropdown]="dropdown" [associatedItems]="associatedItems"
                      (addAssociateItems)="addAssociateItems()" (openRemoveAIModal)="openRemoveAIModal($event)"
                      [selectedItems]="selectedItems" [flagForSelectAllItems]="flagForSelectAllItems"
                      (onAddImageToItem)="onAddImageToItem($event.event, $event.index)"
                      (openRemoveImageItemModal)="openRemoveImageItemModal($event.index, $event.childIndex)" [regId]="regId"
                      [regObj]="regObj" (generateMeasurementCode)="generateMeasurementCode($event.item, $event.index)" />
                  </ng-template>
                </li>
                <li [ngbNavItem]="enumForTab.OTHER_DETAILS">
                  <button ngbNavLink class="nav-link" type="button"> <i class="th th-outline-document-text"></i>Other Details
                  </button>
                  <ng-template ngbNavContent>
                    <app-registration-other-details [regForm]="regForm" [dropdown]="dropdown" [obj]="otherDetailsObj" />
                  </ng-template>
                </li>
                <li [ngbNavItem]="enumForTab.GST">
                  <button ngbNavLink class="nav-link" type="button"> <i class="th th-outline-document-text"></i>GST
                  </button>
                  <ng-template ngbNavContent>
                    <app-registration-gst [regForm]="regForm" [dropdown]="dropdown" [regObj]="regObj" [gstList]="gstList"
                      (addGstDetail)="addGstDetail($event)" (addGST)="addGST()" (openRemoveGSTModal)="openRemoveGSTModal($event)"
                      (openRemoveGSTDetailModal)="openRemoveGSTDetailModal($event.gstIndex, $event.index)" [regId]="regId" />
                  </ng-template>
                </li>
                <li [ngbNavItem]="enumForTab.SHIPPING_ADD">
                  <button ngbNavLink class="nav-link" type="button"> <i class="th th-outline-location"></i>Shipping Address
                  </button>
                  <ng-template ngbNavContent>
                    <app-registration-shipping-address [shippingAddressRequestList]="shippingAddressRequestList" [regForm]="regForm"
                      [dropdown]="dropdown" [regObj]="regObj" (openRemoveSAModal)="openRemoveSAModal($event)"
                      (addShippingAddress)="addShippingAddress()" />
                  </ng-template>
                </li>
                <li [ngbNavItem]="enumForTab.CONTACT_PERSON">
                  <button ngbNavLink class="nav-link" type="button"> <i class="th th-outline-user"></i>Contact Person
                  </button>
                  <ng-template ngbNavContent>
                    <app-registration-contact-person [regForm]="regForm" [dropdown]="dropdown" [regObj]="regObj"
                      [contactPersonList]="contactPersonList" (openRemoveCPModal)="openRemoveCPModal($event)"
                      (addContactPerson)="addContactPerson()" />
                  </ng-template>
                </li>
                <li [ngbNavItem]="enumForTab.BANK_DETAILS">
                  <button ngbNavLink class="nav-link" type="button"> <i class="th th-outline-bank"></i>Bank Details
                  </button>
                  <ng-template ngbNavContent>
                    <app-registration-bank-details [regForm]="regForm" [dropdown]="dropdown" [obj]="bankDetailsObj" />
                  </ng-template>
                </li>
                <li [ngbNavItem]="enumForTab.REMARKS">
                  <button ngbNavLink class="nav-link" type="button"> <i class="th th-outline-note"></i>Remarks
                  </button>
                  <ng-template ngbNavContent>
                    <app-registration-remarks-details [regForm]="regForm" [regObj]="regObj" />
                  </ng-template>
                </li>
              </ul>
              <div [ngbNavOutlet]="nav" class="tab-content pt-0"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class='bottombar-wrapper bottom-fixed'>
      <div class='bottombar-container'>
        <div class='bottombar-left'>
          <button (click)="onSaveRegistration(false)" type="button" class="btn btn-primary btn-icon-text btn-sm"> <i
              class="th th-outline-tick-circle"></i>
            {{regId ? 'Update' : 'Save'}}</button>
          <button *ngIf="regObj.isSaveAsDraft" (click)="onSaveRegistration(true)" type="button"
            class="btn btn-outline-white btn-icon-text btn-sm"><i class="th th-outline-document-text"></i>Save as
            Draft</button>
          <button [routerLink]="['/users/registration/']" type="button"
            class="btn btn-outline-white btn-icon-text btn-sm"><i class="th th-outline-close-circle"></i>Cancel</button>
        </div>
        <div class='bottombar-right'>

        </div>
      </div>
    </div>
  </div>
</div>

<!-- ----------------------------------------------------------------------- -->
<!--                           Contact Person Delete Start                   -->
<!-- ----------------------------------------------------------------------- -->
<div class="modal modal-theme modal-confirmation modal-reject fade" id="cpDeleteModal" tabindex="-1"
  aria-labelledby="cpDeleteModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
            class='th th-close'></i></button>
      </div>
      <div class="modal-body">
        <div class="modal-confirmation-container">
          <div class="modal-confirmation-icon">
            <i class="th th-bold-trash"></i>
          </div>
          <div class="modal-confirmation-content">
            <h5>Are You Sure?</h5>
            <p>You want to remove selected Contact Person.</p>
          </div>
        </div>
        <div class="modal-button-group modal-full-width-btn">
          <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
          <button (click)="removeCP()" type="button" class="btn btn-primary btn-icon-text"> <i
              class="th th-outline-tick-circle"></i>
            Delete</button>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                          Contact Person Delete End                     -->
<!-- ----------------------------------------------------------------------- -->

<!-- ----------------------------------------------------------------------- -->
<!--                           Shipping Address Start                         -->
<!-- ----------------------------------------------------------------------- -->
<div class="modal modal-theme modal-confirmation modal-reject fade" id="spDeleteModal" tabindex="-1"
  aria-labelledby="spDeleteModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
            class='th th-close'></i></button>
      </div>
      <div class="modal-body">
        <div class="modal-confirmation-container">
          <div class="modal-confirmation-icon">
            <i class="th th-bold-trash"></i>
          </div>
          <div class="modal-confirmation-content">
            <h5>Are You Sure?</h5>
            <p>You want to remove selected shipping address.</p>
          </div>
        </div>
        <div class="modal-button-group modal-full-width-btn">
          <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
          <button (click)="removeSA()" type="button" class="btn btn-primary btn-icon-text"> <i
              class="th th-outline-tick-circle"></i>
            Delete</button>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                           Shipping Address End                            -->
<!-- ----------------------------------------------------------------------- -->


<!-- ----------------------------------------------------------------------- -->
<!--                           GST Modal Start                            -->
<!-- ----------------------------------------------------------------------- -->
<div class="modal modal-theme modal-confirmation modal-reject fade" id="gstDeleteModal" tabindex="-1"
  aria-labelledby="gstDeleteModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
            class='th th-close'></i></button>
      </div>
      <div class="modal-body">
        <div class="modal-confirmation-container">
          <div class="modal-confirmation-icon">
            <i class="th th-bold-trash"></i>
          </div>
          <div class="modal-confirmation-content">
            <h5>Are You Sure?</h5>
            <p>You want to Remove selected GST.</p>
          </div>
        </div>
        <div class="modal-button-group modal-full-width-btn">
          <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
          <button (click)="removeGST()" type="button" class="btn btn-primary btn-icon-text"> <i
              class="th th-outline-tick-circle"></i>
            Delete</button>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                            GST Modal End                            -->
<!-- ----------------------------------------------------------------------- -->


<!-- ----------------------------------------------------------------------- -->
<!--                           GST Modal Start                            -->
<!-- ----------------------------------------------------------------------- -->
<div class="modal modal-theme modal-confirmation modal-reject fade" id="gstDetailsDeleteModal" tabindex="-1"
  aria-labelledby="gstDetailsDeleteModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
            class='th th-close'></i></button>
      </div>
      <div class="modal-body">
        <div class="modal-confirmation-container">
          <div class="modal-confirmation-icon">
            <i class="th th-bold-trash"></i>
          </div>
          <div class="modal-confirmation-content">
            <h5>Are You Sure?</h5>
            <p>You want to Remove selected GST Details.</p>
          </div>
        </div>
        <div class="modal-button-group modal-full-width-btn">
          <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
          <button (click)="removeGstDetail()" type="button" class="btn btn-primary btn-icon-text"> <i
              class="th th-outline-tick-circle"></i>
            Delete</button>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                            GST Modal End                            -->
<!-- ----------------------------------------------------------------------- -->

<!-- ----------------------------------------------------------------------- -->
<!--                          AI delete Modal Start                           -->
<!-- ----------------------------------------------------------------------- -->
<div class="modal modal-theme modal-confirmation modal-reject fade" id="associateItemDeleteModal" tabindex="-1"
  aria-labelledby="associateItemDeleteModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
            class='th th-close'></i></button>
      </div>
      <div class="modal-body">
        <div class="modal-confirmation-container">
          <div class="modal-confirmation-icon">
            <i class="th th-bold-trash"></i>
          </div>
          <div class="modal-confirmation-content">
            <h5>Are You Sure?</h5>
            <p>You want to Remove <b>{{"selected Associated Item"}}</b>.</p>
          </div>
        </div>
        <div class="modal-button-group modal-full-width-btn">
          <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
          <button (click)="removeAI()" type="button" class="btn btn-primary btn-icon-text"> <i
              class="th th-outline-tick-circle"></i>
            Delete</button>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                           AI delete Modal End                           -->
<!-- ----------------------------------------------------------------------- -->

<!-- Item Image Delete Modal -->
<div class="modal modal-theme modal-confirmation modal-reject fade" id="itemImgDeleteModal" tabindex="-1"
  aria-labelledby="itemImgDeleteModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
            class='th th-close'></i></button>
      </div>
      <div class="modal-body">
        <div class="modal-confirmation-container">
          <div class="modal-confirmation-icon">
            <i class="th th-bold-trash"></i>
          </div>
          <div class="modal-confirmation-content">
            <h5>Are You Sure?</h5>
            <p>You want to Remove Selected Image from the Item.</p>
          </div>
        </div>
        <div class="modal-button-group modal-full-width-btn">
          <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
          <button (click)="removeImageItem()" type="button" class="btn btn-primary btn-icon-text"> <i
              class="th th-outline-tick-circle"></i>
            Delete</button>
        </div>
      </div>
    </div>
  </div>
</div>


<div class="modal modal-theme modal-confirmation modal-warning-two fade" id="markAsPrimaryRegModal" tabindex="-1"
  aria-labelledby="markAsPrimaryRegModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
            class='th th-close'></i></button>
      </div>
      <div class="modal-body">

        <div class="modal-confirmation-container">
          <div class="modal-confirmation-icon">
            <i class="th th-outline-info-circle"></i>
          </div>
          <div class="modal-confirmation-content">
            <h5>Are You Sure?</h5>
            <p>Do you want to switch Registration Type to Supplier? </p>
            <p><b>Note:</b> Existing Associated Items will be removed.</p>
          </div>
        </div>

        <div class="modal-button-group modal-full-width-btn">
          <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
          <button (click)="onConfirmRegTypeModal()" type="button" class="btn btn-primary btn-icon-text"> <i
              class="th th-outline-tick-circle"></i>Confirm</button>
        </div>
      </div>
    </div>
  </div>
</div>