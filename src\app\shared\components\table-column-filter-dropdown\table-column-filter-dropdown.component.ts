import { Component, OnInit, After<PERSON><PERSON>wInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';

@Component({
  selector: 'app-table-column-filter-dropdown',
  templateUrl: './table-column-filter-dropdown.component.html',
  styleUrls: ['./table-column-filter-dropdown.component.css']
})
export class TableColumnFilterDropdownComponent implements OnInit, AfterViewInit, OnDestroy {
  // To store event listeners for removal later
  private closeDropdownListeners: (() => void)[] = [];
  constructor() { }

  ngOnInit() {
  }
  
  ngAfterViewInit(): void {
    const dropdownButton = document.getElementById('tbl-filter-dropdown');
    if (dropdownButton) {
      const dropdown = dropdownButton.closest('.dropdown');
      const closeButtons = dropdown?.querySelectorAll('.column-filter-dropdown-close');

      closeButtons?.forEach(button => {
        const listener = () => {
          dropdownButton.setAttribute('aria-expanded', 'false');
          dropdown?.querySelector('.dropdown-menu')?.classList.remove('show');
        };

        button.addEventListener('click', listener);

        // Store listener for cleanup
        this.closeDropdownListeners.push(() => button.removeEventListener('click', listener));
      });
    }
  }

  ngOnDestroy(): void {
    // Cleanup event listeners
    this.closeDropdownListeners.forEach(removeListener => removeListener());
  }

}
