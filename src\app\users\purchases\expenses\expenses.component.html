<div class="page-content page-content-with-tabs">
  <div class="page-title-wrapper page-title-with-tab">
    <div class="page-title-left">
      <h4>Expenses</h4>
    </div>
    <div class="page-title-right">
      <div class="dropdown export-dropdown">
        @if(utilsService.checkPageAccess([utilsService.enumForPage.ADD_CONTAINER_EXP, utilsService.enumForPage.ADD_TEMPO_EXP])) {
          <button type="button" class="btn btn-sm btn-primary dropdown-toggle text-white  btn-icon-text"
            data-bs-toggle="dropdown" aria-expanded="false"><i class="th th-outline-add-circle"></i>
            Add Expense
          </button>
        }
        <ul class="dropdown-menu">
          <li [pageAccess]="{page: utilsService.enumForPage.EXPENSES, action: utilsService.enumForPage.ADD_CONTAINER_EXP}"><a
              class="dropdown-item" [routerLink]="['/users/purchases/expenses/new-expense']"><i
                class="th th-outline-add-circle"></i>Add Container Expense</a></li>
          <li [pageAccess]="{page: utilsService.enumForPage.EXPENSES, action: utilsService.enumForPage.ADD_TEMPO_EXP}"><a
              (click)="redirectToDetails()" [routerLink]="['/users/purchases/expenses/new-tempo-expense']"
              class="dropdown-item"><i class="th th-outline-add-circle"></i>Add Tempo Expense</a></li>
        </ul>
      </div>
      <button (click)="onRefresh()" class="btn btn-sm btn-icon btn-outline-white" ngbTooltip="Refresh" placement="left"
        container="body" triggers="hover">
        <i class="th th-outline-refresh-2"></i>
      </button>
    </div>
  </div>
  <div class="content-area">
    <div class='nav-tabs-outer nav-tabs-style2'>
      <nav>
        <div class="nav nav-tabs " id="nav-tab" role="tablist">
          <button [pageAccess]="{page: utilsService.enumForPage.EXPENSES, action: utilsService.enumForPage.VIEW_CONTAINER_EXP}"
            [ngClass]="{'active': selectedTab == enumForTabs.CONTAINER_EXPENSES}"
            (click)="onChangeTab(enumForTabs.CONTAINER_EXPENSES)" class="nav-link" id="container-exp-tab" data-bs-toggle="tab"
            data-bs-target="#container-exp" type="button" role="tab" aria-controls="container-exp" aria-selected="true"> <i
              class="th-outline-buildings-2"></i>Container
            Expenses
          </button>
          <button [pageAccess]="{page: utilsService.enumForPage.EXPENSES, action: utilsService.enumForPage.VIEW_TEMPO_EXP}"
            [ngClass]="{'active': selectedTab == enumForTabs.TEMPO_EXPENSES}" (click)="onChangeTab(enumForTabs.TEMPO_EXPENSES)"
            class="nav-link" id="tempo-exp-tab" data-bs-toggle="tab" data-bs-target="#tempo-exp" type="button" role="tab"
            aria-controls="tempo-exp" aria-selected="true"> <i class="th-outline-home-2"></i>Tempo Expenses
          </button>
        </div>
      </nav>
      <div class="tab-content" id="nav-tabContent">
        <div class="page-filters">
          <div class="page-filters-left">
            <div class="form-group form-group-sm filter-search">
              <div class="form-group-icon-start">
                <i class="th th-outline-search-normal-1 icon-broder "></i>
                <input (change)="onSearch($event)" [(ngModel)]="paginationRequest.searchText" type="search"
                  class="form-control" placeholder="Search by ID/Container">
              </div>
            </div>
            <div class="form-group form-group-sm date-range-filter">
              <div class="form-group-icon-end">
                <i (click)="open()" class="th th-outline-calendar ngx-daterangepicker-action"></i>
                <input #pickerDirective class="form-control" type="text" ngxDaterangepickerMd readonly
                  [formControl]="paginationRequest.dateRangeControl" [showCustomRangeLabel]="true"
                  [alwaysShowCalendars]="true" [ranges]="utilsService.ranges" [linkedCalendars]="false"
                  [showClearButton]="false" placeholder="Expense Date" [autoApply]="true" [showRangeLabelOnInput]="true"
                  startKey="start" endKey="end">
              </div>
            </div>
            <div class="form-group form-group-sm">
              <input mask="separator.2" thousandSeparator="" [(ngModel)]="paginationRequest.priceFrom"
                (change)="onChangeFromToPrice(paginationRequest.priceFrom, paginationRequest.priceTo)" type="text"
                class="form-control" placeholder="Price From">
            </div>
            <div class="form-group form-group-sm">
              <input mask="separator.2" thousandSeparator="" [(ngModel)]="paginationRequest.priceTo"
                (change)="onChangeFromToPrice(paginationRequest.priceFrom, paginationRequest.priceTo)" type="text"
                class="form-control" placeholder="Price To">
            </div>
            <button (click)="onClearFilters()" class="btn btn-link btn-sm">Clear</button>
          </div>
          <div class="page-filters-right">
            <div class="dropdown export-dropdown">
              <button [disabled]="utilsService.isEmptyObjectOrNullUndefined(tempoExpensesList)" type="button" class="btn btn-sm btn-outline-white dropdown-toggle" data-bs-toggle="dropdown"
                aria-expanded="false">
                Export
              </button>
              <ul class="dropdown-menu">
                <li><a (click)="exportReport()" class="dropdown-item">Excel</a></li>
              </ul>
            </div>
          </div>
        </div>
        <div class="card card-theme card-table-sticky">
          <div class="card-body p-0">
            <div class="table-responsive table-theme">
              <table class="table-theme table-hover table table-bordered table-hierarchy table-sticky">
                <thead class="border-less">
                  <tr>
                    <th *ngFor="let th of expensesTH; index as j" [class]="th.class"
                      [ngClass]="{'sorting-asc': paginationRequest.sortColumn==th.keyName && paginationRequest.sortOrder === enumForSortOrder.A, 
                                  'sorting-desc': paginationRequest.sortColumn==th.keyName && paginationRequest.sortOrder === enumForSortOrder.D }"
                      (click)="!utilsService.isNullUndefinedOrBlank(th.keyName) ? onSortTH(th.keyName) : $event.preventDefault()">
                      <div (click)="$event.stopPropagation()" *ngIf="j === 0"
                        class="checkbox checkbox-primary checkbox-small">
                        <input [disabled]="utilsService.isEmptyObjectOrNullUndefined(tempoExpensesList)"
                          (change)="selectAll()" [(ngModel)]="paginationRequest.flagForSelectAll" type="checkbox"
                          id="tbl-checkbox" class="material-inputs filled-in" />
                        <label for="tbl-checkbox"></label>
                      </div>
                      {{th.displayName}}
                    </th>
                    <th class="text-center">Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let item of tempoExpensesList; index as i; trackBy: trackBy">
                    <td class="tbl-user tbl-bold">
                      <div class="tbl-user-checkbox-srno">
                        <div class="checkbox checkbox-primary checkbox-small">
                          <input (change)="selectUnselect(item.id, item.isSelected)" [(ngModel)]="item.isSelected"
                            type="checkbox" id="tbl-checkbox2-{{i}}" class="material-inputs filled-in" />
                          <label for="tbl-checkbox2-{{i}}"></label>
                        </div>
                        <span>{{item.expenseDate | date: 'dd/MM/YYYY'}}</span>
                      </div>
                    </td>
                    <td>#{{item.expenseID}}</td>
                    <td *ngIf="selectedTab == enumForTabs.CONTAINER_EXPENSES">{{item.importType}}</td>
                    <td>
                      <ng-container *ngIf="selectedTab === enumForTabs.TEMPO_EXPENSES else containerExpense">
                        <span>{{item.vehicleNo ? item.vehicleNo : '-' }}</span>
                      </ng-container>
                      <ng-template #containerExpense>
                        <span>{{item.containersName ? item.containersName : '-' }}</span>
                      </ng-template>
                    </td>
                    <td *ngIf="selectedTab === enumForTabs.TEMPO_EXPENSES">
                      <span>{{item.containersName ? item.containersName : '-' }}</span>
                    </td>
                    <td>
                      <span [class]="{'text-warning': item?.expenseStatus?.value === enumForExpenseStatus.WAITING_FOR_APPROVAL,
                                      'text-success': item?.expenseStatus?.value === enumForExpenseStatus.APPROVED,
                                      'text-danger': item?.expenseStatus?.value === enumForExpenseStatus.CANCELLED
                                      }">
                        <b>{{item?.expenseStatus?.label}}</b>
                      </span>
                    </td>
                    <td class="tbl-description">
                      <div [title]="item.notes ? item.notes : ''">
                        {{item.notes ? item.notes : '-' }}
                      </div>
                    </td>
                    <td>
                      <div *ngIf="!utilsService.isEmptyObjectOrNullUndefined(item.documents) && item.documents.length > 0 else noDocs">
                        <app-attachment-download-dropdown [fileList]="item.documents" [tempoExpense]="item"
                          (openDeleteDocsModal)="openDeleteDocsModal($event.index, $event.obj)"
                          [page]="utilsService.enumForPage.EXPENSES" />
                      </div>
                      <ng-template #noDocs>
                        <span class="text-muted">No Attachments</span>
                      </ng-template>
                    </td>
                    <td class="tbl-amount">
                      <div ngbDropdown class="dropdown dropdown-with-tables dropdown-no-arrow" container="body" position="left">
                        <button class="btn btn-link text-primary no-arrow" id="dropdownWithTables" ngbDropdownToggle>
                          ₹ {{item.totalExpenseAmount ? (item.totalExpenseAmount | indianCurrency) : 0}}
                        </button>

                        <div ngbDropdownMenu aria-labelledby="dropdownWithTables" class="dropdown-menu wide-dropdown">
                          <div class="table-responsive">
                            <table class="table-theme table table-bordered table-sticky">
                              <thead class="border-less">
                                <tr>
                                  <th>Expense Type</th>
                                  <th>Amount (₹)</th>
                                  <th *ngIf="selectedTab === enumForTabs.CONTAINER_EXPENSES">Add in avg. price</th>
                                  <th *ngIf="selectedTab === enumForTabs.CONTAINER_EXPENSES">Add in ledger</th>
                                </tr>
                              </thead>
                              <tbody>
                                <tr *ngFor="let child of getExpenseTypeItems(item); index as i">
                                  <td class="tbl-description">{{child.expenseTypeName}}</td>
                                  <td>₹ {{child.amount}}</td>
                                  <td *ngIf="selectedTab === enumForTabs.CONTAINER_EXPENSES">
                                    <div class="d-flex justify-content-center pe-none">
                                      <div class="checkbox checkbox-primary checkbox-small">
                                        <input disabled [(ngModel)]="child.isAddInAvgAmount" type="checkbox" [id]="'avg-price' + i"
                                          class="material-inputs filled-in" />
                                        <label [for]="'avg-price' + i"></label>
                                      </div>
                                    </div>
                                  </td>
                                  <td *ngIf="selectedTab === enumForTabs.CONTAINER_EXPENSES">
                                    <div class="d-flex justify-content-center pe-none">
                                      <div class="checkbox checkbox-primary checkbox-small">
                                        <input disabled [(ngModel)]="child.isAddInLedger" type="checkbox" [id]="'ledger' + i"
                                          class="material-inputs filled-in" />
                                        <label [for]="'ledger' + i"></label>
                                      </div>
                                    </div>
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                          </div>
                        </div>
                      </div>
                    </td>
                    <td class="tbl-action">
                      <div class="tbl-action-group">
                      
                        @if(!item.isCompleted && item.expenseStatus?.value === enumForExpenseStatus.WAITING_FOR_APPROVAL) {
                          @if(utilsService.checkPageAccess([utilsService.enumForPage.APPROVE_CONTAINER_EXP, utilsService.enumForPage.APPROVE_TEMPO_EXP])) {
                          <button (click)="openApproveDeclineModal(true, item)" class="btn btn-xs btn-light-success btn-icon" ngbTooltip="Approve"
                            placement="bottom" container="body" triggers="hover">
                            <i class="th th-outline-tick-circle"></i>
                          </button>
                          }
                          @if(utilsService.checkPageAccess([utilsService.enumForPage.REJECT_CONTAINER_EXP, utilsService.enumForPage.REJECT_TEMPO_EXP])) {
                          <button (click)="openApproveDeclineModal(false, item)" class="btn btn-xs btn-light-danger btn-icon" ngbTooltip="Decline"
                            placement="bottom" container="body" triggers="hover">
                            <i class="th th-outline-close-circle"></i>
                          </button>
                          }
                        }
                        @if(!item.isCompleted && (selectedTab === enumForTabs.CONTAINER_EXPENSES ?
                          utilsService.checkPageAccess([utilsService.enumForPage.EDIT_CONTAINER_EXP]) :
                          utilsService.checkPageAccess([utilsService.enumForPage.EDIT_TEMPO_EXP]))) {
                          <button (click)="redirectToEdit(item.id)" class="btn btn-xs btn-light-primary btn-icon" ngbTooltip="Edit"
                            placement="bottom" container="body" triggers="hover"> <i class="th th-outline-edit"></i></button>
                        }
                        @if(!item.isCompleted && (selectedTab === enumForTabs.CONTAINER_EXPENSES ?
                          utilsService.checkPageAccess([utilsService.enumForPage.DELETE_CONTAINER_EXP]) :
                          utilsService.checkPageAccess([utilsService.enumForPage.DELETE_TEMPO_EXP]))) {
                          <button (click)="openDeleteExpenseModal(item)" class="btn btn-xs btn-light-danger btn-icon" ngbTooltip="Delete"
                            placement="left" container="body" triggers="hover">
                            <i class="th th-outline-trash"></i>
                          </button>
                        }
                      </div>
                    </td>
                  </tr>
                  <tr *ngIf="utilsService.isEmptyObjectOrNullUndefined(tempoExpensesList)">
                    <td colspan="20" class="text-center">
                      <span class="truncate">{{utilsService.serverVariableService.STRING_WHEN_NO_RECORDS_FOUND}}</span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="paginationbox pagination-fixed">
      <app-pagination (pagesizeData)="addPageSizeData($event)" (pageNumber)="pageNumber($event)"
        [page]="paginationRequest.pageNo" [pageSize]="paginationRequest.pageSize"
        [totalData]="paginationRequest.totalData"></app-pagination>
    </div>
  </div>
</div>

<!-- ----------------------------------------------------------------------- -->
<!--                           Delete Modal Start                            -->
<!-- ----------------------------------------------------------------------- -->
<div class="modal modal-theme modal-confirmation modal-reject fade" id="deleteTempoExpenseModal" tabindex="-1"
  aria-labelledby="deleteTempoExpenseModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
            class='th th-close'></i></button>
      </div>
      <div class="modal-body">
        <div class="modal-confirmation-container">
          <div class="modal-confirmation-icon">
            <i class="th th-bold-trash"></i>
          </div>
          <div class="modal-confirmation-content">
            <h5>Are You Sure?</h5>
            <ng-container *ngIf="utilsService.isNullUndefinedOrBlank(selectedDocIndex); else elseBlock">
              <p>You want to Delete <b>#{{tempoObj.expenseID}}</b> Expense.</p>
            </ng-container>
            <ng-template #elseBlock>
              <p>You want to Delete selected Document.</p>
            </ng-template>
          </div>
        </div>
        <div class="modal-button-group modal-full-width-btn">
          <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
          <button
            (click)="!utilsService.isNullUndefinedOrBlank(selectedDocIndex) ? deleteDocFromList() : deleteExpense()"
            type="button" class="btn btn-primary btn-icon-text"> <i class="th th-outline-tick-circle"></i>
            Delete</button>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                           Delete Modal End                            -->
<!-- ----------------------------------------------------------------------- -->


<!-- ----------------------------------------------------------------------- -->
<!--                           Approve / Decline Modal Start                 -->
<!-- ----------------------------------------------------------------------- -->
<div [class]="approveFlag ? 'modal-approve' : 'modal-reject'" class="modal modal-theme modal-confirmation fade"
  id="approveDeclineModal" tabindex="-1" aria-labelledby="approveDeclineModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
            class='th th-close'></i></button>
      </div>
      <div class="modal-body">
        <div class="modal-confirmation-container">
          <div class="modal-confirmation-icon">
            <i [class]="approveFlag ? 'th th-outline-tick-circle' : 'th th-outline-close-circle'"></i>
          </div>
          <div class="modal-confirmation-content">
            <h5>Are You Sure?</h5>
            <p>You want to {{approveFlag ? 'Approve' : 'Decline'}} expense <b>#{{tempoObj.expenseID}}</b> for
              <br /><b>{{selectedTab === enumForTabs.TEMPO_EXPENSES ? tempoObj.vehicleNo : tempoObj.containersName}}</b>
              {{selectedTab === enumForTabs.TEMPO_EXPENSES ? 'Tempo' : 'Container'}}
            </p>
          </div>
        </div>
        <div class="modal-button-group modal-full-width-btn">
          <button type="button" class=" btn btn-sm btn-outline-white btn-icon-text "
            data-bs-dismiss="modal">Close</button>
          <button (click)="onApproveDeclineContainer()" type="button" class="btn btn-sm btn-primary btn-icon-text">
            <i [class]="approveFlag ? 'th th-outline-tick-circle' : 'th th-outline-close-circle'"></i>{{approveFlag ?
            'Approve' : 'Decline'}}</button>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                           Approve / Decline Modal End                   -->
<!-- ----------------------------------------------------------------------- -->
