import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { SharedModule } from 'src/app/shared/shared.module';
import { ExpensesComponent } from './expenses.component';
import { NewExpensesComponent } from './new-expenses/new-expenses.component';
import { NewTempoExpensesComponent } from './new-tempo-expenses/new-tempo-expenses.component';

const routes: Routes = [
    { path: '', component: ExpensesComponent },
    { path: 'new-expense', component: NewExpensesComponent, title: 'Container Expense' },
    { path: 'new-expense/:containerId', component: NewExpensesComponent, title: 'Container Expense' },
    { path: 'edit-expense/:id', component: NewExpensesComponent, title: 'Container Expense' },
    { path: 'new-tempo-expense', component: NewTempoExpensesComponent, title: 'Tempo Expense' },
    { path: 'new-tempo-expense/:tempoId', component: NewTempoExpensesComponent, title: 'Tempo Expense' },
    { path: 'edit-tempo-expense/:id', component: NewTempoExpensesComponent, title: 'Tempo Expense' },
]

@NgModule({
    imports: [
        CommonModule,
        RouterModule.forChild(routes),
        SharedModule.forRoot()
    ],
    declarations: [ExpensesComponent, NewExpensesComponent, NewTempoExpensesComponent]
})
export class ExpensesModule { }
