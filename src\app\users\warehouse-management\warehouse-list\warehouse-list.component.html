<div class="page-content" [pageAccess]="{page: utilsService.enumForPage.WAREHOUSE, action: utilsService.enumForPage.VIEW_WAREHOUSE, view: true}">
  <div class="page-title-wrapper">
    <div class="page-title-left">
      <h4>Warehouse</h4>
    </div>
    <div class="page-title-right">
      <button [pageAccess]="{page: utilsService.enumForPage.WAREHOUSE, action: utilsService.enumForPage.ADD_WAREHOUSE}"
        class="btn btn-sm btn-primary btn-icon-text" (click)="openAddEditModal(null, 'Add')">
        <i class="th th-outline-add-circle"></i>Add New
      </button>
      <button (click)="getAllWarehouse()" class="btn btn-sm btn-icon btn-outline-white">
        <i class="th th-outline-refresh-2"></i>
      </button>
    </div>
  </div>

  <div class="content-area" *ngIf="!utilsService.isEmptyObjectOrNullUndefined(warehouseList)">
    <div class="page-content-wrapper" cdkDropListGroup>
      <div class="row row-gap-2">
        <div class="col-md-4" *ngFor="let item of warehouseList; index as i; trackBy: trackBy" cdkDropList [cdkDropListData]="{ item: item, index: i }"
        (cdkDropListDropped)="drop($event)">
          <div class="card card-grid card-theme2" [cdkDragData]="item" cdkDrag>
            <div class="card-drag-placeholder" *cdkDragPlaceholder></div>
            <div class="card-header">
              <div style="cursor: pointer;" class="card-header-left"
                [routerLink]="['/users/warehouse-management/warehouse-details/' + item.id]">
                <div class="card-grid-icon">
                  <i class="th th-outline-house"></i>
                </div>
                <div class="card-grid-title">
                  <h6>{{item.warehouseName}}</h6>
                  <p *ngIf="item.userName">Manager: {{item.userName}}</p>
                </div>
              </div>
              <div class="card-header-right">
                <button [pageAccess]="{page: utilsService.enumForPage.WAREHOUSE, action: utilsService.enumForPage.EDIT_WAREHOUSE}"
                  class="btn btn-primary btn-xs btn-icon-text" (click)="openAddEditModal(item, 'Edit')"> <i
                    class="th th-outline-edit"></i> Edit </button>
                <ng-container
                  *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.EDIT_WAREHOUSE, this.utilsService.enumForPage.DELETE_WAREHOUSE])">
                  <div class="dropdown" *ngIf="!(item.isActive && item.isMainWarehouse)">
                    <button id="actionDropDown" data-bs-toggle="dropdown" aria-expanded="false" class="btn btn-xs btn-outline-white"
                      data-bs-popper-config='{"strategy":"fixed"}'>
                      <i class="th th-outline-more"></i>
                    </button>
                    <ul aria-labelledby="actionDropDown" class="dropdown-menu">
                      <ng-container>
                        <li [pageAccess]="{page: utilsService.enumForPage.WAREHOUSE, action: utilsService.enumForPage.EDIT_WAREHOUSE}"
                          (click)="openMarkAsPrimary(item, true, i)" *ngIf="!item.isMainWarehouse && item.isActive"><a
                            class="dropdown-item">
                            <i class="th th-outline-star"></i> Mark as Main Warehouse </a></li>
                        <li [pageAccess]="{page: utilsService.enumForPage.WAREHOUSE, action: utilsService.enumForPage.EDIT_WAREHOUSE}"
                          (click)="onChangeStatus(item, false, i)" *ngIf="!item.isActive"><a class="dropdown-item"> <i
                              class="th th-outline-tick-circle"></i> Mark as Active</a></li>
                        <li [pageAccess]="{page: utilsService.enumForPage.WAREHOUSE, action: utilsService.enumForPage.EDIT_WAREHOUSE}"
                          (click)="onChangeStatus(item, true, i)" *ngIf="item.isActive && !item.isMainWarehouse"><a
                            class="dropdown-item">
                            <i class="th th-outline-slash"></i> Mark as Inactive </a></li>
                      </ng-container>
                      <ng-container>
                        <hr class="m-0"
                          [pageAccess]="{page: utilsService.enumForPage.WAREHOUSE, action: utilsService.enumForPage.DELETE_WAREHOUSE}">
                        <li
                          [pageAccess]="{page: utilsService.enumForPage.WAREHOUSE, action: utilsService.enumForPage.DELETE_WAREHOUSE}"
                          *ngIf=" !item.isMainWarehouse" (click)="openDeleteWarehouseModal(item)"><a
                            class="dropdown-item text-danger"> <i class="th th-outline-trash"></i> Delete</a>
                        </li>
                      </ng-container>
                    </ul>
                  </div>
                </ng-container>
                <!-- <button class="drag-icon btn btn-xs btn-outline-white btn-icon" cdkDragHandle>
                  <i class="bi bi-arrows-move"></i>
                </button> -->
              </div>
            </div>
            <div class="card-body">
              <div class="card-grid-details">
                <ul>
                  <li>
                    <label>Warehouse Code</label>
                    <span>{{item.warehouseCode}}</span>
                  </li>
                  <li>
                    <label>Email</label>
                    <span>{{item.warehouseEmail ? item.warehouseEmail : '-'}}</span>
                  </li>
                  <li>
                    <label>Mobile No</label>
                    <span>{{item.warehouseMobileNo}}</span>
                  </li>
                  <li>
                    <label>State</label>
                    <span>{{item.stateName}}</span>
                  </li>
                  <li>
                    <label>City</label>
                    <span>{{item.cityName}}</span>
                  </li>
                  <li>
                    <label>Address</label>
                    <span>{{item.warehouseAddress ? item.warehouseAddress : '-'}}</span>
                  </li>
                  <li>
                    <label>Location Link</label>
                    <span><a (click)="item.locationLink ? utilsService.openURL(item.locationLink) : null"
                        class="text-link">{{item.locationLink ? 'Link' : '-'}}</a></span>
                  </li>
                </ul>
              </div>
            </div>
            <div class="card-footer">
              <div class="card-grid-footer-wrapper">
                <div *ngIf="item.isMainWarehouse" class="badge rounded-pill badge-warning "><i class="th th-bold-star"></i> Main
                  Warehouse
                </div>
                <div *ngIf="item.isActive" class="badge rounded-pill badge-primary-light "><i class="th th-bold-tick-circle"></i>
                  Active
                </div>
                <div *ngIf="!item.isActive" class="badge rounded-pill badge-white-light "><i class="th th-bold-slash"></i>
                  Inactive
                </div>
                <div *ngIf="item.isSalesPoint" class="badge rounded-pill badge-primary-light "><i class="th th-bold-flag-2"></i>
                  Sales Point
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- <div class="col-md-4">
          <div class="card card-grid card-theme2">
            <div class="card-header">
              <div class="card-header-left">
                <div class="card-grid-icon">
                  <i class="th th-outline-house"></i>
                </div>
                <div class="card-grid-title" style="cursor: pointer;"
                  [routerLink]="['/users/warehouse-management/details']">
                  <h6>Warehouse 2</h6>
                  <p>Manager: Alpeshbhai</p>
                </div>
              </div>
              <div class="card-header-right">
                <button class="btn btn-primary btn-xs btn-icon-text" data-bs-toggle="modal"
                  data-bs-target="#addNewWareHouse"> <i class="th th-outline-edit"></i> Edit </button>
                <div class="dropdown">
                  <button id="actionDropDown" data-bs-toggle="dropdown" aria-expanded="false"
                    class="btn btn-xs btn-outline-white" data-bs-popper-config='{"strategy":"fixed"}'>
                    <i class="th th-outline-more"></i>
                  </button>
                  <ul aria-labelledby="actionDropDown" class="dropdown-menu">





                    <li><a href="#" class="dropdown-item"> <i class="th th-outline-star"></i> Mark as Org. Primary </a>
                    </li>
                    <li><a href="#" class="dropdown-item"> <i class="th th-outline-slash"></i> Mark as Inactive </a>
                    </li>
                    <hr class="m-0">
                    <li><a href="#" class="dropdown-item text-danger" data-bs-toggle="modal"
                        data-bs-target="#WarehouseListModal"> <i class="th th-outline-trash"></i> Delete</a>
                    </li>

                  </ul>
                </div>
              </div>
            </div>
            <div class="card-body">
              <div class="card-grid-details">
                <ul>
                  <li>
                    <label>Warehouse Code</label>
                    <span>MECMEC-MEC-1</span>
                  </li>
                  <li>
                    <label>Email</label>
                    <span>{{'<EMAIL>'}}</span>
                  </li>
                  <li>
                    <label>Mobile No</label>
                    <span>+91 98545 58565</span>
                  </li>
                  <li>
                    <label>State</label>
                    <span>Gujrat</span>
                  </li>
                  <li>
                    <label>City</label>
                    <span>Surat</span>
                  </li>
                  <li>
                    <label>Address</label>
                    <span>Yogichok</span>
                  </li>
                  <li>
                    <label>Location Link</label>
                    <span><a href="#" class="text-link">http://sdfsdfhjksdf.com</a></span>
                  </li>
                </ul>
              </div>
            </div>
            <div class="card-footer">
              <div class="card-grid-footer-wrapper">
                <div class="badge rounded-pill badge-primary-light "><i class="th th-bold-tick-circle"></i> Active</div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-4">
          <div class="card card-grid card-theme2">
            <div class="card-header">
              <div class="card-header-left">
                <div class="card-grid-icon">
                  <i class="th th-outline-house"></i>
                </div>
                <div class="card-grid-title" style="cursor: pointer;"
                  [routerLink]="['/users/warehouse-management/details']">
                  <h6>Warehouse 3</h6>
                  <p>Manager: Alpeshbhai</p>
                </div>
              </div>
              <div class="card-header-right">
                <button class="btn btn-primary btn-xs btn-icon-text" data-bs-toggle="modal"
                  data-bs-target="#addNewWareHouse"> <i class="th th-outline-edit"></i> Edit </button>
                <div class="dropdown">
                  <button id="actionDropDown" data-bs-toggle="dropdown" aria-expanded="false"
                    class="btn btn-xs btn-outline-white" data-bs-popper-config='{"strategy":"fixed"}'>
                    <i class="th th-outline-more"></i>
                  </button>
                  <ul aria-labelledby="actionDropDown" class="dropdown-menu">
                    <li><a href="#" class="dropdown-item"> <i class="th th-outline-star"></i> Mark as Org. Primary </a>
                    </li>
                    <li><a href="#" class="dropdown-item"> <i class="th th-outline-tick-circle"></i> Mark as Active </a>
                    </li>
                    <hr class="m-0">
                    <li><a href="#" class="dropdown-item text-danger" data-bs-toggle="modal"
                        data-bs-target="#WarehouseListModal"> <i class="th th-outline-trash"></i> Delete</a>
                    </li>

                  </ul>
                </div>
              </div>
            </div>
            <div class="card-body">
              <div class="card-grid-details">
                <ul>
                  <li>
                    <label>Warehouse Code</label>
                    <span>MECMEC-MEC-1</span>
                  </li>
                  <li>
                    <label>Email</label>
                    <span>{{'<EMAIL>'}}</span>
                  </li>
                  <li>
                    <label>Mobile No</label>
                    <span>+91 98545 58565</span>
                  </li>
                  <li>
                    <label>State</label>
                    <span>Gujrat</span>
                  </li>
                  <li>
                    <label>City</label>
                    <span>Surat</span>
                  </li>
                  <li>
                    <label>Address</label>
                    <span>Yogichok</span>
                  </li>
                  <li>
                    <label>Location Link</label>
                    <span><a href="#" class="text-link">http://sdfsdfhjksdf.com</a></span>
                  </li>
                </ul>
              </div>
            </div>
            <div class="card-footer">
              <div class="card-grid-footer-wrapper">
                <div class="badge rounded-pill badge-white-light "><i class="th th-bold-slash"></i> Inactive</div>
              </div>
            </div>
          </div>
        </div> -->
      </div>
    </div>
  </div>
  <div class="content-area" *ngIf="utilsService.isEmptyObjectOrNullUndefined(warehouseList)">
    <app-no-record />
  </div>
</div>



<!-- -------------------------- Add New Warehouse -------------------------- -->
<div class="color-modal modal modal-lg modal-theme fade" id="addNewWareHouse" tabindex="-1"
  aria-labelledby="addNewWareHouseLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <app-warehouse-add-edit *ngIf="showAddEditModal" [warehouseForm]="warehouseForm" [warehouseObj]="warehouseObj"
      [dropdownsList]="dropdownsList" [addNewWareHouse]="addNewWareHouse" [showAddEditModal]="showAddEditModal"
      [statusForModal]="statusForModal" (getAllWarehouse)="getAllWarehouse()" [isDetails]="false">
    </app-warehouse-add-edit>
  </div>
</div>
<!-- -------------------------- Add New Warehouse -------------------------- -->

<!-- ----------------------------------------------------------------------- -->
<!--                           Delete Modal Start                            -->
<!-- ----------------------------------------------------------------------- -->
<div class="modal modal-theme modal-confirmation modal-reject fade" id="deleteWarehouseModal" tabindex="-1"
  aria-labelledby="deleteWarehouseModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header p-3 pb-0">
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
            class='th th-close'></i></button>
      </div>
      <div class="modal-body">
        <div class="modal-confirmation-container">
          <div class="modal-confirmation-content  mt-0">
            <h5 class="text-start">Delete <b>{{warehouseObj.warehouseName}}</b> Warehouse</h5>
          </div>

          <div class="form-group form-group-sm">
            <div class="form-label text-start">Password to delete warehouse</div>
            <div class="form-group-icon-start form-group-password"><i class="th th-outline-lock"></i>
              <input placeholder="Enter Password" [formControl]="passwordControl"
                [type]="flagForPasswordHideShow ? 'password' : 'text'" class="form-control">
              <button (click)="flagForPasswordHideShow = !flagForPasswordHideShow" class="btn-password">
                <i class="th th th-outline-eye"
                  [ngClass]="{'th th-outline-eye': flagForPasswordHideShow === false, 'th th-outline-eye-slash': flagForPasswordHideShow}"></i></button>
            </div>
            <div class="message error-message" *ngIf="passwordControl.hasError('required') && passwordControl.touched">
              {{utilsService.validationService.PASSWORD_REQUIRED}}
            </div>
          </div>
        </div>
        <div class="modal-button-group">
          <button (click)="deleteWarehouse()" type="button" class="btn btn-danger btn-icon-text"> <i
              class="th th-outline-tick-circle"></i>
            Delete Warehouse</button>
          <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                           Delete Modal End                            -->
<!-- ----------------------------------------------------------------------- -->

<div class="modal modal-theme modal-confirmation modal-warning-two fade" id="markAsPrimaryModal" tabindex="-1"
  aria-labelledby="markAsPrimaryModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
            class='th th-close'></i></button>
      </div>
      <div class="modal-body">

        <div class="modal-confirmation-container">
          <div class="modal-confirmation-icon">
            <i class="th th-outline-info-circle"></i>
          </div>
          <div class="modal-confirmation-content">
            <h5>Are You Sure?</h5>
            <p>Do you want make this warehouse as main warehouse? </p>
            <p><b>Note:</b> Existing main warehouse will not be treated as main warehouse</p>
          </div>
        </div>

        <div class="modal-button-group modal-full-width-btn">
          <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
          <button (click)="onMarkAsPrimary()" type="button" class="btn btn-primary btn-icon-text"> <i class="th th-outline-tick-circle"></i>Confirm</button>
        </div>
      </div>
    </div>
  </div>
</div>