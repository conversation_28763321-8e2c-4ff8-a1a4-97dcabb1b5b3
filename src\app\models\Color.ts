import { deserializeAs, serializeAs } from 'cerialize';

export class Color {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('createdDate')
    @deserializeAs('createdDate')
    private _createdDate: string;

    @serializeAs('lastModifiedDate')
    @deserializeAs('lastModifiedDate')
    private _lastModifiedDate: string;

    @serializeAs('colorName')
    @deserializeAs('colorName')
    private _colorName: string;

    @serializeAs('chineseValue')
    @deserializeAs('chineseValue')
    private _chineseValue: string;

    @serializeAs('shortCode')
    @deserializeAs('shortCode')
    private _shortCode: string;

    @serializeAs('colorCode')
    @deserializeAs('colorCode')
    private _colorCode: string;

    @serializeAs('isActive')
    @deserializeAs('isActive')
    private _isActive: boolean;

    @serializeAs('isSelected')
    @deserializeAs('isSelected')
    private _isSelected: boolean;

    @deserializeAs('isDefault')
    private _isDefault: boolean;

    constructor() {
        this.isActive = false;
        this.isSelected = false;
        this.isDefault = false;
    }

    /**
     * Getter isDefault
     * @return {boolean}
     */
	public get isDefault(): boolean {
		return this._isDefault;
	}

    /**
     * Setter isDefault
     * @param {boolean} value
     */
	public set isDefault(value: boolean) {
		this._isDefault = value;
	}

    /**
     * Getter isSelected
     * @return {boolean}
     */
	public get isSelected(): boolean {
		return this._isSelected;
	}

    /**
     * Setter isSelected
     * @param {boolean} value
     */
	public set isSelected(value: boolean) {
		this._isSelected = value;
	}


    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Getter createdDate
     * @return {string}
     */
	public get createdDate(): string {
		return this._createdDate;
	}

    /**
     * Getter lastModifiedDate
     * @return {string}
     */
	public get lastModifiedDate(): string {
		return this._lastModifiedDate;
	}

    /**
     * Getter colorName
     * @return {string}
     */
	public get colorName(): string {
		return this._colorName;
	}

    /**
     * Getter chineseValue
     * @return {string}
     */
	public get chineseValue(): string {
		return this._chineseValue;
	}

    /**
     * Getter shortCode
     * @return {string}
     */
	public get shortCode(): string {
		return this._shortCode;
	}

    /**
     * Getter colorCode
     * @return {string}
     */
	public get colorCode(): string {
		return this._colorCode;
	}

    /**
     * Getter isActive
     * @return {boolean}
     */
	public get isActive(): boolean {
		return this._isActive;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Setter createdDate
     * @param {string} value
     */
	public set createdDate(value: string) {
		this._createdDate = value;
	}

    /**
     * Setter lastModifiedDate
     * @param {string} value
     */
	public set lastModifiedDate(value: string) {
		this._lastModifiedDate = value;
	}

    /**
     * Setter colorName
     * @param {string} value
     */
	public set colorName(value: string) {
		this._colorName = value;
	}

    /**
     * Setter chineseValue
     * @param {string} value
     */
	public set chineseValue(value: string) {
		this._chineseValue = value;
	}

    /**
     * Setter shortCode
     * @param {string} value
     */
	public set shortCode(value: string) {
		this._shortCode = value;
	}

    /**
     * Setter colorCode
     * @param {string} value
     */
	public set colorCode(value: string) {
		this._colorCode = value;
	}

    /**
     * Setter isActive
     * @param {boolean} value
     */
	public set isActive(value: boolean) {
		this._isActive = value;
	}


}