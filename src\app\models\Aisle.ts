import { deserializeAs, serializeAs } from 'cerialize';
import { Racks } from './Racks';

export class Aisle {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('racks')
    @deserializeAs('racks')
    private _racks: Racks[];

    @serializeAs('aisleName')
    @deserializeAs('aisleName')
    private _aisleName: string;

    @serializeAs('additionalName')
    @deserializeAs('additionalName')
    private _additionalName: string;

    @serializeAs('isMarkAsSalesAisle')
    @deserializeAs('isMarkAsSalesAisle')
    private _isMarkAsSalesAisle: boolean;

    @serializeAs('isActive')
    @deserializeAs('isActive')
    private _isActive: boolean;

    @serializeAs('isExpand')
    @deserializeAs('isExpand')
    private _isExpand: boolean;

    @serializeAs('itemsId')
    @deserializeAs('itemsId')
    private _itemsId: number[];

    @serializeAs('wareHouseId')
    @deserializeAs('wareHouseId')
    private _wareHouseId: number;

    constructor() {
        this.isActive = false;
        this.isMarkAsSalesAisle = false;
        this.isExpand = false;
        this.racks = []
    }


    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Getter racks
     * @return {Racks[]}
     */
	public get racks(): Racks[] {
		return this._racks;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Setter racks
     * @param {Racks[]} value
     */
	public set racks(value: Racks[]) {
		this._racks = value;
	}


    /**
     * Getter aisleName
     * @return {string}
     */
	public get aisleName(): string {
		return this._aisleName;
	}

    /**
     * Getter additionalName
     * @return {string}
     */
	public get additionalName(): string {
		return this._additionalName;
	}

    /**
     * Getter isMarkAsSalesAisle
     * @return {boolean}
     */
	public get isMarkAsSalesAisle(): boolean {
		return this._isMarkAsSalesAisle;
	}

    /**
     * Getter isActive
     * @return {boolean}
     */
	public get isActive(): boolean {
		return this._isActive;
	}

    /**
     * Getter isExpand
     * @return {boolean}
     */
	public get isExpand(): boolean {
		return this._isExpand;
	}

    /**
     * Getter itemsId
     * @return {number[]}
     */
	public get itemsId(): number[] {
		return this._itemsId;
	}

    /**
     * Getter wareHouseId
     * @return {number}
     */
	public get wareHouseId(): number {
		return this._wareHouseId;
	}

    /**
     * Setter aisleName
     * @param {string} value
     */
	public set aisleName(value: string) {
		this._aisleName = value;
	}

    /**
     * Setter additionalName
     * @param {string} value
     */
	public set additionalName(value: string) {
		this._additionalName = value;
	}

    /**
     * Setter isMarkAsSalesAisle
     * @param {boolean} value
     */
	public set isMarkAsSalesAisle(value: boolean) {
		this._isMarkAsSalesAisle = value;
	}

    /**
     * Setter isActive
     * @param {boolean} value
     */
	public set isActive(value: boolean) {
		this._isActive = value;
	}

    /**
     * Setter isExpand
     * @param {boolean} value
     */
	public set isExpand(value: boolean) {
		this._isExpand = value;
	}

    /**
     * Setter itemsId
     * @param {number[]} value
     */
	public set itemsId(value: number[]) {
		this._itemsId = value;
	}

    /**
     * Setter wareHouseId
     * @param {number} value
     */
	public set wareHouseId(value: number) {
		this._wareHouseId = value;
	}

}