<div class="page-content"
    [pageAccess]="{page: utilsService.enumForPage.MASTER, action: utilsService.enumForPage.VIEW_MASTER, view: true}">
    <div class="page-title-wrapper">
        <div class="page-title-left">
            <h4>Sales Order Type Master</h4>
        </div>
        <div class="page-title-right">
            <button class="btn btn-sm btn-primary btn-icon-text"
                [pageAccess]="{page: utilsService.enumForPage.MASTER, action: utilsService.enumForPage.ADD_MASTER}"
                (click)="openAddEditModal(null, 'Add')">
                <i class="th th-outline-add-circle"></i>Add New
            </button>
            <button (click)="getSalesOT()" class="btn btn-sm btn-icon btn-outline-white" ngbTooltip="Refresh"
                placement="left" container="body" triggers="hover">
                <i class="th th-outline-refresh-2"></i>
            </button>
        </div>
    </div>
    <!-- ---------------------------- content-area ----------------------------- -->
    <div class="content-area">
        <div class="page-filters">
            <div class="page-filters-left">
                <div class="form-group form-group-sm filter-search">
                    <div class="form-group-icon-start">
                        <i class="th th-outline-search-normal-1 icon-broder "></i>
                        <input (input)="onSearch($event)" [(ngModel)]="searchText" type="search" class="form-control"
                            placeholder="Search by order type">
                    </div>
                </div>
                <div class="form-group theme-ngselect form-group-sm">
                    <ng-select (change)="onChangeActive()" class="" placeholder="Status" [multiple]="false"
                        [clearable]="true" [items]="activeInactiveStatus" bindLabel="label" bindValue="value"
                        [(ngModel)]="activeFlag" [hideSelected]="false">
                    </ng-select>
                </div>
            </div>
            <div class="page-filters-right">
                <div class="form-group theme-ngselect form-group-sm form-group-export">
                    <div class="dropdown export-dropdown">
                        <button [disabled]="utilsService.isEmptyObjectOrNullUndefined(salesOtList())" type="button"
                            class="btn btn-sm btn-outline-white dropdown-toggle" data-bs-toggle="dropdown"
                            aria-expanded="false">
                            Export
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" (click)="exportReport()">Excel</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="card card-theme card-table-sticky ">
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table-theme table-hover table table-bordered table-sticky">
                        <thead class="border-less">
                            <tr>
                                <th *ngFor="let th of salesOtTH; index as j" [class]="th.class"
                                    [ngClass]="{'sorting-asc': sortColumn==th.keyName && sortOrder === enumForSortOrder.A, 
                                                                            'sorting-desc': sortColumn==th.keyName && sortOrder === enumForSortOrder.D }"
                                    (click)="!utilsService.isNullUndefinedOrBlank(th.keyName) ? onSortTH(th.keyName) : $event.preventDefault()">
                                    <div (click)="$event.stopPropagation()" *ngIf="j === 0"
                                        class="checkbox checkbox-primary checkbox-small">
                                        <input [disabled]="utilsService.isEmptyObjectOrNullUndefined(salesOtList())"
                                            (change)="selectAll()" [(ngModel)]="flagForSelectAll" type="checkbox"
                                            id="tbl-checkbox" class="material-inputs filled-in" />
                                        <label for="tbl-checkbox"></label>
                                    </div>
                                    {{th.displayName}}
                                </th>
                                <!-- <th class="d-flex align-items-center gap-2">
                                    <div class=" checkbox checkbox-primary checkbox-small">
                                        <input type="checkbox" id="tbl-checkbox" class="material-inputs filled-in" />
                                        <label for="tbl-checkbox"></label>
                                    </div>
                                    Sales Order Type Name
                                </th>
                                <th>Date Created</th> -->
                                <th class="tbl-switch"
                                    *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.EDIT_MASTER])">
                                    Status</th>
                                <th class="text-center"
                                    *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.EDIT_MASTER, this.utilsService.enumForPage.DELETE_MASTER])">
                                    Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr *ngFor="let item of salesOtList(); index as i; trackBy: trackBy">
                                <td class="tbl-user tbl-bold">
                                    <div class="tbl-user-checkbox-srno">
                                        <div class="checkbox checkbox-primary checkbox-small">
                                            <input (change)="selectUnselect(item.id, i, item.isSelected)"
                                                [(ngModel)]="item.isSelected" type="checkbox" id="tbl-checkbox2-{{i}}"
                                                class="material-inputs filled-in" />
                                            <label for="tbl-checkbox2-{{i}}"></label>
                                        </div>

                                        <span>{{item.name}}</span>
                                    </div>
                                </td>
                                <td>{{item.lastModifiedDate | date: 'dd/MM/YYYY h:mm a'}}</td>
                                <td class="tbl-switch"
                                    *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.EDIT_MASTER])">
                                    <div class="switch-box">
                                        <label class="switch" htmlFor="switch-{{i}}">
                                            <input [(ngModel)]="item.isActive"
                                                (change)="onChangeStatus(item, item.isActive, i)" type="checkbox"
                                                id='switch-{{i}}' />
                                            <div class="slider round"></div>
                                        </label>
                                    </div>
                                </td>
                                <td class="tbl-action"
                                    *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.EDIT_MASTER, this.utilsService.enumForPage.DELETE_MASTER])">
                                    <div class="tbl-action-group">
                                        <button class="btn btn-xs btn-light-white btn-icon"
                                            [pageAccess]="{page: this.utilsService.enumForPage.MASTER, action: this.utilsService.enumForPage.EDIT_MASTER}"
                                            (click)="openAddEditModal(item, 'Edit')" ngbTooltip="Edit"
                                            placement="bottom" container="body" triggers="hover">
                                            <i class="th th-outline-edit"></i>
                                        </button>
                                        <button *ngIf="!item.isDefault"
                                            class="btn btn-xs btn-light-danger btn-icon"
                                            [pageAccess]="{page: this.utilsService.enumForPage.MASTER, action: this.utilsService.enumForPage.DELETE_MASTER}"
                                            (click)="openDeleteSalesOrderModal(item)" ngbTooltip="Delete"
                                            placement="left" container="body" triggers="hover">
                                            <i class="th th-outline-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr *ngIf="utilsService.isEmptyObjectOrNullUndefined(salesOtList())">
                                <td colspan="20" class="text-center">
                                    <span
                                        class="truncate">{{utilsService.serverVariableService.STRING_WHEN_NO_RECORDS_FOUND}}</span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="paginationbox pagination-fixed">
            <app-pagination (pagesizeData)="addPageSizeData($event)" (pageNumber)="pageNumber($event)" [page]="pageNo"
                [pageSize]="pageSize" [totalData]="totalData"></app-pagination>
        </div>
    </div>
</div>

<!-- ----------------------------------------------------------------------- -->
<!--                     Add and Edit Forms Modal Start                      -->
<!-- ----------------------------------------------------------------------- -->
<div class="sales-order-modal modal modal-theme fade" id="salesOTMasterModal" tabindex="-1"
    aria-labelledby="salesOTMasterModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content" cdkTrapFocusAutoCapture="true" cdkTrapFocus>
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">{{statusForModal === 'Add' ? 'Add New' : 'Edit'}} Sales
                    Order Type</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
                        class='th th-close'></i></button>
            </div>
            <div class="modal-body" [formGroup]="salesOtGroup">
                <!-- modal form  -->
                <div class="row">
                    <div class="col-12">
                        <div class="form-group required">
                            <label class="form-label"> Sales Order Type Name</label>
                            <input [maxlength]="utilsService.validationService.MAX_50" id="f"
                                (keyup.enter)="onSaveSalesOrder()" [(ngModel)]="salesOtObj.name" formControlName="name"
                                type="text" class="form-control" placeholder="Enter Sales Order Type Name">
                            <div class="message error-message"
                                *ngIf="salesOtGroup.controls['name'].hasError('required') &&  salesOtGroup.controls['name'].touched">
                                {{utilsService.validationService.SOT_NAME_REQ}}
                            </div>
                            <div class="message error-message"
                                *ngIf="!salesOtGroup.controls['name'].hasError('required') && !salesOtGroup.controls['name'].valid && salesOtGroup.controls['name'].touched">
                                {{utilsService.validationService.SOT_NAME_INVALID}}
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="form-group d-flex justify-content-between required ">
                            <label class="form-label">Status</label>

                            <div class="switch-box">
                                <label class="switch" htmlFor="switch">
                                    <input [(ngModel)]="salesOtObj.isActive" formControlName="status" type="checkbox"
                                        id='switch' [(ngModel)]="salesOtObj.isActive" />
                                    <div class="slider round"></div>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <div class="modal-footer-group full-width-btn">
                    <button (click)="onSaveSalesOrder()" type="button" class="btn btn-primary btn-icon-text"> <i
                            class="th th-outline-tick-circle"></i>
                        {{statusForModal === 'Add' ? 'Save' : 'Update'}}</button>
                    <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                      Add and Edit Forms Modal End                       -->
<!-- ----------------------------------------------------------------------- -->

<!-- ----------------------------------------------------------------------- -->
<!--                           Delete Modal Start                            -->
<!-- ----------------------------------------------------------------------- -->
<div class="modal modal-theme modal-confirmation modal-reject fade" id="deleteSalesOTMasterModal" tabindex="-1"
    aria-labelledby="deleteSalesOTMasterModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
                        class='th th-close'></i></button>
            </div>
            <div class="modal-body">
                <div class="modal-confirmation-container">
                    <div class="modal-confirmation-icon">
                        <i class="th th-bold-trash"></i>
                    </div>
                    <div class="modal-confirmation-content">
                        <h5>Are You Sure?</h5>
                        <p>You want to Delete <b>{{salesOtObj.name}}</b> sales-order-type.</p>
                    </div>
                </div>
                <div class="modal-button-group modal-full-width-btn">
                    <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
                    <button (click)="deleteSalesOrder()" type="button" class="btn btn-primary btn-icon-text"> <i
                            class="th th-outline-tick-circle"></i>
                        Delete</button>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                           Delete Modal End                            -->
<!-- ----------------------------------------------------------------------- -->