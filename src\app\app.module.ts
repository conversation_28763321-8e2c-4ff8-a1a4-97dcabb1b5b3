import { LocationStrategy, HashLocationStrategy } from "@angular/common";
import { HTTP_INTERCEPTORS, provideHttpClient, withInterceptorsFromDi } from "@angular/common/http";
import { NgModule } from "@angular/core";
import { FormsModule } from "@angular/forms";
import { BrowserModule } from "@angular/platform-browser";
import { NgbModule, NgbDateParserFormatter } from "@ng-bootstrap/ng-bootstrap";
import { NgSelectModule } from "@ng-select/ng-select";
import { NgbDateFullCustomParserFormatter } from "@service/ngbdateFull";
import { ToastrModule } from "ngx-toastr";
import { AppRoutingModule } from "./app-routing.module";
import { AppComponent } from "./app.component";
import { RefreshTokenInterceptor } from "./shared/interceptors/refresh-token.interceptor";
import { rxStompServiceFactory } from "./shared/socket_config/rx-stomp-service-factory";
import { RxStompService } from "./shared/socket_config/rx-stomp.service";
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

@NgModule({
    declarations: [
        AppComponent,
    ],
    imports: [
        BrowserModule,
        AppRoutingModule,
        BrowserAnimationsModule,
        NgSelectModule,
        FormsModule,
        NgbModule,
        ToastrModule.forRoot({
            maxOpened: 1, newestOnTop: true, preventDuplicates: true, autoDismiss: true,
            tapToDismiss: false,
        }),
    ],
    providers: [
        { provide: LocationStrategy, useClass: HashLocationStrategy },
        { provide: RxStompService, useFactory: rxStompServiceFactory },
        { provide: HTTP_INTERCEPTORS, useClass: RefreshTokenInterceptor, multi: true },
        { provide: NgbDateParserFormatter, useClass: NgbDateFullCustomParserFormatter },
        provideHttpClient(withInterceptorsFromDi()),
    ],
    bootstrap: [AppComponent]
})
export class AppModule { }
