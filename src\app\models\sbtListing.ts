import { deserializeAs, serializeAs } from 'cerialize';
import { sbtRequestBySelf } from './sbtRequestBySelf';

export class sbtListing {

    @serializeAs('requestBranchName')
    @deserializeAs('requestBranchName')
    private _requestBranchName: string;

    @serializeAs('branchSelf')
    @deserializeAs('branchSelf')
    private _branchSelf: sbtRequestBySelf[];

    @deserializeAs('isExpand')
    private _isExpand: boolean;

    @deserializeAs('index')
    private _index: number;

    constructor() {
        this.isExpand = false;
    }


    /**
     * Getter index
     * @return {number}
     */
	public get index(): number {
		return this._index;
	}

    /**
     * Setter index
     * @param {number} value
     */
	public set index(value: number) {
		this._index = value;
	}


    /**
     * Getter isExpand
     * @return {boolean}
     */
	public get isExpand(): boolean {
		return this._isExpand;
	}

    /**
     * Setter isExpand
     * @param {boolean} value
     */
	public set isExpand(value: boolean) {
		this._isExpand = value;
	}


    /**
     * Getter requestBranchName
     * @return {string}
     */
	public get requestBranchName(): string {
		return this._requestBranchName;
	}

    /**
     * Getter branchSelf
     * @return {sbtRequestBySelf[]}
     */
	public get branchSelf(): sbtRequestBySelf[] {
		return this._branchSelf;
	}

    /**
     * Setter requestBranchName
     * @param {string} value
     */
	public set requestBranchName(value: string) {
		this._requestBranchName = value;
	}

    /**
     * Setter branchSelf
     * @param {sbtRequestBySelf[]} value
     */
	public set branchSelf(value: sbtRequestBySelf[]) {
		this._branchSelf = value;
	}


}