import { Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { UtilsService } from '../../services/utils.service';
import { SlickCarouselComponent } from 'ngx-slick-carousel';

@Component({
  selector: 'app-image-view-modal',
  templateUrl: './image-view-modal.component.html',
  styleUrls: ['./image-view-modal.component.css']
})
export class ImageViewModalComponent implements OnInit {

  @ViewChild('galleryOne') galleryOne: SlickCarouselComponent;

  @Input({ alias: 'itemImagesList', required: true }) itemImagesList: any[];
  @Input({ alias: 'selectedImageIndex', required: true }) selectedImageIndex: number;
  @Output() onSlideChange: EventEmitter<any> = new EventEmitter<any>();

  ProductModalImageSlider = {
    slidesToShow: 1,
    slidesToScroll: 1,
    infinite: true,
    arrows: true,
    prevArrow: "<button type='button' class='slick-prev'> <i class='icon icon-arrow-left-2'></i> </button>",
    nextArrow: "<button type='button' class='slick-next '> <i class='icon icon-arrow-right-3'></i> </button>",
  };

  constructor(public utilsService: UtilsService) { }

  ngOnInit() {
  }

  onChange = (event: any) => {
    this.onSlideChange.emit(event)
  }

  reInitSlick = () => {
    this.galleryOne.unslick();
    this.galleryOne.initSlick();
  }

}
