<div class="page-content page-content-with-tabs">
    <div class="page-title-wrapper page-title-with-tab">
        <div class="page-title-left">
            <h4>Carton Mapping</h4>
        </div>
        <div class="page-title-right">
            <!-- <button class="btn btn-sm btn-primary">Raise Audit Request</button> -->
            <button class="btn btn-sm btn-icon btn-outline-white" [routerLink]="['/users/purchases/po-import']">
                <i class="th th-close"></i>
            </button>
        </div>
    </div>
    <div class="content-area">
        <div class='nav-tabs-outer nav-tabs-style2'>
            <nav>
                <div class="nav nav-tabs " id="nav-tab" role="tablist">
                    <button *ngIf="!fromWarehouse" (click)="changeStage(1)" class="nav-link"
                        [ngClass]="{'active': stage === 1}" id="branch-allocation-tab" data-bs-toggle="tab"
                        data-bs-target="#branch-allocation" type="button" role="tab" aria-controls="branch-allocation"
                        aria-selected="true"> <i class="th-outline-buildings-2"></i>Branch Allocation
                    </button>
                    <button (click)="changeStage(2)" class="nav-link" [ngClass]="{'active': stage === 2}"
                        id="warehouse-allocation-tab" data-bs-toggle="tab" data-bs-target="#warehouse-allocation"
                        type="button" role="tab" aria-controls="warehouse-allocation" aria-selected="true"> <i
                            class="th-outline-home-2"></i>Warehouse
                        Allocation
                    </button>
                    <button *ngIf="!fromWarehouse" (click)="changeStage(3)" class="nav-link"
                        [ngClass]="{'active': stage === 3}" id="location-allocation-tab" data-bs-toggle="tab"
                        data-bs-target="#location-allocation" type="button" role="tab"
                        aria-controls="location-allocation" aria-selected="true"> <i
                            class="th th-outline-box-time"></i>Location
                        Allocation
                    </button>
                </div>
            </nav>
            <div class="tab-content" id="nav-tabContent">
                <div class="page-filters">
                    <div class="page-filters-left">
                        <div class="form-group form-group-sm filter-search">
                            <div class="form-group-icon-start">
                                <i class="th th-outline-search-normal-1 icon-broder "></i>
                                <input type="text" class="form-control" placeholder="Search by Marka"
                                    [formControl]="searchControl">
                            </div>
                        </div>
                        <div class="form-group theme-ngselect form-group-sm" *ngIf="stage == 1">
                            <ng-select (change)="onChangeContainer()" [(ngModel)]="selectedContainer" class=""
                                placeholder="Container" [multiple]="true" [clearable]="true" [items]="containerDropdown"
                                bindLabel="containerName" bindValue="id">
                                <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                                    <div class="ng-value" *ngFor="let item of items | slice:0:2">
                                        <span class="ng-value-label">{{item['containerName']}}</span>
                                        <span class="ng-value-icon right" (click)="clear(item)"
                                            aria-hidden="true">×</span>
                                    </div>
                                    <div class="ng-value" *ngIf="items.length > 2">
                                        <span class="ng-value-label">{{items.length - 2}} more...</span>
                                    </div>
                                </ng-template>
                            </ng-select>
                        </div>
                    </div>
                    <div class="page-filters-right">
                        <!-- <div class="dropdown export-dropdown">
                                        <button type="button" class="btn btn-sm btn-outline-white dropdown-toggle" data-bs-toggle="dropdown"
                                            aria-expanded="false">
                                            Export
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="#">Action</a></li>
                                            <li><a class="dropdown-item" href="#">Another action</a></li>
                                            <li><a class="dropdown-item" href="#">Something else here</a></li>
                                        </ul>
                                    </div> -->
                    </div>
                </div>
                <div class="card card-theme card-table-sticky3">
                    <div class="card-body p-0">
                        <div class="table-responsive table-theme">
                            <table class="table-theme table-hover table table-bordered table-sticky" *ngIf="stage == 1">
                                <thead class="border-less">
                                    <tr>
                                        <th rowspan="2">Item Details</th>
                                        <th rowspan="2">Marka</th>
                                        <th rowspan="2">Released Date</th>
                                        <th rowspan="2">Container No</th>
                                        <th rowspan="2">Carton</th>
                                        <th rowspan="2">Customer Order</th>
                                        <th rowspan="2">Pending</th>
                                        <ng-container>
                                            <th rowspan="2" *ngFor="let branch of branchHeaderList; index as branchIndex">
                                                {{branch.branchName}}
                                            </th>
                                        </ng-container>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr *ngFor="let item of cartonMappingList; index as i">
                                        <td class="tbl-user">
                                            <div class="tbl-user-checkbox-srno">
                                                <span>{{(i + 1) | padNum}}</span>
                                                <div class="tbl-user-wrapper">
                                                    <div class="tbl-user-image">
                                                        <img loading="lazy"
                                                            [src]="item?.item?.formattedName ? (utilsService.imgPath + item?.item?.formattedName) : ''"
                                                            alt="valamji">
                                                    </div>
                                                    <div class="tbl-user-text-action">
                                                        <div class="tbl-user-text">
                                                            <p>{{item?.item?.displayName}}</p>
                                                            <span>{{item?.item?.skuId}}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <ng-container>
                                                <span class="d-flex flex-column align-items-start">
                                                    <div>{{item.importedItem.marka ? item.importedItem.marka
                                                        : '-'}}
                                                    </div>
                                                    <div>
                                                        <p>{{(item.importedItem.pricePerCarton *
                                                            item.importedItem.poCarton)}}
                                                            PCS</p>
                                                    </div>
                                                    <div>
                                                        <ng-container>
                                                            {{item.importedItem.cartonLength ?
                                                            item.importedItem.cartonLength :
                                                            '-'}} X
                                                            {{item.importedItem.cartonWidth ?
                                                            item.importedItem.cartonWidth :
                                                            '-'}} X
                                                            {{item.importedItem.cartonHeight ?
                                                            item.importedItem.cartonHeight :
                                                            '-'}}
                                                            {{item.importedItem.dimUnitShortCode ?
                                                            item.importedItem.dimUnitShortCode
                                                            : ''}}
                                                        </ng-container>
                                                    </div>
                                                    <div>
                                                        <span class="w-100 d-block" *ngFor="let v of item.importedItem.colors">
                                                            <b>{{!utilsService.isEmptyObjectOrNullUndefined(v) ? v :
                                                                ''}}</b>
                                                        </span>
                                                    </div>
                                                    <div class="tbl-description">
                                                        <p [title]="item?.importedItem?.englishComment">{{item.importedItem.englishComment ?
                                                            item.importedItem.englishComment :
                                                            ''}}</p>
                                                    </div>
                                                </span>
                                            </ng-container>
                                        </td>
                                        <td>
                                            <p>{{item.releasedDate | date: 'dd/MM/YYYY h:mm a'}}</p>
                                        </td>
                                        <td>
                                            <p>{{item?.importedItem?.container?.containerName}}</p>
                                        </td>
                                        <td>{{item.totalCarton}}</td>
                                        <td>
                                            <div class="form-group mb-2 form-group-200" [ngClass]="{'form-error': isCustomerExceeded(item)}">
                                                <input (input)="onCustomerOrderChange(item)" type="text" class="form-control" placeholder="Enter"
                                                    [(ngModel)]="item.customerOrderQty" mask="separator.0" thousandSeparator="" [maxLength]="10">
                                            </div>
                                            <div class="form-group theme-ngselect mb-2 form-group-200">
                                                <ng-select required [disabled]="!item.customerOrderQty" class="cm-dropdown required" placeholder="Drop Location"
                                                    [multiple]="false" [clearable]="false" [(ngModel)]="item.dropLocationId" bindLabel="locationName" bindValue="id"
                                                    [items]="dropoffLocationDropdown" appendTo="body" [closeOnSelect]="true">
                                                    <ng-template ng-label-tmp let-item="item">
                                                        <div class="ng-value" [title]="item.locationName">
                                                            <span class="ng-value-label">{{item.locationName}}</span>
                                                        </div>
                                                    </ng-template>
                                                    <ng-template ng-option-tmp let-item="item">
                                                        <div class="ng-value" [title]="item.locationName">
                                                            <span class="ng-option-label fs-13">{{item.locationName}}</span>
                                                        </div>
                                                    </ng-template>
                                                </ng-select>
                                            </div>
                                        </td>
                                        <td>{{calculatePending(item)}}</td>
                                        <ng-container>
                                            <td *ngFor="let branch of branchHeaderList; index as branchIndex">
                                                <div class="form-group mb-2 form-group-200" [ngClass]="{'form-error': isTotalExceeded(item, branchIndex)}">
                                                    <input (input)="checkTotalTransfer(item, branchIndex)" [(ngModel)]="item.branches[branchIndex].transferQty"
                                                        type="text" class="form-control" placeholder="Enter" mask="separator.0" thousandSeparator="" [maxLength]="7">
                                                </div>
                                                <div class="form-group theme-ngselect mb-2 form-group-200" *ngIf="branch.id !== utilsService?.defaultBranch?.id">
                                                    <ng-select required [disabled]="!item.branches[branchIndex].transferQty" class="cm-dropdown required"
                                                        placeholder="Drop Location" [multiple]="false" [clearable]="false" bindValue="id" [items]="dropoffLocationDropdown"
                                                        appendTo="body" bindLabel="locationName" [(ngModel)]="item.branches[branchIndex].dropLocationId">
                                                        <ng-template ng-label-tmp let-item="item">
                                                            <div class="ng-value" [title]="item.locationName">
                                                                <span class="ng-value-label">{{item.locationName}}</span>
                                                            </div>
                                                        </ng-template>
                                                        <ng-template ng-option-tmp let-item="item">
                                                            <div class="ng-value" [title]="item.locationName">
                                                                <span class="ng-option-label fs-13">{{item.locationName}}</span>
                                                            </div>
                                                        </ng-template>
                                                    </ng-select>
                                                </div>
                                                <p class="mb-1">Ava. Qty: {{getBranchData(item,
                                                    branchIndex)?.availableQty
                                                    ||
                                                    '-'}}</p>
                                                <p class="mb-1"> Breach: {{getBranchData(item, branchIndex)?.breachQty
                                                    ||
                                                    '-'}}
                                                </p>
                                            </td>
                                        </ng-container>
                                    </tr>
                                    <tr *ngIf="utilsService.isEmptyObjectOrNullUndefined(cartonMappingList)">
                                        <td colspan="20" class="text-center">
                                            <span
                                                class="truncate">{{utilsService.serverVariableService.STRING_WHEN_NO_RECORDS_FOUND}}</span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                
                            <table class="table-theme table-hover table table-bordered table-sticky" *ngIf="stage == 2">
                                <thead class="border-less">
                                    <tr>
                                        <th rowspan="2">Item Details</th>
                                        <th rowspan="2">Marka</th>
                                        <th rowspan="2">Released Date</th>
                                        <th rowspan="2">Carton Assigned</th>
                                        <th rowspan="2">Pending</th>
                                        <ng-container *ngFor="let warehouse of warehouseHeaderList; let branchIndex = index">
                                            <th *ngIf="warehouse.isMainWarehouse" class="text-center" colspan="2">
                                                {{ warehouse.warehouseName }}
                                            </th>
                                            <th *ngIf="!warehouse.isMainWarehouse" rowspan="2" class="text-center">
                                                {{ warehouse.warehouseName }}
                                            </th>
                                        </ng-container>
                                    </tr>
                                    <tr>
                                        <ng-container *ngFor="let warehouse of warehouseHeaderList">
                                            <th *ngIf="warehouse.isMainWarehouse" class="text-center border-0 w180">
                                                Available
                                            </th>
                                            <th *ngIf="warehouse.isMainWarehouse" class="text-center border-0 w180">
                                                Mapped
                                                Qty
                                            </th>
                                        </ng-container>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr *ngFor="let item of cartonMappingList; index as i">
                                        <td class="tbl-user">
                                            <div class="tbl-user-checkbox-srno">
                                                <span>{{(i + 1) | padNum}}</span>
                                                <div class="tbl-user-wrapper">
                                                    <div class="tbl-user-image">
                                                        <img loading="lazy"
                                                            [src]="item?.item?.formattedName ? (utilsService.imgPath + item?.item?.formattedName) : ''"
                                                            alt="valamji">
                                                    </div>
                                                    <div class="tbl-user-text-action">
                                                        <div class="tbl-user-text">
                                                            <p>{{item?.item?.displayName}}</p>
                                                            <span>{{item?.item?.skuId}}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <ng-container>
                                                <span class="d-flex flex-column align-items-start">
                                                    <div>{{item.importedItem.marka ? item.importedItem.marka
                                                        : '-'}}
                                                    </div>
                                                    <div>
                                                        <p>{{(item.importedItem.pricePerCarton *
                                                            item.importedItem.poCarton)}}
                                                            PCS</p>
                                                    </div>
                                                    <div>
                                                        <ng-container>
                                                            {{item.importedItem.cartonLength ?
                                                            item.importedItem.cartonLength :
                                                            '-'}} X
                                                            {{item.importedItem.cartonWidth ?
                                                            item.importedItem.cartonWidth :
                                                            '-'}} X
                                                            {{item.importedItem.cartonHeight ?
                                                            item.importedItem.cartonHeight :
                                                            '-'}}
                                                            {{item.dimUnitShortCode ?
                                                            item.dimUnitShortCode
                                                            : ''}}
                                                        </ng-container>
                                                    </div>
                                                    <div>
                                                        <span class="w-100 d-block" *ngFor="let v of item.importedItem.colors">
                                                            <b>{{!utilsService.isEmptyObjectOrNullUndefined(v) ? v :
                                                                ''}}</b>
                                                        </span>
                                                    </div>
                                                    <div class="tbl-description">
                                                        <p [title]="item?.importedItem?.englishComment">{{item.importedItem.englishComment ?
                                                            item.importedItem.englishComment :
                                                            ''}}</p>
                                                    </div>
                                                </span>
                                            </ng-container>
                                        </td>
                                        <td>{{item.releasedDate | date: 'dd/MM/YYYY h:mm a'}}</td>
                                        <td>{{item.assignedCarton}}</td>
                                        <td>{{calculatePendingWarehouse(item)}}</td>
                                        <ng-container *ngFor="let w of item.warehouses; index as a">
                                            <td *ngIf="w.isMainWarehouse" colspan="2">
                                                <div class="d-flex flex-column align-items-center">
                                                    <ng-container *ngFor="let aisle of w.aisleRackQty; index as b">
                                                        <div class="form-group theme-ngselect form-group-inline-control">
                                                            <div class="tbl-form-group-inline mb-2 ">
                
                                                                <ng-container *ngIf="aisle.id">
                                                                    <p class="mb-1"><strong>{{ aisle.displayName
                                                                            }}</strong>
                                                                    </p>
                                                                </ng-container>
                
                                                                <ng-container *ngIf="!aisle.id">
                                                                    <ng-select (change)="onChangeAR(w)" (open)="handleFocus()" class="cm-dropdown"
                                                                        placeholder="Select" [multiple]="false" [clearable]="false"
                                                                        [(ngModel)]="aisle.displayName" bindLabel="displayName"
                                                                        bindValue="displayName" [items]="w.allAisleRacks"
                                                                        [clearable]="true" [dropdownPosition]="'auto'" appendTo="body">
                                                                        <ng-template ng-option-tmp let-item="item">
                                                                            <div [title]="item.displayName">{{
                                                                                item.displayName
                                                                                }}</div>
                                                                        </ng-template>
                                                                    </ng-select>
                                                                </ng-container>
                
                                                                <div class="form-group form-group-sm form-group-200">
                                                                    <div class="form-group-icon-end">
                                                                        <i class="th th-outline-tick-circle text-primary"></i>
                                                                        <input type="text" class="form-control" placeholder="Available"
                                                                            disabled [(ngModel)]="aisle.totalAvailQty">
                                                                    </div>
                                                                </div>
                
                                                                <div class="form-group form-group-sm form-group-200"
                                                                    [ngClass]="{'form-error': totalARExceed(item, a, b)}">
                                                                    <input (input)="onChangeAisleRackTransferQty(w)" type="text"
                                                                        class="form-control" placeholder="Mapped Qty"
                                                                        [(ngModel)]="aisle.transferQty" mask="separator.0"
                                                                        thousandSeparator="" [maxLength]="7">
                                                                </div>
                
                                                            </div>
                                                        </div>
                                                    </ng-container>
                                                    <div class="button-group mb-2">
                                                        <button (click)="addNewAR(i, a)"
                                                            class="btn btn-sm btn-icon-text btn-link fw-400 text-primary">
                                                            <i class="th th-outline-add-circle"></i>Add New
                                                        </button>
                                                        <!-- <button class="btn btn-sm btn-icon-text btn-link fw-400 text-warning">
                                                                                                                <i class="th th-outline-box"></i>Stock Transfer
                                                                                                            </button> -->
                                                    </div>
                                                    <div class="form-group mb-2 form-group-sm form-group-100"
                                                        [ngClass]="{'form-error': warehouseWithoutAR(item, a)}">
                                                        <label class="form-label">Total Qty</label>
                                                        <input [disabled]="isTransferQtyDisabled(w)" [maxlength]="10"
                                                            [(ngModel)]="w.transferQty" type="text" class="form-control"
                                                            placeholder="Enter" mask="separator.0" thousandSeparator="">
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="tbl-level" *ngIf="!w.isMainWarehouse">
                                                <div class="form-group mb-2" [ngClass]="{'form-error': warehouseTotalExceed(item, a)}">
                                                    <input [(ngModel)]="w.transferQty" type="text" class="form-control"
                                                        placeholder="Enter" mask="separator.0" thousandSeparator="" [maxLength]="7">
                                                </div>
                                                <p class="mb-1">Ava. Qty: {{w?.availableQty ||
                                                    '-'}}</p>
                                            </td>
                                        </ng-container>
                                    </tr>
                                    <tr *ngIf="utilsService.isEmptyObjectOrNullUndefined(cartonMappingList)">
                                        <td colspan="20" class="text-center">
                                            <span
                                                class="truncate">{{utilsService.serverVariableService.STRING_WHEN_NO_RECORDS_FOUND}}</span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                
                            <ng-container *ngIf="stage == 3">
                                <div class="mt-4" *ngFor="let parent of cartonMappingAisleRackList; index as i">
                                    <table class="table-theme table-hover table table-bordered table-sticky">
                                        <thead class="border-less">
                                            <tr>
                                                <th rowspan="2">Item Details</th>
                                                <th rowspan="2">Marka</th>
                                                <th rowspan="2">Released Date</th>
                                                <th rowspan="2">Carton Assigned</th>
                                                <th class="text-center" colspan="2">
                                                    {{ parent.warehouses?.warehouseName }}
                                                </th>
                                            </tr>
                                            <tr>
                                                <th class="text-center border-0 w180">Available</th>
                                                <th class="text-center border-0 w180">Mapped Qty</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <ng-container *ngFor="let item of parent.items; index as x">
                                                <tr>
                                                    <td class="tbl-user">
                                                        <div class="tbl-user-checkbox-srno">
                                                            <span>{{(x + 1) | padNum}}</span>
                                                            <div class="tbl-user-wrapper">
                                                                <div class="tbl-user-image">
                                                                    <img loading="lazy"
                                                                        [src]="item?.item?.formattedName ? (utilsService.imgPath + item?.item?.formattedName) : ''"
                                                                        alt="valamji">
                                                                </div>
                                                                <div class="tbl-user-text-action">
                                                                    <div class="tbl-user-text">
                                                                        <p>{{item?.item?.displayName}}</p>
                                                                        <span>{{item?.item?.skuId}}</span>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <ng-container>
                                                            <span class="d-flex flex-column align-items-start">
                                                                <div>{{item.importedItem.marka ? item.importedItem.marka
                                                                    : '-'}}
                                                                </div>
                                                                <div>
                                                                    <p>{{(item.importedItem.pricePerCarton *
                                                                        item.importedItem.poCarton)}}
                                                                        PCS</p>
                                                                </div>
                                                                <div>
                                                                    <ng-container>
                                                                        {{item.importedItem.cartonLength ?
                                                                        item.importedItem.cartonLength :
                                                                        '-'}} X
                                                                        {{item.importedItem.cartonWidth ?
                                                                        item.importedItem.cartonWidth :
                                                                        '-'}} X
                                                                        {{item.importedItem.cartonHeight ?
                                                                        item.importedItem.cartonHeight :
                                                                        '-'}}
                                                                        {{item.importedItem.dimUnitShortCode ?
                                                                        item.importedItem.dimUnitShortCode
                                                                        : ''}}
                                                                    </ng-container>
                                                                </div>
                                                                <div>
                                                                    <span class="w-100 d-block"
                                                                        *ngFor="let v of item.importedItem.colors">
                                                                        <b>{{!utilsService.isEmptyObjectOrNullUndefined(v)
                                                                            ?
                                                                            v : ''}}</b>
                                                                    </span>
                                                                </div>
                                                                <div class="tbl-description">
                                                                    <p [title]="item?.importedItem?.englishComment">
                                                                        {{item.importedItem.englishComment
                                                                        ?
                                                                        item.importedItem.englishComment :
                                                                        ''}}</p>
                                                                </div>
                                                            </span>
                                                        </ng-container>
                                                    </td>
                                                    <td>{{item.releasedDate | date: 'dd/MM/YYYY h:mm a'}}</td>
                                                    <td>{{item.assignedCarton ? item.assignedCarton : '-'}}</td>
                                                    <td colspan="2">
                                                        <ng-container *ngFor="let aisle of item.locations; index as b">
                                                            <div class="form-group theme-ngselect form-group-inline-control">
                                                                <div class="tbl-form-group-inline mb-2">
                
                                                                    <ng-container *ngIf="aisle.id">
                                                                        <p class="mb-1"><strong>{{ aisle.displayName
                                                                                }}</strong>
                                                                        </p>
                                                                    </ng-container>
                
                                                                    <ng-container *ngIf="!aisle.id">
                                                                        <ng-select (open)="handleFocus()" class="cm-dropdown"
                                                                            placeholder="Select" [multiple]="false" [clearable]="false"
                                                                            [(ngModel)]="aisle.displayName" bindValue="displayName"
                                                                            [items]="parent.warehouses?.allAisleRacks"
                                                                            [clearable]="true" appendTo="body">
                                                                            <ng-template ng-option-tmp let-item="item">
                                                                                <div [title]="item.displayName">{{
                                                                                    item.displayName }}</div>
                                                                            </ng-template>
                
                                                                            <ng-template ng-label-tmp let-item="item">
                                                                                <div>{{ item.displayName }}</div>
                                                                            </ng-template>
                                                                        </ng-select>
                                                                    </ng-container>
                
                                                                    <div class="form-group form-group-sm form-group-200">
                                                                        <div class="form-group-icon-end">
                                                                            <i class="th th-outline-tick-circle text-primary"></i>
                                                                            <input type="text" class="form-control"
                                                                                placeholder="Available" disabled
                                                                                [(ngModel)]="aisle.totalAvailQty">
                                                                        </div>
                                                                    </div>
                
                                                                    <div class="form-group form-group-sm form-group-200"
                                                                        [ngClass]="{'form-error': locationTotalExceed(item, b)}">
                                                                        <input type="text" class="form-control" placeholder="Mapped Qty"
                                                                            [(ngModel)]="aisle.transferQty" mask="separator.0"
                                                                            thousandSeparator="" [maxLength]="7">
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </ng-container>
                                                        <div class="button-group mb-2">
                                                            <button (click)="addNewThird(i, x)"
                                                                class="btn btn-sm btn-icon-text btn-link fw-400 text-primary">
                                                                <i class="th th-outline-add-circle"></i>Add New
                                                            </button>
                                                            <!-- <button class="btn btn-sm btn-icon-text btn-link fw-400 text-warning">
                                                                                                                    <i class="th th-outline-box"></i>Stock Transfer
                                                                                                                </button> -->
                                                        </div>
                                                    </td>
                                                </tr>
                                            </ng-container>
                                            <tr *ngIf="utilsService.isEmptyObjectOrNullUndefined(cartonMappingAisleRackList)">
                                                <td colspan="20" class="text-center">
                                                    <span
                                                        class="truncate">{{utilsService.serverVariableService.STRING_WHEN_NO_RECORDS_FOUND}}</span>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <div *ngIf="utilsService.isEmptyObjectOrNullUndefined(cartonMappingAisleRackList) && stage == 3">
                                    <app-no-record />
                                </div>
                            </ng-container>
                        </div>
                    </div>
                </div>
                <div class='bottombar-wrapper bottom-fixed'>
                    <div class='bottombar-container'>
                        <div class='bottombar-left'>
                            <button [disabled]="branchHeaderList.length === 0" *ngIf="stage === 1" (click)="onSave()"
                                type="button" class="btn btn-primary btn-icon-text btn-sm">
                                <i class="th th-outline-tick-circle"></i>Save
                            </button>

                            <button [disabled]="cartonMappingList.length === 0" *ngIf="stage === 2"
                                (click)="onSaveWarehouseCM()" type="button"
                                class="btn btn-primary btn-icon-text btn-sm">
                                <i class="th th-outline-tick-circle"></i>Save
                            </button>

                            <button [disabled]="cartonMappingAisleRackList.length === 0" *ngIf="stage === 3"
                                (click)="onSaveThird()" type="button" class="btn btn-primary btn-icon-text btn-sm">
                                <i class="th th-outline-tick-circle"></i>Save
                            </button>
                            <button type="button" class="btn btn-outline-white btn-icon-text btn-sm"
                                [routerLink]="['/users/purchases/po-import']"><i
                                    class="th th-outline-close-circle"></i>Cancel</button>
                        </div>
                        <div class='bottombar-right'>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>