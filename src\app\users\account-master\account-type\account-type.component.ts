import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Deserialize, Serialize } from 'cerialize';
import { debounceTime, distinctUntilChanged, Subject } from 'rxjs';
import { AccountType } from 'src/app/models/AccountType';
import { ACCOUNT_TYPE_MASTER_COLS, activeInactiveStatus } from 'src/app/shared/constants/constant';
import { UtilsService } from 'src/app/shared/services/utils.service';
declare var window: any;

@Component({
  selector: 'app-account-type',
  templateUrl: './account-type.component.html',
  styleUrls: ['./account-type.component.css']
})
export class AccountTypeComponent implements OnInit {

  activeInactiveStatus = activeInactiveStatus;
  searchSubject: Subject<string> = new Subject<string>();
  
  selectedIds: any[] = [];
  selectedDemo1: number = 1;
  enumForSortOrder = this.utilsService.enumForSortOrder;
  pageNo: number = 1;
  pageSize: string = '100';
  sortOrder: string;
  sortColumn: string;
  activeFlag: boolean = true;
  selectedAccountGroup : any;
  searchText: any;
  accountTypeTH : any[] = [];
  accountTypeList: AccountType[] = [];
  accountTypeObj = new AccountType();
  flagForSelectAll: boolean = false;
  totalData: number;
  pagination: any;

  statusForModal: string = null;
  addEditModel: any;
  deleteModel: any;

  accountTypeFormGroup: FormGroup;
  accountGroups : any[];
  
  
  constructor(public utilsService : UtilsService, private fb: FormBuilder) {
        this.accountTypeTH = ACCOUNT_TYPE_MASTER_COLS;
   }

  ngOnInit() {
    this.searchSubject.pipe(debounceTime(300), distinctUntilChanged()).subscribe((res: string) => {
        this.pageNo = 1;
        this.searchText = res;
        this.getAccountTypes();
    }); 
    this.getAccountTypes();
    this.getFormGroup();
    this.addEditModel = new window.bootstrap.Modal(
      document.getElementById('accountTypeModal')
    );

    this.deleteModel = new window.bootstrap.Modal(
      document.getElementById('deleteModel')
    );

    document.getElementById('accountTypeModal').addEventListener('shown.bs.modal', () => {
      document.getElementById('name').focus();
    });
	this.getRequiredData();
  }

  getAccountTypes() {
    this.selectedIds = []
    this.flagForSelectAll = false;
    this.totalData = 0;

    const param = {
      pageNo: this.pageNo,
      pageSize: this.pageSize,
      sortOrder: this.sortOrder,
      sortColumn: this.sortColumn,
      searchText: this.searchText,
      isActive: this.activeFlag,
      accountGroup : this.selectedAccountGroup
    }

    this.utilsService.postMethodAPI(false, this.utilsService.serverVariableService.ACCOUNT_TYPE_LISTING, param, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.accountTypeList = Deserialize(response.content, AccountType);
        this.totalData = response.totalElements;
        this.pagination = response;
      } else {
        this.accountTypeList = [];
      }
    })

  }

   // status 
    onChangeStatus(item: AccountType, value, index) {
  
      this.utilsService.putMethodAPI(true, this.utilsService.serverVariableService.ACCOUNT_TYPE_STATUS_CHANGE + `${item.id}/${value}`, {}, '', (response) => {
        if (!this.utilsService.isEmptyObjectOrNullUndefined(response.data)) {
          this.accountTypeList[index].isActive = value
        } else {
          this.accountTypeList[index].isActive = !value
        }
        if(!this.pagination?.first && this.pagination?.last && this.pagination?.numberOfElements === 1 && this.activeFlag) {
          this.pageNo = this.pageNo - 1
        }
        this.getAccountTypes()
      }, true);
    }

    onChangeMarkAsDefault(item: AccountType, value, index) {
      this.utilsService.putMethodAPI(true, this.utilsService.serverVariableService.ACCOUNT_TYPE_DEFAILT_CHANGE + `${item.id}`, {}, '', (response) => {
        if (!this.utilsService.isEmptyObjectOrNullUndefined(response.data)) {
          this.accountTypeList[index].markAsDefault = value
        } else {
          this.accountTypeList[index].markAsDefault = !value
        }
        if(!this.pagination?.first && this.pagination?.last && this.pagination?.numberOfElements === 1 && this.activeFlag) {
          this.pageNo = this.pageNo - 1
        }
        this.getAccountTypes()
      }, true);
    }    
  
    addPageSizeData(event) {
      this.pageNo = 1;
      this.pageSize = event;
      this.getAccountTypes();
    }
  
    pageNumber(event) {
      this.pageNo = event
      this.getAccountTypes();
    }
  
    trackBy(index: number, item: AccountType): number {
      return item.id;
    }

    selectAll() {
      if (this.flagForSelectAll === true) {
        this.selectedIds = new Array<string>();
      }
      const obj = this.accountTypeList.filter((val, index) => {
        if (this.flagForSelectAll === true) {
          val['isSelected'] = true;
          this.selectedIds.push(val.id);
        } else {
          val['isSelected'] = false;
          this.selectedIds.splice(index, 1);
        }
      });
      if (this.flagForSelectAll === false) {
        this.selectedIds = new Array<string>();
      }
    }
  
    selectUnselect(id: number, index, value) {
  
      const isSelected = this.selectedIds.includes(id);
  
      if (value && !isSelected) {
  
        this.selectedIds.push(id);
  
      } else if (!value && isSelected) {
  
        const assetIndex = this.selectedIds.indexOf(id);
        this.selectedIds.splice(assetIndex, 1);
      }
      this.flagForSelectAll = this.checkIfAllSelected();
    }
  
    checkIfAllSelected() {
      let flag = true;
      this.accountTypeList.filter((val, index) => {
        if (val['isSelected'] === false) {
          flag = false;
          return;
        }
      });
      return flag;
    }
  
    // sorting 
  
    onSortTH(key) {
  
      if (this.utilsService.isEmptyObjectOrNullUndefined(this.accountTypeList)) {
        return;
      }
  
      if (key === this.sortColumn) {
        if (this.sortOrder === this.enumForSortOrder.A) {
          this.sortOrder = this.enumForSortOrder.D;
        } else if (this.sortOrder === this.enumForSortOrder.D) {
          this.sortOrder = this.enumForSortOrder.A;
        }
      } else {
        this.sortOrder = this.enumForSortOrder.D;
      }
  
      this.sortColumn = key;
      this.getAccountTypes();
    }

    getFormGroup() {
        this.accountTypeFormGroup = this.fb.group({
          name: ['', Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.PATTERN_FOR_ALPHANUMERIC_WITH_SPECIAL_CHAR)])],
          account_group: ['', Validators.compose([Validators.required])],
          description: [''],
          status: [true],
          markAsDefault: [false]
        })  
	}

	onSaveColor() {
		if (this.accountTypeFormGroup.invalid) {
		  this.accountTypeFormGroup.markAllAsTouched();
		  return;
		}
	
		let param = this.utilsService.trimObjectValues(Serialize(this.accountTypeObj));
		this.utilsService.postMethodAPI(true, this.utilsService.serverVariableService.ACCOUNT_TYPE_SAVE_EDIT_DELETE, param, (response) => {
		  if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
			this.addEditModel.hide();
			this.getAccountTypes();
		  }
		})
	}
	
	openAddEditModal(obj: AccountType, status: string) {
		this.accountTypeObj = new AccountType();
		this.accountTypeFormGroup.reset();
		this.statusForModal = status;

		if (this.statusForModal === 'Add') {
		setTimeout(() => {
			this.accountTypeObj.isActive = true;
		}, 100);
		}

		if (obj) {
		setTimeout(() => {
			this.accountTypeObj = Serialize(obj);
			this.accountTypeObj.accountGroup = obj.accountGroup['value'];
		}, 100);
		}
		this.addEditModel.show();
	}

	openDeleteColorModal(obj: AccountType) {
		this.accountTypeObj = Serialize(obj)
		this.deleteModel.show();
	}

	onChangeActive() {
		this.pageNo = 1;
		// this.pageSize = '100';
		this.getAccountTypes();
	}

	//Search
	onSearch(event: any) {
		this.searchSubject.next(event.target.value);
	}

	//required data
	getRequiredData() {
		this.accountGroups = [];
		this.utilsService.getMethodAPI(false, this.utilsService.serverVariableService.ACCOUNT_TYPE_REQUIRED_DATA, null, (response) => {
			if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
				this.accountGroups = response['Account_Group'];
			}
		})
	}

	deleteObj() {
		this.utilsService.deleteMethodAPI(true, this.utilsService.serverVariableService.ACCOUNT_TYPE_SAVE_EDIT_DELETE + `?accountTypeId=${this.accountTypeObj.id}`, {}, (response) => {
			if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
			this.deleteModel.hide();
			if(!this.pagination?.first && this.pagination?.last && this.pagination?.numberOfElements === 1) {
				this.pageNo = this.pageNo - 1
			}
			this.getAccountTypes();
			}
		})
	}
}
