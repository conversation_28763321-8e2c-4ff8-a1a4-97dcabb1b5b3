import { deserializeAs, serializeAs } from 'cerialize';

export class AccountChart {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('accountName')
    @deserializeAs('accountName')
    private _accountName: string;
    
    @serializeAs('accountCode')
    @deserializeAs('accountCode')
    private _accountCode: string;

    @serializeAs('description')
    @deserializeAs('description')
    private _description: string;

    @serializeAs('isActive')
    @deserializeAs('isActive')
    private _isActive: boolean;
    
    @serializeAs('isSubAccount')
    @deserializeAs('isSubAccount')
    private _isSubAccount: boolean;
   
    @serializeAs('isSelected')
    @deserializeAs('isSelected')
    private _isSelected: boolean;

    @serializeAs('accountTypeId')
    @deserializeAs('accountTypeId')
    private _accountTypeId: any;
   
    @serializeAs('accountTypeName')
    @deserializeAs('accountTypeName')
    private _accountTypeName: any;
    
    @serializeAs('parentAccountId')
    @deserializeAs('parentAccountId')
    private _parentAccountId: any;

    @deserializeAs('lastModifiedDate')
    private _lastModifiedDate: string;
    
    @deserializeAs('accountChartList')
    private _accountChartList: AccountChart[];
    
    @deserializeAs('index')
    private _index: number;
  
    @serializeAs('isExpand')
    @deserializeAs('isExpand')
    private _isExpand: boolean;

    constructor() {
        this.isActive = true;
        this.isSubAccount = false;
    }
    

    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Getter accountName
     * @return {string}
     */
	public get accountName(): string {
		return this._accountName;
	}

    /**
     * Getter accountCode
     * @return {string}
     */
	public get accountCode(): string {
		return this._accountCode;
	}

    /**
     * Getter description
     * @return {string}
     */
	public get description(): string {
		return this._description;
	}

    /**
     * Getter isActive
     * @return {boolean}
     */
	public get isActive(): boolean {
		return this._isActive;
	}

    /**
     * Getter isSubAccount
     * @return {boolean}
     */
	public get isSubAccount(): boolean {
		return this._isSubAccount;
	}

    /**
     * Getter isSelected
     * @return {boolean}
     */
	public get isSelected(): boolean {
		return this._isSelected;
	}

    /**
     * Getter accountTypeId
     * @return {any}
     */
	public get accountTypeId(): any {
		return this._accountTypeId;
	}

    /**
     * Getter lastModifiedDate
     * @return {string}
     */
	public get lastModifiedDate(): string {
		return this._lastModifiedDate;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Setter accountName
     * @param {string} value
     */
	public set accountName(value: string) {
		this._accountName = value;
	}

    /**
     * Setter accountCode
     * @param {string} value
     */
	public set accountCode(value: string) {
		this._accountCode = value;
	}

    /**
     * Setter description
     * @param {string} value
     */
	public set description(value: string) {
		this._description = value;
	}

    /**
     * Setter isActive
     * @param {boolean} value
     */
	public set isActive(value: boolean) {
		this._isActive = value;
	}

    /**
     * Setter isSubAccount
     * @param {boolean} value
     */
	public set isSubAccount(value: boolean) {
		this._isSubAccount = value;
	}

    /**
     * Setter isSelected
     * @param {boolean} value
     */
	public set isSelected(value: boolean) {
		this._isSelected = value;
	}

    /**
     * Setter accountTypeId
     * @param {any} value
     */
	public set accountTypeId(value: any) {
		this._accountTypeId = value;
	}

    /**
     * Setter lastModifiedDate
     * @param {string} value
     */
	public set lastModifiedDate(value: string) {
		this._lastModifiedDate = value;
	}


    /**
     * Getter parentAccountId
     * @return {any}
     */
	public get parentAccountId(): any {
		return this._parentAccountId;
	}

    /**
     * Setter parentAccountId
     * @param {any} value
     */
	public set parentAccountId(value: any) {
		this._parentAccountId = value;
	}


    /**
     * Getter accountChartList
     * @return {AccountChart[]}
     */
	public get accountChartList(): AccountChart[] {
		return this._accountChartList;
	}

    /**
     * Setter accountChartList
     * @param {AccountChart[]} value
     */
	public set accountChartList(value: AccountChart[]) {
		this._accountChartList = value;
	}


    /**
     * Getter index
     * @return {number}
     */
	public get index(): number {
		return this._index;
	}

    /**
     * Setter index
     * @param {number} value
     */
	public set index(value: number) {
		this._index = value;
	}


    /**
     * Getter accountTypeName
     * @return {any}
     */
	public get accountTypeName(): any {
		return this._accountTypeName;
	}

    /**
     * Setter accountTypeName
     * @param {any} value
     */
	public set accountTypeName(value: any) {
		this._accountTypeName = value;
	}
 

    /**
     * Getter isExpand
     * @return {boolean}
     */
	public get isExpand(): boolean {
		return this._isExpand;
	}

    /**
     * Setter isExpand
     * @param {boolean} value
     */
	public set isExpand(value: boolean) {
		this._isExpand = value;
	}

}