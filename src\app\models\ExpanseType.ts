import { deserializeAs, serializeAs } from 'cerialize';

export class ExpanseType {

    @serializeAs('isActive')
    @deserializeAs('isActive')
    private _isActive: boolean;

    @serializeAs('isSelected')
    @deserializeAs('isSelected')
    private _isSelected: boolean;
    
    @serializeAs('isDefault')
    @deserializeAs('isDefault')
    private _isDefault: boolean;

    @serializeAs('lastModifiedDate')
    @deserializeAs('lastModifiedDate')
    private _lastModifiedDate: string;

    @serializeAs('expenseTypeName')
    @deserializeAs('expenseTypeName')
    private _expenseTypeName: string;

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    constructor() {
        this.isActive = false;
        this.isSelected = false;
    }


    /**
     * Getter expenseTypeName
     * @return {string}
     */
	public get expenseTypeName(): string {
		return this._expenseTypeName;
	}

    /**
     * Setter expenseTypeName
     * @param {string} value
     */
	public set expenseTypeName(value: string) {
		this._expenseTypeName = value;
	}


    /**
     * Getter isActive
     * @return {boolean}
     */
	public get isActive(): boolean {
		return this._isActive;
	}

    /**
     * Getter isSelected
     * @return {boolean}
     */
	public get isSelected(): boolean {
		return this._isSelected;
	}

    /**
     * Getter lastModifiedDate
     * @return {string}
     */
	public get lastModifiedDate(): string {
		return this._lastModifiedDate;
	}

    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Setter isActive
     * @param {boolean} value
     */
	public set isActive(value: boolean) {
		this._isActive = value;
	}

    /**
     * Setter isSelected
     * @param {boolean} value
     */
	public set isSelected(value: boolean) {
		this._isSelected = value;
	}

    /**
     * Setter lastModifiedDate
     * @param {string} value
     */
	public set lastModifiedDate(value: string) {
		this._lastModifiedDate = value;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}


    /**
     * Getter isDefault
     * @return {boolean}
     */
	public get isDefault(): boolean {
		return this._isDefault;
	}

    /**
     * Setter isDefault
     * @param {boolean} value
     */
	public set isDefault(value: boolean) {
		this._isDefault = value;
	}

}