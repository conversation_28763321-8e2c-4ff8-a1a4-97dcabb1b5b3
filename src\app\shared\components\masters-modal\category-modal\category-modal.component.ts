import { Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { Serialize } from 'cerialize';
import { Category } from 'src/app/models/Category';
import { UtilsService } from 'src/app/shared/services/utils.service';

@Component({
  selector: 'app-category-modal',
  templateUrl: './category-modal.component.html',
  styleUrls: ['./category-modal.component.css']
})
export class CategoryModalComponent implements OnInit {

  filenameForFlag: string;
  flagForInvalidDocSize = false;
  flagForInvalidExtension = false;

  @Input({ alias: 'categoryGroup', required: true }) categoryGroup: FormGroup;
  @Input({ alias: 'categoryObj', required: true }) categoryObj: Category;
  @Input({ alias: 'selectedFlag', required: true })   selectedFlag: File;
  @Input({ alias: 'page', required: true }) page: string;
  @Input({ alias: 'flag', required: true }) flag: ElementRef;
  @Input({ alias: 'categoryMasterModal', required: true }) categoryMasterModal: any;

  @Output() getRequiredData: EventEmitter<any> = new EventEmitter<any>();

  constructor(public utilsService: UtilsService) { }

  ngOnInit() {
  }

  onSelectProfile(event): void {

    if (event.target.files && event.target.files[0]) {
      this.flagForInvalidExtension = false;
      this.flagForInvalidDocSize = false;
      const reader = new FileReader();
      const max_file_size = 5242880;
      reader.readAsDataURL(event.target.files[0]); // read file as data url
      const selectedFile = event.target.files[0];
      if (selectedFile) {
        const ext = selectedFile.name.substr(selectedFile.name.lastIndexOf('.') + 1);
        const ext1 = (ext).toLowerCase();

        if (ext1 === 'jpeg' || ext1 === 'png' || ext1 === 'jpg' || ext1 === 'jfif') {
          if (max_file_size < selectedFile.size) {
            this.flagForInvalidDocSize = true;
            this.filenameForFlag = '';
            this.utilsService.toasterService.error(this.utilsService.validationService.IMAGE_MAX_FILE_SIZE)
            this.selectedFlag = undefined;
          } else {
            this.filenameForFlag = event.target.files[0].name;
            this.categoryObj.categoryImg = event.target.files[0].name
            this.selectedFlag = event.target.files[0];
            // console.log(this.selectedFlag);
            
          }
        } else {
          this.flagForInvalidExtension = true;
          this.filenameForFlag = '';
          this.selectedFlag = undefined;
          this.utilsService.toasterService.error(this.utilsService.validationService.IMAGE_INVALID_EXTENSION)
        }
      }

    }
  }

  openLink(link) {
    const filePreview = `${this.utilsService.imgPath}${link}`
    if (filePreview) {
      if (link) {
        window.open(filePreview, "_blank");
      }
    }
  }

  onSaveCategory() {

    const formData = new FormData();

    let group = this.categoryGroup
    let modal = this.categoryMasterModal

    if (group.invalid) {
      group.markAllAsTouched();
      return;
    }

    if (this.selectedFlag) {
      formData.set('categoryImg', this.selectedFlag);
    }

    if (!this.categoryObj.categoryImg) {
      this.utilsService.toasterService.error('Category Image is required!', '', {
        positionClass: 'toast-top-right',
        closeButton: true,
        timeOut: 10000
      });
      return;
    }

    let param = this.utilsService.trimObjectValues(Serialize(this.categoryObj));
    delete param['isSelected'];
    delete param['isExpand'];
    delete param['categoryList'];
    formData.set('saveCategoryInfo', JSON.stringify(Serialize(param)));

    this.utilsService.postMethodAPI(true, this.utilsService.serverVariableService.CATEGORY_SAVE_EDIT_DELETE, formData, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        modal.hide();
        this.getRequiredData.emit();
      }
    })
  }

}
