<div class="page-content page-content-with-tabs">
    <div class="page-title-wrapper page-title-with-tab">
        <div class="page-title-left">
            <h4>Audit Tickets</h4>
        </div>
        <div class="page-title-right">
            <button class="btn btn-sm btn-primary btn-icon-text"
                [routerLink]="['/users/audit-tickets/new-audit-tickets']">
                <i class="th th-outline-add-circle"></i>Add New
            </button>
            <Button class="btn btn-sm btn-icon btn-outline-white" ngbTooltip="Refresh" placement="left" container="body"
                triggers="hover">
                <i class="th th-outline-refresh-2"></i>
            </Button>
        </div>
    </div>
    <div class="content-area">
        <div class='nav-tabs-outer nav-tabs-style2'>
            <nav>
                <div class="nav nav-tabs" id="nav-tab" role="tablist">
                    <button class="nav-link active" id="nav-New-tab" data-bs-toggle="tab" data-bs-target="#nav-New"
                        type="button" role="tab" aria-controls="nav-New" aria-selected="true"> <i
                            class="th th-outline-document-text"></i>New</button>
                    <button class="nav-link" id="nav-Resolved-tab" data-bs-toggle="tab" data-bs-target="#nav-Resolved"
                        type="button" role="tab" aria-controls="nav-Resolved" aria-selected="false"><i
                            class="th th-outline-tick-circle"></i>Resolved</button>
                    <button class="nav-link" id="nav-Deleted-tab" data-bs-toggle="tab" data-bs-target="#nav-Deleted"
                        type="button" role="tab" aria-controls="nav-Deleted" aria-selected="false"><i
                            class="th th-outline-trash"></i>Deleted</button>

                </div>
            </nav>
            <div class="tab-content" id="nav-tabContent">
                <div class="tab-pane fade show active pt-0" id="nav-New" role="tabpanel" aria-labelledby="nav-New-tab">
                    <app-audit-ticket-new />
                </div>
                <div class="tab-pane fade pt-0" id="nav-Resolved" role="tabpanel" aria-labelledby="nav-Resolved-tab">
                    <app-audit-ticket-resolved />
                </div>
                <div class="tab-pane fade pt-0" id="nav-Deleted" role="tabpanel" aria-labelledby="nav-Deleted-tab">
                    <app-audit-ticket-deleted />
                </div>
            </div>
        </div>
    </div>
</div>


<!-- ----------------------------------------------------------------------- -->
<!--                 Create Audit Ticket Forms Modal Start                 -->
<!-- ----------------------------------------------------------------------- -->
<div class="modal modal-theme fade" id="auditTicketModal" tabindex="-1" aria-labelledby="auditTicketModalLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="auditTicketModalLabel">Create Audit Ticket</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
                        class='th th-close'></i></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-12">
                        <div class="form-group theme-ngselect required">
                            <label class="form-label">Ticket Subject</label>
                            <ng-select placeholder="Ticket Subject" [multiple]="false" [clearable]="false"
                                [items]="demo" bindLabel="name" bindValue="id" [(ngModel)]="selectedDemo1">
                            </ng-select>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="form-group theme-ngselect">
                            <label class="form-label">Assign to</label>
                            <ng-select placeholder="Assign to" [multiple]="false" [clearable]="false" [items]="demo"
                                bindLabel="name" bindValue="id" [(ngModel)]="selectedDemo1">
                            </ng-select>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="form-group required">
                            <label class="form-label">Date</label>
                            <div class="form-control-wrapper">
                                <div class="form-group-icon-end">
                                    <i class="th th-outline-calendar"></i>
                                    <input type="text" class="form-control" placeholder="Select Date">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="form-group">
                            <label class="form-label">Note</label>
                            <textarea class="form-control" placeholder="Enter Note"></textarea>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <div class="modal-footer-group full-width-btn">
                    <button type="button" class="btn btn-primary btn-icon-text"> <i
                            class="th th-outline-tick-circle"></i>
                        Save</button>
                    <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                  Create Audit Ticket Forms Modal End                  -->
<!-- ----------------------------------------------------------------------- -->