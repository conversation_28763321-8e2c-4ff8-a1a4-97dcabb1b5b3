<div class="card card-theme card-forms">
    <div class="card-body p-0">
        <div class="row" [formGroup]="poImageFGP">
            <div class="col-8">
                <div class="inner-title-wrapper">
                    <div class="inner-title-left">
                        <div class="inner-title-text">
                            <h6 class="">PO Color Image Mapping</h6>
                        </div>
                    </div>
                    <div class="inner-title-rigth">
                    </div>
                </div>
                <div class="table-responsive" formArrayName="imageMapping">
                    <table class="table-theme table-hover table table-bordered ">
                        <thead class="border-less">
                            <tr>
                                <th>Color</th>
                                <th>Upload Images</th>
                                <th class="text-center">Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="tbl-add-row" *ngFor="let contact of imageMapping.controls; index as i"
                                [formGroupName]="i">
                                <td class="tbl-form-group">
                                    <div class="form-group theme-ngselect form-border-less">
                                        <ng-select [ngClass]="{'required': contact.get('color').invalid && contact.get('color').touched}" class=""
                                            placeholder="Select Color" [multiple]="true" [clearable]="false" formControlName="color"
                                            [items]="pOImageMapping[i]?.colorDropdown" bindLabel="label" bindValue="value" [clearSearchOnAdd]="true"
                                            [(ngModel)]="pOImageMapping[i].colors" appendTo=".theme-ngselect" [closeOnSelect]="false">
                                        </ng-select>
                                    </div>
                                </td>
                                <td class="tbl-user ">
                                    <div class="tbl-user-checkbox-srno">
                                        <div class="tbl-user-wrapper">
                                            <div class="tbl-user-image tbl-image-close"
                                                *ngFor="let img of pOImageMapping[i].images; index as j"
                                                [ngbTooltip]="img.fileName ? img.fileName : img.originalName">
                                                <img *ngIf="img.originalname || img.formattedName"
                                                    [src]="img.originalname ? img.originalname  : utilsService.imgPath + img.formattedName"
                                                    alt="valamji">
                                                <button (click)="onRemoveColorImg(i, img, j)" class="btn-close">
                                                    <i class="th th-close"></i>
                                                </button>
                                            </div>
                                            <button (click)="colorPo.click()" class="btn btn-sm btn-icon btn-light-info"
                                                ngbTooltip="Upload" placement="bottom" container="body"
                                                triggers="hover">
                                                <i class="bi bi-upload"></i>
                                            </button>
                                            <input #colorPo type="file" (change)="onSelectImage($event, i);colorPo.value = ''"
                                                accept="image/*" hidden [multiple]="true">
                                        </div>
                                    </div>
                                </td>
                                <td class="tbl-action">
                                    <div class="tbl-action-group">
                                        <button (click)="openRemoveModal(i)" *ngIf="pOImageMapping.length > 1"
                                            class="btn btn-xs btn-light-danger btn-icon" ngbTooltip="Remove Mapping"
                                            placement="left" container="body" triggers="hover">
                                            <i class="th th-outline-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr *ngIf="utilsService.isEmptyObjectOrNullUndefined(pOImageMapping)">
                                <td colspan="20" class="text-center">
                                    <span
                                        class="truncate">{{utilsService.serverVariableService.STRING_WHEN_NO_RECORDS_FOUND}}</span>
                                </td>
                            </tr>
                        </tbody>
                        <tfoot>
                            <tr class="tbl-add-new">
                                <td colspan="100">
                                    <button (click)="addPOMapping.emit()"
                                        class="btn btn-sm btn-link btn-icon-text text-primary"> <i
                                            class="th-bold-add-circle"></i>
                                        Add New Row
                                    </button>
                                </td>
                            </tr>
                        </tfoot>

                    </table>
                </div>
            </div>
        </div>
    </div>
</div>