import { serializeAs } from "cerialize";
import { Subject } from "rxjs";

export class LoadingContainerPagination {

    @serializeAs('status')
    private _status: string;

    @serializeAs('toDate')
    private _toDate: string;

    @serializeAs('fromDate')
    private _fromDate: string;

    @serializeAs('pageNo')
    private _pageNo: number;

    @serializeAs('pageSize')
    private _pageSize: string;

    @serializeAs('searchByPoContainer')
    private _searchByPoContainer: string;

    @serializeAs('expectedDeliveryDate')
    private _expectedDeliveryDate: string;

    @serializeAs('loadedDate')
    private _loadedDate: string;

    @serializeAs('isFlag')
    private _isFlag: boolean;

    private _totalData: number;

    private _pagination: any;

    private _dateRange: any;

    private _temp_expectedDeliveryDate: any;

    private _temp_loadedDate: any;

    private _flagForSelectAll: any;

    private _searchByPoContainerSubject: Subject<string>;

    constructor() {
        this.pageNo = 1;
        this.pageSize = "100"
        this.fromDate = null;
        this.toDate = null;
        this.flagForSelectAll = false;
        this.searchByPoContainerSubject = new Subject<string>();
        this.isFlag = true;
    }


    /**
     * Getter isFlag
     * @return {boolean}
     */
	public get isFlag(): boolean {
		return this._isFlag;
	}

    /**
     * Setter isFlag
     * @param {boolean} value
     */
	public set isFlag(value: boolean) {
		this._isFlag = value;
	}


    /**
     * Getter loadedDate
     * @return {string}
     */
	public get loadedDate(): string {
		return this._loadedDate;
	}

    /**
     * Getter temp_loadedDate
     * @return {any}
     */
	public get temp_loadedDate(): any {
		return this._temp_loadedDate;
	}

    /**
     * Setter loadedDate
     * @param {string} value
     */
	public set loadedDate(value: string) {
		this._loadedDate = value;
	}

    /**
     * Setter temp_loadedDate
     * @param {any} value
     */
	public set temp_loadedDate(value: any) {
		this._temp_loadedDate = value;
	}


    /**
     * Getter flagForSelectAll
     * @return {any}
     */
	public get flagForSelectAll(): any {
		return this._flagForSelectAll;
	}

    /**
     * Setter flagForSelectAll
     * @param {any} value
     */
	public set flagForSelectAll(value: any) {
		this._flagForSelectAll = value;
	}


    /**
     * Getter status
     * @return {string}
     */
	public get status(): string {
		return this._status;
	}

    /**
     * Getter toDate
     * @return {string}
     */
	public get toDate(): string {
		return this._toDate;
	}

    /**
     * Getter fromDate
     * @return {string}
     */
	public get fromDate(): string {
		return this._fromDate;
	}

    /**
     * Getter pageNo
     * @return {number}
     */
	public get pageNo(): number {
		return this._pageNo;
	}

    /**
     * Getter pageSize
     * @return {string}
     */
	public get pageSize(): string {
		return this._pageSize;
	}

    /**
     * Getter searchByPoContainer
     * @return {string}
     */
	public get searchByPoContainer(): string {
		return this._searchByPoContainer;
	}

    /**
     * Getter expectedDeliveryDate
     * @return {string}
     */
	public get expectedDeliveryDate(): string {
		return this._expectedDeliveryDate;
	}

    /**
     * Getter totalData
     * @return {number}
     */
	public get totalData(): number {
		return this._totalData;
	}

    /**
     * Getter pagination
     * @return {any}
     */
	public get pagination(): any {
		return this._pagination;
	}

    /**
     * Getter dateRange
     * @return {any}
     */
	public get dateRange(): any {
		return this._dateRange;
	}

    /**
     * Getter temp_expectedDeliveryDate
     * @return {any}
     */
	public get temp_expectedDeliveryDate(): any {
		return this._temp_expectedDeliveryDate;
	}

    /**
     * Getter searchByPoContainerSubject
     * @return {Subject<string>}
     */
	public get searchByPoContainerSubject(): Subject<string> {
		return this._searchByPoContainerSubject;
	}

    /**
     * Setter status
     * @param {string} value
     */
	public set status(value: string) {
		this._status = value;
	}

    /**
     * Setter toDate
     * @param {string} value
     */
	public set toDate(value: string) {
		this._toDate = value;
	}

    /**
     * Setter fromDate
     * @param {string} value
     */
	public set fromDate(value: string) {
		this._fromDate = value;
	}

    /**
     * Setter pageNo
     * @param {number} value
     */
	public set pageNo(value: number) {
		this._pageNo = value;
	}

    /**
     * Setter pageSize
     * @param {string} value
     */
	public set pageSize(value: string) {
		this._pageSize = value;
	}

    /**
     * Setter searchByPoContainer
     * @param {string} value
     */
	public set searchByPoContainer(value: string) {
		this._searchByPoContainer = value;
	}

    /**
     * Setter expectedDeliveryDate
     * @param {string} value
     */
	public set expectedDeliveryDate(value: string) {
		this._expectedDeliveryDate = value;
	}

    /**
     * Setter totalData
     * @param {number} value
     */
	public set totalData(value: number) {
		this._totalData = value;
	}

    /**
     * Setter pagination
     * @param {any} value
     */
	public set pagination(value: any) {
		this._pagination = value;
	}

    /**
     * Setter dateRange
     * @param {any} value
     */
	public set dateRange(value: any) {
		this._dateRange = value;
	}

    /**
     * Setter temp_expectedDeliveryDate
     * @param {any} value
     */
	public set temp_expectedDeliveryDate(value: any) {
		this._temp_expectedDeliveryDate = value;
	}

    /**
     * Setter searchByPoContainerSubject
     * @param {Subject<string>} value
     */
	public set searchByPoContainerSubject(value: Subject<string>) {
		this._searchByPoContainerSubject = value;
	}


}