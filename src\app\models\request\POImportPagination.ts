import { FormControl } from "@angular/forms";
import { serializeAs } from "cerialize";
import { Subject } from "rxjs";

export class POImportPagination {

    @serializeAs('status')
    private _status: string;

    @serializeAs('searchText')
    private _searchText: string;

    @serializeAs('customerId')
    private _customerId: number;

    @serializeAs('supplierId')
    private _supplierId: number;

    @serializeAs('toDate')
    private _toDate: string;

    @serializeAs('fromDate')
    private _fromDate: string;

    @serializeAs('pageNo')
    private _pageNo: number;

    @serializeAs('pageSize')
    private _pageSize: string;

    @serializeAs('purchaseOrderSearch')
    private _purchaseOrderSearch: string;

    @serializeAs('searchPurchaseComments')
    private _searchPurchaseComments: string;

    @serializeAs('searchByPoContainer')
    private _searchByPoContainer: string;

    @serializeAs('expectedDeliveryDate')
    private _expectedDeliveryDate: string;

    @serializeAs('loadedDate')
    private _loadedDate: string;

    @serializeAs('importerId')
    private _importerId: number;

    @serializeAs('tempoStatus')
    private _tempoStatus: number;

    @serializeAs('fromDateTime')
    private _fromDateTime: string;

    @serializeAs('toDateTime')
    private _toDateTime: string;

    private _totalData: number;

    private _pagination: any;

    private _dateRange: any;

    private _dateRangeControl: FormControl<any>;

    private _tempoDateRange: any;

    private _temp_expectedDeliveryDate: any;

    private _temp_loadedDate: any;

    private _purchaseOrderSearchSubject: Subject<string>;

    private _searchPurchaseCommentsSubject: Subject<string>;

    private _searchByPoContainerSubject: Subject<string>;

    private _searchByMarkaSubject: Subject<string>;

    constructor() {
        this.pageNo = 1;
        this.pageSize = "100"
        this.purchaseOrderSearchSubject = new Subject<string>();
        this.searchPurchaseCommentsSubject = new Subject<string>();
        this.searchByPoContainerSubject = new Subject<string>();
        this.searchByMarkaSubject = new Subject<string>();
        this.dateRangeControl = new FormControl<any>('');
    }


    /**
     * Getter status
     * @return {string}
     */
	public get status(): string {
		return this._status;
	}

    /**
     * Getter searchText
     * @return {string}
     */
	public get searchText(): string {
		return this._searchText;
	}

    /**
     * Getter customerId
     * @return {number}
     */
	public get customerId(): number {
		return this._customerId;
	}

    /**
     * Getter supplierId
     * @return {number}
     */
	public get supplierId(): number {
		return this._supplierId;
	}

    /**
     * Getter toDate
     * @return {string}
     */
	public get toDate(): string {
		return this._toDate;
	}

    /**
     * Getter fromDate
     * @return {string}
     */
	public get fromDate(): string {
		return this._fromDate;
	}

    /**
     * Getter pageNo
     * @return {number}
     */
	public get pageNo(): number {
		return this._pageNo;
	}

    /**
     * Getter pageSize
     * @return {string}
     */
	public get pageSize(): string {
		return this._pageSize;
	}

    /**
     * Getter purchaseOrderSearch
     * @return {string}
     */
	public get purchaseOrderSearch(): string {
		return this._purchaseOrderSearch;
	}

    /**
     * Getter searchPurchaseComments
     * @return {string}
     */
	public get searchPurchaseComments(): string {
		return this._searchPurchaseComments;
	}

    /**
     * Getter searchByPoContainer
     * @return {string}
     */
	public get searchByPoContainer(): string {
		return this._searchByPoContainer;
	}

    /**
     * Getter expectedDeliveryDate
     * @return {string}
     */
	public get expectedDeliveryDate(): string {
		return this._expectedDeliveryDate;
	}

    /**
     * Getter loadedDate
     * @return {string}
     */
	public get loadedDate(): string {
		return this._loadedDate;
	}

    /**
     * Getter importerId
     * @return {number}
     */
	public get importerId(): number {
		return this._importerId;
	}

    /**
     * Getter tempoStatus
     * @return {number}
     */
	public get tempoStatus(): number {
		return this._tempoStatus;
	}

    /**
     * Getter fromDateTime
     * @return {string}
     */
	public get fromDateTime(): string {
		return this._fromDateTime;
	}

    /**
     * Getter toDateTime
     * @return {string}
     */
	public get toDateTime(): string {
		return this._toDateTime;
	}

    /**
     * Getter totalData
     * @return {number}
     */
	public get totalData(): number {
		return this._totalData;
	}

    /**
     * Getter pagination
     * @return {any}
     */
	public get pagination(): any {
		return this._pagination;
	}

    /**
     * Getter dateRange
     * @return {any}
     */
	public get dateRange(): any {
		return this._dateRange;
	}

    /**
     * Getter dateRangeControl
     * @return {FormControl<any>}
     */
	public get dateRangeControl(): FormControl<any> {
		return this._dateRangeControl;
	}

    /**
     * Getter tempoDateRange
     * @return {any}
     */
	public get tempoDateRange(): any {
		return this._tempoDateRange;
	}

    /**
     * Getter temp_expectedDeliveryDate
     * @return {any}
     */
	public get temp_expectedDeliveryDate(): any {
		return this._temp_expectedDeliveryDate;
	}

    /**
     * Getter temp_loadedDate
     * @return {any}
     */
	public get temp_loadedDate(): any {
		return this._temp_loadedDate;
	}

    /**
     * Getter purchaseOrderSearchSubject
     * @return {Subject<string>}
     */
	public get purchaseOrderSearchSubject(): Subject<string> {
		return this._purchaseOrderSearchSubject;
	}

    /**
     * Getter searchPurchaseCommentsSubject
     * @return {Subject<string>}
     */
	public get searchPurchaseCommentsSubject(): Subject<string> {
		return this._searchPurchaseCommentsSubject;
	}

    /**
     * Getter searchByPoContainerSubject
     * @return {Subject<string>}
     */
	public get searchByPoContainerSubject(): Subject<string> {
		return this._searchByPoContainerSubject;
	}

    /**
     * Getter searchByMarkaSubject
     * @return {Subject<string>}
     */
	public get searchByMarkaSubject(): Subject<string> {
		return this._searchByMarkaSubject;
	}

    /**
     * Setter status
     * @param {string} value
     */
	public set status(value: string) {
		this._status = value;
	}

    /**
     * Setter searchText
     * @param {string} value
     */
	public set searchText(value: string) {
		this._searchText = value;
	}

    /**
     * Setter customerId
     * @param {number} value
     */
	public set customerId(value: number) {
		this._customerId = value;
	}

    /**
     * Setter supplierId
     * @param {number} value
     */
	public set supplierId(value: number) {
		this._supplierId = value;
	}

    /**
     * Setter toDate
     * @param {string} value
     */
	public set toDate(value: string) {
		this._toDate = value;
	}

    /**
     * Setter fromDate
     * @param {string} value
     */
	public set fromDate(value: string) {
		this._fromDate = value;
	}

    /**
     * Setter pageNo
     * @param {number} value
     */
	public set pageNo(value: number) {
		this._pageNo = value;
	}

    /**
     * Setter pageSize
     * @param {string} value
     */
	public set pageSize(value: string) {
		this._pageSize = value;
	}

    /**
     * Setter purchaseOrderSearch
     * @param {string} value
     */
	public set purchaseOrderSearch(value: string) {
		this._purchaseOrderSearch = value;
	}

    /**
     * Setter searchPurchaseComments
     * @param {string} value
     */
	public set searchPurchaseComments(value: string) {
		this._searchPurchaseComments = value;
	}

    /**
     * Setter searchByPoContainer
     * @param {string} value
     */
	public set searchByPoContainer(value: string) {
		this._searchByPoContainer = value;
	}

    /**
     * Setter expectedDeliveryDate
     * @param {string} value
     */
	public set expectedDeliveryDate(value: string) {
		this._expectedDeliveryDate = value;
	}

    /**
     * Setter loadedDate
     * @param {string} value
     */
	public set loadedDate(value: string) {
		this._loadedDate = value;
	}

    /**
     * Setter importerId
     * @param {number} value
     */
	public set importerId(value: number) {
		this._importerId = value;
	}

    /**
     * Setter tempoStatus
     * @param {number} value
     */
	public set tempoStatus(value: number) {
		this._tempoStatus = value;
	}

    /**
     * Setter fromDateTime
     * @param {string} value
     */
	public set fromDateTime(value: string) {
		this._fromDateTime = value;
	}

    /**
     * Setter toDateTime
     * @param {string} value
     */
	public set toDateTime(value: string) {
		this._toDateTime = value;
	}

    /**
     * Setter totalData
     * @param {number} value
     */
	public set totalData(value: number) {
		this._totalData = value;
	}

    /**
     * Setter pagination
     * @param {any} value
     */
	public set pagination(value: any) {
		this._pagination = value;
	}

    /**
     * Setter dateRange
     * @param {any} value
     */
	public set dateRange(value: any) {
		this._dateRange = value;
	}

    /**
     * Setter dateRangeControl
     * @param {FormControl<any>} value
     */
	public set dateRangeControl(value: FormControl<any>) {
		this._dateRangeControl = value;
	}

    /**
     * Setter tempoDateRange
     * @param {any} value
     */
	public set tempoDateRange(value: any) {
		this._tempoDateRange = value;
	}

    /**
     * Setter temp_expectedDeliveryDate
     * @param {any} value
     */
	public set temp_expectedDeliveryDate(value: any) {
		this._temp_expectedDeliveryDate = value;
	}

    /**
     * Setter temp_loadedDate
     * @param {any} value
     */
	public set temp_loadedDate(value: any) {
		this._temp_loadedDate = value;
	}

    /**
     * Setter purchaseOrderSearchSubject
     * @param {Subject<string>} value
     */
	public set purchaseOrderSearchSubject(value: Subject<string>) {
		this._purchaseOrderSearchSubject = value;
	}

    /**
     * Setter searchPurchaseCommentsSubject
     * @param {Subject<string>} value
     */
	public set searchPurchaseCommentsSubject(value: Subject<string>) {
		this._searchPurchaseCommentsSubject = value;
	}

    /**
     * Setter searchByPoContainerSubject
     * @param {Subject<string>} value
     */
	public set searchByPoContainerSubject(value: Subject<string>) {
		this._searchByPoContainerSubject = value;
	}

    /**
     * Setter searchByMarkaSubject
     * @param {Subject<string>} value
     */
	public set searchByMarkaSubject(value: Subject<string>) {
		this._searchByMarkaSubject = value;
	}


}