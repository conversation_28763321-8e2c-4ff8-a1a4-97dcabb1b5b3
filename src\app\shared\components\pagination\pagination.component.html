<div class='pagination-container'>
  <div class='pagination-left'>
    <!-- <p class='mb-0'>1-1 of 100 items</p> -->
    <p class='mb-0'>{{+pageSize * (page -1 )+1}}-{{ +pageSize * page}} of {{ totalData }} items</p>
  </div>
  <div class='pagination-right'>
    <ul>
      <li class="previous"><button class="btn btn-sm" (click)="onPrevious()" [disabled]="this.page == 1">
          <i class='th th-outline-arrow-left-2'></i></button>
      </li>
      <li class="number" *ngFor="let title of ChangeArray" [ngClass]="this.page == title ? 'active': ''"><button
          class="btn btn-sm" (click)="onPaginationClick(title)">
          {{title}}</button>
      </li>
      <!-- <li class="number active"><button class="btn btn-sm">1</button></li>
      <li class="number"><button class="btn btn-sm">2</button></li>
      <li class="number"><button class="btn btn-sm">...</button></li>
      <li class="number"><button class="btn btn-sm">14</button></li>
      <li class="number"><button class="btn btn-sm">15</button></li> -->
      <li class="next"><button class="btn btn-sm" (click)="onNext()"
          [disabled]="this.paginatedArray.length == this.page">
          <i class='th th-outline-arrow-right-3'></i></button>
      </li>
    </ul>
    <div class="form-group theme-ngselect form-group-sm mb-0">
      <ng-select *ngIf="!smallTable" [items]="pageSizeList" bindLabel="label" bindValue="value" [(ngModel)]="pageSize"
        (keyup)="onKeyDown($event)" (change)="onSelectPageLength()" [clearable]="false" [searchable]="false">
      </ng-select>
      <ng-select *ngIf="smallTable" [items]="pageSizeList" bindLabel="label" bindValue="value" [(ngModel)]="pageSize"
        appendTo="body" [dropdownPosition]="'auto'" (keyup)="onKeyDown($event)"
        (change)="onSelectPageLength()" [clearable]="false" [searchable]="false">
      </ng-select>
    </div>
  </div>
</div>
