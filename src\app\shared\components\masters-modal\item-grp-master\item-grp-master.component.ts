import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { Serialize } from 'cerialize';
import { Item } from 'src/app/models/Item';
import { ItemGroup } from 'src/app/models/ItemGroup';
import { UtilsService } from 'src/app/shared/services/utils.service';

@Component({
  selector: 'app-item-grp-master',
  templateUrl: './item-grp-master.component.html',
  styleUrls: ['./item-grp-master.component.css']
})
export class ItemGrpMasterComponent implements OnInit {

  @Input({ alias: 'itemGroupForm', required: true }) itemGroupForm: FormGroup;
  @Input({ alias: 'itemGroupObj', required: true }) itemGroupObj: ItemGroup;
  @Input({ alias: 'hsnCodeDropdown', required: true }) hsnCodeDropdown: any;
  @Input({ alias: 'itemObj', required: true }) itemObj: Item;
  @Input({ alias: 'modal', required: true }) modal: any;
  @Input({ alias: 'flattenedParentCategory', required: true }) flattenedParentCategory: any[];
  @Input({ alias: 'page', required: true }) page: string;

  @Output() getRequiredData: EventEmitter<any> = new EventEmitter<any>();

  constructor(public utilsService: UtilsService) { }

  ngOnInit() {
  }

  onSave() {

    const formData = new FormData();
    if (this.itemGroupForm.invalid) {
      this.itemGroupForm.markAllAsTouched();
      return;
    }

    this.itemGroupObj.isActive = true;

    formData.set('itemGroupInfo', JSON.stringify(Serialize(this.itemGroupObj)));
    this.utilsService.postMethodAPI(true, this.utilsService.serverVariableService.IG_SAVE_EDIT_DELETE, formData, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        if(response.id) {
          this.itemObj.itemGroupId = response.id
        }
        this.getRequiredData.emit();
        this.modal.hide();
      }
    })

  }

  clearAll() {
    this.itemGroupObj.hsnCodeId = null;
  }

}
