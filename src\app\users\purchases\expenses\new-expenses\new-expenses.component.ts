import { Compo<PERSON>, On<PERSON><PERSON>roy, OnInit, ViewChild, inject } from "@angular/core";
import { FormGroup, FormBuilder, Validators, FormArray } from "@angular/forms";
import { ActivatedRoute } from "@angular/router";
import { EnumForExpenseFinal } from "@enums/EnumForExpenseFinal.enum";
import { ContainerExpense, ContainerExpenseList } from "@modal/ContainerExpense";
import { UtilsService } from "@service/utils.service";
import { Serialize, Deserialize } from "cerialize";
import dayjs from "dayjs";
import moment from "moment";
import { DaterangepickerDirective } from "ngx-daterangepicker-material";
import { Subject, takeUntil, tap } from "rxjs";
declare var window: any;

interface Dropdown {
  containers: any[];
  currency: any[];
  expenseCarringTypes: any[];
  importer: any[];
  OurShippingTypes: any[];
  CHAShippingTypes: any[];
  expenseTypes: any[];
}

@Component({
  selector: 'app-new-expenses',
  templateUrl: './new-expenses.component.html',
  styleUrls: ['./new-expenses.component.css']
})
export class NewExpensesComponent implements OnInit, OnDestroy {
  demo = [
    { id: 1, name: 'Option 1' },
    { id: 2, name: 'Option 2' },
    { id: 3, name: 'Option 3' },
  ];

  selectedDemo1: number = 1;

  @ViewChild(DaterangepickerDirective, { static: true }) pickerDirectiveModal: DaterangepickerDirective;
  utilsService = inject(UtilsService);

  enumForExpenseFinal = EnumForExpenseFinal
  dropdown: Dropdown = {
    containers: [],
    currency: [],
    expenseCarringTypes: [],
    importer: [],
    OurShippingTypes: [],
    CHAShippingTypes: [],
    expenseTypes: []
  }

  deleteContainerExpense: any;
  containerMaxDate: any;

  expenseId: number;
  containerId: number;
  selectedExpIndex: number;
  expenseGroup: FormGroup;
  containerExpenseObj = new ContainerExpense();

  fromContainerRedirect: boolean = false;
  isOldRecordFound: boolean = false;

  destroy$ = new Subject();

  constructor(private fb: FormBuilder, private route: ActivatedRoute) {
    this.expenseId = +this.route.snapshot.params['id']

    // From Container
    this.fromContainerRedirect = false;
    this.containerId = +(this.route.snapshot.paramMap.get('containerId'));
    if (this.containerId) {
      this.fromContainerRedirect = true;
    }
  }

  ngOnInit() {
    this.initForm();

    // Current date initally
    this.initStartDate();

    this.getRequiredData(null, true);

    this.containerMaxDate = dayjs().endOf('day').toDate().toISOString();

    this.deleteContainerExpense = new window.bootstrap.Modal(
      document.getElementById('deleteContainerExpense')
    );

    this.expenseGroup.get('containerID')?.valueChanges.pipe(
      tap(value => {
        const importerId = this.dropdown.containers.find(v => v.id === value)?.importerId || null;
        const shippingTypes = this.dropdown.containers.find(v => v.id === value)?.shippingTypes?.value || null;
        this.expenseGroup.get('importerID')?.setValue(importerId);
        this.expenseGroup.get('chaShippingTypes').setValue(shippingTypes);
      }),
      takeUntil(this.destroy$)
    ).subscribe();

  }

  ngOnDestroy(): void {
    this.destroy$.next(true);
    this.destroy$.complete();
  }

  initForm = () => {
    this.expenseGroup = this.fb.group({
      expenseDate: [null, Validators.compose([Validators.required])],
      containerID: [null, Validators.compose([Validators.required])],
      ourShippingTypes: [null, Validators.compose([Validators.required])],
      importerID: [null, Validators.compose([Validators.required])],
      notes: [null],
      chaShippingTypes: [null, Validators.compose([Validators.required])],
      expenseCarringType: [null],
      ourPrice: [null],
      chaPrice: [null],
      otherPrice: [null],
      expenseTypeItems: this.fb.array([])
    });
  }

  initStartDate = () => {
    this.containerExpenseObj.temp_date = this.containerExpenseObj.temp_date ?? {};
    this.containerExpenseObj.temp_date.start = moment().toDate();
    this.containerExpenseObj.temp_date.end = moment().toDate();
  }

  get expenseCarringType() {
    return this.expenseGroup.get('expenseCarringType').value;
  }

  // On Change expenseCarringType
  onChangeExpenseCarringType = (value: string) => {
    const otherPriceControl = this.expenseGroup.get('otherPrice');
    const chaPriceControl = this.expenseGroup.get('chaPrice');
    const ourPriceControl = this.expenseGroup.get('ourPrice');

    [otherPriceControl, chaPriceControl, ourPriceControl].forEach(control => {
      control.clearValidators();
      control.markAsUntouched();
      control.updateValueAndValidity();
    });

    switch (value) {
      case this.enumForExpenseFinal.OTHER:
        otherPriceControl.setValidators([Validators.required]);
        otherPriceControl.updateValueAndValidity();
        break;

      case this.enumForExpenseFinal.AGENT:
        chaPriceControl.setValidators([Validators.required]);
        chaPriceControl.updateValueAndValidity();
        break;

      case this.enumForExpenseFinal.OURS:
        ourPriceControl.setValidators([Validators.required]);
        ourPriceControl.updateValueAndValidity();
        break;
      default:
        break;
    }
  };

  defaultExpenseCarringType = () => {
    this.containerExpenseObj.expenseCarringType = this.enumForExpenseFinal.OURS;
    this.expenseGroup.get('expenseCarringType').setValue(this.enumForExpenseFinal.OURS);
    this.onChangeExpenseCarringType(this.enumForExpenseFinal.OURS);
  }

  getRequiredData = (changedContainer: number, firstLoad: boolean) => {
    let API = null;
    if (changedContainer || this.containerId) {
      const containerID = changedContainer ? changedContainer : this.containerId;
      API = `${this.utilsService.serverVariableService.REQ_CONTAINER_EXP}?containerID=${containerID}`;
    } else {
      API = `${this.utilsService.serverVariableService.REQ_CONTAINER_EXP}${this.expenseId ? `?id=${this.expenseId}` : ''}`;
    }

    this.utilsService.getMethodAPI(false, API, null, (response: any) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {

        // Default
        this.defaultExpenseCarringType();

        this.dropdown = {
          containers: response.containers || [],
          currency: response.currency || [],
          expenseCarringTypes: response.expenseCarringTypes || [],
          importer: this.utilsService.transformDropdownItems((response.importer || [])),
          OurShippingTypes: response.shippingTypes || [],
          CHAShippingTypes: response.shippingTypes || [],
          expenseTypes: this.utilsService.transformDropdownItems((response.expenseTypes || []))
        };

        // Redirect from Tempo
        if (this.fromContainerRedirect && firstLoad) {
          this.containerExpenseObj.containerID = this.containerId;
          this.expenseGroup.get('containerID').setValue(this.containerId);
          if(response?.poExpense) {
            this.setGetData(response);
            this.isOldRecordFound = true;
          }
        }

        if (this.expenseId && (response?.poExpense)) {
          this.expenseGroup.get('containerID').disable();
          this.expenseGroup.get('containerID').updateValueAndValidity();
          this.setGetData(response);
        }

        // On container change check if it already has data
        if (changedContainer) {
          if(response?.expensePOTypeItems) {
            this.setExpenseType(response);
          } else {
            this.resetFormExpense(changedContainer);
          }
          if (response?.poExpense) {
            this.setGetData(response);
            this.isOldRecordFound = true;
          } else if (this.isOldRecordFound) {
            this.isOldRecordFound = false;
            this.resetFormExpense(changedContainer);
          }
        }

        if (!this.expenseId && !changedContainer) {
          this.addExpenseTypeItem();
        }

         //Dropdown disabled value checking
        this.dropdown.importer = this.utilsService.filterIsActiveLV(this.dropdown?.importer, (this.containerExpenseObj?.importerID || null));
      }
    })
  }

  resetFormExpense = (changedContainer: number) => {
    this.expenseGroup.reset();
    const fa = this.expenseGroup.get('expenseTypeItems') as FormArray;
    fa.clear();
    this.containerExpenseObj = new ContainerExpense();
    this.containerExpenseObj.containerID = changedContainer;
    this.expenseGroup.get('containerID').setValue(changedContainer);
    this.addExpenseTypeItem();
    this.defaultExpenseCarringType();
    this.initStartDate();
  }

  // documents
  onSelectAttachments = (event) => {

    let selectedFiles: FileList | null = null;
    if (event.type === 'drop') {
      event.preventDefault();
      selectedFiles = event.dataTransfer?.files;
    }

    if (event.type === 'dragover') {
      event.preventDefault();
    }

    if (event.type === 'change') {
      selectedFiles = event.target.files;
    }

    if (event.type === 'paste') {
      const items = (event.clipboardData.items);
      const dataTransfer = new DataTransfer();

      for (const item of items) {
        if (item.type.indexOf('image') === 0) {
          const blob = item.getAsFile();
          const fileName = blob?.name;
          if (blob !== null && fileName) {
            const fileName = `${Date.now()}-image.${item.type.split('/')[1]}`;
            const fileFromBlob = new File([blob], fileName, { type: item.type });
            dataTransfer.items.add(fileFromBlob);
          }
        }
      }
      selectedFiles = dataTransfer.files;
    }

    const max_file_size = 5242880;

    if (selectedFiles) {
      Array.from(selectedFiles).forEach((file: File, i: number) => {
        const ext = file.name.substr(file.name.lastIndexOf('.') + 1).toLowerCase();

        if (['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx', 'jfif', 'webp', 'xls', 'xlsx'].includes(ext)) {
          if (file.size > max_file_size) {
            this.utilsService.toasterService.error(this.utilsService.validationService.ATTACHMENT_MAX_FILE_SIZE + "5 MB");
          } else {
            const fileUrl = URL.createObjectURL(file);
            let fileData = null;
            fileData = {
              id: null,
              file: file,
              originalName: file.name,
              formattedName: fileUrl,
            };

            if (this.containerExpenseObj?.docs.length >= 10) {
              this.utilsService.toasterService.error('You have exceeded the file upload limit of 10', '', {
                positionClass: 'toast-top-right',
                closeButton: true,
                timeOut: 10000
              });
              selectedFiles = null;
              return;
            }

            this.containerExpenseObj?.docs.push(fileData);
            selectedFiles = null;
          }
        } else {
          this.utilsService.toasterService.error(this.utilsService.validationService.FILE_INVALID_EXTENSION);
        }
      });
    }

  }

  openLink = (link, newUpload: any) => {
    const filePreview = link ? `${this.utilsService.imgPath}${link}` : null

    if (!this.utilsService.isEmptyObjectOrNullUndefined(newUpload)) {
      window.open(newUpload, "_blank");
      return;
    }

    if (!this.utilsService.isEmptyObjectOrNullUndefined(filePreview)) {
      window.open(filePreview, "_blank");
      return;
    }
  }

  removeAttachment(i: number, file) {
    this.containerExpenseObj?.docs?.splice(i, 1)
    if (file.id) {
      this.containerExpenseObj.deletedDocumentID.push(file.id)
    }
  }

  openDateModal = () => {
    if (!this.pickerDirectiveModal.picker.isShown) {
      this.pickerDirectiveModal.open();
    } else this.pickerDirectiveModal.hide()
  }

  // Expense Form Array
  get expenseTypeItems() {
    return this.expenseGroup.get('expenseTypeItems') as FormArray;
  }

  addExpenseTypeItem = () => {
    this.expenseTypeItems.push(this.fb.group({
      expenseTypeId: [null, Validators.compose([Validators.required])],
      amount: [null, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.ONLY_GREATER_THAN_ZERO)])],
      isAddInLedger: [null],
      isAddInAvgAmount: [null]
    }));

    const expObj = new ContainerExpenseList()
    expObj.isAddInAvgAmount = true;
    expObj.expenseDropdown = this.utilsService.transformDropdownItems(this.dropdown.expenseTypes);
    this.containerExpenseObj.expenseTypeItems.push(expObj);
  }

  openExpenseDeleteModal = (index: number) => {
    this.selectedExpIndex = index;
    this.deleteContainerExpense.show();
  }

  onDeleteExpense = () => {
    const selectedExpense = this.containerExpenseObj.expenseTypeItems[this.selectedExpIndex];

    if (selectedExpense?.id) {
      this.containerExpenseObj.deletedTypeID.push(selectedExpense.id);
    }

    this.expenseTypeItems.removeAt(this.selectedExpIndex);
    this.containerExpenseObj.expenseTypeItems.splice(this.selectedExpIndex, 1);

    this.deleteContainerExpense.hide();
  }

  get totalAmount(): number {
    return this.expenseTypeItems.controls.reduce((acc, group) => {
      const val = parseFloat(group.get('amount')?.value?.toString()) || 0;
      return (acc + val);
    }, 0);
  }

  // Save Expense
  onSave = () => {

    const formData = new FormData();

    if (this.expenseGroup.invalid) {
      this.expenseGroup.markAllAsTouched();
      return;
    }

    if (this.utilsService.isEmptyObjectOrNullUndefined(this.containerExpenseObj.temp_date)) {
      return;
    }

    if (!this.utilsService.isEverythingUnique(this.containerExpenseObj.expenseTypeItems, 'expenseTypeId')) {
      this.utilsService.toasterService.error('Expense Type should be unique.', '', {
        positionClass: 'toast-top-right',
        closeButton: true,
        timeOut: 10000
      });
      return;
    }

    if (this.containerExpenseObj?.docs) {
      this.containerExpenseObj.docs.map((v, i) => {
        if (v.file) {
          formData.append('docs', v.file);
        }
      })
    }

    let date = null;
    if (this.containerExpenseObj.temp_date) {
      date = dayjs(this.containerExpenseObj.temp_date?.start).format('YYYY-MM-DD HH:mm:00');
    }
    this.containerExpenseObj.expenseDate = Serialize(date)

    const param = Serialize(this.containerExpenseObj) as ContainerExpense;
    formData.set('expenseInfo', JSON.stringify(param));

    this.utilsService.postMethodAPI(true, this.utilsService.serverVariableService.CONTAINER_EXP_SAVE_DEL, formData, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.onCloseCancel();
      }
    })

  }

  // On Close/Cancel
  onCloseCancel = () => {
    if (this.fromContainerRedirect) {
      this.utilsService.redirectTo(`/users/purchases/po-import/`);
    } else {
      this.utilsService.redirectTo('/users/purchases/expenses/');
    }
  }

  setGetData = (response: any) => {
    this.containerExpenseObj = Deserialize(response.poExpense, ContainerExpense);
    if (response?.documents) {
      this.containerExpenseObj.docs = (response.documents || [])
    }

    if (response?.poExpense?.expenseDate) {
      this.containerExpenseObj.temp_date = this.containerExpenseObj.temp_date ?? {};
      this.containerExpenseObj.temp_date.start = moment(response.poExpense.expenseDate).toDate();
    }

    if (response?.poExpense?.expenseCarringType) {
      this.containerExpenseObj.expenseCarringType = response.poExpense.expenseCarringType.value;
      this.onChangeExpenseCarringType(response.poExpense.expenseCarringType.value);
    }

    if (response.poExpense?.ourShippingTypes) {
      this.containerExpenseObj.ourShippingTypes = response.poExpense.ourShippingTypes.value;
    }

    if (response.poExpense?.chaShippingTypes) {
      this.containerExpenseObj.chaShippingTypes = response.poExpense.chaShippingTypes.value;
    }

    if (response.poExpense?.expensePOTypeItems || response.expensePOTypeItems) {
      this.setExpenseType(response);
    }
  }

  setExpenseType = (response: any) => {
    this.containerExpenseObj.expenseTypeItems = Deserialize(response.poExpense?.expensePOTypeItems ? response.poExpense?.expensePOTypeItems : response.expensePOTypeItems, ContainerExpenseList);

    const fa = this.expenseGroup.get('expenseTypeItems') as FormArray;
    fa.clear();

    for (let i = 0; i < this.containerExpenseObj.expenseTypeItems.length; i++) {
      const item = this.containerExpenseObj.expenseTypeItems[i];
      fa.push(this.fb.group({
        expenseTypeId: [item.expenseTypeId, Validators.compose([Validators.required])],
        amount: [item.amount, [Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.ONLY_GREATER_THAN_ZERO)])]],
        isAddInLedger: [item.isAddInLedger],
        isAddInAvgAmount: [item.isAddInAvgAmount]
      }));

      fa.at(i).get('expenseTypeId').enable();
      fa.at(i).get('amount').enable();
      if(item.isDefault) {
        fa.at(i).get('expenseTypeId').disable();
        fa.at(i).get('amount').disable();
      }

      fa.at(i).get('expenseTypeId').updateValueAndValidity();
      fa.at(i).get('amount').updateValueAndValidity();

      item.expenseDropdown = this.utilsService.filterIsActive(this.dropdown.expenseTypes, item.expenseTypeId);
    }
  }

  onContainerChange = (containerId: number) => {
    this.getRequiredData(containerId, false);
  }

  onChangeAddIn(index: number, changedField: 'avg' | 'ledger') {
    const item = this.containerExpenseObj.expenseTypeItems[index];

    if (changedField === 'avg') {
      if (!item.isAddInAvgAmount && !item.isAddInLedger) {
        item.isAddInLedger = true;
      }
    }

    if (changedField === 'ledger') {
      if (!item.isAddInLedger && !item.isAddInAvgAmount) {
        item.isAddInAvgAmount = true;
      }
    }
  }
}
