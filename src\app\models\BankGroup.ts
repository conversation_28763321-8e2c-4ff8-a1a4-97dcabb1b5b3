import { deserializeAs, serializeAs } from 'cerialize';
import { BankList } from './BankLink';

export class BankGroup {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('isActive')
    @deserializeAs('isActive')
    private _isActive: boolean;

    @serializeAs('isSelected')
    @deserializeAs('isSelected')
    private _isSelected: boolean;

    @serializeAs('bankName')
    @deserializeAs('bankName')
    private _bankName: string;

    @serializeAs('gstNo')
    @deserializeAs('gstNo')
    private _gstNo: string;

    @serializeAs('note')
    @deserializeAs('note')
    private _note: string;

    @serializeAs('lastModifiedDate')
    @deserializeAs('lastModifiedDate')
    private _lastModifiedDate: string;

    @serializeAs('date')
    @deserializeAs('date')
    private _date: any;

    @deserializeAs('temp_date')
    private _temp_date: any;

    @serializeAs('openingBalance')
    @deserializeAs('openingBalance')
    private _openingBalance: any;

    @serializeAs('linkBankCount')
    @deserializeAs('linkBankCount')
    private _linkBankCount: number;

    @serializeAs('bankLinkRequests')
    @deserializeAs('bankLinkRequests')
    private _bankLinkRequests: BankList[];

    @deserializeAs('isDefault')
    private _isDefault: boolean;

    constructor() {
        this.isActive = false;
        this.isSelected = false;
        this.bankLinkRequests = [];
        this.isDefault = false;
    }
    

    /**
     * Getter isDefault
     * @return {boolean}
     */
	public get isDefault(): boolean {
		return this._isDefault;
	}

    /**
     * Setter isDefault
     * @param {boolean} value
     */
	public set isDefault(value: boolean) {
		this._isDefault = value;
	}
    

    /**
     * Getter lastModifiedDate
     * @return {string}
     */
	public get lastModifiedDate(): string {
		return this._lastModifiedDate;
	}

    /**
     * Setter lastModifiedDate
     * @param {string} value
     */
	public set lastModifiedDate(value: string) {
		this._lastModifiedDate = value;
	}


    /**
     * Getter temp_date
     * @return {any}
     */
	public get temp_date(): any {
		return this._temp_date;
	}

    /**
     * Setter temp_date
     * @param {any} value
     */
	public set temp_date(value: any) {
		this._temp_date = value;
	}


    /**
     * Getter bankLinkRequests
     * @return {BankList[]}
     */
	public get bankLinkRequests(): BankList[] {
		return this._bankLinkRequests;
	}

    /**
     * Setter bankLinkRequests
     * @param {BankList[]} value
     */
	public set bankLinkRequests(value: BankList[]) {
		this._bankLinkRequests = value;
	}


    /**
     * Getter date
     * @return {any}
     */
	public get date(): any {
		return this._date;
	}

    /**
     * Setter date
     * @param {any} value
     */
	public set date(value: any) {
		this._date = value;
	}


    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Getter isActive
     * @return {boolean}
     */
	public get isActive(): boolean {
		return this._isActive;
	}

    /**
     * Getter isSelected
     * @return {boolean}
     */
	public get isSelected(): boolean {
		return this._isSelected;
	}

    /**
     * Getter bankName
     * @return {string}
     */
	public get bankName(): string {
		return this._bankName;
	}

    /**
     * Getter gstNo
     * @return {string}
     */
	public get gstNo(): string {
		return this._gstNo;
	}

    /**
     * Getter note
     * @return {string}
     */
	public get note(): string {
		return this._note;
	}

    /**
     * Getter openingBalance
     * @return {any}
     */
	public get openingBalance(): any {
		return this._openingBalance;
	}

    /**
     * Getter linkBankCount
     * @return {number}
     */
	public get linkBankCount(): number {
		return this._linkBankCount;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Setter isActive
     * @param {boolean} value
     */
	public set isActive(value: boolean) {
		this._isActive = value;
	}

    /**
     * Setter isSelected
     * @param {boolean} value
     */
	public set isSelected(value: boolean) {
		this._isSelected = value;
	}

    /**
     * Setter bankName
     * @param {string} value
     */
	public set bankName(value: string) {
		this._bankName = value;
	}

    /**
     * Setter gstNo
     * @param {string} value
     */
	public set gstNo(value: string) {
		this._gstNo = value;
	}

    /**
     * Setter note
     * @param {string} value
     */
	public set note(value: string) {
		this._note = value;
	}

    /**
     * Setter openingBalance
     * @param {any} value
     */
	public set openingBalance(value: any) {
		this._openingBalance = value;
	}

    /**
     * Setter linkBankCount
     * @param {number} value
     */
	public set linkBankCount(value: number) {
		this._linkBankCount = value;
	}


}