import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'padNum'
})

export class PadNumPipe implements PipeTransform {

  transform(value: number): any {
    if (this.isNumber(value)) {
      if (value >= 100) {
        return value.toString()
      } else {
        return `0${value}`.slice(-2);
      }
    } else {
      return '';
    }
  }  

  toInteger(value: any): number {
    return parseInt(`${value}`, 10);
  }

  isNumber(value: any): value is number {
    return !isNaN(this.toInteger(value));
  }

}
