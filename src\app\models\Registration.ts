import { deserializeAs, serializeAs } from 'cerialize';
import { RegistrationCP } from './RegistrationContactPerson';
import { RegistrationSA } from './RegistrationShippingAdd';
import { RegistrationCustomerDetails } from './RegistrationCustomerDetails';
import { RegistrationSupplierDetails } from './RegistrationSupplierDetails';
import { RegistrationOtherDetails } from './RegistrationOtherDetails';
import { RegistrationBankDetails } from './RegistrationBankDetails';
import { RegistrationGST } from './RegistrationGST';
import { RegistrationAI } from './RegistrationAssociateItem';

export class Registration {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('isDeleteFile')
    @deserializeAs('isDeleteFile')
    private _isDeleteFile: boolean;

    // @serializeAs('registrationId')
    @deserializeAs('registrationId')
    private _registrationId: number;

    @serializeAs('contactPersonList')
    @deserializeAs('contactPersonList')
    private _contactPersonList: RegistrationCP[];

    @serializeAs('shippingAddressList')
    @deserializeAs('shippingAddressList')
    private _shippingAddressList: RegistrationSA[];

    @serializeAs('associatedItems')
    @deserializeAs('associatedItems')
    private _associatedItems: RegistrationAI[];
    
    @serializeAs('companyName')
    @deserializeAs('companyName')
    private _companyName: string;

    @serializeAs('displayName')
    @deserializeAs('displayName')
    private _displayName: string;

    @serializeAs('email')
    @deserializeAs('email')
    private _email: string;

    @serializeAs('phone')
    @deserializeAs('phone')
    private _phone: string;

    @serializeAs('fullName')
    @deserializeAs('fullName')
    private _fullName: string;

    @serializeAs('registrationTypeName')
    @deserializeAs('registrationTypeName')
    private _registrationTypeName: string;

    @serializeAs('registrationType')
    @deserializeAs('registrationType')
    private _registrationType: string;

    @serializeAs('registrationDocsList')
    @deserializeAs('registrationDocsList')
    private _registrationDocsList: any[];

    @serializeAs('countryCode')
    @deserializeAs('countryCode')
    private _countryCode: string;

    @serializeAs('isActive')
    @deserializeAs('isActive')
    private _isActive: boolean;

    // @serializeAs('isSelected')
    @deserializeAs('isSelected')
    private _isSelected: boolean;

    @serializeAs('firstName')
    @deserializeAs('firstName')
    private _firstName: string;

    @serializeAs('middleName')
    @deserializeAs('middleName')
    private _middleName: string;

    @serializeAs('lastName')
    @deserializeAs('lastName')
    private _lastName: string;

    @serializeAs('countryId')
    @deserializeAs('countryId')
    private _countryId: number;

    @serializeAs('isWhatsAppNo')
    @deserializeAs('isWhatsAppNo')
    private _isWhatsAppNo: number;

    @serializeAs('countyForWhatsAppId')
    @deserializeAs('countyForWhatsAppId')
    private _countyForWhatsAppId: number;

    @serializeAs('whatsAppNo')
    @deserializeAs('whatsAppNo')
    private _whatsAppNo: string;

    @serializeAs('isTelegramNo')
    @deserializeAs('isTelegramNo')
    private _isTelegramNo: number;

    @serializeAs('countyForTelegramId')
    @deserializeAs('countyForTelegramId')
    private _countyForTelegramId: number;

    @serializeAs('telegramNo')
    @deserializeAs('telegramNo')
    private _telegramNo: string;

    @serializeAs('remarks')
    @deserializeAs('remarks')
    private _remarks: string;

    @deserializeAs('profileUrl')
    private _profileUrl: any;

    @deserializeAs('originalProfileUrl')
    private _originalProfileUrl: any;

    @deserializeAs('countryExtension')
    private _countryExtension: string;

    @serializeAs('customerDetail')
    @deserializeAs('customerDetail')
    private _customerDetail: RegistrationCustomerDetails;

    @serializeAs('supplierDetail')
    @deserializeAs('supplierDetail')
    private _supplierDetail: RegistrationSupplierDetails;

    @serializeAs('otherDetail')
    @deserializeAs('otherDetail')
    private _otherDetail: RegistrationOtherDetails;

    @serializeAs('bankDetail')
    @deserializeAs('bankDetail')
    private _bankDetail: RegistrationBankDetails;

    @serializeAs('gstList')
    @deserializeAs('gstList')
    private _gstList: RegistrationGST[];

    @deserializeAs('countryMaster')
    private _countryMaster: any;

    @deserializeAs('isNewCustomer')
    private _isNewCustomer: boolean;

    @deserializeAs('isBlocked')
    private _isBlocked: boolean;

    @deserializeAs('isBlackList')
    private _isBlackList: boolean;

    @deserializeAs('associateItems')
    private _associateItems: number;
    
    @serializeAs('isSaveAsDraft')
    @deserializeAs('isSaveAsDraft')
    private _isSaveAsDraft: boolean;

    @serializeAs('deleteContactPersonIds')
    @deserializeAs('deleteContactPersonIds')
    private _deleteContactPersonIds: number[];

    @serializeAs('deleteGstDetailIds')
    @deserializeAs('deleteGstDetailIds')
    private _deleteGstDetailIds: number[];

    @serializeAs('deleteRegistrationDocIds')
    @deserializeAs('deleteRegistrationDocIds')
    private _deleteRegistrationDocIds: number[];

    @serializeAs('deleteAssociateIds')
    @deserializeAs('deleteAssociateIds')
    private _deleteAssociateIds: number[];

    @serializeAs('deleteShippingIds')
    @deserializeAs('deleteShippingIds')
    private _deleteShippingIds: number[];

    @serializeAs('deleteGstIds')
    @deserializeAs('deleteGstIds')
    private _deleteGstIds: number[];

    @deserializeAs('registrationProfile')
    private _registrationProfile: any;

    @deserializeAs('supplierId')
    private _supplierId: any;

    @deserializeAs('supplierDetailShortCode')
    private _supplierDetailShortCode: any;

    @serializeAs('formattedName')
    @deserializeAs('formattedName')
    private _formattedName: string;

    @serializeAs('originalName')
    @deserializeAs('originalName')
    private _originalName: string;

    @deserializeAs('blacklistReason')
    private _blacklistReason: string;

    @deserializeAs('blocklistReason')
    private _blocklistReason: string;

    constructor() {
        this.registrationDocsList = [];
        this.isActive = false;
        this.isSelected = false;
        this.contactPersonList = [];
        this.shippingAddressList = [];
        this.associatedItems = [];
        this.gstList = [];
        this.customerDetail = new RegistrationCustomerDetails();
        this.supplierDetail = new RegistrationSupplierDetails();
        this.otherDetail = new RegistrationOtherDetails();
        this.bankDetail = new RegistrationBankDetails();
        this.isNewCustomer = false;
        this.isBlackList = false;
        this.isBlocked = false;
        this.isSaveAsDraft = false;

        this.deleteContactPersonIds = [];
        this.deleteGstDetailIds = [];
        this.deleteRegistrationDocIds = [];
        this.deleteAssociateIds = [];
        this.deleteShippingIds = [];
        this.deleteGstIds = []
    }


    /**
     * Getter blocklistReason
     * @return {string}
     */
	public get blocklistReason(): string {
		return this._blocklistReason;
	}

    /**
     * Setter blocklistReason
     * @param {string} value
     */
	public set blocklistReason(value: string) {
		this._blocklistReason = value;
	}


    /**
     * Getter blacklistReason
     * @return {string}
     */
	public get blacklistReason(): string {
		return this._blacklistReason;
	}

    /**
     * Setter blacklistReason
     * @param {string} value
     */
	public set blacklistReason(value: string) {
		this._blacklistReason = value;
	}


    /**
     * Getter isDeleteFile
     * @return {boolean}
     */
	public get isDeleteFile(): boolean {
		return this._isDeleteFile;
	}

    /**
     * Setter isDeleteFile
     * @param {boolean} value
     */
	public set isDeleteFile(value: boolean) {
		this._isDeleteFile = value;
	}


    /**
     * Getter originalName
     * @return {string}
     */
	public get originalName(): string {
		return this._originalName;
	}

    /**
     * Setter originalName
     * @param {string} value
     */
	public set originalName(value: string) {
		this._originalName = value;
	}


    /**
     * Getter isSaveAsDraft
     * @return {boolean}
     */
	public get isSaveAsDraft(): boolean {
		return this._isSaveAsDraft;
	}

    /**
     * Setter isSaveAsDraft
     * @param {boolean} value
     */
	public set isSaveAsDraft(value: boolean) {
		this._isSaveAsDraft = value;
	}


    /**
     * Getter formattedName
     * @return {string}
     */
	public get formattedName(): string {
		return this._formattedName;
	}

    /**
     * Setter formattedName
     * @param {string} value
     */
	public set formattedName(value: string) {
		this._formattedName = value;
	}


    /**
     * Getter supplierDetailShortCode
     * @return {any}
     */
	public get supplierDetailShortCode(): any {
		return this._supplierDetailShortCode;
	}

    /**
     * Setter supplierDetailShortCode
     * @param {any} value
     */
	public set supplierDetailShortCode(value: any) {
		this._supplierDetailShortCode = value;
	}


    /**
     * Getter supplierId
     * @return {any}
     */
	public get supplierId(): any {
		return this._supplierId;
	}

    /**
     * Setter supplierId
     * @param {any} value
     */
	public set supplierId(value: any) {
		this._supplierId = value;
	}


    /**
     * Getter registrationProfile
     * @return {any}
     */
	public get registrationProfile(): any {
		return this._registrationProfile;
	}

    /**
     * Setter registrationProfile
     * @param {any} value
     */
	public set registrationProfile(value: any) {
		this._registrationProfile = value;
	}


    /**
     * Getter isBlocked
     * @return {boolean}
     */
	public get isBlocked(): boolean {
		return this._isBlocked;
	}

    /**
     * Getter isBlackList
     * @return {boolean}
     */
	public get isBlackList(): boolean {
		return this._isBlackList;
	}

    /**
     * Setter isBlocked
     * @param {boolean} value
     */
	public set isBlocked(value: boolean) {
		this._isBlocked = value;
	}

    /**
     * Setter isBlackList
     * @param {boolean} value
     */
	public set isBlackList(value: boolean) {
		this._isBlackList = value;
	}


    /**
     * Getter associateItems
     * @return {number}
     */
	public get associateItems(): number {
		return this._associateItems;
	}

    /**
     * Setter associateItems
     * @param {number} value
     */
	public set associateItems(value: number) {
		this._associateItems = value;
	}


    /**
     * Getter associatedItems
     * @return {RegistrationAI[]}
     */
	public get associatedItems(): RegistrationAI[] {
		return this._associatedItems;
	}

    /**
     * Setter associatedItems
     * @param {RegistrationAI[]} value
     */
	public set associatedItems(value: RegistrationAI[]) {
		this._associatedItems = value;
	}


    /**
     * Getter deleteGstIds
     * @return {number[]}
     */
	public get deleteGstIds(): number[] {
		return this._deleteGstIds;
	}

    /**
     * Setter deleteGstIds
     * @param {number[]} value
     */
	public set deleteGstIds(value: number[]) {
		this._deleteGstIds = value;
	}


    /**
     * Getter deleteShippingIds
     * @return {number[]}
     */
	public get deleteShippingIds(): number[] {
		return this._deleteShippingIds;
	}

    /**
     * Setter deleteShippingIds
     * @param {number[]} value
     */
	public set deleteShippingIds(value: number[]) {
		this._deleteShippingIds = value;
	}


    /**
     * Getter deleteAssociateIds
     * @return {number[]}
     */
	public get deleteAssociateIds(): number[] {
		return this._deleteAssociateIds;
	}

    /**
     * Setter deleteAssociateIds
     * @param {number[]} value
     */
	public set deleteAssociateIds(value: number[]) {
		this._deleteAssociateIds = value;
	}


    /**
     * Getter deleteRegistrationDocIds
     * @return {number[]}
     */
	public get deleteRegistrationDocIds(): number[] {
		return this._deleteRegistrationDocIds;
	}

    /**
     * Setter deleteRegistrationDocIds
     * @param {number[]} value
     */
	public set deleteRegistrationDocIds(value: number[]) {
		this._deleteRegistrationDocIds = value;
	}


    /**
     * Getter deleteGstDetailIds
     * @return {number[]}
     */
	public get deleteGstDetailIds(): number[] {
		return this._deleteGstDetailIds;
	}

    /**
     * Setter deleteGstDetailIds
     * @param {number[]} value
     */
	public set deleteGstDetailIds(value: number[]) {
		this._deleteGstDetailIds = value;
	}


    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Getter registrationId
     * @return {number}
     */
	public get registrationId(): number {
		return this._registrationId;
	}

    /**
     * Getter contactPersonList
     * @return {RegistrationCP[]}
     */
	public get contactPersonList(): RegistrationCP[] {
		return this._contactPersonList;
	}

    /**
     * Getter shippingAddressList
     * @return {RegistrationSA[]}
     */
	public get shippingAddressList(): RegistrationSA[] {
		return this._shippingAddressList;
	}

    /**
     * Getter companyName
     * @return {string}
     */
	public get companyName(): string {
		return this._companyName;
	}

    /**
     * Getter displayName
     * @return {string}
     */
	public get displayName(): string {
		return this._displayName;
	}

    /**
     * Getter email
     * @return {string}
     */
	public get email(): string {
		return this._email;
	}

    /**
     * Getter phone
     * @return {string}
     */
	public get phone(): string {
		return this._phone;
	}

    /**
     * Getter fullName
     * @return {string}
     */
	public get fullName(): string {
		return this._fullName;
	}

    /**
     * Getter registrationTypeName
     * @return {string}
     */
	public get registrationTypeName(): string {
		return this._registrationTypeName;
	}

    /**
     * Getter registrationType
     * @return {string}
     */
	public get registrationType(): string {
		return this._registrationType;
	}

    /**
     * Getter registrationDocsList
     * @return {any[]}
     */
	public get registrationDocsList(): any[] {
		return this._registrationDocsList;
	}

    /**
     * Getter countryCode
     * @return {string}
     */
	public get countryCode(): string {
		return this._countryCode;
	}

    /**
     * Getter isActive
     * @return {boolean}
     */
	public get isActive(): boolean {
		return this._isActive;
	}

    /**
     * Getter isSelected
     * @return {boolean}
     */
	public get isSelected(): boolean {
		return this._isSelected;
	}

    /**
     * Getter firstName
     * @return {string}
     */
	public get firstName(): string {
		return this._firstName;
	}

    /**
     * Getter middleName
     * @return {string}
     */
	public get middleName(): string {
		return this._middleName;
	}

    /**
     * Getter lastName
     * @return {string}
     */
	public get lastName(): string {
		return this._lastName;
	}

    /**
     * Getter countryId
     * @return {number}
     */
	public get countryId(): number {
		return this._countryId;
	}

    /**
     * Getter isWhatsAppNo
     * @return {number}
     */
	public get isWhatsAppNo(): number {
		return this._isWhatsAppNo;
	}

    /**
     * Getter countyForWhatsAppId
     * @return {number}
     */
	public get countyForWhatsAppId(): number {
		return this._countyForWhatsAppId;
	}

    /**
     * Getter whatsAppNo
     * @return {string}
     */
	public get whatsAppNo(): string {
		return this._whatsAppNo;
	}

    /**
     * Getter isTelegramNo
     * @return {number}
     */
	public get isTelegramNo(): number {
		return this._isTelegramNo;
	}

    /**
     * Getter countyForTelegramId
     * @return {number}
     */
	public get countyForTelegramId(): number {
		return this._countyForTelegramId;
	}

    /**
     * Getter telegramNo
     * @return {string}
     */
	public get telegramNo(): string {
		return this._telegramNo;
	}

    /**
     * Getter remarks
     * @return {string}
     */
	public get remarks(): string {
		return this._remarks;
	}

    /**
     * Getter profileUrl
     * @return {any}
     */
	public get profileUrl(): any {
		return this._profileUrl;
	}

    /**
     * Getter originalProfileUrl
     * @return {any}
     */
	public get originalProfileUrl(): any {
		return this._originalProfileUrl;
	}

    /**
     * Getter countryExtension
     * @return {string}
     */
	public get countryExtension(): string {
		return this._countryExtension;
	}

    /**
     * Getter customerDetail
     * @return {RegistrationCustomerDetails}
     */
	public get customerDetail(): RegistrationCustomerDetails {
		return this._customerDetail;
	}

    /**
     * Getter supplierDetail
     * @return {RegistrationSupplierDetails}
     */
	public get supplierDetail(): RegistrationSupplierDetails {
		return this._supplierDetail;
	}

    /**
     * Getter otherDetail
     * @return {RegistrationOtherDetails}
     */
	public get otherDetail(): RegistrationOtherDetails {
		return this._otherDetail;
	}

    /**
     * Getter bankDetail
     * @return {RegistrationBankDetails}
     */
	public get bankDetail(): RegistrationBankDetails {
		return this._bankDetail;
	}

    /**
     * Getter gstList
     * @return {RegistrationGST[]}
     */
	public get gstList(): RegistrationGST[] {
		return this._gstList;
	}

    /**
     * Getter countryMaster
     * @return {any}
     */
	public get countryMaster(): any {
		return this._countryMaster;
	}

    /**
     * Getter isNewCustomer
     * @return {boolean}
     */
	public get isNewCustomer(): boolean {
		return this._isNewCustomer;
	}

    /**
     * Getter deleteContactPersonIds
     * @return {number[]}
     */
	public get deleteContactPersonIds(): number[] {
		return this._deleteContactPersonIds;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Setter registrationId
     * @param {number} value
     */
	public set registrationId(value: number) {
		this._registrationId = value;
	}

    /**
     * Setter contactPersonList
     * @param {RegistrationCP[]} value
     */
	public set contactPersonList(value: RegistrationCP[]) {
		this._contactPersonList = value;
	}

    /**
     * Setter shippingAddressList
     * @param {RegistrationSA[]} value
     */
	public set shippingAddressList(value: RegistrationSA[]) {
		this._shippingAddressList = value;
	}

    /**
     * Setter companyName
     * @param {string} value
     */
	public set companyName(value: string) {
		this._companyName = value;
	}

    /**
     * Setter displayName
     * @param {string} value
     */
	public set displayName(value: string) {
		this._displayName = value;
	}

    /**
     * Setter email
     * @param {string} value
     */
	public set email(value: string) {
		this._email = value;
	}

    /**
     * Setter phone
     * @param {string} value
     */
	public set phone(value: string) {
		this._phone = value;
	}

    /**
     * Setter fullName
     * @param {string} value
     */
	public set fullName(value: string) {
		this._fullName = value;
	}

    /**
     * Setter registrationTypeName
     * @param {string} value
     */
	public set registrationTypeName(value: string) {
		this._registrationTypeName = value;
	}

    /**
     * Setter registrationType
     * @param {string} value
     */
	public set registrationType(value: string) {
		this._registrationType = value;
	}

    /**
     * Setter registrationDocsList
     * @param {any[]} value
     */
	public set registrationDocsList(value: any[]) {
		this._registrationDocsList = value;
	}

    /**
     * Setter countryCode
     * @param {string} value
     */
	public set countryCode(value: string) {
		this._countryCode = value;
	}

    /**
     * Setter isActive
     * @param {boolean} value
     */
	public set isActive(value: boolean) {
		this._isActive = value;
	}

    /**
     * Setter isSelected
     * @param {boolean} value
     */
	public set isSelected(value: boolean) {
		this._isSelected = value;
	}

    /**
     * Setter firstName
     * @param {string} value
     */
	public set firstName(value: string) {
		this._firstName = value;
	}

    /**
     * Setter middleName
     * @param {string} value
     */
	public set middleName(value: string) {
		this._middleName = value;
	}

    /**
     * Setter lastName
     * @param {string} value
     */
	public set lastName(value: string) {
		this._lastName = value;
	}

    /**
     * Setter countryId
     * @param {number} value
     */
	public set countryId(value: number) {
		this._countryId = value;
	}

    /**
     * Setter isWhatsAppNo
     * @param {number} value
     */
	public set isWhatsAppNo(value: number) {
		this._isWhatsAppNo = value;
	}

    /**
     * Setter countyForWhatsAppId
     * @param {number} value
     */
	public set countyForWhatsAppId(value: number) {
		this._countyForWhatsAppId = value;
	}

    /**
     * Setter whatsAppNo
     * @param {string} value
     */
	public set whatsAppNo(value: string) {
		this._whatsAppNo = value;
	}

    /**
     * Setter isTelegramNo
     * @param {number} value
     */
	public set isTelegramNo(value: number) {
		this._isTelegramNo = value;
	}

    /**
     * Setter countyForTelegramId
     * @param {number} value
     */
	public set countyForTelegramId(value: number) {
		this._countyForTelegramId = value;
	}

    /**
     * Setter telegramNo
     * @param {string} value
     */
	public set telegramNo(value: string) {
		this._telegramNo = value;
	}

    /**
     * Setter remarks
     * @param {string} value
     */
	public set remarks(value: string) {
		this._remarks = value;
	}

    /**
     * Setter profileUrl
     * @param {any} value
     */
	public set profileUrl(value: any) {
		this._profileUrl = value;
	}

    /**
     * Setter originalProfileUrl
     * @param {any} value
     */
	public set originalProfileUrl(value: any) {
		this._originalProfileUrl = value;
	}

    /**
     * Setter countryExtension
     * @param {string} value
     */
	public set countryExtension(value: string) {
		this._countryExtension = value;
	}

    /**
     * Setter customerDetail
     * @param {RegistrationCustomerDetails} value
     */
	public set customerDetail(value: RegistrationCustomerDetails) {
		this._customerDetail = value;
	}

    /**
     * Setter supplierDetail
     * @param {RegistrationSupplierDetails} value
     */
	public set supplierDetail(value: RegistrationSupplierDetails) {
		this._supplierDetail = value;
	}

    /**
     * Setter otherDetail
     * @param {RegistrationOtherDetails} value
     */
	public set otherDetail(value: RegistrationOtherDetails) {
		this._otherDetail = value;
	}

    /**
     * Setter bankDetail
     * @param {RegistrationBankDetails} value
     */
	public set bankDetail(value: RegistrationBankDetails) {
		this._bankDetail = value;
	}

    /**
     * Setter gstList
     * @param {RegistrationGST[]} value
     */
	public set gstList(value: RegistrationGST[]) {
		this._gstList = value;
	}

    /**
     * Setter countryMaster
     * @param {any} value
     */
	public set countryMaster(value: any) {
		this._countryMaster = value;
	}

    /**
     * Setter isNewCustomer
     * @param {boolean} value
     */
	public set isNewCustomer(value: boolean) {
		this._isNewCustomer = value;
	}

    /**
     * Setter deleteContactPersonIds
     * @param {number[]} value
     */
	public set deleteContactPersonIds(value: number[]) {
		this._deleteContactPersonIds = value;
	}

}