name: Deployment
on:
  push:
    branches:
      - git-action
jobs:
  deploy:
    runs-on: self-hosted
    env:
      SFTP_HOST: ${{ secrets.TEST_SERVER }}
      SFTP_USER: ${{ secrets.TEST_USER }}
      SFTP_PASS: ${{ secrets.TEST_PASS }}
      REMOTE_DIR: /var/www/html/valamji/test
      LOCAL_BUILD: dist/valamji-frontend
    steps:
      - name: Check commit message
        id: check_commit_message
        if: contains(github.event.head_commit.message, 'Deploy to test')
        run: |
          echo 'Deploy!'
          echo "::set-output name=should_deploy::true"
      - name: Checkout code
        uses: actions/checkout@v2
      - name: Set up Node.js
        uses: actions/setup-node@v2
        with:
          node-version: 16.14.0
      - name: Install Dependencies
        run: npm ci
        shell: cmd
      - name: Build Angular App
        run: npm run testing-dev
        shell: cmd
      - name: WinSCP Deploy Script
        shell: cmd
        run: |
        
            @echo off
            setlocal enabledelayedexpansion

            REM === Get timestamp for backup folder name ===
            for /f "tokens=1-3 delims=/ " %%a in ('date /t') do (
                set "MM=%%a"
                set "DD=%%b"
                set "YYYY=%%c"
            )
            for /f "tokens=1-2 delims=: " %%x in ("%time%") do (
                set "HH=%%x"
                set "MMIN=%%y"
            )
            if "!HH:~0,1!"==" " set "HH=0!HH:~1,1!"
            for /f "tokens=2 delims=:" %%x in ("%time%") do (
                for /f "tokens=1 delims=." %%y in ("%%x") do set "SS=%%y"
            )
            set "BACKUP_SUBDIR=Backup_!YYYY!-!MM!-!DD!_!HH!!MMIN!!SS!"
            set "REMOTE_BACKUP_PATH=%REMOTE_DIR%/!BACKUP_SUBDIR!"

            REM === Create WinSCP script to backup remote test folder ===
            (
              echo option batch abort
              echo option confirm off
              echo open sftp://%SFTP_USER%:%SFTP_PASS%@%SFTP_HOST%
              echo cd "%REMOTE_DIR%"
              echo call mkdir "!BACKUP_SUBDIR!"
              echo mv * "!BACKUP_SUBDIR!/"
              echo exit
            ) > remote_backup_script.txt

            REM === Run remote backup script ===
            "C:\Program Files (x86)\WinSCP\WinSCP.com" /script=remote_backup_script.txt /log=winscp_backup.log

            REM === Create WinSCP script to upload new local build ===
            (
              echo option batch abort
              echo option confirm off
              echo open sftp://%SFTP_USER%:%SFTP_PASS%@%SFTP_HOST%
              echo lcd "%LOCAL_BUILD%"
              echo cd "%REMOTE_DIR%"
              echo put *
              echo exit
            ) > remote_upload_script.txt

            REM === Run upload script ===
            "C:\Program Files (x86)\WinSCP\WinSCP.com" /script=remote_upload_script.txt /log=winscp_upload.log


