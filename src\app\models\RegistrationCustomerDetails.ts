import { deserializeAs, serializeAs } from "cerialize";

export class RegistrationCustomerDetails {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('userId')
    @deserializeAs('userId')
    private _userId: number;

    @serializeAs('creditLimit')
    @deserializeAs('creditLimit')
    private _creditLimit: number;

    @serializeAs('creditReason')
    @deserializeAs('creditReason')
    private _creditReason: string;
    
    private _creditReasonT: string;

    @serializeAs('days')
    @deserializeAs('days')
    private _days: number;

    @serializeAs('daysReason')
    @deserializeAs('daysReason')
    private _daysReason: string;

    private _daysReasonT: string;

    @serializeAs('isBlacklist')
    @deserializeAs('isBlacklist')
    private _isBlacklist: number;

    @serializeAs('blacklistReason')
    @deserializeAs('blacklistReason')
    private _blacklistReason: string;

    @serializeAs('isBlocklist')
    @deserializeAs('isBlocklist')
    private _isBlocklist: number;

    @serializeAs('blocklistReason')
    @deserializeAs('blocklistReason')
    private _blocklistReason: string;

    @serializeAs('referencePerson')
    @deserializeAs('referencePerson')
    private _referencePerson: string;

    @serializeAs('countryForLedgerId')
    @deserializeAs('countryForLedgerId')
    private _countryForLedgerId: number;

    @serializeAs('personNo')
    @deserializeAs('personNo')
    private _personNo: string;

    @serializeAs('countryForPersonId')
    @deserializeAs('countryForPersonId')
    private _countryForPersonId: number;

    @serializeAs('sendLedgerToMobile')
    @deserializeAs('sendLedgerToMobile')
    private _sendLedgerToMobile: string;

    @serializeAs('isSendNotification')
    @deserializeAs('isSendNotification')
    private _isSendNotification: number;

    @serializeAs('isMarkAsDebtor')
    @deserializeAs('isMarkAsDebtor')
    private _isMarkAsDebtor: number;

    @serializeAs('isNewCustomer')
    @deserializeAs('isNewCustomer')
    private _isNewCustomer: number;

    @deserializeAs('customerDoc')
    private _customerDoc: any;

    @deserializeAs('userDocs')
    private _userDocs: any[];

    @deserializeAs('creditLimitT')
    private _creditLimitT: any;

    @deserializeAs('daysT')
    private _daysT: any;

    constructor() {
        this.userDocs = [];
    }


    /**
     * Getter daysT
     * @return {any}
     */
	public get daysT(): any {
		return this._daysT;
	}

    /**
     * Setter daysT
     * @param {any} value
     */
	public set daysT(value: any) {
		this._daysT = value;
	}


    /**
     * Getter creditLimitT
     * @return {any}
     */
	public get creditLimitT(): any {
		return this._creditLimitT;
	}

    /**
     * Setter creditLimitT
     * @param {any} value
     */
	public set creditLimitT(value: any) {
		this._creditLimitT = value;
	}


    /**
     * Getter userDocs
     * @return {any[]}
     */
	public get userDocs(): any[] {
		return this._userDocs;
	}

    /**
     * Setter userDocs
     * @param {any[]} value
     */
	public set userDocs(value: any[]) {
		this._userDocs = value;
	}


    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Getter userId
     * @return {number}
     */
	public get userId(): number {
		return this._userId;
	}

    /**
     * Getter creditLimit
     * @return {number}
     */
	public get creditLimit(): number {
		return this._creditLimit;
	}

    /**
     * Getter creditReason
     * @return {string}
     */
	public get creditReason(): string {
		return this._creditReason;
	}

    /**
     * Getter days
     * @return {number}
     */
	public get days(): number {
		return this._days;
	}

    /**
     * Getter daysReason
     * @return {string}
     */
	public get daysReason(): string {
		return this._daysReason;
	}

    /**
     * Getter isBlacklist
     * @return {number}
     */
	public get isBlacklist(): number {
		return this._isBlacklist;
	}

    /**
     * Getter blacklistReason
     * @return {string}
     */
	public get blacklistReason(): string {
		return this._blacklistReason;
	}

    /**
     * Getter isBlocklist
     * @return {number}
     */
	public get isBlocklist(): number {
		return this._isBlocklist;
	}

    /**
     * Getter blocklistReason
     * @return {string}
     */
	public get blocklistReason(): string {
		return this._blocklistReason;
	}

    /**
     * Getter referencePerson
     * @return {string}
     */
	public get referencePerson(): string {
		return this._referencePerson;
	}

    /**
     * Getter countryForLedgerId
     * @return {number}
     */
	public get countryForLedgerId(): number {
		return this._countryForLedgerId;
	}

    /**
     * Getter personNo
     * @return {string}
     */
	public get personNo(): string {
		return this._personNo;
	}

    /**
     * Getter countryForPersonId
     * @return {number}
     */
	public get countryForPersonId(): number {
		return this._countryForPersonId;
	}

    /**
     * Getter sendLedgerToMobile
     * @return {string}
     */
	public get sendLedgerToMobile(): string {
		return this._sendLedgerToMobile;
	}

    /**
     * Getter isSendNotification
     * @return {number}
     */
	public get isSendNotification(): number {
		return this._isSendNotification;
	}

    /**
     * Getter isMarkAsDebtor
     * @return {number}
     */
	public get isMarkAsDebtor(): number {
		return this._isMarkAsDebtor;
	}

    /**
     * Getter isNewCustomer
     * @return {number}
     */
	public get isNewCustomer(): number {
		return this._isNewCustomer;
	}

    /**
     * Getter customerDoc
     * @return {any}
     */
	public get customerDoc(): any {
		return this._customerDoc;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Setter userId
     * @param {number} value
     */
	public set userId(value: number) {
		this._userId = value;
	}

    /**
     * Setter creditLimit
     * @param {number} value
     */
	public set creditLimit(value: number) {
		this._creditLimit = value;
	}

    /**
     * Setter creditReason
     * @param {string} value
     */
	public set creditReason(value: string) {
		this._creditReason = value;
	}

    /**
     * Setter days
     * @param {number} value
     */
	public set days(value: number) {
		this._days = value;
	}

    /**
     * Setter daysReason
     * @param {string} value
     */
	public set daysReason(value: string) {
		this._daysReason = value;
	}

    /**
     * Setter isBlacklist
     * @param {number} value
     */
	public set isBlacklist(value: number) {
		this._isBlacklist = value;
	}

    /**
     * Setter blacklistReason
     * @param {string} value
     */
	public set blacklistReason(value: string) {
		this._blacklistReason = value;
	}

    /**
     * Setter isBlocklist
     * @param {number} value
     */
	public set isBlocklist(value: number) {
		this._isBlocklist = value;
	}

    /**
     * Setter blocklistReason
     * @param {string} value
     */
	public set blocklistReason(value: string) {
		this._blocklistReason = value;
	}

    /**
     * Setter referencePerson
     * @param {string} value
     */
	public set referencePerson(value: string) {
		this._referencePerson = value;
	}

    /**
     * Setter countryForLedgerId
     * @param {number} value
     */
	public set countryForLedgerId(value: number) {
		this._countryForLedgerId = value;
	}

    /**
     * Setter personNo
     * @param {string} value
     */
	public set personNo(value: string) {
		this._personNo = value;
	}

    /**
     * Setter countryForPersonId
     * @param {number} value
     */
	public set countryForPersonId(value: number) {
		this._countryForPersonId = value;
	}

    /**
     * Setter sendLedgerToMobile
     * @param {string} value
     */
	public set sendLedgerToMobile(value: string) {
		this._sendLedgerToMobile = value;
	}

    /**
     * Setter isSendNotification
     * @param {number} value
     */
	public set isSendNotification(value: number) {
		this._isSendNotification = value;
	}

    /**
     * Setter isMarkAsDebtor
     * @param {number} value
     */
	public set isMarkAsDebtor(value: number) {
		this._isMarkAsDebtor = value;
	}

    /**
     * Setter isNewCustomer
     * @param {number} value
     */
	public set isNewCustomer(value: number) {
		this._isNewCustomer = value;
	}

    /**
     * Setter customerDoc
     * @param {any} value
     */
	public set customerDoc(value: any) {
		this._customerDoc = value;
	}


    /**
     * Getter creditReasonT
     * @return {string}
     */
	public get creditReasonT(): string {
		return this._creditReasonT;
	}

    /**
     * Getter daysReasonT
     * @return {string}
     */
	public get daysReasonT(): string {
		return this._daysReasonT;
	}

    /**
     * Setter creditReasonT
     * @param {string} value
     */
	public set creditReasonT(value: string) {
		this._creditReasonT = value;
	}

    /**
     * Setter daysReasonT
     * @param {string} value
     */
	public set daysReasonT(value: string) {
		this._daysReasonT = value;
	}

}