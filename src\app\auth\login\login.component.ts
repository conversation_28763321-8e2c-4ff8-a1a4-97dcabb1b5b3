import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ServerVariableService } from 'src/app/shared/services/server-variable.service';
import { UtilsService } from 'src/app/shared/services/utils.service';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.css']
})
export class LoginComponent implements OnInit {

  flagForPasswordHideShow: boolean;
  loginFormGroup: FormGroup;

  constructor(public utilsService: UtilsService, private fb: FormBuilder, private serverVariableService: ServerVariableService) { }

  ngOnInit() {
    this.loginForm();
    this.flagForPasswordHideShow = true;
  }

  loginForm() {
    this.loginFormGroup = this.fb.group({
      email: ['', Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.LEADING_SPACE_NOT_ALLOW)])],
      password: ['', Validators.compose([Validators.required])]
    })
  }

  onLogin() {

    if (this.loginFormGroup.invalid) {
      this.loginFormGroup.markAllAsTouched();
      return;
    }

    const formGroupValue = this.loginFormGroup.value;

    const param = {
      mobile: formGroupValue.email,
      password: formGroupValue.password
    }

    this.utilsService.postMethodAPI(true, this.serverVariableService.LOGIN_API, param, (response) => {

      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        const loginResponse = response;
        this.setLocalStorage(loginResponse, loginResponse.token).then(() => {
          this.utilsService.redirectTo('users/dashboard');
        })
      }
    })

  }

  setLocalStorage(loginResponse: any, token: string) {

    const promise = new Promise((resolve, reject) => {
      try {
        this.utilsService.username = `${loginResponse.username}`;
        this.utilsService.storeDataLocally('userData', JSON.stringify(loginResponse));
        this.utilsService.storeDataLocally('token', token);
        resolve('Success')
      }
      catch {
        reject()
      }
    });

    return promise;
  }

}
