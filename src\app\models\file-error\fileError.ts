import { deserializeAs, serializeAs } from 'cerialize';

export class FileError {

    @serializeAs('lineNo')
    @deserializeAs('lineNo')
    private _lineNo: number;

    @serializeAs('error')
    @deserializeAs('error')
    private _error: string;

    @serializeAs('columnName')
    @deserializeAs('columnName')
    private _columnName: string;

    constructor() {

    }


    /**
     * Getter lineNo
     * @return {number}
     */
	public get lineNo(): number {
		return this._lineNo;
	}

    /**
     * Getter error
     * @return {string}
     */
	public get error(): string {
		return this._error;
	}

    /**
     * Getter columnName
     * @return {string}
     */
	public get columnName(): string {
		return this._columnName;
	}

    /**
     * Setter lineNo
     * @param {number} value
     */
	public set lineNo(value: number) {
		this._lineNo = value;
	}

    /**
     * Setter error
     * @param {string} value
     */
	public set error(value: string) {
		this._error = value;
	}

    /**
     * Setter columnName
     * @param {string} value
     */
	public set columnName(value: string) {
		this._columnName = value;
	}


}