<div class="modal-body" [formGroup]="categoryGroup">
  <div class="row">
    <div class="col-12">
      <div class="form-group required">
        <label class="form-label">Category Name</label>
        <input id="f1" (keyup.enter)="onSaveCategory()" [maxlength]="utilsService.validationService.MAX_50" id="f1"
          formControlName="name" [(ngModel)]="categoryObj.categoryName" type="text" class="form-control"
          placeholder="Enter Category Name">
        <div class="message error-message"
          *ngIf="categoryGroup.controls['name'].hasError('required') &&  categoryGroup.controls['name'].touched">
          {{utilsService.validationService.CATEGORY_NAME_REQ}}
        </div>
        <div class="message error-message"
          *ngIf="!categoryGroup.controls['name'].hasError('required') && !categoryGroup.controls['name'].valid && categoryGroup.controls['name'].touched">
          {{utilsService.validationService.CATEGORY_NAME_INVALID}}
        </div>
      </div>
    </div>
    <div class="col-12">
      <div class="form-group required">
        <label class="form-label">Category Image</label>
        <button class='btn btn-fileupload btn-fileupload-white'> <i class="bi bi-upload"></i>
          Upload Image
          <input #flag (change)="onSelectProfile($event)" type="file" ref={imageRef}
            accept="image/x-png,image/jpeg,image/jpg">
        </button>
        <div (click)="!filenameForFlag ? openLink(categoryObj.categoryImg): null" *ngIf="categoryObj.categoryImg"
          class="message success-message">
          {{categoryObj.categoryImg}}
        </div>
      </div>
    </div>
    <div class="col-12">
      <div class="form-group">
        <label class="form-label">Note</label>
        <textarea [maxLength]="utilsService.validationService.MAX_150" formControlName="note"
          [(ngModel)]="categoryObj.note" class="form-control" rows="3" placeholder="Enter note"></textarea>
      </div>
    </div>
    <div class="col-12" *ngIf="page !== utilsService.enumForPage.ITEM_GROUP">
      <div class="form-group required d-flex justify-content-between">
        <label class="form-label">Status</label>
        <div class="switch-box">
          <label class="switch" htmlFor="switch">
            <input type="checkbox" id='switch' formControlName="status" [(ngModel)]="categoryObj.isActive" />
            <div class="slider round"></div>
          </label>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="modal-footer">
  <div class="modal-footer-group full-width-btn">
    <button (click)="onSaveCategory()" type="button" class="btn btn-primary btn-icon-text"> <i
        class="th th-outline-tick-circle"></i>
      Save</button>
    <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
  </div>
</div>