import { Component, OnInit, ViewChild } from '@angular/core';
import { Deserialize, Serialize } from 'cerialize';
import dayjs from 'dayjs';
import moment from 'moment';
import { DaterangepickerDirective } from 'ngx-daterangepicker-material';
import { debounceTime, distinctUntilChanged } from 'rxjs';
import { ManualJournal } from 'src/app/models/ManualJournal';
import { ManualJournalPagination } from 'src/app/models/request/ManualJournalPagination';
import { activeInactiveStatus, MJ_HEADER } from 'src/app/shared/constants/constant';
import { UtilsService } from 'src/app/shared/services/utils.service';
declare var window: any;

@Component({
  selector: 'app-manual-journal',
  templateUrl: './manual-journal.component.html',
  styleUrls: ['./manual-journal.component.css']
})
export class ManualJournalComponent implements OnInit {

  @ViewChild(DaterangepickerDirective, { static: true }) pickerDirective: DaterangepickerDirective;
  activeInactiveStatus = activeInactiveStatus
  enumForSortOrder = this.utilsService.enumForSortOrder;

  deleteMJModal: any;
  mjTH: any[] = [];
  selectedIds: any[] = [];
  paginationRequest = new ManualJournalPagination();
  mjList: ManualJournal[] = [];
  mjObj = new ManualJournal();
  dateRange: any = null;

  constructor(public utilsService: UtilsService) {
    this.mjTH = MJ_HEADER;
    this.dateRange = {
      start: moment().startOf('month'),
      end: moment().endOf('month')
    };
  }

  ngOnInit() {

    this.paginationRequest.searchSubject.pipe(debounceTime(300), distinctUntilChanged()).subscribe((res: string) => {
      this.paginationRequest.pageNo = 1;
      // this.paginationRequest.pageSize = '100';
      this.paginationRequest.searchText = null;
      this.paginationRequest.searchText = res;
      this.getAllMJ();
    });

    this.deleteMJModal = new window.bootstrap.Modal(
      document.getElementById('deleteMJModal')
    );

    // this.getAllMJ();
  }

  getAllMJ() {

    let ls_param = null
    ls_param = JSON.parse(localStorage.getItem('param'))

    if (!this.utilsService.isNullUndefinedOrBlank(ls_param)) {
      if (ls_param.pageName === 'mj') {
        this.paginationRequest.pageNo = ls_param.pageNo,
          this.paginationRequest.pageSize = ls_param.pageSize,
          this.paginationRequest.sortOrder = ls_param.sortOrder
          this.paginationRequest.sortColumn = ls_param.sortColumn,
          this.paginationRequest.searchText = ls_param.searchText,
          this.paginationRequest.isActive = ls_param.isActive
          this.paginationRequest.fromDate = ls_param.fromDate
          this.paginationRequest.toDate = ls_param.toDate
          if(ls_param.dateRange && ls_param.fromDate && ls_param.toDate) {
            this.dateRange = {
              start: moment(this.utilsService.convertToDate(ls_param.fromDate)),
              end: moment(this.utilsService.convertToDate(ls_param.toDate))
            };
          }
      }
    }

    this.paginationRequest.flagForSelectAll = false;
    this.selectedIds = []
    this.utilsService.postMethodAPI(false, this.utilsService.serverVariableService.MANUAL_JOURNAL_LISTING, Serialize(this.paginationRequest), (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.mjList = Deserialize(response.content, ManualJournal);
        this.mjList = this.mjList.map(a => {
          a.date = this.utilsService.convertToDate(a.date)
          return a
        })
        this.paginationRequest.totalData = response.totalElements;
        this.paginationRequest.pagination = response;
        localStorage.removeItem('param')
      } else {
        this.mjList = [];
      }
    })

  }

  addPageSizeData(event) {
    this.paginationRequest.pageNo = 1;
    this.paginationRequest.pageSize = event;
    this.getAllMJ();
  }

  pageNumber(event) {
    this.paginationRequest.pageNo = event
    this.getAllMJ();
  }

  trackBy(index: number, item: ManualJournal): number {
    return item.id;
  }

  // Select Deselect 
  selectAll() {
    if (this.paginationRequest.flagForSelectAll === true) {
      this.selectedIds = new Array<string>();
    }
    const obj = this.mjList.filter((val, index) => {
      if (this.paginationRequest.flagForSelectAll === true) {
        val['isSelected'] = true;
        this.selectedIds.push(val.id);
      } else {
        val['isSelected'] = false;
        this.selectedIds.splice(index, 1);
      }
    });
    if (this.paginationRequest.flagForSelectAll === false) {
      this.selectedIds = new Array<string>();
    }
  }

  selectUnselect(id: number, index, value) {

    const isSelected = this.selectedIds.includes(id);

    if (value && !isSelected) {

      this.selectedIds.push(id);

    } else if (!value && isSelected) {

      const assetIndex = this.selectedIds.indexOf(id);
      this.selectedIds.splice(assetIndex, 1);
    }
    this.paginationRequest.flagForSelectAll = this.checkIfAllSelected();
  }

  checkIfAllSelected() {
    let flag = true;
    this.mjList.filter((val, index) => {
      if (val['isSelected'] === false) {
        flag = false;
        return;
      }
    });
    return flag;
  }

  // sorting 
  onSortTH(key) {

    if (this.utilsService.isEmptyObjectOrNullUndefined(this.mjList)) {
      return;
    }

    if (key === this.paginationRequest.sortColumn) {
      if (this.paginationRequest.sortOrder === this.enumForSortOrder.A) {
        this.paginationRequest.sortOrder = this.enumForSortOrder.D;
      } else if (this.paginationRequest.sortOrder === this.enumForSortOrder.D) {
        this.paginationRequest.sortOrder = this.enumForSortOrder.A;
      }
    } else {
      this.paginationRequest.sortOrder = this.enumForSortOrder.D;
    }

    this.paginationRequest.sortColumn = key;
    this.getAllMJ();
  }

  //Search
  onSearch(event: any) {
    this.paginationRequest.searchSubject.next(event.target.value);
  }

  // active/inactive
  onChangeActive() {
    this.paginationRequest.pageNo = 1;
    this.getAllMJ();
  }

  //delete 
  openDeleteMJModal(obj: ManualJournal) {
    this.mjObj = Serialize(obj)
    this.deleteMJModal.show();
  }

  deleteMJ() {
    this.utilsService.deleteMethodAPI(true, this.utilsService.serverVariableService.MANUAL_JOURNAL_SAVE_EDIT_DELETE + `?manualJournalId=${this.mjObj.id}`, {}, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.deleteMJModal.hide();
        if (!this.paginationRequest.pagination?.first && this.paginationRequest.pagination?.last && this.paginationRequest.pagination?.numberOfElements === 1) {
          this.paginationRequest.pageNo = this.paginationRequest.pageNo - 1
        }
        this.getAllMJ();
      }
    })
  }

  //redirection
  redirectToDetails() {
    let param = null;
    param = {
      pageNo: this.paginationRequest.pageNo,
      pageSize: this.paginationRequest.pageSize,
      sortOrder: this.paginationRequest.sortOrder,
      sortColumn: this.paginationRequest.sortColumn,
      searchText: this.paginationRequest.searchText,
      isActive: this.paginationRequest.isActive,
      fromDate: this.paginationRequest.fromDate,
      toDate: this.paginationRequest.toDate,
      dateRange: this.dateRange,
      pageName: 'mj'
    }
    localStorage.setItem('param', JSON.stringify(param))
  }

  onClear() {
    this.paginationRequest.searchText = null
    this.paginationRequest.fromDate = null;
    this.paginationRequest.toDate = null;
    this.dateRange = null;
    this.getAllMJ()
  }

  open(): void {
    if(!this.pickerDirective.picker.isShown) {
      this.pickerDirective.open();
    } else this.pickerDirective.hide()
  }

  onChangeDateRange() {
    if (this.dateRange?.start && this.dateRange?.end) {
      this.paginationRequest.fromDate = dayjs(this.dateRange.start).format('DD-MM-YYYY');
      this.paginationRequest.toDate = dayjs(this.dateRange.end).format('DD-MM-YYYY');
      this.getAllMJ();
    }
  }
}
