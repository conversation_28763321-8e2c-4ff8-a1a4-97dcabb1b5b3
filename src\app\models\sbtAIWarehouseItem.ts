import { deserializeAs, serializeAs } from 'cerialize';

export class sbtAIWarehouseItem {

    @serializeAs('warehouseId')
    @deserializeAs('warehouseId')
    private _warehouseId: number;

    @serializeAs('warehouseName')
    @deserializeAs('warehouseName')
    private _warehouseName: string;

    @serializeAs('itemWarehouseMarka')
    @deserializeAs('itemWarehouseMarka')
    private _itemWarehouseMarka: sbtItemWarehouseMarka[];

    @deserializeAs('looseSum')
    private _looseSum: number;

    @deserializeAs('cartonSum')
    private _cartonSum: number;

    @deserializeAs('totalSum')
    private _totalSum: number;

    constructor() {
        this.itemWarehouseMarka = []
    }


    /**
     * Getter cartonSum
     * @return {number}
     */
	public get cartonSum(): number {
		return this._cartonSum;
	}

    /**
     * Getter totalSum
     * @return {number}
     */
	public get totalSum(): number {
		return this._totalSum;
	}

    /**
     * Setter cartonSum
     * @param {number} value
     */
	public set cartonSum(value: number) {
		this._cartonSum = value;
	}

    /**
     * Setter totalSum
     * @param {number} value
     */
	public set totalSum(value: number) {
		this._totalSum = value;
	}


    /**
     * Getter looseSum
     * @return {number}
     */
	public get looseSum(): number {
		return this._looseSum;
	}

    /**
     * Setter looseSum
     * @param {number} value
     */
	public set looseSum(value: number) {
		this._looseSum = value;
	}


    /**
     * Getter warehouseId
     * @return {number}
     */
	public get warehouseId(): number {
		return this._warehouseId;
	}

    /**
     * Getter warehouseName
     * @return {string}
     */
	public get warehouseName(): string {
		return this._warehouseName;
	}

    /**
     * Getter itemWarehouseMarka
     * @return {sbtItemWarehouseMarka[]}
     */
	public get itemWarehouseMarka(): sbtItemWarehouseMarka[] {
		return this._itemWarehouseMarka;
	}

    /**
     * Setter warehouseId
     * @param {number} value
     */
	public set warehouseId(value: number) {
		this._warehouseId = value;
	}

    /**
     * Setter warehouseName
     * @param {string} value
     */
	public set warehouseName(value: string) {
		this._warehouseName = value;
	}

    /**
     * Setter itemWarehouseMarka
     * @param {sbtItemWarehouseMarka[]} value
     */
	public set itemWarehouseMarka(value: sbtItemWarehouseMarka[]) {
		this._itemWarehouseMarka = value;
	}


}

export class sbtItemWarehouseMarka {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('cartonPc')
    @deserializeAs('cartonPc')
    private _cartonPc: number;

    @serializeAs('cartonOfSum')
    @deserializeAs('cartonOfSum')
    private _cartonOfSum: number;

    @serializeAs('total')
    @deserializeAs('total')
    private _total: number;

    @serializeAs('marka')
    @deserializeAs('marka')
    private _marka: string;

    @deserializeAs('isSelected')
    private _isSelected: boolean;

    @serializeAs('itemWarehouseStock')
    @deserializeAs('itemWarehouseStock')
    private _itemWarehouseStock: sbtItemWarehouseStock[];

    constructor() {
        this.itemWarehouseStock = []
        this.isSelected = false;
    }


    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}
    

    /**
     * Getter isSelected
     * @return {boolean}
     */
	public get isSelected(): boolean {
		return this._isSelected;
	}

    /**
     * Setter isSelected
     * @param {boolean} value
     */
	public set isSelected(value: boolean) {
		this._isSelected = value;
	}



    /**
     * Getter cartonPc
     * @return {number}
     */
	public get cartonPc(): number {
		return this._cartonPc;
	}

    /**
     * Setter cartonPc
     * @param {number} value
     */
	public set cartonPc(value: number) {
		this._cartonPc = value;
	}

    /**
     * Getter cartonOfSum
     * @return {number}
     */
	public get cartonOfSum(): number {
		return this._cartonOfSum;
	}

    /**
     * Setter cartonOfSum
     * @param {number} value
     */
	public set cartonOfSum(value: number) {
		this._cartonOfSum = value;
	}

    /**
     * Getter total
     * @return {number}
     */
	public get total(): number {
		return this._total;
	}

    /**
     * Setter total
     * @param {number} value
     */
	public set total(value: number) {
		this._total = value;
	}

    /**
     * Getter marka
     * @return {string}
     */
	public get marka(): string {
		return this._marka;
	}

    /**
     * Setter marka
     * @param {string} value
     */
	public set marka(value: string) {
		this._marka = value;
	}

    /**
     * Getter itemWarehouseStock
     * @return {sbtItemWarehouseStock[]}
     */
	public get itemWarehouseStock(): sbtItemWarehouseStock[] {
		return this._itemWarehouseStock;
	}

    /**
     * Setter itemWarehouseStock
     * @param {sbtItemWarehouseStock[]} value
     */
	public set itemWarehouseStock(value: sbtItemWarehouseStock[]) {
		this._itemWarehouseStock = value;
	}
    
}

export class sbtItemWarehouseStock {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('carton')
    @deserializeAs('carton')
    private _carton: number;

    @serializeAs('cartonPc')
    @deserializeAs('cartonPc')
    private _cartonPc: number;

    @serializeAs('loose')
    @deserializeAs('loose')
    private _loose: number;

    @serializeAs('aisleId')
    @deserializeAs('aisleId')
    private _aisleId: number;

    @serializeAs('aisleName')
    @deserializeAs('aisleName')
    private _aisleName: string;

    @serializeAs('rackId')
    @deserializeAs('rackId')
    private _rackId: number;

    @serializeAs('rackName')
    @deserializeAs('rackName')
    private _rackName: string;

    @serializeAs('cartonQty')
    @deserializeAs('cartonQty')
    private _cartonQty: number;

    @serializeAs('looseField')
    @deserializeAs('looseField')
    private _looseField: number;

    @serializeAs('cartonField')
    @deserializeAs('cartonField')
    private _cartonField: number;

    constructor() {}


    /**
     * Getter looseField
     * @return {number}
     */
	public get looseField(): number {
		return this._looseField;
	}

    /**
     * Setter looseField
     * @param {number} value
     */
	public set looseField(value: number) {
		this._looseField = value;
	}

    /**
     * Getter cartonField
     * @return {number}
     */
	public get cartonField(): number {
		return this._cartonField;
	}

    /**
     * Setter cartonField
     * @param {number} value
     */
	public set cartonField(value: number) {
		this._cartonField = value;
	}
    

    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Getter carton
     * @return {number}
     */
	public get carton(): number {
		return this._carton;
	}

    /**
     * Setter carton
     * @param {number} value
     */
	public set carton(value: number) {
		this._carton = value;
	}

    /**
     * Getter cartonPc
     * @return {number}
     */
	public get cartonPc(): number {
		return this._cartonPc;
	}

    /**
     * Setter cartonPc
     * @param {number} value
     */
	public set cartonPc(value: number) {
		this._cartonPc = value;
	}

    /**
     * Getter loose
     * @return {number}
     */
	public get loose(): number {
		return this._loose;
	}

    /**
     * Setter loose
     * @param {number} value
     */
	public set loose(value: number) {
		this._loose = value;
	}

    /**
     * Getter aisleId
     * @return {number}
     */
	public get aisleId(): number {
		return this._aisleId;
	}

    /**
     * Setter aisleId
     * @param {number} value
     */
	public set aisleId(value: number) {
		this._aisleId = value;
	}

    /**
     * Getter aisleName
     * @return {string}
     */
	public get aisleName(): string {
		return this._aisleName;
	}

    /**
     * Setter aisleName
     * @param {string} value
     */
	public set aisleName(value: string) {
		this._aisleName = value;
	}

    /**
     * Getter rackId
     * @return {number}
     */
	public get rackId(): number {
		return this._rackId;
	}

    /**
     * Setter rackId
     * @param {number} value
     */
	public set rackId(value: number) {
		this._rackId = value;
	}

    /**
     * Getter rackName
     * @return {string}
     */
	public get rackName(): string {
		return this._rackName;
	}

    /**
     * Setter rackName
     * @param {string} value
     */
	public set rackName(value: string) {
		this._rackName = value;
	}

    /**
     * Getter cartonQty
     * @return {number}
     */
	public get cartonQty(): number {
		return this._cartonQty;
	}

    /**
     * Setter cartonQty
     * @param {number} value
     */
	public set cartonQty(value: number) {
		this._cartonQty = value;
	}


    
}