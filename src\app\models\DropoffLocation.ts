import { deserializeAs, serializeAs } from 'cerialize';

export class DropOffLocation {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('warehouseId')
    @deserializeAs('warehouseId')
    private _warehouseId: number;

    @serializeAs('locationName')
    @deserializeAs('locationName')
    private _locationName: string;

    @serializeAs('description')
    @deserializeAs('description')
    private _description: string;

    @serializeAs('isActive')
    @deserializeAs('isActive')
    private _isActive: boolean;

    constructor() {
        this.isActive = false;
    }


    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Getter warehouseId
     * @return {number}
     */
	public get warehouseId(): number {
		return this._warehouseId;
	}

    /**
     * Getter locationName
     * @return {string}
     */
	public get locationName(): string {
		return this._locationName;
	}

    /**
     * Getter description
     * @return {string}
     */
	public get description(): string {
		return this._description;
	}

    /**
     * Getter isActive
     * @return {boolean}
     */
	public get isActive(): boolean {
		return this._isActive;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Setter warehouseId
     * @param {number} value
     */
	public set warehouseId(value: number) {
		this._warehouseId = value;
	}

    /**
     * Setter locationName
     * @param {string} value
     */
	public set locationName(value: string) {
		this._locationName = value;
	}

    /**
     * Setter description
     * @param {string} value
     */
	public set description(value: string) {
		this._description = value;
	}

    /**
     * Setter isActive
     * @param {boolean} value
     */
	public set isActive(value: boolean) {
		this._isActive = value;
	}


}