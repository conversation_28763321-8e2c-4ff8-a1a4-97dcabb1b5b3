<div class="main-content-image-preview">
  <div class="page-content">
    <div class="text-center mb-4">
      <h4 class="fw-bold">Pocket Knife</h4>
      <p class="text-muted">SKU: <strong>VO55555</strong></p>
    </div>
    <div class="content-area m-4">
      <div class="card card-theme card-table-sticky">
        <div class="card-body p-0">
          <div class="row row-cols-3 g-5 item-grid-view">
            <div class="col" *ngFor="let item of imageList; index as i">
              <div class="card border-0 product-card">
                <div class="card-image-marka">
                  <img [src]="item.formattedName ? (utilsService.imgPath + item.formattedName) : null" alt="valamji" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>