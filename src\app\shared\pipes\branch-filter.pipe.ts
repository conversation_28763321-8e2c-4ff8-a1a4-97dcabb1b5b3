import { Pipe, PipeTransform } from '@angular/core';
import { BranchSideBar } from '../constants/interface';

@Pipe({
  name: 'branchFilter'
})
export class BranchFilterPipe implements PipeTransform {

  transform(items: BranchSideBar[], isLoggedInBranch: boolean): BranchSideBar[] {
    if (isLoggedInBranch) {
      return items.filter(item => item.isLoggedInBranch);
    }
    else {
      return items.filter(item => !item.isLoggedInBranch);
    }
  }

}
