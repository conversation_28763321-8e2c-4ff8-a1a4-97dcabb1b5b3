/* tslint:disable:no-unused-variable */
import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { DebugElement } from '@angular/core';

import { NewManualJournalComponent } from './new-manual-journal.component';

describe('NewManualJournalComponent', () => {
  let component: NewManualJournalComponent;
  let fixture: ComponentFixture<NewManualJournalComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ NewManualJournalComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(NewManualJournalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
