import { HttpErrorResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '@env/environment';
import { ResponseWrapperDTO } from '@modal/response/ResponseWrapperDTO';
import { UtilsService } from '@service/utils.service';
import { Observable, Subject, takeUntil, map, finalize, catchError, of, tap, retry } from 'rxjs';
import { BranchSideBar } from '../../constants/interface';

@Injectable({
  providedIn: 'root'
})
export class SidebarService {

  destroy$ = new Subject();

  constructor(private utilsService: UtilsService) { }

  getDefaultBranch = (showToaster: boolean): Observable<BranchSideBar[]> => {
    const API = `${environment.API_URL}${this.utilsService.serverVariableService.USER_BRANCHES}?userId=${this.utilsService.userId}`;
    const headers = this.utilsService.setHeaders(this.utilsService.serverVariableService.USER_BRANCHES);

    this.utilsService.showLoader++;

    return this.utilsService.http.get<ResponseWrapperDTO>(API, { headers }).pipe(
      tap(response => {
        if (showToaster) {
          this.utilsService.toasterService.success(response.message, '', this.utilsService.toastConfig);
        }
      }),
      map(response => response.data as BranchSideBar[]),
      retry({count: 5, delay: 5000}),
      catchError((error: HttpErrorResponse) => {
        this.utilsService.errorHandler(error);
        return of(null);
      }),
      finalize(() => {
        if (this.utilsService.showLoader > 0) {
          this.utilsService.showLoader--;
        }
      }),
      takeUntil(this.destroy$),
    );
  };
  
}

