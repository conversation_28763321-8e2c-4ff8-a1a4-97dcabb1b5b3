import { deserializeAs, serializeAs } from 'cerialize';
import { sbtAssociateItem } from './sbtAssociateItems';

export class sbtRequestBySelf {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('expectedDate')
    @deserializeAs('expectedDate')
    private _expectedDate: string;

    @deserializeAs('temp_expectedDate')
    private _temp_expectedDate: any;

    @serializeAs('tempoNumber')
    @deserializeAs('tempoNumber')
    private _tempoNumber: string;

    @serializeAs('note')
    @deserializeAs('note')
    private _note: string;

    @serializeAs('pickupPersonId')
    @deserializeAs('pickupPersonId')
    private _pickupPersonId: number;

    @serializeAs('contactExtensionId')
    @deserializeAs('contactExtensionId')
    private _contactExtensionId: number;

    @serializeAs('mobileNo')
    @deserializeAs('mobileNo')
    private _mobileNo: string;

    @serializeAs('reasonId')
    @deserializeAs('reasonId')
    private _reasonId: number;

    @serializeAs('requestToBranchId')
    @deserializeAs('requestToBranchId')
    private _requestToBranchId: number;

    @deserializeAs('oldRequestBranchId')
    private _oldRequestBranchId: number;

    @serializeAs('deleteAssociatedItemIds')
    @deserializeAs('deleteAssociatedItemIds')
    private _deleteAssociatedItemIds: number[];

    @serializeAs('deleteTransferSelfFileIds')
    @deserializeAs('deleteTransferSelfFileIds')
    private _deleteTransferSelfFileIds: number[];

    @deserializeAs('docs')
    private _docs: any[];

    @serializeAs('transferAssociatedItems')
    @deserializeAs('transferAssociatedItems')
    private _transferAssociatedItems: sbtAssociateItem[];

    @deserializeAs('requestFromBranchName')
    private _requestFromBranchName: string;
    
    @deserializeAs('requestToBranchName')
    private _requestToBranchName: string;

    @deserializeAs('requestedBy')
    private _requestedBy: string;

    @deserializeAs('reason')
    private _reason: string;

    @deserializeAs('requestStatus')
    private _requestStatus: any;

    @deserializeAs('isExpand')
    private _isExpand: boolean;

    @deserializeAs('transferId')
    private _transferId: number;

    @deserializeAs('requestDate')
    private _requestDate: any;

    constructor() {
        this.isExpand = false;
        this.deleteAssociatedItemIds = []
        this.deleteTransferSelfFileIds = []
        this.docs = []
        this.transferAssociatedItems = []
    }


    /**
     * Getter requestDate
     * @return {any}
     */
	public get requestDate(): any {
		return this._requestDate;
	}

    /**
     * Setter requestDate
     * @param {any} value
     */
	public set requestDate(value: any) {
		this._requestDate = value;
	}


    /**
     * Getter transferId
     * @return {number}
     */
	public get transferId(): number {
		return this._transferId;
	}

    /**
     * Setter transferId
     * @param {number} value
     */
	public set transferId(value: number) {
		this._transferId = value;
	}


    /**
     * Getter isExpand
     * @return {boolean}
     */
	public get isExpand(): boolean {
		return this._isExpand;
	}

    /**
     * Setter isExpand
     * @param {boolean} value
     */
	public set isExpand(value: boolean) {
		this._isExpand = value;
	}


    /**
     * Getter requestFromBranchName
     * @return {string}
     */
	public get requestFromBranchName(): string {
		return this._requestFromBranchName;
	}

    /**
     * Getter requestToBranchName
     * @return {string}
     */
	public get requestToBranchName(): string {
		return this._requestToBranchName;
	}

    /**
     * Getter requestedBy
     * @return {string}
     */
	public get requestedBy(): string {
		return this._requestedBy;
	}

    /**
     * Getter reason
     * @return {string}
     */
	public get reason(): string {
		return this._reason;
	}

    /**
     * Getter requestStatus
     * @return {any}
     */
	public get requestStatus(): any {
		return this._requestStatus;
	}

    /**
     * Setter requestFromBranchName
     * @param {string} value
     */
	public set requestFromBranchName(value: string) {
		this._requestFromBranchName = value;
	}

    /**
     * Setter requestToBranchName
     * @param {string} value
     */
	public set requestToBranchName(value: string) {
		this._requestToBranchName = value;
	}

    /**
     * Setter requestedBy
     * @param {string} value
     */
	public set requestedBy(value: string) {
		this._requestedBy = value;
	}

    /**
     * Setter reason
     * @param {string} value
     */
	public set reason(value: string) {
		this._reason = value;
	}

    /**
     * Setter requestStatus
     * @param {any} value
     */
	public set requestStatus(value: any) {
		this._requestStatus = value;
	}


    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Getter expectedDate
     * @return {string}
     */
	public get expectedDate(): string {
		return this._expectedDate;
	}

    /**
     * Getter temp_expectedDate
     * @return {any}
     */
	public get temp_expectedDate(): any {
		return this._temp_expectedDate;
	}

    /**
     * Getter tempoNumber
     * @return {string}
     */
	public get tempoNumber(): string {
		return this._tempoNumber;
	}

    /**
     * Getter note
     * @return {string}
     */
	public get note(): string {
		return this._note;
	}

    /**
     * Getter pickupPersonId
     * @return {number}
     */
	public get pickupPersonId(): number {
		return this._pickupPersonId;
	}

    /**
     * Getter contactExtensionId
     * @return {number}
     */
	public get contactExtensionId(): number {
		return this._contactExtensionId;
	}

    /**
     * Getter mobileNo
     * @return {string}
     */
	public get mobileNo(): string {
		return this._mobileNo;
	}

    /**
     * Getter reasonId
     * @return {number}
     */
	public get reasonId(): number {
		return this._reasonId;
	}

    /**
     * Getter requestToBranchId
     * @return {number}
     */
	public get requestToBranchId(): number {
		return this._requestToBranchId;
	}

    /**
     * Getter oldRequestBranchId
     * @return {number}
     */
	public get oldRequestBranchId(): number {
		return this._oldRequestBranchId;
	}

    /**
     * Getter deleteAssociatedItemIds
     * @return {number[]}
     */
	public get deleteAssociatedItemIds(): number[] {
		return this._deleteAssociatedItemIds;
	}

    /**
     * Getter deleteTransferSelfFileIds
     * @return {number[]}
     */
	public get deleteTransferSelfFileIds(): number[] {
		return this._deleteTransferSelfFileIds;
	}

    /**
     * Getter docs
     * @return {any[]}
     */
	public get docs(): any[] {
		return this._docs;
	}

    /**
     * Getter transferAssociatedItems
     * @return {sbtAssociateItem[]}
     */
	public get transferAssociatedItems(): sbtAssociateItem[] {
		return this._transferAssociatedItems;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Setter expectedDate
     * @param {string} value
     */
	public set expectedDate(value: string) {
		this._expectedDate = value;
	}

    /**
     * Setter temp_expectedDate
     * @param {any} value
     */
	public set temp_expectedDate(value: any) {
		this._temp_expectedDate = value;
	}

    /**
     * Setter tempoNumber
     * @param {string} value
     */
	public set tempoNumber(value: string) {
		this._tempoNumber = value;
	}

    /**
     * Setter note
     * @param {string} value
     */
	public set note(value: string) {
		this._note = value;
	}

    /**
     * Setter pickupPersonId
     * @param {number} value
     */
	public set pickupPersonId(value: number) {
		this._pickupPersonId = value;
	}

    /**
     * Setter contactExtensionId
     * @param {number} value
     */
	public set contactExtensionId(value: number) {
		this._contactExtensionId = value;
	}

    /**
     * Setter mobileNo
     * @param {string} value
     */
	public set mobileNo(value: string) {
		this._mobileNo = value;
	}

    /**
     * Setter reasonId
     * @param {number} value
     */
	public set reasonId(value: number) {
		this._reasonId = value;
	}

    /**
     * Setter requestToBranchId
     * @param {number} value
     */
	public set requestToBranchId(value: number) {
		this._requestToBranchId = value;
	}

    /**
     * Setter oldRequestBranchId
     * @param {number} value
     */
	public set oldRequestBranchId(value: number) {
		this._oldRequestBranchId = value;
	}

    /**
     * Setter deleteAssociatedItemIds
     * @param {number[]} value
     */
	public set deleteAssociatedItemIds(value: number[]) {
		this._deleteAssociatedItemIds = value;
	}

    /**
     * Setter deleteTransferSelfFileIds
     * @param {number[]} value
     */
	public set deleteTransferSelfFileIds(value: number[]) {
		this._deleteTransferSelfFileIds = value;
	}

    /**
     * Setter docs
     * @param {any[]} value
     */
	public set docs(value: any[]) {
		this._docs = value;
	}

    /**
     * Setter transferAssociatedItems
     * @param {sbtAssociateItem[]} value
     */
	public set transferAssociatedItems(value: sbtAssociateItem[]) {
		this._transferAssociatedItems = value;
	}


}