import { deserializeAs, serializeAs } from 'cerialize';
import { POImportItem } from './POImportItem';

export class POImportList {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @deserializeAs('showIsPrintQrCode')
    private _showIsPrintQrCode: boolean;

    @serializeAs('supplierId')
    @deserializeAs('supplierId')
    private _supplierId: number

    @deserializeAs('importerId')
    private _importerId: number

    @deserializeAs('countryExtension')
    private _countryExtension: any;

    @deserializeAs('phone')
    private _phone: any;

    @serializeAs('mobileNo')
    @deserializeAs('mobileNo')
    private _mobileNo: string;

    @serializeAs('supplierName')
    @deserializeAs('supplierName')
    private _supplierName: string;

    @serializeAs('supplierShortCode')
    @deserializeAs('supplierShortCode')
    private _supplierShortCode: string;

    @deserializeAs('isExpand')
    private _isExpand: boolean;

    @serializeAs('poImports')
    @deserializeAs('poImports')
    private _poImports: POImportsSub[];

    @serializeAs('poImportCreate')
    @deserializeAs('poImportCreate')
    private _poImportCreate: POImportsSub[];

    @serializeAs('poImportReceivedCh')
    @deserializeAs('poImportReceivedCh')
    private _poImportReceivedCh: POImportsSub[];

    @serializeAs('poImportItemList')
    @deserializeAs('poImportItemList')
    private _poImportItemList: POImportItem[];

    @deserializeAs('grnItems')
    private _grnItems: POImportItem[];

    @deserializeAs('containerName')
    private _containerName: string;

    @deserializeAs('importerName')
    private _importerName: string;

    @deserializeAs('loadedDate')
    private _loadedDate: string;

    @deserializeAs('trackingLink')
    private _trackingLink: string;

    @deserializeAs('expectedDeliveryDate')
    private _expectedDeliveryDate: string;

    @deserializeAs('totalCartons')
    private _totalCartons: number;

    @deserializeAs('cbmPrice')
    private _cbmPrice: number;

    @deserializeAs('conversationRate')
    private _conversationRate: number;

    @deserializeAs('status')
    private _status: any;

    @deserializeAs('selectedContainer')
    private _selectedContainer: any;

    @deserializeAs('containerId')
    private _containerId: number;

    @deserializeAs('note')
    private _note: string;

    @deserializeAs('date')
    private _date: string;

    @deserializeAs('cartonQtyVehicle')
    private _cartonQtyVehicle: number;

    @deserializeAs('vehicleNo')
    private _vehicleNo: string;

    @deserializeAs('supplierAmount')
    private _supplierAmount: number;

    @deserializeAs('poLimit')
    private _poLimit: number;

    @deserializeAs('totalContainerAmount')
    private _totalContainerAmount: number;

    @deserializeAs('totalLoadedCarton')
    private _totalLoadedCarton: number;
    
    @deserializeAs('grnReceivedCartons')
    private _grnReceivedCartons: number;

    @deserializeAs('releasedDate')
    private _releasedDate: any;

    @deserializeAs('shippingTypes')
    private _shippingTypes: any;

    @deserializeAs('containersName')
    private _containersName: any;

    @deserializeAs('isStartGrnFlag')
    private _isStartGrnFlag: boolean;

    @deserializeAs('isTempoCreated')
    private _isTempoCreated: boolean;

    @deserializeAs('grnno')
    private _grnno: string;

    @deserializeAs('tempoNo')
    private _tempoNo: string;

    @deserializeAs('grndate')
    private _grndate: string;

    @deserializeAs('totalReceivedQty')
    private _totalReceivedQty: number;

    @deserializeAs('grnStatus')
    private _grnStatus: any;

    @deserializeAs('markIsCompleted')
    private _markIsCompleted: boolean;

    @deserializeAs('containerIDs')
    private _containerIDs: number[];

    @deserializeAs('showCartonMapping')
    private _showCartonMapping: boolean;

    constructor() {
        this.poImports = [];
        this.poImportCreate = []
        this.poImportReceivedCh = [];
        this.poImportItemList = [];
        this.isExpand = false;
        this.isStartGrnFlag = false;
        this.isTempoCreated = false;
        this.grnItems = [];
        this.markIsCompleted = false;
        this.containerIDs = []
        this.showCartonMapping = false;
        this.showIsPrintQrCode = false;
    }

    /**
     * Getter showIsPrintQrCode
     * @return {boolean}
     */
	public get showIsPrintQrCode(): boolean {
		return this._showIsPrintQrCode;
	}

    /**
     * Setter showIsPrintQrCode
     * @param {boolean} value
     */
	public set showIsPrintQrCode(value: boolean) {
		this._showIsPrintQrCode = value;
	}

    /**
     * Getter showCartonMapping
     * @return {boolean}
     */
	public get showCartonMapping(): boolean {
		return this._showCartonMapping;
	}

    /**
     * Setter showCartonMapping
     * @param {boolean} value
     */
	public set showCartonMapping(value: boolean) {
		this._showCartonMapping = value;
	}
    


    /**
     * Getter containerIDs
     * @return {number[]}
     */
	public get containerIDs(): number[] {
		return this._containerIDs;
	}

    /**
     * Setter containerIDs
     * @param {number[]} value
     */
	public set containerIDs(value: number[]) {
		this._containerIDs = value;
	}


    /**
     * Getter markIsCompleted
     * @return {boolean}
     */
	public get markIsCompleted(): boolean {
		return this._markIsCompleted;
	}

    /**
     * Setter markIsCompleted
     * @param {boolean} value
     */
	public set markIsCompleted(value: boolean) {
		this._markIsCompleted = value;
	}

    /**
     * Getter importerId
     * @return {number}
     */
	public get importerId(): number {
		return this._importerId;
	}

    /**
     * Setter importerId
     * @param {number} value
     */
	public set importerId(value: number) {
		this._importerId = value;
	}
    

    /**
     * Getter grnStatus
     * @return {any}
     */
	public get grnStatus(): any {
		return this._grnStatus;
	}

    /**
     * Setter grnStatus
     * @param {any} value
     */
	public set grnStatus(value: any) {
		this._grnStatus = value;
	}


    /**
     * Getter grnItems
     * @return {POImportItem[]}
     */
	public get grnItems(): POImportItem[] {
		return this._grnItems;
	}


    /**
     * Getter grnReceivedCartons
     * @return {number}
     */
	public get grnReceivedCartons(): number {
		return this._grnReceivedCartons;
	}

    /**
     * Setter grnReceivedCartons
     * @param {number} value
     */
	public set grnReceivedCartons(value: number) {
		this._grnReceivedCartons = value;
	}

    /**
     * Setter grnItems
     * @param {POImportItem[]} value
     */
	public set grnItems(value: POImportItem[]) {
		this._grnItems = value;
	}


    /**
     * Getter totalReceivedQty
     * @return {number}
     */
	public get totalReceivedQty(): number {
		return this._totalReceivedQty;
	}

    /**
     * Setter totalReceivedQty
     * @param {number} value
     */
	public set totalReceivedQty(value: number) {
		this._totalReceivedQty = value;
	}


    /**
     * Getter grndate
     * @return {string}
     */
	public get grndate(): string {
		return this._grndate;
	}

    /**
     * Setter grndate
     * @param {string} value
     */
	public set grndate(value: string) {
		this._grndate = value;
	}


    /**
     * Getter tempoNo
     * @return {string}
     */
	public get tempoNo(): string {
		return this._tempoNo;
	}

    /**
     * Setter tempoNo
     * @param {string} value
     */
	public set tempoNo(value: string) {
		this._tempoNo = value;
	}


    /**
     * Getter grnno
     * @return {string}
     */
	public get grnno(): string {
		return this._grnno;
	}

    /**
     * Setter grnno
     * @param {string} value
     */
	public set grnno(value: string) {
		this._grnno = value;
	}


    /**
     * Getter isTempoCreated
     * @return {boolean}
     */
	public get isTempoCreated(): boolean {
		return this._isTempoCreated;
	}

    /**
     * Setter isTempoCreated
     * @param {boolean} value
     */
	public set isTempoCreated(value: boolean) {
		this._isTempoCreated = value;
	}
    

    /**
     * Getter isStartGrnFlag
     * @return {boolean}
     */
	public get isStartGrnFlag(): boolean {
		return this._isStartGrnFlag;
	}

    /**
     * Setter isStartGrnFlag
     * @param {boolean} value
     */
	public set isStartGrnFlag(value: boolean) {
		this._isStartGrnFlag = value;
	}



    /**
     * Getter containersName
     * @return {any}
     */
	public get containersName(): any {
		return this._containersName;
	}

    /**
     * Setter containersName
     * @param {any} value
     */
	public set containersName(value: any) {
		this._containersName = value;
	}


    /**
     * Getter shippingTypes
     * @return {any}
     */
	public get shippingTypes(): any {
		return this._shippingTypes;
	}

    /**
     * Setter shippingTypes
     * @param {any} value
     */
	public set shippingTypes(value: any) {
		this._shippingTypes = value;
	}


    /**
     * Getter releasedDate
     * @return {any}
     */
	public get releasedDate(): any {
		return this._releasedDate;
	}

    /**
     * Setter releasedDate
     * @param {any} value
     */
	public set releasedDate(value: any) {
		this._releasedDate = value;
	}


    /**
     * Getter totalContainerAmount
     * @return {number}
     */
	public get totalContainerAmount(): number {
		return this._totalContainerAmount;
	}

    /**
     * Getter totalLoadedCarton
     * @return {number}
     */
	public get totalLoadedCarton(): number {
		return this._totalLoadedCarton;
	}

    /**
     * Setter totalContainerAmount
     * @param {number} value
     */
	public set totalContainerAmount(value: number) {
		this._totalContainerAmount = value;
	}

    /**
     * Setter totalLoadedCarton
     * @param {number} value
     */
	public set totalLoadedCarton(value: number) {
		this._totalLoadedCarton = value;
	}


    /**
     * Getter poLimit
     * @return {number}
     */
	public get poLimit(): number {
		return this._poLimit;
	}

    /**
     * Setter poLimit
     * @param {number} value
     */
	public set poLimit(value: number) {
		this._poLimit = value;
	}


    /**
     * Getter supplierAmount
     * @return {number}
     */
	public get supplierAmount(): number {
		return this._supplierAmount;
	}

    /**
     * Setter supplierAmount
     * @param {number} value
     */
	public set supplierAmount(value: number) {
		this._supplierAmount = value;
	}


    /**
     * Getter date
     * @return {string}
     */
	public get date(): string {
		return this._date;
	}

    /**
     * Getter cartonQtyVehicle
     * @return {number}
     */
	public get cartonQtyVehicle(): number {
		return this._cartonQtyVehicle;
	}

    /**
     * Getter vehicleNo
     * @return {string}
     */
	public get vehicleNo(): string {
		return this._vehicleNo;
	}

    /**
     * Setter date
     * @param {string} value
     */
	public set date(value: string) {
		this._date = value;
	}

    /**
     * Setter cartonQtyVehicle
     * @param {number} value
     */
	public set cartonQtyVehicle(value: number) {
		this._cartonQtyVehicle = value;
	}

    /**
     * Setter vehicleNo
     * @param {string} value
     */
	public set vehicleNo(value: string) {
		this._vehicleNo = value;
	}


    /**
     * Getter note
     * @return {string}
     */
	public get note(): string {
		return this._note;
	}

    /**
     * Setter note
     * @param {string} value
     */
	public set note(value: string) {
		this._note = value;
	}


    /**
     * Getter selectedContainer
     * @return {any}
     */
	public get selectedContainer(): any {
		return this._selectedContainer;
	}

    /**
     * Getter containerId
     * @return {number}
     */
	public get containerId(): number {
		return this._containerId;
	}

    /**
     * Setter selectedContainer
     * @param {any} value
     */
	public set selectedContainer(value: any) {
		this._selectedContainer = value;
	}

    /**
     * Setter containerId
     * @param {number} value
     */
	public set containerId(value: number) {
		this._containerId = value;
	}


    /**
     * Getter cbmPrice
     * @return {number}
     */
	public get cbmPrice(): number {
		return this._cbmPrice;
	}

    /**
     * Getter conversationRate
     * @return {number}
     */
	public get conversationRate(): number {
		return this._conversationRate;
	}

    /**
     * Setter cbmPrice
     * @param {number} value
     */
	public set cbmPrice(value: number) {
		this._cbmPrice = value;
	}

    /**
     * Setter conversationRate
     * @param {number} value
     */
	public set conversationRate(value: number) {
		this._conversationRate = value;
	}


    /**
     * Getter status
     * @return {any}
     */
	public get status(): any {
		return this._status;
	}

    /**
     * Setter status
     * @param {any} value
     */
	public set status(value: any) {
		this._status = value;
	}


    /**
     * Getter totalCartons
     * @return {number}
     */
	public get totalCartons(): number {
		return this._totalCartons;
	}

    /**
     * Setter totalCartons
     * @param {number} value
     */
	public set totalCartons(value: number) {
		this._totalCartons = value;
	}


    /**
     * Getter expectedDeliveryDate
     * @return {string}
     */
	public get expectedDeliveryDate(): string {
		return this._expectedDeliveryDate;
	}

    /**
     * Setter expectedDeliveryDate
     * @param {string} value
     */
	public set expectedDeliveryDate(value: string) {
		this._expectedDeliveryDate = value;
	}


    /**
     * Getter containerName
     * @return {string}
     */
	public get containerName(): string {
		return this._containerName;
	}

    /**
     * Getter importerName
     * @return {string}
     */
	public get importerName(): string {
		return this._importerName;
	}

    /**
     * Getter loadedDate
     * @return {string}
     */
	public get loadedDate(): string {
		return this._loadedDate;
	}

    /**
     * Getter trackingLink
     * @return {string}
     */
	public get trackingLink(): string {
		return this._trackingLink;
	}

    /**
     * Setter containerName
     * @param {string} value
     */
	public set containerName(value: string) {
		this._containerName = value;
	}

    /**
     * Setter importerName
     * @param {string} value
     */
	public set importerName(value: string) {
		this._importerName = value;
	}

    /**
     * Setter loadedDate
     * @param {string} value
     */
	public set loadedDate(value: string) {
		this._loadedDate = value;
	}

    /**
     * Setter trackingLink
     * @param {string} value
     */
	public set trackingLink(value: string) {
		this._trackingLink = value;
	}


    /**
     * Getter poImportItemList
     * @return {POImportItem[]}
     */
	public get poImportItemList(): POImportItem[] {
		return this._poImportItemList;
	}

    /**
     * Setter poImportItemList
     * @param {POImportItem[]} value
     */
	public set poImportItemList(value: POImportItem[]) {
		this._poImportItemList = value;
	}


    /**
     * Getter poImportReceivedCh
     * @return {POImportsSub[]}
     */
	public get poImportReceivedCh(): POImportsSub[] {
		return this._poImportReceivedCh;
	}

    /**
     * Setter poImportReceivedCh
     * @param {POImportsSub[]} value
     */
	public set poImportReceivedCh(value: POImportsSub[]) {
		this._poImportReceivedCh = value;
	}


    /**
     * Getter poImportCreate
     * @return {POImportsSub[]}
     */
	public get poImportCreate(): POImportsSub[] {
		return this._poImportCreate;
	}

    /**
     * Setter poImportCreate
     * @param {POImportsSub[]} value
     */
	public set poImportCreate(value: POImportsSub[]) {
		this._poImportCreate = value;
	}

        /**
     * Getter supplierId
     * @return {number}
     */
	public get supplierId(): number {
		return this._supplierId;
	}

        /**
     * Setter supplierId
     * @param {number} value
     */
	public set supplierId(value: number) {
		this._supplierId = value;
	}
    

    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Getter countryExtension
     * @return {any}
     */
	public get countryExtension(): any {
		return this._countryExtension;
	}

    /**
     * Getter phone
     * @return {any}
     */
	public get phone(): any {
		return this._phone;
	}

    /**
     * Getter mobileNo
     * @return {string}
     */
	public get mobileNo(): string {
		return this._mobileNo;
	}

    /**
     * Getter supplierName
     * @return {string}
     */
	public get supplierName(): string {
		return this._supplierName;
	}

    /**
     * Getter supplierShortCode
     * @return {string}
     */
	public get supplierShortCode(): string {
		return this._supplierShortCode;
	}

    /**
     * Getter isExpand
     * @return {boolean}
     */
	public get isExpand(): boolean {
		return this._isExpand;
	}

    /**
     * Getter poImports
     * @return {POImportsSub[]}
     */
	public get poImports(): POImportsSub[] {
		return this._poImports;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Setter countryExtension
     * @param {any} value
     */
	public set countryExtension(value: any) {
		this._countryExtension = value;
	}

    /**
     * Setter phone
     * @param {any} value
     */
	public set phone(value: any) {
		this._phone = value;
	}

    /**
     * Setter mobileNo
     * @param {string} value
     */
	public set mobileNo(value: string) {
		this._mobileNo = value;
	}

    /**
     * Setter supplierName
     * @param {string} value
     */
	public set supplierName(value: string) {
		this._supplierName = value;
	}

    /**
     * Setter supplierShortCode
     * @param {string} value
     */
	public set supplierShortCode(value: string) {
		this._supplierShortCode = value;
	}

    /**
     * Setter isExpand
     * @param {boolean} value
     */
	public set isExpand(value: boolean) {
		this._isExpand = value;
	}

    /**
     * Setter poImports
     * @param {POImportsSub[]} value
     */
	public set poImports(value: POImportsSub[]) {
		this._poImports = value;
	}


}

export class POImportsSub {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('cbmPrice')
    @deserializeAs('cbmPrice')
    private _cbmPrice: number;

    @serializeAs('mobileNo')
    @deserializeAs('mobileNo')
    private _mobileNo: string;

    @serializeAs('notes')
    @deserializeAs('notes')
    private _notes: string;

    // @serializeAs('poImportDate')
    @deserializeAs('poImportDate')
    private _poImportDate: string;

    @serializeAs('poImportItemList')
    @deserializeAs('poImportItemList')
    private _poImportItemList: POImportItem[];

    @deserializeAs('isExpand')
    private _isExpand: boolean;

    @deserializeAs('purchaseOrder')
    private _purchaseOrder: string;

    @deserializeAs('conversationRateCurrencyName')
    private _conversationRateCurrencyName: string;

    @deserializeAs('conversationRate')
    private _conversationRate: string;

    @deserializeAs('shippingTypes')
    private _shippingTypes: any;

    @deserializeAs('totalCartons')
    private _totalCartons: any;

    @deserializeAs('rcDate')
    private _rcDate: any;

    @deserializeAs('rcId')
    private _rcId: any;

    @deserializeAs('totalAmount')
    private _totalAmount: number;

    @deserializeAs('receivedCarton')
    private _receivedCarton: number;

    constructor( ) {
        this.poImportItemList = [];
        this.isExpand = false;
    }


    /**
     * Getter receivedCarton
     * @return {number}
     */
	public get receivedCarton(): number {
		return this._receivedCarton;
	}

    /**
     * Setter receivedCarton
     * @param {number} value
     */
	public set receivedCarton(value: number) {
		this._receivedCarton = value;
	}


    /**
     * Getter totalAmount
     * @return {number}
     */
	public get totalAmount(): number {
		return this._totalAmount;
	}

    /**
     * Setter totalAmount
     * @param {number} value
     */
	public set totalAmount(value: number) {
		this._totalAmount = value;
	}
    

    /**
     * Getter rcDate
     * @return {any}
     */
	public get rcDate(): any {
		return this._rcDate;
	}

    /**
     * Setter rcDate
     * @param {any} value
     */
	public set rcDate(value: any) {
		this._rcDate = value;
	}

    /**
     * Getter rcId
     * @return {any}
     */
	public get rcId(): any {
		return this._rcId;
	}

    /**
     * Setter rcId
     * @param {any} value
     */
	public set rcId(value: any) {
		this._rcId = value;
	}
    

    /**
     * Getter cbmPrice
     * @return {number}
     */
	public get cbmPrice(): number {
		return this._cbmPrice;
	}

    /**
     * Setter cbmPrice
     * @param {number} value
     */
	public set cbmPrice(value: number) {
		this._cbmPrice = value;
	}
    

    /**
     * Getter totalCartons
     * @return {any}
     */
	public get totalCartons(): any {
		return this._totalCartons;
	}

    /**
     * Setter totalCartons
     * @param {any} value
     */
	public set totalCartons(value: any) {
		this._totalCartons = value;
	}
    

    /**
     * Getter shippingTypes
     * @return {any}
     */
	public get shippingTypes(): any {
		return this._shippingTypes;
	}

    /**
     * Setter shippingTypes
     * @param {any} value
     */
	public set shippingTypes(value: any) {
		this._shippingTypes = value;
	}


    /**
     * Getter conversationRateCurrencyName
     * @return {string}
     */
	public get conversationRateCurrencyName(): string {
		return this._conversationRateCurrencyName;
	}

    /**
     * Setter conversationRateCurrencyName
     * @param {string} value
     */
	public set conversationRateCurrencyName(value: string) {
		this._conversationRateCurrencyName = value;
	}

    /**
     * Getter conversationRate
     * @return {string}
     */
	public get conversationRate(): string {
		return this._conversationRate;
	}

    /**
     * Setter conversationRate
     * @param {string} value
     */
	public set conversationRate(value: string) {
		this._conversationRate = value;
	}
    

    /**
     * Getter purchaseOrder
     * @return {string}
     */
	public get purchaseOrder(): string {
		return this._purchaseOrder;
	}

    /**
     * Setter purchaseOrder
     * @param {string} value
     */
	public set purchaseOrder(value: string) {
		this._purchaseOrder = value;
	}
    

    /**
     * Getter poImportDate
     * @return {string}
     */
	public get poImportDate(): string {
		return this._poImportDate;
	}

    /**
     * Setter poImportDate
     * @param {string} value
     */
	public set poImportDate(value: string) {
		this._poImportDate = value;
	}
    

    /**
     * Getter isExpand
     * @return {boolean}
     */
	public get isExpand(): boolean {
		return this._isExpand;
	}

    /**
     * Setter isExpand
     * @param {boolean} value
     */
	public set isExpand(value: boolean) {
		this._isExpand = value;
	}


    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Getter mobileNo
     * @return {string}
     */
	public get mobileNo(): string {
		return this._mobileNo;
	}

    /**
     * Setter mobileNo
     * @param {string} value
     */
	public set mobileNo(value: string) {
		this._mobileNo = value;
	}

    /**
     * Getter notes
     * @return {string}
     */
	public get notes(): string {
		return this._notes;
	}

    /**
     * Setter notes
     * @param {string} value
     */
	public set notes(value: string) {
		this._notes = value;
	}

    /**
     * Getter poImportItemList
     * @return {POImportItem[]}
     */
	public get poImportItemList(): POImportItem[] {
		return this._poImportItemList;
	}

    /**
     * Setter poImportItemList
     * @param {POImportItem[]} value
     */
	public set poImportItemList(value: POImportItem[]) {
		this._poImportItemList = value;
	}


}