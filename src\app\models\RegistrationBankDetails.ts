import { deserializeAs, serializeAs } from 'cerialize';

export class RegistrationBankDetails {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('holderName')
    @deserializeAs('holderName')
    private _holderName: string;

    @serializeAs('bankName')
    @deserializeAs('bankName')
    private _bankName: string;

    @serializeAs('accountNo')
    @deserializeAs('accountNo')
    private _accountNo: string;

    @serializeAs('reEnterAccountNo')
    @deserializeAs('reEnterAccountNo')
    private _reEnterAccountNo: string;

    @serializeAs('IFSC')
    @deserializeAs('IFSC')
    private _IFSC: string;

    @deserializeAs('ifsc')
    private _ifsc: string;

    @serializeAs('currencyId')
    @deserializeAs('currencyId')
    private _currencyId: number;

    constructor() {}


    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Getter holderName
     * @return {string}
     */
	public get holderName(): string {
		return this._holderName;
	}

    /**
     * Getter bankName
     * @return {string}
     */
	public get bankName(): string {
		return this._bankName;
	}

    /**
     * Getter accountNo
     * @return {string}
     */
	public get accountNo(): string {
		return this._accountNo;
	}

    /**
     * Getter reEnterAccountNo
     * @return {string}
     */
	public get reEnterAccountNo(): string {
		return this._reEnterAccountNo;
	}

    /**
     * Getter IFSC
     * @return {string}
     */
	public get IFSC(): string {
		return this._IFSC;
	}

    /**
     * Getter ifsc
     * @return {string}
     */
	public get ifsc(): string {
		return this._ifsc;
	}

    /**
     * Getter currencyId
     * @return {number}
     */
	public get currencyId(): number {
		return this._currencyId;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Setter holderName
     * @param {string} value
     */
	public set holderName(value: string) {
		this._holderName = value;
	}

    /**
     * Setter bankName
     * @param {string} value
     */
	public set bankName(value: string) {
		this._bankName = value;
	}

    /**
     * Setter accountNo
     * @param {string} value
     */
	public set accountNo(value: string) {
		this._accountNo = value;
	}

    /**
     * Setter reEnterAccountNo
     * @param {string} value
     */
	public set reEnterAccountNo(value: string) {
		this._reEnterAccountNo = value;
	}

    /**
     * Setter IFSC
     * @param {string} value
     */
	public set IFSC(value: string) {
		this._IFSC = value;
	}

    /**
     * Setter ifsc
     * @param {string} value
     */
	public set ifsc(value: string) {
		this._ifsc = value;
	}

    /**
     * Setter currencyId
     * @param {number} value
     */
	public set currencyId(value: number) {
		this._currencyId = value;
	}
    
}