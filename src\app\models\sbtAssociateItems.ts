import { deserializeAs, serializeAs } from 'cerialize';
import { sbtAIWarehouseItem } from './sbtAIWarehouseItem';

export class sbtAssociateItem {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @deserializeAs('itemDropdown')
    private _itemDropdown: any[];

    @deserializeAs('orderUnitDropdown')
    private _orderUnitDropdown: any[];

    @serializeAs('itemId')
    @deserializeAs('itemId')
    private _itemId: number;

    @serializeAs('orderUnit')
    @deserializeAs('orderUnit')
    private _orderUnit: any;

    @serializeAs('requestQty')
    @deserializeAs('requestQty')
    private _requestQty: number;

    @serializeAs('pcsCarton')
    @deserializeAs('pcsCarton')
    private _pcsCarton: number;

    @serializeAs('isMarka')
    @deserializeAs('isMarka')
    private _isMarka: boolean;

    @serializeAs('itemMarkas')
    @deserializeAs('itemMarkas')
    private _itemMarkas: any[];

    @deserializeAs('currentStock')
    private _currentStock: number;

    @deserializeAs('totalPcs')
    private _totalPcs: number;

    @deserializeAs('marka')
    private _marka: string;

    @deserializeAs('formattedName')
    private _formattedName: string;

    @deserializeAs('selectedMarkas')
    private _selectedMarkas: any;

    @deserializeAs('markaLooseOrCartonTotal')
    private _markaLooseOrCartonTotal: any;

    @deserializeAs('stockTransferBreachAlert')
    private _stockTransferBreachAlert: any;

    @deserializeAs('transferBreachAlert')
    private _transferBreachAlert: any;

    @deserializeAs('itemWarehouseStock')
    private _itemWarehouseStock: sbtAIWarehouseItem[];

    @deserializeAs('displayName')
    private _displayName: string;

    @deserializeAs('skuId')
    private _skuId: string;

    @deserializeAs('itemName')
    private _itemName: string;

    @deserializeAs('breachAlert')
    private _breachAlert: any[];

    @deserializeAs('branchHeaderList')
    private _branchHeaderList: any[];

    constructor() {
        this.itemMarkas = [];
        this.itemDropdown = [];
        this.orderUnitDropdown = [];
        this.itemWarehouseStock = [];
        this.branchHeaderList = [];
    }


    /**
     * Getter branchHeaderList
     * @return {any[]}
     */
	public get branchHeaderList(): any[] {
		return this._branchHeaderList;
	}

    /**
     * Setter branchHeaderList
     * @param {any[]} value
     */
	public set branchHeaderList(value: any[]) {
		this._branchHeaderList = value;
	}


    /**
     * Getter breachAlert
     * @return {any[]}
     */
	public get breachAlert(): any[] {
		return this._breachAlert;
	}

    /**
     * Setter breachAlert
     * @param {any[]} value
     */
	public set breachAlert(value: any[]) {
		this._breachAlert = value;
	}


    /**
     * Getter itemName
     * @return {string}
     */
	public get itemName(): string {
		return this._itemName;
	}

    /**
     * Setter itemName
     * @param {string} value
     */
	public set itemName(value: string) {
		this._itemName = value;
	}


    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Getter itemDropdown
     * @return {any[]}
     */
	public get itemDropdown(): any[] {
		return this._itemDropdown;
	}

    /**
     * Getter orderUnitDropdown
     * @return {any[]}
     */
	public get orderUnitDropdown(): any[] {
		return this._orderUnitDropdown;
	}

    /**
     * Getter itemId
     * @return {number}
     */
	public get itemId(): number {
		return this._itemId;
	}

    /**
     * Getter orderUnit
     * @return {any}
     */
	public get orderUnit(): any {
		return this._orderUnit;
	}

    /**
     * Getter requestQty
     * @return {number}
     */
	public get requestQty(): number {
		return this._requestQty;
	}

    /**
     * Getter pcsCarton
     * @return {number}
     */
	public get pcsCarton(): number {
		return this._pcsCarton;
	}

    /**
     * Getter isMarka
     * @return {boolean}
     */
	public get isMarka(): boolean {
		return this._isMarka;
	}

    /**
     * Getter itemMarkas
     * @return {any[]}
     */
	public get itemMarkas(): any[] {
		return this._itemMarkas;
	}

    /**
     * Getter currentStock
     * @return {number}
     */
	public get currentStock(): number {
		return this._currentStock;
	}

    /**
     * Getter totalPcs
     * @return {number}
     */
	public get totalPcs(): number {
		return this._totalPcs;
	}

    /**
     * Getter marka
     * @return {string}
     */
	public get marka(): string {
		return this._marka;
	}

    /**
     * Getter formattedName
     * @return {string}
     */
	public get formattedName(): string {
		return this._formattedName;
	}

    /**
     * Getter selectedMarkas
     * @return {any}
     */
	public get selectedMarkas(): any {
		return this._selectedMarkas;
	}

    /**
     * Getter markaLooseOrCartonTotal
     * @return {any}
     */
	public get markaLooseOrCartonTotal(): any {
		return this._markaLooseOrCartonTotal;
	}

    /**
     * Getter stockTransferBreachAlert
     * @return {any}
     */
	public get stockTransferBreachAlert(): any {
		return this._stockTransferBreachAlert;
	}

    /**
     * Getter transferBreachAlert
     * @return {any}
     */
	public get transferBreachAlert(): any {
		return this._transferBreachAlert;
	}

    /**
     * Getter itemWarehouseStock
     * @return {sbtAIWarehouseItem[]}
     */
	public get itemWarehouseStock(): sbtAIWarehouseItem[] {
		return this._itemWarehouseStock;
	}

    /**
     * Getter displayName
     * @return {string}
     */
	public get displayName(): string {
		return this._displayName;
	}

    /**
     * Getter skuId
     * @return {string}
     */
	public get skuId(): string {
		return this._skuId;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Setter itemDropdown
     * @param {any[]} value
     */
	public set itemDropdown(value: any[]) {
		this._itemDropdown = value;
	}

    /**
     * Setter orderUnitDropdown
     * @param {any[]} value
     */
	public set orderUnitDropdown(value: any[]) {
		this._orderUnitDropdown = value;
	}

    /**
     * Setter itemId
     * @param {number} value
     */
	public set itemId(value: number) {
		this._itemId = value;
	}

    /**
     * Setter orderUnit
     * @param {any} value
     */
	public set orderUnit(value: any) {
		this._orderUnit = value;
	}

    /**
     * Setter requestQty
     * @param {number} value
     */
	public set requestQty(value: number) {
		this._requestQty = value;
	}

    /**
     * Setter pcsCarton
     * @param {number} value
     */
	public set pcsCarton(value: number) {
		this._pcsCarton = value;
	}

    /**
     * Setter isMarka
     * @param {boolean} value
     */
	public set isMarka(value: boolean) {
		this._isMarka = value;
	}

    /**
     * Setter itemMarkas
     * @param {any[]} value
     */
	public set itemMarkas(value: any[]) {
		this._itemMarkas = value;
	}

    /**
     * Setter currentStock
     * @param {number} value
     */
	public set currentStock(value: number) {
		this._currentStock = value;
	}

    /**
     * Setter totalPcs
     * @param {number} value
     */
	public set totalPcs(value: number) {
		this._totalPcs = value;
	}

    /**
     * Setter marka
     * @param {string} value
     */
	public set marka(value: string) {
		this._marka = value;
	}

    /**
     * Setter formattedName
     * @param {string} value
     */
	public set formattedName(value: string) {
		this._formattedName = value;
	}

    /**
     * Setter selectedMarkas
     * @param {any} value
     */
	public set selectedMarkas(value: any) {
		this._selectedMarkas = value;
	}

    /**
     * Setter markaLooseOrCartonTotal
     * @param {any} value
     */
	public set markaLooseOrCartonTotal(value: any) {
		this._markaLooseOrCartonTotal = value;
	}

    /**
     * Setter stockTransferBreachAlert
     * @param {any} value
     */
	public set stockTransferBreachAlert(value: any) {
		this._stockTransferBreachAlert = value;
	}

    /**
     * Setter transferBreachAlert
     * @param {any} value
     */
	public set transferBreachAlert(value: any) {
		this._transferBreachAlert = value;
	}

    /**
     * Setter itemWarehouseStock
     * @param {sbtAIWarehouseItem[]} value
     */
	public set itemWarehouseStock(value: sbtAIWarehouseItem[]) {
		this._itemWarehouseStock = value;
	}

    /**
     * Setter displayName
     * @param {string} value
     */
	public set displayName(value: string) {
		this._displayName = value;
	}

    /**
     * Setter skuId
     * @param {string} value
     */
	public set skuId(value: string) {
		this._skuId = value;
	}
   

   

}

export class sbtAImarka {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('warehouseId')
    @deserializeAs('warehouseId')
    private _warehouseId: number;

    @serializeAs('markaId')
    @deserializeAs('markaId')
    private _markaId: number;

    @serializeAs('carton')
    @deserializeAs('carton')
    private _carton: number;

    @serializeAs('loose')
    @deserializeAs('loose')
    private _loose: number;

    constructor() {

    }


    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Getter warehouseId
     * @return {number}
     */
	public get warehouseId(): number {
		return this._warehouseId;
	}

    /**
     * Setter warehouseId
     * @param {number} value
     */
	public set warehouseId(value: number) {
		this._warehouseId = value;
	}

    /**
     * Getter markaId
     * @return {number}
     */
	public get markaId(): number {
		return this._markaId;
	}

    /**
     * Setter markaId
     * @param {number} value
     */
	public set markaId(value: number) {
		this._markaId = value;
	}

    /**
     * Getter carton
     * @return {number}
     */
	public get carton(): number {
		return this._carton;
	}

    /**
     * Setter carton
     * @param {number} value
     */
	public set carton(value: number) {
		this._carton = value;
	}

    /**
     * Getter loose
     * @return {number}
     */
	public get loose(): number {
		return this._loose;
	}

    /**
     * Setter loose
     * @param {number} value
     */
	public set loose(value: number) {
		this._loose = value;
	}


}