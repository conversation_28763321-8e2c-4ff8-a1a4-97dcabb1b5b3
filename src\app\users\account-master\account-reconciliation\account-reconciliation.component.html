<div class="page-content page-content-with-tabs">
  <div class="page-title-wrapper">
    <div class="page-title-left">
      <h4>Account Reconciliation</h4>
    </div>
    <div class="page-title-right">

    </div>
  </div>
  <div class="content-area">
    <div class="account-reconciliation">
      <div class="account-reconciliation-left">
        <div class="page-title-wrapper-sub">
          <div class="page-title-sub-left">
            <div class="form-group theme-ngselect form-group-sm">
              <ng-select placeholder="Status" [multiple]="false" [clearable]="false" [items]="demo" bindLabel="name"
                bindValue="id" [(ngModel)]="selectedDemo1">
              </ng-select>
            </div>
            <div class="form-group theme-ngselect form-group-sm">
              <ng-select placeholder="Bank" [multiple]="false" [clearable]="false" [items]="demo" bindLabel="name"
                bindValue="id" [(ngModel)]="selectedDemo1">
              </ng-select>
            </div>
            <div class="form-group theme-ngselect form-group-sm">
              <ng-select placeholder="All Transactions" [multiple]="false" [clearable]="false" [items]="demo"
                bindLabel="name" bindValue="id" [(ngModel)]="selectedDemo1">
              </ng-select>
            </div>
          </div>
          <div class="page-title-sub-right">
            <div class="form-group form-group-sm">
              <div class="form-group-icon-start">
                <i class="th th-outline-search-normal-1 icon-broder"></i>
                <input type="search" class="form-control" placeholder="Search">
              </div>
            </div>
          </div>
        </div>
        <div class="content-area-sub">
          <!-- Note - class name "without-bank-label" dynamic -->
          <div class="bank-account-amout">
            <div class="bank-account-label">
              <span>Bank</span>
            </div>
            <div class="bank-account-amout-left">
              <div class="bank-account-amout-list">
                <ul>
                  <!-- <li>
                    <p>Last Updated Date</p>
                    <h6>30/12/2024</h6>
                  </li> -->
                  <li>
                    <p>Prev. Closing Amount</p>
                    <h6>₹14,480</h6>
                  </li>
                  <li>
                    <p>Current Closing Amount</p>
                    <h6>₹20,500 </h6>
                  </li>
                </ul>
              </div>
            </div>
            <div class="bank-account-amout-right">
              <div class="bank-account-amout-list">
                <ul>
                  <li>
                    <p>Last Updated Date</p>
                    <span>30/12/2024</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          <div class="card card-theme">
            <div class="card-body ps-0 pe-0">
              <div class='attachments-container attachments-container-style2'>
                <div class='attachments-content'>
                  <button class='btn btn-attachments'><i class="bi bi-upload"></i></button>
                  <p>Drag and Drop file here or <span class='text-primary'>Choose file</span></p>
                </div>
                <input type="file" ref={imageRef} multiple />
              </div>
            </div>
          </div>
          <div class="card card-theme card-table-sticky3">
            <div class="card-body p-0">
              <div class="table-responsive">
                <table class="table-theme table-hover table table-bordered table-sticky">
                  <thead class="border-less">
                    <tr>
                      <th class="d-flex align-items-center gap-2">
                        <div class="checkbox checkbox-primary checkbox-small">
                          <input type="checkbox" id="tbl-checkbox" class="material-inputs filled-in" />
                          <label for="tbl-checkbox"></label>
                        </div>
                        Date
                      </th>
                      <th>Details</th>
                      <th>Credit</th>
                      <th>Deposit</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let item of [1,2,3,4,5,6]">
                      <td class="tbl-user tbl-bold">
                        <div class="tbl-user-checkbox-srno">
                          <div class="checkbox checkbox-primary checkbox-small">
                            <input type="checkbox" id="tbl-checkbox2" class="material-inputs filled-in" />
                            <label for="tbl-checkbox2"></label>
                          </div>
                          <span>19/11/2027 02:40PM</span>
                        </div>
                      </td>
                      <td>
                        <p class="text-primary mb-0"><b>Ref - 1252</b></p>
                        <p class="mb-0">Ink Pen Purchase</p>
                        <p class="mb-0">Pay By: Alpeshhai</p>
                      </td>
                      <td>-</td>
                      <td>₹750.00</td>
                    </tr>
                    <tr class="tbl-selected-row">
                      <td class="tbl-user tbl-bold">
                        <div class="tbl-user-checkbox-srno">
                          <div class="checkbox checkbox-primary checkbox-small">
                            <input type="checkbox" id="tbl-checkbox2" class="material-inputs filled-in" checked />
                            <label for="tbl-checkbox2"></label>
                          </div>
                          <span>19/11/2027 02:40PM</span>
                        </div>
                      </td>
                      <td>
                        <p class="text-primary mb-0"><b>Ref - 1252</b></p>
                        <p class="mb-0">Ink Pen Purchase</p>
                        <p class="mb-0">Pay By: Alpeshhai</p>
                      </td>
                      <td>-</td>
                      <td>₹750.00</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <app-pagination></app-pagination>

          <div class="bank-account-amout bottom-fixed">
            <div class="bank-account-label">
              <span>System</span>
            </div>
            <div class="bank-account-amout-left">
              <div class="bank-account-amout-list">
                <ul>
                  <li>
                    <p>Prev. Closing Amount</p>
                    <h6>₹14,480</h6>
                  </li>
                  <li>
                    <p>Current Closing Amount</p>
                    <h6>17,500 </h6>
                  </li>
                </ul>
              </div>
            </div>
            <div class="bank-account-amout-right">
              <div class="bank-account-amout-list">
                <ul>
                  <li>
                    <p>Difference</p>
                    <h6 class="text-danger">-₹3,000.00</h6>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="account-reconciliation-right">
        <div class="page-title-wrapper-sub">
          <div class="page-title-sub-left">
            <h4>Best Match</h4>
          </div>
          <div class="page-title-sub-right">
            <div class="form-group form-group-sm">
              <div class="form-group-icon-start">
                <i class="th th-outline-search-normal-1 icon-broder"></i>
                <input type="search" class="form-control" placeholder="Search">
              </div>
            </div>
          </div>
        </div>
        <div class="content-area-sub">
          <div class="card card-theme card-table-sticky">
            <div class="card-body p-0">
              <div class="table-responsive">
                <table class="table-theme table-hover table table-bordered table-sticky">
                  <thead class="border-less">
                    <tr>
                      <th>Matches Transactions</th>
                      <th class="text-center">Action</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td>
                        <p class="text-primary mb-0"><b>Expence For ₹750.00</b></p>
                        <p class="mb-0">Date: 07/09/2023, 11:30 AM</p>
                        <p class="mb-0">Pay By: Alpeshhai</p>
                      </td>
                      <td class="tbl-action">
                        <div class="tbl-action-group">
                          <button class="btn btn-xs btn-primary" ngbTooltip="Tooltip" placement="bottom"
                            container="body" triggers="hover">Select</button>
                        </div>
                        <div class="badge badge-warning">Partial Payment</div>
                      </td>
                    </tr>
                    <tr *ngFor="let item of [1,2,3,4,5,6]">
                      <td>
                        <p class="text-primary mb-0"><b>Expence For ₹750.00</b></p>
                        <p class="mb-0">Date: 07/09/2023, 11:30 AM</p>
                        <p class="mb-0">Pay By: Alpeshhai</p>
                      </td>
                      <td class="tbl-action">
                        <div class="tbl-action-group">
                          <button class="btn btn-xs btn-outline-white" ngbTooltip="Tooltip" placement="bottom"
                            container="body" triggers="hover">Select</button>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          <app-pagination></app-pagination>
        </div>
      </div>
    </div>
  </div>

  <div class='bottombar-wrapper bottom-fixed'>
    <div class='bottombar-container'>
      <div class='bottombar-left'>
        <button type="button" class="btn btn-primary btn-icon-text btn-sm"> <i class="th th-outline-tick-circle"></i>
          Save</button>
        <button type="button" class="btn btn-outline-white btn-icon-text btn-sm"><i
            class="th th-outline-close-circle"></i>Cancel</button>
      </div>
      <div class='bottombar-right'>

      </div>
    </div>
  </div>
</div>