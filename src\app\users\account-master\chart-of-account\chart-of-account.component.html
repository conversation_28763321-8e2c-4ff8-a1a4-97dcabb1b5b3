<div class="page-content"
  [pageAccess]="{page: utilsService.enumForPage.ACCOUNTS, action: utilsService.enumForPage.VIEW_ACCOUNTS, view: true}">
  <div class="page-title-wrapper">
    <div class="page-title-left">
      <h4>Chart Of Account Master</h4>
    </div>
    <div class="page-title-right">
      <button class="btn btn-sm btn-primary btn-icon-text" (click)="openAddEditModal(null, 'Add')"
        [pageAccess]="{page: utilsService.enumForPage.ACCOUNTS, action: utilsService.enumForPage.ADD_ACCOUNTS}">
        <i class="th th-outline-add-circle"></i>Add New
      </button>
      <Button class="btn btn-sm btn-icon btn-outline-white" (click)="getChartData()" ngbTooltip="Refresh"
        placement="left" container="body" triggers="hover">
        <i class="th th-outline-refresh-2"></i>
      </Button>
    </div>
  </div>
  <div class="content-area">
    <div class="page-filters">
      <div class="page-filters-left">
        <div class="form-group form-group-sm">
          <div class="form-group-icon-start">
            <i class="th th-outline-search-normal-1 icon-broder "></i>
            <input (input)="onSearch($event)" type="search" class="form-control" placeholder="Search by name"
              [(ngModel)]="searchText">
          </div>
        </div>
        <div class="form-group theme-ngselect form-group-sm">
          <ng-select (change)="onChangeActive()"  placeholder="Account Type" [multiple]="false" [clearable]="true" [items]="pageAccountTypes" 
            bindLabel="typeName" bindValue="id" [(ngModel)]="selectedAccountType" [hideSelected]="false">
          </ng-select>
        </div>
        <div class="form-group theme-ngselect form-group-sm">
          <ng-select (change)="onChangeActive()" class="" placeholder="Status" [multiple]="false" [clearable]="true"
            [items]="activeInactiveStatus" bindLabel="label" bindValue="value" [(ngModel)]="activeFlag"
            [hideSelected]="false">
          </ng-select>
        </div>
      </div>
      <div class="page-filters-right">
        <button class="btn btn-sm btn-primary btn-icon-text" (click)="onExpandAll()">
          <i [ngClass]="{'bi bi-chevron-double-up': isExpandAll, 'bi bi-chevron-double-down': !isExpandAll}" class=""></i> {{isExpandAll ? 'Collapse All' : 'Expand All'}}
        </button>
      </div>
    </div>
    <div class="card card-theme card-table-sticky">
      <div class="card-body p-0">
        <div class="table-responsive">
          <table
            class="table-theme table-hover table table-bordered table-hierarchy table-hierarchy-without-checkbox table-sticky">
            <thead class="border-less">
              <tr>
                <th *ngFor="let th of chartAcTH; index as j" [class]="th.class" [ngClass]="{'sorting-asc': sortColumn==th.keyName && sortOrder === enumForSortOrder.A, 
                                          'sorting-desc': sortColumn==th.keyName && sortOrder === enumForSortOrder.D }"
                  (click)="!utilsService.isNullUndefinedOrBlank(th.keyName) ? onSortTH(th.keyName) : $event.preventDefault()">
                  <!-- <div (click)="$event.stopPropagation()" *ngIf="j === 0"
                    class="checkbox checkbox-primary checkbox-small">
                    <input [disabled]="utilsService.isEmptyObjectOrNullUndefined(accountChartList)"
                      (change)="selectAll()" [(ngModel)]="flagForSelectAll" type="checkbox" id="tbl-checkbox"
                      class="material-inputs filled-in" />
                    <label for="tbl-checkbox"></label>
                  </div> -->
                  {{th.displayName}}
                </th>
                <th class="tbl-switch"
                  *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.EDIT_ACCOUNTS])">Status</th>
                <th class="text-center"
                  *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.EDIT_ACCOUNTS, this.utilsService.enumForPage.DELETE_ACCOUNTS])">
                  Action</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngIf="utilsService.isEmptyObjectOrNullUndefined(accountChartList)">
                <td colspan="6" class="text-center">
                  <span class="truncate">{{utilsService.serverVariableService.STRING_WHEN_NO_RECORDS_FOUND}}</span>
                </td>
              </tr>
              <ng-container *ngTemplateOutlet="recursiveTable; context: { $implicit: accountChartList }"></ng-container>
            </tbody>
          </table>
          <ng-template #recursiveTable let-list let-chartId="chartId" let-parent="parent">
            <ng-container *ngFor="let chartAc of list; index as i; trackBy: trackBy">
              <tr [ngClass]="{'collapse tr-hierarchy-collapse': chartAc.index != 0, 'show': parent?.isExpand && chartAc.index != 0}"
                id="collapseHierarchyLevel{{chartId ? chartId : ''}}" [class]="chartAc.categoryClass">
                <td [attr.data-indent]="chartAc.index" class="tbl-user tbl-bold">
                  <div class="category-level-border">
                    <span class="category-level-span"></span>
                    <ng-container *ngFor="let child of [1,2,3,4,5]; index as j">
                      <span class="category-level-span-{{j}}"></span>
                    </ng-container>
                    <!-- <span class="category-level-span-2"></span>
                    <span class="category-level-span-3" *ngIf="chartAc.index > 2"></span>
                    <span class="category-level-span-4" *ngIf="chartAc.index > 3"></span> -->
                    <div class="tbl-user-checkbox-srno">
                      <!-- <div class="checkbox checkbox-primary checkbox-small">
                        <input type="checkbox" id="tbl-checkbox1" class="material-inputs filled-in" />
                        <label for="tbl-checkbox1"></label>
                      </div> -->
                      <div class="tbl-user-wrapper" data-bs-toggle="collapse"
                        [attr.data-bs-target]="'#collapseHierarchyLevel' + (chartAc.id)"
                        aria-expanded="false"
                        [attr.aria-controls]="'#collapseHierarchyLevel' + (chartAc.id)" (click)="onCollapse(chartAc)">
                        <div class="tbl-user-image">
                          <i *ngIf="!chartAc.accountChartList" class="th th-outline-box-1"></i>
                          <i *ngIf="chartAc.accountChartList?.length > 0" class="th th-outline-folder-open"></i>
                        </div>
                        <div class="tbl-user-text">
                          <p>{{chartAc.accountName}}
                          <span class="tbl-user-counter"
                            *ngIf="chartAc.accountChartList?.length > 0">{{chartAc.accountChartList?.length}}</span></p>
                        </div>
                      </div>
                    </div>
                  </div>

                </td>
                <td>{{ chartAc.accountCode }}</td>
                <td>{{chartAc.accountTypeName}}</td>
                <td>{{chartAc.lastModifiedDate | date: 'dd/MM/YYYY h:mm a'}}</td>
                <td class="tbl-switch"
                  *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.EDIT_ACCOUNTS])">
                  <div class="switch-box">
                    <label class="switch" htmlFor="switch-{{chartAc.id}}">
                      <input (change)="onChangeStatus(chartAc, chartAc.isActive, i)" type="checkbox"
                        id='switch-{{chartAc.id}}' [(ngModel)]="chartAc.isActive" />
                      <div class="slider round"></div>
                    </label>
                  </div>
                </td>
                <td class="tbl-action"
                  *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.EDIT_ACCOUNTS, this.utilsService.enumForPage.DELETE_ACCOUNTS])">
                  <div class="tbl-action-group">
                    <button class="btn btn-xs btn-light-white btn-icon" (click)="openAddEditModal(chartAc, 'Edit')"
                      [pageAccess]="{page: this.utilsService.enumForPage.ACCOUNTS, action: this.utilsService.enumForPage.EDIT_ACCOUNTS}"
                      ngbTooltip="Edit" placement="bottom" container="body" triggers="hover">
                      <i class="th th-outline-edit"></i>
                    </button>
                    <button class="btn btn-xs btn-light-danger btn-icon" (click)="openDeleteModel(chartAc)"
                      [pageAccess]="{page: this.utilsService.enumForPage.ACCOUNTS, action: this.utilsService.enumForPage.DELETE_ACCOUNTS}"
                      ngbTooltip="Delete" placement="left" container="body" triggers="hover">
                      <i class="th th-outline-trash"></i>
                    </button>
                  </div>
                </td>
              </tr>
              <ng-container *ngIf="chartAc.accountChartList">
                <ng-container
                  *ngTemplateOutlet="recursiveTable; context: { $implicit: chartAc.accountChartList, chartId : chartAc.id, parent: chartAc }"></ng-container>
              </ng-container>
            </ng-container>
          </ng-template>
        </div>
      </div>
    </div>
    <div class="paginationbox pagination-fixed">
      <app-pagination (pagesizeData)="addPageSizeData($event)" (pageNumber)="pageNumber($event)" [page]="pageNo"
        [pageSize]="pageSize" [totalData]="totalData"></app-pagination>
    </div>
  </div>
</div>

<!-- ----------------------------------------------------------------------- -->
<!--                     Add and Edit Forms Modal Start                      -->
<!-- ----------------------------------------------------------------------- -->
<div class="modal modal-theme fade" id="chartOfAccountModal" tabindex="-1" aria-labelledby="colorModalLabel"
  aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel">{{statusForModal === 'Add' ? 'Add New' : 'Edit'}} Account</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
            class='th th-close'></i></button>
      </div>
      <div class="modal-body" [formGroup]="chartOfAccountForm">
        <div class="row">
          <div class="col-12">
            <div class="form-group theme-ngselect theme-ngselect-group required">
              <label class="form-label">Account Type</label>
              <ng-select placeholder="Account Type" [multiple]="false" [clearable]="false"
                [items]="filteredAccountTypes" bindLabel="typeName" bindValue="id"
                [(ngModel)]="accountChartObj.accountTypeId" formControlName="account_type"
                (change)="onChangeAccountType()" groupBy="groupValue">
                <ng-template ng-optgroup-tmp let-item="item">
                  {{ item.groupValue}}
                </ng-template>
              </ng-select>
              <div class="message error-message"
                *ngIf="chartOfAccountForm.controls['account_type'].hasError('required') &&  chartOfAccountForm.controls['account_type'].touched">
                {{utilsService.validationService.ACCOUNT_TYPE_REQUIRED}}
              </div>
            </div>
          </div>
          <div class="col-12" *ngIf="!accountChartObj.id || accountChartObj?.index > 0">
            <div class="form-group">
              <div class=" checkbox checkbox-primary checkbox-small">
                <input type="checkbox" id="markassubaccount" class="material-inputs filled-in"
                  formControlName="markAsSubAccount" [(ngModel)]="accountChartObj.isSubAccount" />
                <label for="markassubaccount">Mark this a sub-account</label>
              </div>
            </div>
          </div>
          <div class="col-12" *ngIf="accountChartObj.isSubAccount">
            <div class="form-group theme-ngselect theme-ngselect-group-list  required">
              <label class="form-label">Parent Account</label>
              <ng-select placeholder="Parent Account" [multiple]="false" [clearable]="false"
                [items]="flattenedParentAccounts" bindLabel="accountName" bindValue="id"
                [(ngModel)]="accountChartObj.parentAccountId" formControlName="parentAccount">
                <ng-template ng-option-tmp let-item="item">
                  <span [style.padding-left.px]="item.index * 25" [ngClass]="{'ng-option-child-label' : item.isChild}"
                    [class]="item.className">
                    {{ item.accountName }}
                  </span>
                </ng-template>
              </ng-select>
              <div class="message error-message"
                *ngIf="chartOfAccountForm.controls['parentAccount'].hasError('required') &&  chartOfAccountForm.controls['parentAccount'].touched">
                {{utilsService.validationService.PARENT_ACCOUNT_INVALID}}
              </div>
            </div>
          </div>
          <div class="col-12">
            <div class="form-group required">
              <label class="form-label">{{accountChartObj.isSubAccount ? 'Sub Account Name' : 'Account Name'}}</label>
              <input type="text" class="form-control" [placeholder]="accountChartObj.isSubAccount ? 'Enter Sub Account Name' : 'Enter Account Name'"
                [maxlength]="utilsService.validationService.MAX_50" [(ngModel)]="accountChartObj.accountName"
                formControlName="name" id="name">
              <div class="message error-message"
                *ngIf="chartOfAccountForm.controls['name'].hasError('required') &&  chartOfAccountForm.controls['name'].touched">
                {{!accountChartObj.isSubAccount ? utilsService.validationService.ACCOUNT_NAME_REQUIRED :
                utilsService.validationService.SUB_ACCOUNT_NAME_REQUIRED}}
              </div>
              <div class="message error-message"
                *ngIf="!chartOfAccountForm.controls['name'].hasError('required') && !chartOfAccountForm.controls['name'].valid && chartOfAccountForm.controls['name'].touched">
                {{!accountChartObj.isSubAccount ? utilsService.validationService.ACCOUNT_NAME_INVALID :
                utilsService.validationService.SUB_ACCOUNT_NAME_REQUIRED}}
              </div>
            </div>
          </div>
          <div class="col-12">
            <div class="form-group required">
              <label class="form-label">Account Code</label>
              <input type="text" class="form-control" placeholder="Enter Account Code"
                [maxlength]="utilsService.validationService.MAX_50" [(ngModel)]="accountChartObj.accountCode"
                formControlName="code">
              <div class="message error-message"
                *ngIf="chartOfAccountForm.controls['code'].hasError('required') &&  chartOfAccountForm.controls['code'].touched">
                {{!accountChartObj.isSubAccount ? utilsService.validationService.ACCOUNT_CODE_REQUIRED :
                utilsService.validationService.SUB_ACCOUNT_CODE_REQUIRED}}
              </div>
              <div class="message error-message"
                *ngIf="!chartOfAccountForm.controls['code'].hasError('required') && !chartOfAccountForm.controls['code'].valid && chartOfAccountForm.controls['code'].touched">
                {{!accountChartObj.isSubAccount ? utilsService.validationService.ACCOUNT_CODE_INVALID :
                utilsService.validationService.SUB_ACCOUNT_CODE_INVALID}}
              </div>
            </div>
          </div>
          <div class="col-12">
            <div class="form-group">
              <label class="form-label">Description</label>
              <textarea class="form-control" placeholder="Enter Description"
                [maxlength]="utilsService.validationService.MAX_1000" [(ngModel)]="accountChartObj.description"
                formControlName="description"></textarea>
            </div>
          </div>
          <div class="col-12">
            <div class="form-group required d-flex justify-content-between">
              <label class="form-label">Status</label>
              <div class="switch-box">
                <label class="switch" htmlFor="switch">
                  <input type="checkbox" id='switch' checked formControlName="status"
                    [(ngModel)]="accountChartObj.isActive" />
                  <div class="slider round"></div>
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <div class="modal-footer-group full-width-btn">
          <button type="button" class="btn btn-primary btn-icon-text" (click)="onSaveChartOfAccounts()"> <i
              class="th th-outline-tick-circle"></i>
            {{statusForModal === 'Add' ? 'Save' : 'Update'}}</button>
          <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                      Add and Edit Forms Modal End                       -->
<!-- ----------------------------------------------------------------------- -->

<!-- ----------------------------------------------------------------------- -->
<!--                           Delete Modal Start                            -->
<!-- ----------------------------------------------------------------------- -->
<div class="modal modal-theme modal-confirmation modal-reject fade" id="deleteModal" tabindex="-1"
  aria-labelledby="deleteModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
            class='th th-close'></i></button>
      </div>
      <div class="modal-body">
        <div class="modal-confirmation-container">
          <div class="modal-confirmation-icon">
            <i class="th th-bold-trash"></i>
          </div>
          <div class="modal-confirmation-content">
            <h5>Are You Sure?</h5>
            <p>You want to Delete <b>{{accountChartObj.accountName}}</b> Chart of Account.</p>
          </div>
        </div>
        <div class="modal-button-group modal-full-width-btn">
          <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
          <button type="button" class="btn btn-primary btn-icon-text" (click)="deleteObj()"> <i
              class="th th-outline-tick-circle"></i>
            Delete</button>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                           Delete Modal End                            -->
<!-- ----------------------------------------------------------------------- -->