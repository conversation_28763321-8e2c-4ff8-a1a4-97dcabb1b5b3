.card {
    &.card-theme {
        border: 0;
        border-radius: 0;

        &.card-table-sticky,
        &.card-table-sticky2,
        &.card-table-sticky3 {
            .table-responsive {
                overflow-y: auto;
                position: relative;
            }
        }

        &.card-table-sticky {
            .table-responsive {
                // max-height: calc(100vh - 204px);
                max-height: calc(100vh - 190px);
            }
        }

        &.card-table-sticky3 {
            .table-responsive {
                // max-height: calc(100vh - 241px);
                // max-height: calc(100vh - 204px);
                max-height: calc(100vh - 222px);
            }
        }


        &.card-scaner {
            .purchases-import-scan-wrapper {
                height: calc(100vh - 189px);
            }
        }

    }

    &.card-theme2 {
        border: 1px solid $stock_light;
        background-color: $bg_blue;
        overflow: hidden;

        .card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            background-color: #E2F4FF;
            border-bottom: 0px;

            .card-header-right,
            .card-header-left {
                display: flex;
                align-items: center;
                gap: 0 10px;
            }

            .card-header-title {
                h2 {
                    font-size: 12px;
                    font-weight: 600;
                    line-height: 15.6px;
                    color: $text_black_color;
                    margin-bottom: 0px;
                }
            }
        }

        .card-body {
            padding: 10px;
        }
    }

    &.card-theme3 {
        border: 1px solid $stock_light;
        background-color: $white_color;
        border-radius: 9px;
        overflow: hidden;
        margin-bottom: 20px;

        &:last-child {
            margin-bottom: 0;
        }

        .card-header {
            background-color: $primary_light_color;
            padding: 10px 17px;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            gap: 0 10px;
            border-bottom: 1px solid $stock_light;

            .card-header-right,
            .card-header-left {
                display: flex;
                align-items: center;
                gap: 0 10px;
            }

            .card-icon {
                width: 17px;
                height: 17px;
                display: flex;
                align-items: center;
                justify-content: center;

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: contain;
                }
            }

            .card-header-title {
                h2 {
                    font-size: 16px;
                    font-weight: 600;
                    line-height: 20.8px;
                    color: $text_black_color;
                    margin-bottom: 0px;
                }
            }

            .button-group {
                position: relative;

                &::after {
                    content: '';
                    position: absolute;
                    width: 1px;
                    height: 17px;
                    background-color: $stock_light;
                    right: -5px;
                    top: 50%;
                    transform: translateY(-50%);
                }
            }
        }

        .card-body {
            padding: 17px;

            .form-group {
                margin-bottom: 14px;
            }

        }

        .card-inner-title {
            margin-bottom: 20px;

            h6 {
                font-size: 18px;
                font-weight: 600;
                line-height: 23.4px;
                color: $text_black_color;
                margin-bottom: 0px;
            }
        }
    }


    .card-theme4 {
        border: 1px solid $stock_light;
        background-color: $white_color;
        overflow: hidden;

        .card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            background-color: $bg_grey;
            border-bottom: 0px;
            padding: 10px;

            .card-header-right,
            .card-header-left {
                display: flex;
                align-items: center;
                gap: 0 10px;
            }

            .card-header-title {
                h2 {
                    font-size: 14px;
                    font-weight: 600;
                    line-height: 18.2px;
                    color: $text_black_color;
                    margin-bottom: 0px;
                }
            }
        }

        .card-body {
            padding: 10px;

            .card-body-wrapper {
                padding: 10px;
            }
        }
    }



    .card-body {
        &.card-body-grid-2 {
            display: flex;
            align-items: baseline;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 8px;

            ul {
                flex: 0 0 40%;

                li {
                    gap: 4px;
                    justify-content: space-between;

                    label {
                        min-width: 90px;
                    }

                    span {
                        text-align: end;
                    }
                }
            }
        }

        .inline-list {
            margin: 0px;
            padding: 0px;
            max-width: 72%;
            overflow-y: hidden;

            li {
                display: flex;
                align-items: center;
                list-style: none;
                width: 100%;
                margin-bottom: 6px;

                label {
                    font-size: 12px;
                    font-weight: 400;
                    line-height: 20.4px;
                    color: $text_color;
                    margin-bottom: 0px;
                    min-width: 120px;
                    word-wrap: break-word;
                    word-break: break-word;
                    white-space: normal;
                }

                span {
                    font-size: 12px;
                    font-weight: 600;
                    line-height: 15.6px;
                    color: $text_black_color;
                    margin-bottom: 0px;
                    word-wrap: break-word;
                    word-break: break-word;
                    white-space: normal;
                }
            }
        }

        .card-bg-image {
            width: 80px;
            height: 80px;
            position: absolute;
            right: 30px;
            bottom: 20px;

            img {
                width: 100%;
                height: 100%;
                object-fit: contain;
            }
        }
    }



    .card-row {
        margin-left: -10px;
        margin-left: -10px;

        .card-col {
            padding-left: 10px;
            padding-right: 10px;
        }
    }


    &.card-amount {
        background-color: #F5FCFF;
        border-radius: 10px;
        border: 1px solid $stock_light;
        box-shadow: none;
        outline: 0;

        .card-body {
            padding: 6px 10px;

            h6 {
                font-weight: 400;
                font-size: 12px;
                line-height: 20.4px;
                letter-spacing: 0;
                color: $text_black_color;
                margin-bottom: 0;
            }

            p {
                font-weight: 600;
                font-size: 12px;
                line-height: 15.6px;
                letter-spacing: 0;
                color: #13172E;
                margin-bottom: 0;
            }

        }
    }
}