import { deserializeAs, serializeAs } from 'cerialize';

export class AccountType {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('typeName')
    @deserializeAs('typeName')
    private _typeName: string;

    @serializeAs('description')
    @deserializeAs('description')
    private _description: string;

    @serializeAs('isActive')
    @deserializeAs('isActive')
    private _isActive: boolean;
   
    @serializeAs('markAsDefault')
    @deserializeAs('markAsDefault')
    private _markAsDefault: boolean;

    @serializeAs('isSelected')
    @deserializeAs('isSelected')
    private _isSelected: boolean;

    @serializeAs('accountGroup')
    @deserializeAs('accountGroup')
    private _accountGroup: any;

    @deserializeAs('lastModifiedDate')
    private _lastModifiedDate: string;

    constructor() {
        this.isActive = true;
    }
    

    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Getter typeName
     * @return {string}
     */
	public get typeName(): string {
		return this._typeName;
	}

    /**
     * Getter description
     * @return {string}
     */
	public get description(): string {
		return this._description;
	}

    /**
     * Getter isActive
     * @return {boolean}
     */
	public get isActive(): boolean {
		return this._isActive;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Setter typeName
     * @param {string} value
     */
	public set typeName(value: string) {
		this._typeName = value;
	}

    /**
     * Setter description
     * @param {string} value
     */
	public set description(value: string) {
		this._description = value;
	}

    /**
     * Setter isActive
     * @param {boolean} value
     */
	public set isActive(value: boolean) {
		this._isActive = value;
	}
 

    /**
     * Getter isSelected
     * @return {boolean}
     */
	public get isSelected(): boolean {
		return this._isSelected;
	}

    /**
     * Setter isSelected
     * @param {boolean} value
     */
	public set isSelected(value: boolean) {
		this._isSelected = value;
	}


    /**
     * Getter accountGroup
     * @return {any}
     */
	public get accountGroup(): any {
		return this._accountGroup;
	}

    /**
     * Setter accountGroup
     * @param {any} value
     */
	public set accountGroup(value: any) {
		this._accountGroup = value;
	}


    /**
     * Getter lastModifiedDate
     * @return {string}
     */
	public get lastModifiedDate(): string {
		return this._lastModifiedDate;
	}

    /**
     * Setter lastModifiedDate
     * @param {string} value
     */
	public set lastModifiedDate(value: string) {
		this._lastModifiedDate = value;
	}


    /**
     * Getter markAsDefault
     * @return {boolean}
     */
	public get markAsDefault(): boolean {
		return this._markAsDefault;
	}

    /**
     * Setter markAsDefault
     * @param {boolean} value
     */
	public set markAsDefault(value: boolean) {
		this._markAsDefault = value;
	}


}