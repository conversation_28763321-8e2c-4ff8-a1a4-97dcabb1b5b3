import { Directive, ElementRef, HostListener } from '@angular/core';

@Directive({
  selector: '[appNumberOnly]'
})
export class NumberOnlyDirective {

  constructor(private el: ElementRef) { }

  @HostListener('input', ['$event']) onInputChange(event: KeyboardEvent): void {
    const inputElement = this.el.nativeElement;
    inputElement.value = inputElement.value.replace(/[^0-9]/g, '');
    event.stopPropagation();
  }
}
