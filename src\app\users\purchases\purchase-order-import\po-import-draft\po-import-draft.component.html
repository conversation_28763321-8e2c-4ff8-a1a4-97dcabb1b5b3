<div class="page-filters">
    <div class="page-filters-left">
        <!-- <div class="form-group form-group-sm filter-search w-25">
            <div class="form-group-icon-start">
                <i class="th th-outline-search-normal-1 icon-broder "></i>
                <input (input)="onSearch($event)" [(ngModel)]="paginationRequest.purchaseOrderSearch" type="text"
                    class="form-control" placeholder="Search by ID">
            </div>
        </div> -->
        <div class="form-group form-group-sm filter-search">
            <div class="form-group-icon-start">
                <i class="th th-outline-search-normal-1 icon-broder "></i>
                <input (change)="onSearchPOItemComments($event)" [(ngModel)]="paginationRequest.searchPurchaseComments"
                    type="text" class="form-control" placeholder="Search by SKU & Comment">
            </div>
        </div>
        <div class="form-group form-group-sm date-range-filter">
            <div class="form-group-icon-end">
                <i (click)="open()" class="th th-outline-calendar ngx-daterangepicker-action"></i>
                <input #pickerDirective class="form-control" type="text" ngxDaterangepickerMd readonly
                    [formControl]="paginationRequest.dateRangeControl" [showCustomRangeLabel]="true" [alwaysShowCalendars]="true"
                    [ranges]="utilsService.ranges" [linkedCalendars]="false" [showClearButton]="false"
                    placeholder="Order Date" [autoApply]="true" [showRangeLabelOnInput]="true" startKey="start"
                    endKey="end">
            </div>
        </div>
        <div class="form-group theme-ngselect form-group-sm">
            <ng-select (change)="onChangeActive()" placeholder="Supplier" [multiple]="false" [clearable]="true"
                [items]="dropdown?.supplierDropdown" bindLabel="displayName" bindValue="id"
                [(ngModel)]="paginationRequest.supplierId">
            </ng-select>
        </div>
        <div class="form-group theme-ngselect form-group-sm">
            <ng-select (change)="onChangeActive()" placeholder="Customer" [multiple]="false" [clearable]="true"
                [items]="dropdown?.customerDropdown" bindLabel="displayName" bindValue="id"
                [(ngModel)]="paginationRequest.customerId">
            </ng-select>
        </div>
        <button (click)="onClear.emit()" class="btn btn-link btn-sm">Clear</button>
    </div>
    <div class="page-filters-right">
        <!-- <app-table-column-filter-dropdown-new [allHeaderArr]="allHeaderArr" [columnArr]="columnArr"
            (saveCol)="saveCol.emit()" (checkIfAllSelected)="checkIfAllSelected.emit()" /> -->

    </div>
</div>

<div class="card card-theme card-table-sticky3">
    <div class="card-body p-0">
        <div class="table-responsive ">
            <table class="table-theme table table-bordered tbl-collapse">
                <thead class="border-less">
                    <tr class="">
                        <th>
                            # Shop No
                        </th>
                        <th>Supplier Name</th>
                        <th>Mobile No</th>
                        <th>Total Amount (RMB)</th>
                        <th>PO Limit (RMB)</th>
                        <th class="text-end">Action</th>
                    </tr>
                </thead>


                <tbody>
                    <ng-container *ngFor="let item of poImportList; index as i; trackBy: trackBy">
                        <tr [ngClass]="{'tbl-bg-secondary-two': item.isExpand}" (click)="toggleExpand(i)">
                            <td class=" tbl-user">
                                <div class="tbl-user-checkbox-srno">
                                    <span>{{(i + 1) | padNum}}.</span>
                                    <b class="text-black">
                                        {{item.supplierShortCode}}
                                    </b>
                                </div>
                            </td>
                            <td>{{item.supplierName}}</td>
                            <td>{{item['countryExtension']}} {{item['phone']}}</td>
                            <td>{{item.supplierAmount ? (item.supplierAmount | indianCurrency) : 0}}</td>
                            <td>{{item.poLimit ? (item.poLimit | indianCurrency) : 0}}</td>
                            <td class="tbl-action" (click)="$event.stopPropagation()">
                                <div class="tbl-action-group justify-content-end">
                                    <button (click)="redirectToDetails.emit()"
                                        [pageAccess]="{page: utilsService.enumForPage.PO, action: utilsService.enumForPage.MOVE_TO_PO_CREATED}"
                                        [routerLink]="'/users/purchases/po-import/move-to-po-created/' + item?.poImports[0]?.id"
                                        class="btn btn-xs btn-light-primary btn-icon" ngbTooltip="Move to PO Created" placement="left" container="body"
                                        triggers="hover">
                                        <i class="th-outline-arrow-right-1"></i>
                                    </button>
                                    <button (click)="toggleExpand(i)" class="btn btn-xs text-color btn-icon btn-link"
                                        data-bs-toggle="collapse" [ngClass]="{'collapse-arrow': item.isExpand}"
                                        role="button" aria-expanded="false" [attr.data.target]="'#table-collapse-2'+ i"
                                        [attr.aria-controls]="'table-collapse-2'+ i">
                                        <i class="th th-outline-arrow-right-3"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>

                        <tr *ngIf="item.isExpand" class="collapse" [ngClass]="{'show': item.isExpand}"
                            [id]="'table-collapse-2' + i">
                            <td colspan="30" class=" tbl-collapse-child tbl-collapse-child-responsive">


                                <table class="table-theme table table-bordered">
                                    <thead class="border-less">
                                        <tr>
                                            <!-- <th> # Purchase Order ID </th> -->
                                            <th>PO Date</th>
                                            <th>Cartons</th>
                                            <th>Total Amount (RMB)</th>
                                            <th>Order Note</th>
                                            <th class="text-end">Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <ng-container
                                            *ngFor="let subItem of item.poImports; index as j; trackBy: trackBySubItem">
                                            <tr class="tbl-bg-gray" (click)="toggleExpandChild(i, j)">
                                                <!-- <td class=" tbl-user ">
                                                            <div class="tbl-user-checkbox-srno">
                                                                <span>01.</span>
                                                                <b class="text-black">
                                                                    PO-1252
                                                                </b>
                                                            </div>
                                                        </td> -->
                                                <td class="">{{subItem.poImportDate ? (subItem.poImportDate | date:
                                                    'dd/MM/yyyy') : '-'}}</td>
                                                <td class="">{{subItem.totalCartons ? subItem.totalCartons : '-'}}</td>
                                                <td class="">{{subItem.totalAmount ? (subItem.totalAmount |
                                                    indianCurrency) : '-'}}</td>
                                                <td class="tbl-description">{{subItem.notes ? subItem.notes : '-'}}</td>
                                                <td class="tbl-action " (click)="$event.stopPropagation()">
                                                    <div class="tbl-action-group justify-content-end">
                                                        <!-- <button
                                                            [routerLink]="'/users/purchases/po-import/edit-draft/' + subItem.id"
                                                            class="btn btn-xs btn-light-primary btn-icon"
                                                            ngbTooltip="Move to PO Created" placement="bottom"
                                                            container="body" triggers="hover">
                                                            <i class="th-outline-arrow-right-1"></i>
                                                        </button> -->
                                                        <div class="dropdown"
                                                            *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.EDIT_PO, this.utilsService.enumForPage.DEL_PO])">
                                                            <button class="btn btn-xs btn-light-white btn-icon" id="actionDropDown" data-bs-toggle="dropdown"
                                                                aria-expanded="false" data-bs-popper-config='{"strategy":"fixed"}' ngbTooltip="More Option" placement="bottom"
                                                                container="body" triggers="hover">
                                                                <i class="th th-outline-more"></i>
                                                            </button>
                                                            <ul class="dropdown-menu" aria-labelledby="actionDropDown">
                                                                <ng-container>
                                                                    <li [pageAccess]="{page: utilsService.enumForPage.PO, action: utilsService.enumForPage.EDIT_PO}">
                                                                        <a class="dropdown-item" (click)="redirectToDetails.emit()"
                                                                            [routerLink]="'/users/purchases/po-import/edit-draft/' + subItem.id"><i
                                                                                class="th th-outline-edit"></i>
                                                                            Edit
                                                                        </a>
                                                                    </li>
                                                                </ng-container>
                                                        
                                                                <ng-container>
                                                                    <li [pageAccess]="{page: utilsService.enumForPage.PO, action: utilsService.enumForPage.DEL_PO}">
                                                                        <hr class="dropdown-divider">
                                                                    </li>
                                                                    <li><a (click)="openDeleteModal(subItem)" class="dropdown-item text-danger"><i
                                                                                class="th th-outline-trash"></i>Delete</a>
                                                                    </li>
                                                                </ng-container>
                                                            </ul>
                                                        
                                                        </div>
                                                        <button (click)="toggleExpandChild(i, j)"
                                                            class="btn btn-xs text-color btn-icon btn-link"
                                                            data-bs-toggle="collapse"
                                                            [ngClass]="{'collapse-arrow': subItem.isExpand}"
                                                            role="button" aria-expanded="false"
                                                            [attr.data.target]="'#table-collapse-2'+ i + j"
                                                            [attr.aria-controls]="'table-collapse-2'+ i + j">
                                                            <i class="th th-outline-arrow-right-3"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>

                                            <tr *ngIf="subItem.isExpand" class="collapse"
                                                [ngClass]="{'show': subItem.isExpand}"
                                                [id]="'table-collapse-2' + i + j">
                                                <td colspan="30"
                                                    class=" tbl-collapse-child tbl-collapse-child-responsive">

                                                    <div class="table-responsive">
                                                        <table
                                                            class="table-theme table table-bordered table-sticky">
                                                            <thead class="border-less">
                                                                <tr class="tbl-bg-light-three">
                                                                    <ng-container
                                                                        *ngFor="let th of headerObj?.optionsArray | filterByShippingType: subItem?.shippingTypes?.value; index as i">
                                                                        <th *ngIf="th.show" [class]="th.class">
                                                                            {{th.header}}
                                                                        </th>
                                                                    </ng-container>
                                                                </tr>
                                                                <tr>
                                                                    <ng-container
                                                                        *ngFor="let th of headerObj?.optionsArray | filterByShippingType: subItem?.shippingTypes?.value; index as k">
                                                                        <th *ngIf="th.show" [class]="th.class"
                                                                            [innerHTML]="th.displayName"></th>
                                                                    </ng-container>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                <tr [ngClass]="{'tbl-bg-danger': child.purchaseRationFlag}"
                                                                    *ngFor="let child of subItem.poImportItemList; index as l; trackBy: trackByChild">
                                                                    <ng-container
                                                                        *ngFor="let column of headerObj.columnArr | filterByShippingType: subItem?.shippingTypes?.value;">
                                                                        <td class="tbl-user" *ngIf="column.show">
                                                                            <ng-container [ngSwitch]="column.key">

                                                                                <ng-container *ngSwitchCase="'item'">
                                                                                    <div class="tbl-user-checkbox-srno">
                                                                                        <span>{{(l + 1) | padNum}}.</span>
                                                                                        <div class="tbl-user-wrapper">
                                                                                            <div class="tbl-user-image"
                                                                                                *ngIf="child?.item">
                                                                                                <img *ngIf="child.item?.formattedName"
                                                                                                    loading="lazy"
                                                                                                    [src]="child.item.formattedName ? (utilsService.imgPath + child.item.formattedName) : ''"
                                                                                                    alt="valamji">
                                                                                                <ng-container
                                                                                                    *ngIf="!child.item?.formattedName">{{
                                                                                                    child.displayName?.charAt(0).toUpperCase()
                                                                                                    }}
                                                                                                </ng-container>
                                                                                            </div>
                                                                                            <div
                                                                                                class="tbl-user-text-action">
                                                                                                <div
                                                                                                    class="tbl-user-text">
                                                                                                    <p>{{child.item?.skuId}}
                                                                                                    </p>
                                                                                                    <span class="tbl-description">{{child.item?.displayName}}</span>
                                                                                                </div>
                                                                                            </div>
                                                                                            <div class="dropdown" *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.VIEW_ITEM, this.utilsService.enumForPage.DEL_PO_ITEM])">
                                                                                                <button
                                                                                                    class="btn btn-xs btn-light-white btn-icon"
                                                                                                    id="actionDropDown"
                                                                                                    data-bs-toggle="dropdown"
                                                                                                    aria-expanded="false"
                                                                                                    data-bs-popper-config='{"strategy":"fixed"}'
                                                                                                    ngbTooltip="More Option"
                                                                                                    placement="bottom"
                                                                                                    container="body"
                                                                                                    triggers="hover"
                                                                                                    ngbTooltip="More Option"
                                                                                                    placement="bottom"
                                                                                                    container="body"
                                                                                                    triggers="hover">
                                                                                                    <i
                                                                                                        class="th th-outline-more"></i>
                                                                                                </button>
                                                                                                <ul class="dropdown-menu"
                                                                                                    aria-labelledby="actionDropDown">
                                                                                                    <li [pageAccess]="{page: utilsService.enumForPage.ITEM, action: utilsService.enumForPage.VIEW_ITEM}">
                                                                                                        <a class="dropdown-item" (click)="utilsService.openItemDetailsInNewTab(child.itemId)">
                                                                                                            <i class="th th-outline-eye"></i>View Item Details
                                                                                                        </a>
                                                                                                    </li>
                                                                                                    <ng-container>
                                                                                                        <hr *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.VIEW_ITEM, this.utilsService.enumForPage.DEL_PO_ITEM])"
                                                                                                            class="dropdown-divider">
                                                                                                        <li [pageAccess]="{page: utilsService.enumForPage.PO, action: utilsService.enumForPage.DEL_PO_ITEM}">
                                                                                                            <a (click)="onItemDel(child, subItem)" class="dropdown-item text-danger">
                                                                                                                <i class="th th-outline-trash"></i>Delete
                                                                                                            </a>
                                                                                                        </li>
                                                                                                    </ng-container>
                                                                                                    <!-- <li><a class="dropdown-item" data-bs-toggle="modal" data-bs-target="#ChangeStatusModal_4">
                                                                                                            <i class="th th-outline-status"></i>Change
                                                                                                            Status</a>
                                                                                                    </li>
                                                                                                    <li><a class="dropdown-item" href="" [routerLink]="['/users/purchases/new-po-import-expenses']"><i
                                                                                                                class="th th-outline-add-circle"></i>Add
                                                                                                            Expense</a></li>
                                                                                                    <li><a class="dropdown-item" href="" [routerLink]="['/users/audit-tickets/list']"><i
                                                                                                                class="th th-outline-box-1"></i>Audit
                                                                                                            Ticket</a>
                                                                                                    </li>
                                                                                                    <li><a class="dropdown-item" href="" [routerLink]="['/users/purchases/new-payments']"><i
                                                                                                                class="th th-outline-dollar-circle"></i>Make
                                                                                                            Payment</a></li>
                                                                                                    <li><a class="dropdown-item" href=""><i class="th th-outline-tick-circle"></i>
                                                                                                            Mark As Received</a>
                                                                                                    </li> -->
                                                                                                    <!-- <li></li> -->
                                                                                                </ul>

                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                </ng-container>

                                                                                <ng-container
                                                                                    *ngSwitchCase="'purchaseBreachQty'">
                                                                                    <div>{{(child.item.levelBreachQtys
                                                                                        &&
                                                                                        child.item.levelBreachQtys.breachQtys)
                                                                                        ?
                                                                                        (child.item.levelBreachQtys.levelName
                                                                                        + ': ' +
                                                                                        child.item.levelBreachQtys.breachQtys)
                                                                                        : '-'}}
                                                                                    </div>
                                                                                </ng-container>

                                                                                <ng-container *ngSwitchCase="'toFrom'">
                                                                                    {{child?.item?.seasonMaster?.fromDate
                                                                                    ?
                                                                                    ((child.item.seasonMaster.fromDate |
                                                                                    date:
                                                                                    'dd-MM') + ' to ' +
                                                                                    (child.item.seasonMaster.toDate |
                                                                                    date:
                                                                                    'dd-MM')) : '-'}}
                                                                                </ng-container>

                                                                                <ng-container
                                                                                    *ngSwitchCase="'advanceDate'">
                                                                                    <span class="text-center">
                                                                                        {{child.item?.seasonMaster?.advanceDate
                                                                                        ?
                                                                                        (child.item?.seasonMaster?.advanceDate
                                                                                        |
                                                                                        date:
                                                                                        'dd-MM') : '-'}}</span>
                                                                                </ng-container>

                                                                                <ng-container *ngSwitchCase="4">
                                                                                    -
                                                                                </ng-container>

                                                                                <ng-container
                                                                                    *ngSwitchCase="'salesPrice'">
                                                                                    {{child.item.itemPrice ?
                                                                                    child.item.itemPrice : '-'}}
                                                                                </ng-container>

                                                                                <ng-container *ngSwitchCase="'marka'">
                                                                                    <div>
                                                                                        {{child.marka ? child.marka :
                                                                                        '-'}}
                                                                                    </div>
                                                                                </ng-container>

                                                                                <ng-container *ngSwitchCase="'color'">
                                                                                    <span class="w-100 d-block" *ngFor="let v of child.colorName">
                                                                                        {{!utilsService.isEmptyObjectOrNullUndefined(v) ? v : ''}}
                                                                                    </span>
                                                                                    <!-- <div>
                                                                                        {{child.colorName ?
                                                                                        child.colorName : '-'}}
                                                                                    </div> -->
                                                                                </ng-container>
                                                                                <ng-container *ngSwitchCase="'note'">
                                                                                    <div class="tbl-po-notes">
                                                                                        {{child.note ? child.note :
                                                                                        '-'}}
                                                                                    </div>
                                                                                </ng-container>
                                                                                <ng-container *ngSwitchCase="'englishComment'">
                                                                                    <div class="tbl-po-notes">
                                                                                        {{child.englishComment ?
                                                                                        child.englishComment : '-'}}
                                                                                    </div>
                                                                                </ng-container>
                                                                                <ng-container *ngSwitchCase="'chinaComment'">
                                                                                    <div class="tbl-po-notes">
                                                                                        {{child.chinaComment ?
                                                                                        child.chinaComment : '-'}}
                                                                                    </div>
                                                                                </ng-container>
                                                                                <ng-container
                                                                                    *ngSwitchCase="'expectedDeliveryDate'">
                                                                                    <div>
                                                                                        {{child.expectedDeliveryDate ?
                                                                                        (child.expectedDeliveryDate |
                                                                                        date: 'dd/MM/yyyy') : '-'}}
                                                                                    </div>
                                                                                </ng-container>
                                                                                <ng-container
                                                                                    *ngSwitchCase="'poCarton'">
                                                                                    {{child.poCarton ? child.poCarton :
                                                                                    '-'}}
                                                                                </ng-container>
                                                                                <ng-container
                                                                                    *ngSwitchCase="'pricePerCarton'">
                                                                                    {{child.pricePerCarton ?
                                                                                    child.pricePerCarton : '-'}}
                                                                                </ng-container>
                                                                                <ng-container
                                                                                    *ngSwitchCase="'totalPcsQty'">
                                                                                    {{(child.totalPcsQty) ?
                                                                                    (child.totalPcsQty)
                                                                                    : '-'}}
                                                                                </ng-container>
                                                                                <ng-container
                                                                                    *ngSwitchCase="'pricePerItem'">
                                                                                    {{child.pricePerItem ?
                                                                                    child.pricePerItem :
                                                                                    '-'}}
                                                                                </ng-container>
                                                                                <ng-container
                                                                                    *ngSwitchCase="'totalAmount'">
                                                                                    {{child.Total_Price ?
                                                                                    child.Total_Price :
                                                                                    '-'}}
                                                                                </ng-container>
                                                                                <ng-container
                                                                                    *ngSwitchCase="'expDeliveryCost'">
                                                                                    {{child.expDeliveryCost ?
                                                                                    child.expDeliveryCost : 0}}
                                                                                </ng-container>
                                                                                <ng-container
                                                                                    *ngSwitchCase="'totalAmountWithExp'">
                                                                                    {{child.totalAmountWithExp ?
                                                                                    (child.totalAmountWithExp |
                                                                                    indianCurrency) : 0}}
                                                                                </ng-container>
                                                                                <ng-container
                                                                                    *ngSwitchCase="'conversationRate'">
                                                                                    <!-- {{subItem.conversationRateCurrencyName
                                                                                    ?
                                                                                    subItem.conversationRateCurrencyName
                                                                                    : ''}} -->
                                                                                    {{subItem.conversationRate ?
                                                                                    subItem.conversationRate : '-'}}
                                                                                </ng-container>
                                                                                <ng-container
                                                                                    *ngSwitchCase="'totalAmountWithExpInINR'">
                                                                                    {{child.totalAmountWithExpInINR ?
                                                                                    (child.totalAmountWithExpInINR |
                                                                                    indianCurrency):
                                                                                    0}}
                                                                                </ng-container>
                                                                                <ng-container
                                                                                    *ngSwitchCase="'chinaFinalExpextedCode'">
                                                                                    {{child.chinaFinalExpextedCode ?
                                                                                    child.chinaFinalExpextedCode : '-'}}
                                                                                </ng-container>

                                                                                <ng-container
                                                                                    *ngSwitchCase="'shippingTypes'">
                                                                                    {{subItem.shippingTypes ?
                                                                                    (subItem.shippingTypes.value) :
                                                                                    '-'}}
                                                                                </ng-container>
                                                                                <ng-container *ngSwitchCase="'itemDim'">
                                                                                    {{child.cartonLength ?
                                                                                    child.cartonLength : '-'}} x
                                                                                    {{child.cartonWidth ?
                                                                                    child.cartonWidth : '-'}}
                                                                                    x
                                                                                    {{child.cartonHeight ?
                                                                                    child.cartonHeight : '-'}}
                                                                                    {{child?.cartonDimensionUnit ?
                                                                                    child?.cartonDimensionUnit?.shortCode :
                                                                                    ''}}
                                                                                </ng-container>
                                                                                <ng-container *ngSwitchCase="'dimAge'">
                                                                                    {{child.dimAge}}
                                                                                </ng-container>

                                                                                <ng-container
                                                                                    *ngSwitchCase="'CBM/Carton'">
                                                                                    {{child.cbmCarton ?
                                                                                    child.cbmCarton : '-'}}
                                                                                </ng-container>
                                                                                <ng-container
                                                                                    *ngSwitchCase="'Total CBM'">
                                                                                    {{child.totalCbm ? child.totalCbm :
                                                                                    '-'}}
                                                                                </ng-container>
                                                                                <ng-container
                                                                                    *ngSwitchCase="'CBM Price'">
                                                                                    {{subItem.cbmPrice ?
                                                                                    (subItem.cbmPrice | indianCurrency)
                                                                                    : 0}}
                                                                                </ng-container>
                                                                                <ng-container
                                                                                    *ngSwitchCase="'TotalCBMExpense'">
                                                                                    {{child.totalCBMExpense ?
                                                                                    (child.totalCBMExpense |
                                                                                    indianCurrency) : 0}}
                                                                                </ng-container>
                                                                                <ng-container
                                                                                    *ngSwitchCase="'ShippingExpense/PCS'">
                                                                                    {{child.shippingExpense ?
                                                                                    (child.shippingExpense |
                                                                                    indianCurrency) : 0}}
                                                                                </ng-container>

                                                                                <ng-container
                                                                                    *ngSwitchCase="'transportCharges'">
                                                                                    {{child.transportCharges ?
                                                                                    (child.transportCharges |
                                                                                    indianCurrency) : 0}}
                                                                                </ng-container>
                                                                                <ng-container
                                                                                    *ngSwitchCase="'totalTransportationChargesM2S'">
                                                                                    {{child.TotalTransportationCharges ?
                                                                                    (child.TotalTransportationCharges |
                                                                                    indianCurrency) : 0}}
                                                                                </ng-container>
                                                                                <ng-container
                                                                                    *ngSwitchCase="'transportationChargesM2SperPCS'">
                                                                                    {{child.Transportationcharges_Mumbai_to_Surat
                                                                                    ?
                                                                                    (child.Transportationcharges_Mumbai_to_Surat
                                                                                    | indianCurrency) : 0}}
                                                                                </ng-container>
                                                                                <ng-container
                                                                                    *ngSwitchCase="'totalInsurance'">
                                                                                    {{child.TotalInsurance ?
                                                                                    (child.TotalInsurance |
                                                                                    indianCurrency) : 0}}
                                                                                </ng-container>
                                                                                <ng-container
                                                                                    *ngSwitchCase="'insurancePerPcs'">
                                                                                    {{child.insurancePCS ?
                                                                                    (child.insurancePCS |
                                                                                    indianCurrency) : 0}}
                                                                                </ng-container>
                                                                                <ng-container
                                                                                    *ngSwitchCase="'gstAmtPerPcs'">
                                                                                    {{child.GSTAmount_PCS ?
                                                                                    (child.GSTAmount_PCS |
                                                                                    indianCurrency) :
                                                                                    0}}
                                                                                </ng-container>
                                                                                <ng-container
                                                                                    *ngSwitchCase="'craneExpense'">
                                                                                    {{child.craneExpense ?
                                                                                    (child.craneExpense |
                                                                                    indianCurrency) :
                                                                                    0}}
                                                                                </ng-container>
                                                                                <ng-container
                                                                                    *ngSwitchCase="'craneExpPcs'">
                                                                                    {{child.CraneExpense_PCS ?
                                                                                    (child.CraneExpense_PCS |
                                                                                    indianCurrency) :
                                                                                    0}}
                                                                                </ng-container>
                                                                                <ng-container
                                                                                    *ngSwitchCase="'totalExp'">
                                                                                    {{child.Total_Expense ?
                                                                                    (child.Total_Expense |
                                                                                    indianCurrency) : 0}}
                                                                                </ng-container>
                                                                                <ng-container
                                                                                    *ngSwitchCase="'chinaToSuratPadtar'">
                                                                                    {{child.ChinaToSuratFinalPrice ?
                                                                                    (child.ChinaToSuratFinalPrice |
                                                                                    indianCurrency) : 0}}
                                                                                </ng-container>

                                                                                <ng-container
                                                                                    *ngSwitchCase="'cartonWeight'">
                                                                                    {{child.Weight_Carton ?
                                                                                    child.Weight_Carton : 0}}
                                                                                </ng-container>
                                                                                <ng-container
                                                                                    *ngSwitchCase="'totalWeight'">
                                                                                    {{child.total_Weight ?
                                                                                    child.total_Weight : 0}}
                                                                                </ng-container>
                                                                                <ng-container
                                                                                    *ngSwitchCase="'cartonWeightRu'">
                                                                                    {{child.Weight_kg ?
                                                                                    child.Weight_kg : 0}}
                                                                                </ng-container>
                                                                                <ng-container
                                                                                    *ngSwitchCase="'totalLoadAmt'">
                                                                                    {{child.total_load_amt_Ru ?
                                                                                    child.total_load_amt_Ru : 0}}
                                                                                </ng-container>
                                                                                <ng-container
                                                                                    *ngSwitchCase="'shippingCostperPieceINR'">
                                                                                    {{child.shipingCost ?
                                                                                    child.shipingCost : 0}}
                                                                                </ng-container>
                                                                                <ng-container
                                                                                    *ngSwitchCase="'totalShippingExpWeight'">
                                                                                    {{child.totalShippingExpense ?
                                                                                    child.totalShippingExpense : 0}}
                                                                                </ng-container>

                                                                                <ng-container
                                                                                    *ngSwitchCase="'percentage'">
                                                                                    {{child.Percentage ?
                                                                                    child.Percentage :
                                                                                    0}}
                                                                                </ng-container>
                                                                                <ng-container
                                                                                    *ngSwitchCase="'totalExpPCSper'">
                                                                                    {{child.totalExpense ?
                                                                                    child.totalExpense : '-'}}
                                                                                </ng-container>
                                                                                <ng-container
                                                                                    *ngSwitchCase="'totalFinalCostPCSper'">
                                                                                    {{child.totalFinalCost ?
                                                                                    child.totalFinalCost : '-'}}
                                                                                </ng-container>

                                                                                <ng-container>
                                                                                    <ng-container
                                                                                        *ngIf="column.key ==50">
                                                                                        {{child.item_Amount ?
                                                                                        (child.item_Amount |
                                                                                        indianCurrency) : 0}}
                                                                                    </ng-container>
                                                                                    <ng-container
                                                                                        *ngIf="column.key ==51">
                                                                                        {{child.item_Amount ?
                                                                                        (child.item_Amount |
                                                                                        indianCurrency) : 0}}
                                                                                    </ng-container>
                                                                                </ng-container>

                                                                                <ng-container
                                                                                    *ngIf="column.key =='expensePcs'">
                                                                                    {{child.Expense_PCS ?
                                                                                    (child.Expense_PCS | indianCurrency)
                                                                                    :
                                                                                    0}}
                                                                                </ng-container>
                                                                                <ng-container
                                                                                    *ngIf="column.key =='totalItemAmt'">
                                                                                    {{child.totalItemAmount ?
                                                                                    (child.totalItemAmount |
                                                                                    indianCurrency) :
                                                                                    0}}
                                                                                </ng-container>

                                                                                <ng-container
                                                                                    *ngSwitchCase="'lastRecord'">
                                                                                    <ng-container
                                                                                        *ngFor="let ls of child?.lastRecord">
                                                                                        {{ls?.supplierShortCode}},
                                                                                        {{ls?.pricePerCarton}},
                                                                                        {{ls?.poCarton}} <br />
                                                                                    </ng-container>
                                                                                </ng-container>
                                                                                <ng-container *ngSwitchCase="55">
                                                                                    -
                                                                                </ng-container>

                                                                                <ng-container *ngSwitchCase="56">
                                                                                    -
                                                                                </ng-container>
                                                                                <ng-container *ngSwitchCase="57">
                                                                                    {{child.gst_amounts ? (child.gst_amounts
                                                                                    | indianCurrency) : 0}}
                                                                                </ng-container>
                                                                                
                                                                                <ng-container *ngSwitchCase="58">
                                                                                    {{child.gstPers ? child.gstPers : '-'}}
                                                                                </ng-container>
                                                                                <ng-container
                                                                                    *ngSwitchCase="'extraExpense'">
                                                                                    {{child.extraExpense ?
                                                                                    child.extraExpense :
                                                                                    '-'}}
                                                                                </ng-container>
                                                                                <ng-container
                                                                                    *ngSwitchCase="'purchaseRation'">
                                                                                    {{child.purchaseRatio ?
                                                                                    child.purchaseRatio : 0}}
                                                                                </ng-container>
                                                                            </ng-container>
                                                                        </td>
                                                                    </ng-container>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>

                                                </td>
                                            </tr>
                                        </ng-container>
                                    </tbody>
                                </table>

                            </td>
                        </tr>
                    </ng-container>
                    <ng-container>
                        <tr *ngIf="utilsService.isEmptyObjectOrNullUndefined(poImportList)">
                            <td colspan="20" class="text-center">
                                <span
                                    class="truncate">{{utilsService.serverVariableService.STRING_WHEN_NO_RECORDS_FOUND}}</span>
                            </td>
                        </tr>
                    </ng-container>
                </tbody>

            </table>
        </div>
    </div>
</div>

<div class="paginationbox pagination-fixed">
    <app-pagination (pagesizeData)="addPageSizeData($event)" (pageNumber)="pageNumber($event)"
        [page]="paginationRequest.pageNo" [pageSize]="paginationRequest.pageSize"
        [totalData]="paginationRequest.totalData"></app-pagination>
</div>