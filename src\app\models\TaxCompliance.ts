import { deserializeAs, serializeAs } from 'cerialize';

export class TaxCompliance {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('createdDate')
    @deserializeAs('createdDate')
    private _createdDate: string;

    @serializeAs('isActive')
    @deserializeAs('isActive')
    private _isActive: boolean;

    @serializeAs('isSelected')
    @deserializeAs('isSelected')
    private _isSelected: boolean;

    @serializeAs('isFlag')
    @deserializeAs('isFlag')
    private _isFlag: boolean;

    @serializeAs('taxName')
    @deserializeAs('taxName')
    private _taxName: string;
    
    @serializeAs('rate')
    @deserializeAs('rate')
    private _rate: any;

    @serializeAs('cgstrate')
    @deserializeAs('cgstrate')
    private _cgstrate: any;

    @serializeAs('sgstrate')
    @deserializeAs('sgstrate')
    private _sgstrate: any;

    @serializeAs('lastModifiedDate')
    @deserializeAs('lastModifiedDate')
    private _lastModifiedDate: string;

    @deserializeAs('isDefault')
    private _isDefault: boolean;

    constructor() {
        this.isActive = false;
        this.isSelected = false;
        this.isFlag = false;
        this.isDefault = false;
    }

    /**
     * Getter isDefault
     * @return {boolean}
     */
	public get isDefault(): boolean {
		return this._isDefault;
	}

    /**
     * Setter isDefault
     * @param {boolean} value
     */
	public set isDefault(value: boolean) {
		this._isDefault = value;
	}

    /**
     * Getter lastModifiedDate
     * @return {string}
     */
	public get lastModifiedDate(): string {
		return this._lastModifiedDate;
	}

    /**
     * Setter lastModifiedDate
     * @param {string} value
     */
	public set lastModifiedDate(value: string) {
		this._lastModifiedDate = value;
	}


    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Getter createdDate
     * @return {string}
     */
	public get createdDate(): string {
		return this._createdDate;
	}

    /**
     * Getter isActive
     * @return {boolean}
     */
	public get isActive(): boolean {
		return this._isActive;
	}

    /**
     * Getter isSelected
     * @return {boolean}
     */
	public get isSelected(): boolean {
		return this._isSelected;
	}

    /**
     * Getter isFlag
     * @return {boolean}
     */
	public get isFlag(): boolean {
		return this._isFlag;
	}

    /**
     * Getter taxName
     * @return {string}
     */
	public get taxName(): string {
		return this._taxName;
	}

    /**
     * Getter rate
     * @return {any}
     */
	public get rate(): any {
		return this._rate;
	}

    /**
     * Getter cgstrate
     * @return {any}
     */
	public get cgstrate(): any {
		return this._cgstrate;
	}

    /**
     * Getter sgstrate
     * @return {any}
     */
	public get sgstrate(): any {
		return this._sgstrate;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Setter createdDate
     * @param {string} value
     */
	public set createdDate(value: string) {
		this._createdDate = value;
	}

    /**
     * Setter isActive
     * @param {boolean} value
     */
	public set isActive(value: boolean) {
		this._isActive = value;
	}

    /**
     * Setter isSelected
     * @param {boolean} value
     */
	public set isSelected(value: boolean) {
		this._isSelected = value;
	}

    /**
     * Setter isFlag
     * @param {boolean} value
     */
	public set isFlag(value: boolean) {
		this._isFlag = value;
	}

    /**
     * Setter taxName
     * @param {string} value
     */
	public set taxName(value: string) {
		this._taxName = value;
	}

    /**
     * Setter rate
     * @param {any} value
     */
	public set rate(value: any) {
		this._rate = value;
	}

    /**
     * Setter cgstrate
     * @param {any} value
     */
	public set cgstrate(value: any) {
		this._cgstrate = value;
	}

    /**
     * Setter sgstrate
     * @param {any} value
     */
	public set sgstrate(value: any) {
		this._sgstrate = value;
	}

}   