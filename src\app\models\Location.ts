import { deserializeAs, serializeAs } from 'cerialize';

export class LocationCountryCityState {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('idOfCountry')
    @deserializeAs('idOfCountry')
    private _idOfCountry: number;

    @serializeAs('idOfState')
    @deserializeAs('idOfState')
    private _idOfState: number;

    @serializeAs('createdDate')
    @deserializeAs('createdDate')
    private _createdDate: string;

    @serializeAs('countryExtension')
    @deserializeAs('countryExtension')
    private _countryExtension: string;

    @serializeAs('name')
    @deserializeAs('name')
    private _name: string;

    @serializeAs('code')
    @deserializeAs('code')
    private _code: string;

    @serializeAs('flagUrl')
    @deserializeAs('flagUrl')
    private _flagUrl: string;

    @serializeAs('isActive')
    @deserializeAs('isActive')
    private _isActive: boolean;

    @serializeAs('isSelected')
    @deserializeAs('isSelected')
    private _isSelected: boolean;

    @serializeAs('markAsPurchaseState')
    @deserializeAs('markAsPurchaseState')
    private _markAsPurchaseState: boolean;

    @serializeAs('markAsPurchaseCity')
    @deserializeAs('markAsPurchaseCity')
    private _markAsPurchaseCity: boolean;

    @serializeAs('countryName')
    @deserializeAs('countryName')
    private _countryName: string;

    @serializeAs('stateName')
    @deserializeAs('stateName')
    private _stateName: string;

    @serializeAs('country')
    @deserializeAs('country')
    private _country: any;

    @serializeAs('state')
    @deserializeAs('state')
    private _state: any;

    @serializeAs('shippingDays')
    @deserializeAs('shippingDays')
    private _shippingDays: number;

    @serializeAs('markAsPurchaseCountry')
    @deserializeAs('markAsPurchaseCountry')
    private _markAsPurchaseCountry: boolean;

    @serializeAs('lastModifiedDate')
    @deserializeAs('lastModifiedDate')
    private _lastModifiedDate: string;

    @deserializeAs('originalName')
    private _originalName: string;

    @deserializeAs('isMarkAsPurchaseCountry')
    private _isMarkAsPurchaseCountry: boolean;

    @deserializeAs('isDefault')
    private _isDefault: boolean;

    constructor() {
        this.isActive = false;
        this.isSelected = false;
        this.markAsPurchaseCity = false;
        this.markAsPurchaseCountry = false;
        this.markAsPurchaseState = false;
        this.isMarkAsPurchaseCountry = false;
        this.isDefault = false;
    }

    /**
     * Getter isDefault
     * @return {boolean}
     */
	public get isDefault(): boolean {
		return this._isDefault;
	}

    /**
     * Setter isDefault
     * @param {boolean} value
     */
	public set isDefault(value: boolean) {
		this._isDefault = value;
	}

    /**
     * Getter countryExtension
     * @return {string}
     */
	public get countryExtension(): string {
		return this._countryExtension;
	}

    /**
     * Setter countryExtension
     * @param {string} value
     */
	public set countryExtension(value: string) {
		this._countryExtension = value;
	}


    /**
     * Getter originalName
     * @return {string}
     */
	public get originalName(): string {
		return this._originalName;
	}

    /**
     * Setter originalName
     * @param {string} value
     */
	public set originalName(value: string) {
		this._originalName = value;
	}


    /**
     * Getter isMarkAsPurchaseCountry
     * @return {boolean}
     */
	public get isMarkAsPurchaseCountry(): boolean {
		return this._isMarkAsPurchaseCountry;
	}

    /**
     * Setter isMarkAsPurchaseCountry
     * @param {boolean} value
     */
	public set isMarkAsPurchaseCountry(value: boolean) {
		this._isMarkAsPurchaseCountry = value;
	}


    /**
     * Getter lastModifiedDate
     * @return {string}
     */
	public get lastModifiedDate(): string {
		return this._lastModifiedDate;
	}

    /**
     * Setter lastModifiedDate
     * @param {string} value
     */
	public set lastModifiedDate(value: string) {
		this._lastModifiedDate = value;
	}


    /**
     * Getter markAsPurchaseCountry
     * @return {boolean}
     */
	public get markAsPurchaseCountry(): boolean {
		return this._markAsPurchaseCountry;
	}

    /**
     * Setter markAsPurchaseCountry
     * @param {boolean} value
     */
	public set markAsPurchaseCountry(value: boolean) {
		this._markAsPurchaseCountry = value;
	}


    /**
     * Getter idOfState
     * @return {number}
     */
	public get idOfState(): number {
		return this._idOfState;
	}

    /**
     * Setter idOfState
     * @param {number} value
     */
	public set idOfState(value: number) {
		this._idOfState = value;
	}


    /**
     * Getter state
     * @return {any}
     */
	public get state(): any {
		return this._state;
	}

    /**
     * Setter state
     * @param {any} value
     */
	public set state(value: any) {
		this._state = value;
	}


    /**
     * Getter markAsPurchaseCity
     * @return {boolean}
     */
	public get markAsPurchaseCity(): boolean {
		return this._markAsPurchaseCity;
	}

    /**
     * Getter shippingDays
     * @return {number}
     */
	public get shippingDays(): number {
		return this._shippingDays;
	}

    /**
     * Setter markAsPurchaseCity
     * @param {boolean} value
     */
	public set markAsPurchaseCity(value: boolean) {
		this._markAsPurchaseCity = value;
	}

    /**
     * Setter shippingDays
     * @param {number} value
     */
	public set shippingDays(value: number) {
		this._shippingDays = value;
	}


    /**
     * Getter country
     * @return {any}
     */
	public get country(): any {
		return this._country;
	}

    /**
     * Setter country
     * @param {any} value
     */
	public set country(value: any) {
		this._country = value;
	}


    /**
     * Getter idOfCountry
     * @return {number}
     */
	public get idOfCountry(): number {
		return this._idOfCountry;
	}

    /**
     * Setter idOfCountry
     * @param {number} value
     */
	public set idOfCountry(value: number) {
		this._idOfCountry = value;
	}


    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Getter createdDate
     * @return {string}
     */
	public get createdDate(): string {
		return this._createdDate;
	}

    /**
     * Getter name
     * @return {string}
     */
	public get name(): string {
		return this._name;
	}

    /**
     * Getter code
     * @return {string}
     */
	public get code(): string {
		return this._code;
	}

    /**
     * Getter flagUrl
     * @return {string}
     */
	public get flagUrl(): string {
		return this._flagUrl;
	}

    /**
     * Getter isActive
     * @return {boolean}
     */
	public get isActive(): boolean {
		return this._isActive;
	}

    /**
     * Getter isSelected
     * @return {boolean}
     */
	public get isSelected(): boolean {
		return this._isSelected;
	}

    /**
     * Getter markAsPurchaseState
     * @return {boolean}
     */
	public get markAsPurchaseState(): boolean {
		return this._markAsPurchaseState;
	}

    /**
     * Getter countryName
     * @return {string}
     */
	public get countryName(): string {
		return this._countryName;
	}

    /**
     * Getter stateName
     * @return {string}
     */
	public get stateName(): string {
		return this._stateName;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Setter createdDate
     * @param {string} value
     */
	public set createdDate(value: string) {
		this._createdDate = value;
	}

    /**
     * Setter name
     * @param {string} value
     */
	public set name(value: string) {
		this._name = value;
	}

    /**
     * Setter code
     * @param {string} value
     */
	public set code(value: string) {
		this._code = value;
	}

    /**
     * Setter flagUrl
     * @param {string} value
     */
	public set flagUrl(value: string) {
		this._flagUrl = value;
	}

    /**
     * Setter isActive
     * @param {boolean} value
     */
	public set isActive(value: boolean) {
		this._isActive = value;
	}

    /**
     * Setter isSelected
     * @param {boolean} value
     */
	public set isSelected(value: boolean) {
		this._isSelected = value;
	}

    /**
     * Setter markAsPurchaseState
     * @param {boolean} value
     */
	public set markAsPurchaseState(value: boolean) {
		this._markAsPurchaseState = value;
	}

    /**
     * Setter countryName
     * @param {string} value
     */
	public set countryName(value: string) {
		this._countryName = value;
	}

    /**
     * Setter stateName
     * @param {string} value
     */
	public set stateName(value: string) {
		this._stateName = value;
	}


}