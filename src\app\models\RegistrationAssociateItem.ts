import { deserializeAs, serializeAs } from "cerialize";

export class RegistrationAI {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('cbm')
    @deserializeAs('cbm')
    private _cbm: number;

    @deserializeAs('isDeleteFlag')
    private _isDeleteFlag: boolean;

    @deserializeAs('itemIsActive')
    private _itemIsActive: boolean;

    @serializeAs('supplierSku')
    @deserializeAs('supplierSku')
    private _supplierSku: string;

    @serializeAs('cartonLength')
    @deserializeAs('cartonLength')
    private _cartonLength: number;

    @serializeAs('cartonWidth')
    @deserializeAs('cartonWidth')
    private _cartonWidth: number;

    @serializeAs('cartonHeight')
    @deserializeAs('cartonHeight')
    private _cartonHeight: number;

    @serializeAs('cartonWeight')
    @deserializeAs('cartonWeight')
    private _cartonWeight: number;

    @serializeAs('cartonQuantity')
    @deserializeAs('cartonQuantity')
    private _cartonQuantity: number;

    @serializeAs('pricePerItem')
    @deserializeAs('pricePerItem')
    private _pricePerItem: number;

    @serializeAs('pricePerCarton')
    @deserializeAs('pricePerCarton')
    private _pricePerCarton: number;

    @serializeAs('tag')
    @deserializeAs('tag')
    private _tag: string;

    @serializeAs('englishComment')
    @deserializeAs('englishComment')
    private _englishComment: string;

    @serializeAs('chinaComment')
    @deserializeAs('chinaComment')
    private _chinaComment: string;

    @serializeAs('measurementCode')
    @deserializeAs('measurementCode')
    private _measurementCode: string;

    @serializeAs('itemId')
    @deserializeAs('itemId')
    private _itemId: number;

    @serializeAs('hsnCodeId')
    @deserializeAs('hsnCodeId')
    private _hsnCodeId: number;

    @serializeAs('unitId')
    @deserializeAs('unitId')
    private _unitId: number;

    @serializeAs('cartonWeightDim')
    @deserializeAs('cartonWeightDim')
    private _cartonWeightDim: number;

    @serializeAs('fileIndexes')
    @deserializeAs('fileIndexes')
    private _fileIndexes: number[];

    @serializeAs('colorIds')
    @deserializeAs('colorIds')
    private _colorIds: number[];

    // @serializeAs('poColorId')
    @deserializeAs('poColorId')
    private _poColorId: number[];

    @deserializeAs('images')
    private _images: any[];

    @deserializeAs('itemDropdown')
    private _itemDropdown: any[];

    @deserializeAs('unitDropdown')
    private _unitDropdown: any[];

    @deserializeAs('cartownWdropdown')
    private _cartownWdropdown: any[];

    @deserializeAs('colorDropdown')
    private _colorDropdown: any[];

    @deserializeAs('hsnCode')
    private _hsnCode: string;

    @deserializeAs('imageIndex')
    private _imageIndex: number;

    @deserializeAs('itemColors')
    private _itemColors: any;

    @deserializeAs('itemDocList')
    private _itemDocList: any[];

    @deserializeAs('itemSKUId')
    private _itemSKUId: any;

    @deserializeAs('itemName')
    private _itemName: any;

    @deserializeAs('isSelected')
    private _isSelected: boolean;

    @serializeAs('isActive')
    @deserializeAs('isActive')
    private _isActive: boolean;

    @deserializeAs('marketType')
    private _marketType: string;

    @deserializeAs('itemCarton')
    private _itemCarton: string;

    @deserializeAs('itemPrice')
    private _itemPrice: string;

    @deserializeAs('cartonWeightDimId')
    private _cartonWeightDimId: number;

    @deserializeAs('itemFileFormatedName')
    private _itemFileFormatedName: string;

    @deserializeAs('breachAlert')
    private _breachAlert: any;

    @deserializeAs('displayName')
    private _displayName: any;

    @deserializeAs('skuId')
    private _skuId: any;

    @deserializeAs('formattedName')
    private _formattedName: any;

    @deserializeAs('color')
    private _color: any;

    constructor() {
        this.isDeleteFlag = false;
        this.isSelected = false;
        this.itemIsActive = false;
        this.fileIndexes = [];
        this.colorIds = [];
        this.poColorId = []
        this.images = [];
        this.colorDropdown = [];
        this.itemDropdown = [];
        this.unitDropdown = [];
        this.itemDocList = [];
        this.cartownWdropdown = [];
    }


    /**
     * Getter color
     * @return {any}
     */
	public get color(): any {
		return this._color;
	}

    /**
     * Setter color
     * @param {any} value
     */
	public set color(value: any) {
		this._color = value;
	}
    


    /**
     * Getter poColorId
     * @return {number[]}
     */
	public get poColorId(): number[] {
		return this._poColorId;
	}

    /**
     * Setter poColorId
     * @param {number[]} value
     */
	public set poColorId(value: number[]) {
		this._poColorId = value;
	}


    /**
     * Getter itemIsActive
     * @return {boolean}
     */
	public get itemIsActive(): boolean {
		return this._itemIsActive;
	}

    /**
     * Setter itemIsActive
     * @param {boolean} value
     */
	public set itemIsActive(value: boolean) {
		this._itemIsActive = value;
	}

    
    /**
     * Getter isDeleteFlag
     * @return {boolean}
     */
	public get isDeleteFlag(): boolean {
		return this._isDeleteFlag;
	}

    /**
     * Setter isDeleteFlag
     * @param {boolean} value
     */
	public set isDeleteFlag(value: boolean) {
		this._isDeleteFlag = value;
	}


    /**
     * Getter displayName
     * @return {any}
     */
	public get displayName(): any {
		return this._displayName;
	}

    /**
     * Getter skuId
     * @return {any}
     */
	public get skuId(): any {
		return this._skuId;
	}

    /**
     * Getter formattedName
     * @return {any}
     */
	public get formattedName(): any {
		return this._formattedName;
	}

    /**
     * Setter displayName
     * @param {any} value
     */
	public set displayName(value: any) {
		this._displayName = value;
	}

    /**
     * Setter skuId
     * @param {any} value
     */
	public set skuId(value: any) {
		this._skuId = value;
	}

    /**
     * Setter formattedName
     * @param {any} value
     */
	public set formattedName(value: any) {
		this._formattedName = value;
	}


    /**
     * Getter breachAlert
     * @return {any}
     */
	public get breachAlert(): any {
		return this._breachAlert;
	}

    /**
     * Setter breachAlert
     * @param {any} value
     */
	public set breachAlert(value: any) {
		this._breachAlert = value;
	}


    /**
     * Getter itemFileFormatedName
     * @return {string}
     */
	public get itemFileFormatedName(): string {
		return this._itemFileFormatedName;
	}

    /**
     * Setter itemFileFormatedName
     * @param {string} value
     */
	public set itemFileFormatedName(value: string) {
		this._itemFileFormatedName = value;
	}


    /**
     * Getter cartonWeightDimId
     * @return {number}
     */
	public get cartonWeightDimId(): number {
		return this._cartonWeightDimId;
	}

    /**
     * Setter cartonWeightDimId
     * @param {number} value
     */
	public set cartonWeightDimId(value: number) {
		this._cartonWeightDimId = value;
	}


    /**
     * Getter itemCarton
     * @return {string}
     */
	public get itemCarton(): string {
		return this._itemCarton;
	}

    /**
     * Getter itemPrice
     * @return {string}
     */
	public get itemPrice(): string {
		return this._itemPrice;
	}

    /**
     * Setter itemCarton
     * @param {string} value
     */
	public set itemCarton(value: string) {
		this._itemCarton = value;
	}

    /**
     * Setter itemPrice
     * @param {string} value
     */
	public set itemPrice(value: string) {
		this._itemPrice = value;
	}


    /**
     * Getter isActive
     * @return {boolean}
     */
	public get isActive(): boolean {
		return this._isActive;
	}

    /**
     * Setter isActive
     * @param {boolean} value
     */
	public set isActive(value: boolean) {
		this._isActive = value;
	}


    /**
     * Getter marketType
     * @return {string}
     */
	public get marketType(): string {
		return this._marketType;
	}

    /**
     * Setter marketType
     * @param {string} value
     */
	public set marketType(value: string) {
		this._marketType = value;
	}


    /**
     * Getter cartownWdropdown
     * @return {any[]}
     */
	public get cartownWdropdown(): any[] {
		return this._cartownWdropdown;
	}

    /**
     * Setter cartownWdropdown
     * @param {any[]} value
     */
	public set cartownWdropdown(value: any[]) {
		this._cartownWdropdown = value;
	}


    /**
     * Getter cartonWeightDim
     * @return {number}
     */
	public get cartonWeightDim(): number {
		return this._cartonWeightDim;
	}

    /**
     * Setter cartonWeightDim
     * @param {number} value
     */
	public set cartonWeightDim(value: number) {
		this._cartonWeightDim = value;
	}


    /**
     * Getter unitDropdown
     * @return {any[]}
     */
	public get unitDropdown(): any[] {
		return this._unitDropdown;
	}

    /**
     * Setter unitDropdown
     * @param {any[]} value
     */
	public set unitDropdown(value: any[]) {
		this._unitDropdown = value;
	}


    /**
     * Getter isSelected
     * @return {boolean}
     */
	public get isSelected(): boolean {
		return this._isSelected;
	}

    /**
     * Setter isSelected
     * @param {boolean} value
     */
	public set isSelected(value: boolean) {
		this._isSelected = value;
	}


    /**
     * Getter itemSKUId
     * @return {any}
     */
	public get itemSKUId(): any {
		return this._itemSKUId;
	}

    /**
     * Getter itemName
     * @return {any}
     */
	public get itemName(): any {
		return this._itemName;
	}

    /**
     * Setter itemSKUId
     * @param {any} value
     */
	public set itemSKUId(value: any) {
		this._itemSKUId = value;
	}

    /**
     * Setter itemName
     * @param {any} value
     */
	public set itemName(value: any) {
		this._itemName = value;
	}


    /**
     * Getter itemColors
     * @return {any}
     */
	public get itemColors(): any {
		return this._itemColors;
	}

    /**
     * Getter itemDocList
     * @return {any[]}
     */
	public get itemDocList(): any[] {
		return this._itemDocList;
	}

    /**
     * Setter itemColors
     * @param {any} value
     */
	public set itemColors(value: any) {
		this._itemColors = value;
	}

    /**
     * Setter itemDocList
     * @param {any[]} value
     */
	public set itemDocList(value: any[]) {
		this._itemDocList = value;
	}


    /**
     * Getter imageIndex
     * @return {number}
     */
	public get imageIndex(): number {
		return this._imageIndex;
	}

    /**
     * Setter imageIndex
     * @param {number} value
     */
	public set imageIndex(value: number) {
		this._imageIndex = value;
	}


    /**
     * Getter colorDropdown
     * @return {any[]}
     */
	public get colorDropdown(): any[] {
		return this._colorDropdown;
	}

    /**
     * Setter colorDropdown
     * @param {any[]} value
     */
	public set colorDropdown(value: any[]) {
		this._colorDropdown = value;
	}


    /**
     * Getter hsnCode
     * @return {string}
     */
	public get hsnCode(): string {
		return this._hsnCode;
	}

    /**
     * Setter hsnCode
     * @param {string} value
     */
	public set hsnCode(value: string) {
		this._hsnCode = value;
	}


    /**
     * Getter itemDropdown
     * @return {any[]}
     */
	public get itemDropdown(): any[] {
		return this._itemDropdown;
	}

    /**
     * Setter itemDropdown
     * @param {any[]} value
     */
	public set itemDropdown(value: any[]) {
		this._itemDropdown = value;
	}


    /**
     * Getter images
     * @return {any[]}
     */
	public get images(): any[] {
		return this._images;
	}

    /**
     * Setter images
     * @param {any[]} value
     */
	public set images(value: any[]) {
		this._images = value;
	}


    /**
     * Getter cbm
     * @return {number}
     */
	public get cbm(): number {
		return this._cbm;
	}

    /**
     * Setter cbm
     * @param {number} value
     */
	public set cbm(value: number) {
		this._cbm = value;
	}


    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}


    /**
     * Getter supplierSku
     * @return {string}
     */
	public get supplierSku(): string {
		return this._supplierSku;
	}

    /**
     * Getter cartonLength
     * @return {number}
     */
	public get cartonLength(): number {
		return this._cartonLength;
	}

    /**
     * Getter cartonWidth
     * @return {number}
     */
	public get cartonWidth(): number {
		return this._cartonWidth;
	}

    /**
     * Getter cartonHeight
     * @return {number}
     */
	public get cartonHeight(): number {
		return this._cartonHeight;
	}

    /**
     * Getter cartonWeight
     * @return {number}
     */
	public get cartonWeight(): number {
		return this._cartonWeight;
	}

    /**
     * Getter cartonQuantity
     * @return {number}
     */
	public get cartonQuantity(): number {
		return this._cartonQuantity;
	}

    /**
     * Getter pricePerItem
     * @return {number}
     */
	public get pricePerItem(): number {
		return this._pricePerItem;
	}

    /**
     * Getter pricePerCarton
     * @return {number}
     */
	public get pricePerCarton(): number {
		return this._pricePerCarton;
	}

    /**
     * Getter tag
     * @return {string}
     */
	public get tag(): string {
		return this._tag;
	}

    /**
     * Getter englishComment
     * @return {string}
     */
	public get englishComment(): string {
		return this._englishComment;
	}

    /**
     * Getter chinaComment
     * @return {string}
     */
	public get chinaComment(): string {
		return this._chinaComment;
	}

    /**
     * Getter measurementCode
     * @return {string}
     */
	public get measurementCode(): string {
		return this._measurementCode;
	}

    /**
     * Getter itemId
     * @return {number}
     */
	public get itemId(): number {
		return this._itemId;
	}

    /**
     * Getter hsnCodeId
     * @return {number}
     */
	public get hsnCodeId(): number {
		return this._hsnCodeId;
	}

    /**
     * Getter unitId
     * @return {number}
     */
	public get unitId(): number {
		return this._unitId;
	}

    /**
     * Getter fileIndexes
     * @return {number[]}
     */
	public get fileIndexes(): number[] {
		return this._fileIndexes;
	}

    /**
     * Getter colorIds
     * @return {number[]}
     */
	public get colorIds(): number[] {
		return this._colorIds;
	}

    /**
     * Setter supplierSku
     * @param {string} value
     */
	public set supplierSku(value: string) {
		this._supplierSku = value;
	}

    /**
     * Setter cartonLength
     * @param {number} value
     */
	public set cartonLength(value: number) {
		this._cartonLength = value;
	}

    /**
     * Setter cartonWidth
     * @param {number} value
     */
	public set cartonWidth(value: number) {
		this._cartonWidth = value;
	}

    /**
     * Setter cartonHeight
     * @param {number} value
     */
	public set cartonHeight(value: number) {
		this._cartonHeight = value;
	}

    /**
     * Setter cartonWeight
     * @param {number} value
     */
	public set cartonWeight(value: number) {
		this._cartonWeight = value;
	}

    /**
     * Setter cartonQuantity
     * @param {number} value
     */
	public set cartonQuantity(value: number) {
		this._cartonQuantity = value;
	}

    /**
     * Setter pricePerItem
     * @param {number} value
     */
	public set pricePerItem(value: number) {
		this._pricePerItem = value;
	}

    /**
     * Setter pricePerCarton
     * @param {number} value
     */
	public set pricePerCarton(value: number) {
		this._pricePerCarton = value;
	}

    /**
     * Setter tag
     * @param {string} value
     */
	public set tag(value: string) {
		this._tag = value;
	}

    /**
     * Setter englishComment
     * @param {string} value
     */
	public set englishComment(value: string) {
		this._englishComment = value;
	}

    /**
     * Setter chinaComment
     * @param {string} value
     */
	public set chinaComment(value: string) {
		this._chinaComment = value;
	}

    /**
     * Setter measurementCode
     * @param {string} value
     */
	public set measurementCode(value: string) {
		this._measurementCode = value;
	}

    /**
     * Setter itemId
     * @param {number} value
     */
	public set itemId(value: number) {
		this._itemId = value;
	}

    /**
     * Setter hsnCodeId
     * @param {number} value
     */
	public set hsnCodeId(value: number) {
		this._hsnCodeId = value;
	}

    /**
     * Setter unitId
     * @param {number} value
     */
	public set unitId(value: number) {
		this._unitId = value;
	}

    /**
     * Setter fileIndexes
     * @param {number[]} value
     */
	public set fileIndexes(value: number[]) {
		this._fileIndexes = value;
	}

    /**
     * Setter colorIds
     * @param {number[]} value
     */
	public set colorIds(value: number[]) {
		this._colorIds = value;
	}

}
