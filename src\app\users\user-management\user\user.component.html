<div class="page-content"
    [pageAccess]="{page: utilsService.enumForPage.USER, action: utilsService.enumForPage.VIEW_USER, view: true}">
    <div class="page-title-wrapper">
        <div class="page-title-left">
            <h4>Users Master</h4>
        </div>
        <div class="page-title-right">
            <button (click)="redirectToDetails()"
                [pageAccess]="{page: utilsService.enumForPage.USER, action: utilsService.enumForPage.ADD_USER}"
                [routerLink]="'/users/user-management/users/new-user'" class="btn btn-sm btn-primary btn-icon-text">
                <i class="th th-outline-add-circle"></i>Add New
            </button>
            <button ngbTooltip="Refresh" placement="left" container="body" triggers="hover" (click)="getAllUsers()"
                class="btn btn-sm btn-icon btn-outline-white">
                <i class="th th-outline-refresh-2"></i>
            </button>
        </div>
    </div>

    <div class="content-area">
        <div class="page-filters">
            <div class="page-filters-left">
                <div class="form-group form-group-sm filter-search">
                    <div class="form-group-icon-start">
                        <i class="th th-outline-search-normal-1 icon-broder "></i>
                        <input (input)="onSearch($event)" [(ngModel)]="paginationRequest.name" type="search"
                            class="form-control" placeholder="Search by name">
                    </div>
                </div>
                <div class="form-group theme-ngselect form-group-sm">
                    <ng-select (change)="onChangeActive()" class="" placeholder="Branch" [multiple]="false"
                        [clearable]="true" [items]="dropdown?.branches" bindLabel="branchName" bindValue="id"
                        [(ngModel)]="paginationRequest.idOfBranch">
                    </ng-select>
                </div>
                <div class="form-group theme-ngselect form-group-sm">
                    <ng-select (change)="onChangeActive()" class="" placeholder="Warehouse" [multiple]="false"
                        [clearable]="true" [items]="dropdown?.wareHouse" bindLabel="warehouseName" bindValue="id"
                        [(ngModel)]="paginationRequest.idOfWareHouse">
                    </ng-select>
                </div>
                <div class="form-group theme-ngselect form-group-sm">
                    <ng-select (change)="onChangeActive()" class="" placeholder="Department" [multiple]="false"
                        [clearable]="true" [items]="dropdown?.departments" bindLabel="name" bindValue="id"
                        [(ngModel)]="paginationRequest.idOfDepartment">
                    </ng-select>
                </div>
                <div class="form-group theme-ngselect form-group-sm">
                    <ng-select (change)="onChangeActive()" class="" placeholder="Role" [multiple]="false"
                        [clearable]="true" [items]="dropdown?.roles" bindLabel="name" bindValue="id"
                        [(ngModel)]="paginationRequest.idOfRole">
                    </ng-select>
                </div>
                <div class="form-group theme-ngselect form-group-sm">
                    <ng-select (change)="onChangeActive()" class="" placeholder="Status" [multiple]="false"
                        [clearable]="true" [items]="activeInactiveStatus" bindLabel="label" bindValue="value"
                        [(ngModel)]="paginationRequest.isActive" [hideSelected]="false">
                    </ng-select>
                </div>
                <button (click)="onClearFilters()" class="btn btn-link btn-sm">Clear</button>
            </div>
            <div class="page-filters-right">
                <div class="form-group theme-ngselect form-group-sm form-group-export">
                    <div class="dropdown export-dropdown">
                        <button [disabled]="utilsService.isEmptyObjectOrNullUndefined(usersList)" type="button"
                            class="btn btn-sm btn-outline-white dropdown-toggle" data-bs-toggle="dropdown"
                            aria-expanded="false">
                            Export
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" (click)="exportReport()">Excel</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="table-responsive">
            <table class="table-theme table-hover table table-bordered ">
                <thead class="border-less">
                    <tr>
                        <th *ngFor="let th of usersTH; index as j" [class]="th.class"
                            [ngClass]="{'sorting-asc': paginationRequest.sortColumn==th.keyName && paginationRequest.sortOrder === enumForSortOrder.A, 
                                        'sorting-desc': paginationRequest.sortColumn==th.keyName && paginationRequest.sortOrder === enumForSortOrder.D }"
                            (click)="!utilsService.isNullUndefinedOrBlank(th.keyName) ? onSortTH(th.keyName) : $event.preventDefault()">
                            <div (click)="$event.stopPropagation()" *ngIf="j === 0"
                                class="checkbox checkbox-primary checkbox-small">
                                <input [disabled]="utilsService.isEmptyObjectOrNullUndefined(usersList)"
                                    (change)="selectAll()" [(ngModel)]="paginationRequest.flagForSelectAll"
                                    type="checkbox" id="tbl-checkbox" class="material-inputs filled-in" />
                                <label for="tbl-checkbox"></label>
                            </div>
                            {{th.displayName}}
                        </th>
                        <!-- <th class="d-flex align-items-center gap-2">
                            <div class="checkbox checkbox-primary checkbox-small">
                                <input type="checkbox" id="tbl-checkbox" class="material-inputs filled-in" />
                                <label for="tbl-checkbox"></label>
                            </div>
                            Name
                        </th>
                        <th>Username</th>
                        <th>Phonenumber</th>
                        <th>Branch</th>
                        <th>Warehouse </th>
                        <th>Department </th>
                        <th>Role </th>
                        <th>Availability </th> -->
                        <th class="tbl-switch"
                            *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.EDIT_USER])">Status</th>
                        <th class="text-center"
                            *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.EDIT_USER, this.utilsService.enumForPage.DELETE_USER])">
                            Action</th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let item of usersList; index as i; trackBy: trackBy">
                        <td class="tbl-user">
                            <div class="tbl-user-checkbox-srno">
                                <div class="checkbox checkbox-primary checkbox-small">
                                    <input (change)="selectUnselect(item.id, i, item.isSelected)"
                                        [(ngModel)]="item.isSelected" type="checkbox" id="tbl-checkbox2-{{i}}"
                                        class="material-inputs filled-in">
                                    <label for="tbl-checkbox2-{{i}}"></label>
                                </div>
                                <div class="tbl-user-wrapper">
                                    <div class="tbl-user-image"><img
                                            [src]="item.profileUrl ? (utilsService.imgPath + item.profileUrl) : 'assets/images/avatar-default.svg'"
                                            alt="valamji">
                                    </div>
                                    <div class="tbl-user-text">
                                        <p>{{item.name}}</p>
                                        <span>{{item.email}}</span>
                                    </div>
                                </div>
                            </div>
                        </td>
                        <td>{{item.userName ? item.userName : '-'}}</td>
                        <td>{{item.contactExtension?.countryExtension}} {{item.mobileNo}}</td>
                        <!-- <td class="text-warning">Repeat</td> -->
                        <td>
                            <span class="w-100 d-block" *ngFor="let v of item.branchNames">
                                {{!utilsService.isEmptyObjectOrNullUndefined(v) ? v : ''}}
                            </span>
                        </td>
                        <td>
                            <span class="w-100 d-block" *ngFor="let v of item.wareHouseName">
                                {{!utilsService.isEmptyObjectOrNullUndefined(v) ? v : ''}}
                            </span>
                        </td>
                        <td>{{item.departmentName}}</td>
                        <td>{{item.roleName}}</td>
                        <td [ngClass]="{'text-success': !item.isAbsent, 'text-warning': item.isAbsent}">{{item.isAbsent
                            ? 'No' : 'Yes'}}</td>
                        <!-- <td class="text-warningsucces">Present</td> -->
                        <td class="tbl-switch"
                            *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.EDIT_USER])">
                            <div class="switch-box">
                                <label class="switch" htmlFor="switch-{{i}}">
                                    <input [disabled]="(utilsService.userId === item.id) || item.isDefault" type="checkbox"
                                        id='switch-{{i}}' [(ngModel)]="item.isActive"
                                        (change)="onChangeStatus(item, item.isActive, i)" />
                                    <div class="slider round"></div>
                                </label>
                            </div>
                        </td>
                        <td class="tbl-action"
                            *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.EDIT_USER, this.utilsService.enumForPage.DELETE_USER])">
                            <div class="tbl-action-group">
                                <button ngbTooltip="Edit" placement="bottom" container="body" triggers="hover"
                                    [pageAccess]="{page: this.utilsService.enumForPage.USER, action: this.utilsService.enumForPage.EDIT_USER}"
                                    (click)="utilsService.redirectTo('/users/user-management/users/edit-user/' + item.id); redirectToDetails()"
                                    class="btn btn-xs btn-light-primary btn-icon">
                                    <i class="th th-outline-edit"></i>
                                </button>
                                <button [ngbTooltip]="!item.isAbsent ? 'Mark Absent' : 'Mark Present'" placement="left"
                                    container="body" triggers="hover"
                                    [ngClass]="{'btn-light-success': !item.isAbsent, 'btn-light-danger': item.isAbsent}"
                                    [pageAccess]="{page: this.utilsService.enumForPage.USER, action: this.utilsService.enumForPage.EDIT_USER}"
                                    (click)="openMarkAsAbsentModal(item, true, i)" class="btn btn-xs btn-icon">
                                    <i class="th th-outline-user-remove"></i>
                                </button>
                                <button [pageAccess]="{page: this.utilsService.enumForPage.USER, action: this.utilsService.enumForPage.DELETE_USER}"
                                    *ngIf="(utilsService.userId !== item.id) && !item.isDefault" (click)="openDeleteUser(item)" ngbTooltip="Delete"
                                    placement="left" container="body" triggers="hover" class="btn btn-xs btn-light-danger btn-icon">
                                    <i class="th th-outline-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <tr *ngIf="utilsService.isEmptyObjectOrNullUndefined(usersList)">
                        <td colspan="20" class="text-center">
                            <span
                                class="truncate">{{utilsService.serverVariableService.STRING_WHEN_NO_RECORDS_FOUND}}</span>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div class="paginationbox pagination-fixed">
            <app-pagination (pagesizeData)="addPageSizeData($event)" (pageNumber)="pageNumber($event)"
                [page]="paginationRequest.pageNo" [pageSize]="paginationRequest.pageSize"
                [totalData]="paginationRequest.totalData"></app-pagination>
        </div>
    </div>
</div>

<!-- Modal -->
<div class="modal modal-theme modal-confirmation fade" id="userActiveModal" tabindex="-1"
    [ngClass]="{'modal-warning': !usersObj.isAbsent, 'modal-approve': usersObj.isAbsent}"
    aria-labelledby="userActiveModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
                        class='th th-close'></i></button>
            </div>
            <div class="modal-body">

                <div class="modal-confirmation-container">
                    <div class="modal-confirmation-icon">
                        <i class=""
                            [ngClass]="{'th th-bold-user-add': usersObj.isAbsent, 'th th-bold-user-remove': !usersObj.isAbsent}"></i>
                    </div>
                    <div class="modal-confirmation-content">
                        <h5>Are You Sure?</h5>
                        <p>You want to mark <b class="text-black">“{{usersObj.name}}”</b> as <br> <b
                                class="text-black">“{{usersObj.isAbsent ? 'Present' : 'Absent'}}”</b></p>
                    </div>
                </div>
                <div class="modal-button-group modal-full-width-btn">
                    <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
                    <button (click)="markAsAbsent()" type="button" class="btn btn-primary btn-icon-text"> <i
                            class="th th-outline-tick-circle"></i> Yes</button>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Modal -->


<!-- ---------------------------- Delete Modal ----------------------------- -->
<div class="modal modal-theme modal-confirmation modal-reject fade" id="userDeleteModal" tabindex="-1"
    aria-labelledby="userDeleteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
                        class='th th-close'></i></button>
            </div>
            <div class="modal-body">

                <div class="modal-confirmation-container">
                    <div class="modal-confirmation-icon">
                        <i class="th th-bold-trash"></i>
                    </div>
                    <div class="modal-confirmation-content">
                        <h5>Are You Sure?</h5>
                        <p>You want to Delete User <b>{{usersObj.name}}</b> </p>
                    </div>
                </div>
                <div class="modal-button-group modal-full-width-btn">
                    <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
                    <button (click)="deleteUser()" type="button" class="btn btn-primary btn-icon-text"> <i
                            class="th th-outline-tick-circle"></i> Delete</button>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- ---------------------------- Delete Modal ----------------------------- -->