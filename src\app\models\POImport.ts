import { deserializeAs, serializeAs } from 'cerialize';
import { POImportItem } from './POImportItem';

export class POImport {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('poCreateId')
    @deserializeAs('poCreateId')
    private _poCreateId: number;


    @serializeAs('customerId')
    @deserializeAs('customerId')
    private _customerId: number;

    @serializeAs('supplierId')
    @deserializeAs('supplierId')
    private _supplierId: number;

    @serializeAs('mobileNo')
    @deserializeAs('mobileNo')
    private _mobileNo: string;

    @serializeAs('orderType')
    @deserializeAs('orderType')
    private _orderType: string;

    // @serializeAs('mobileExtension')
    @deserializeAs('mobileExtension')
    private _mobileExtension: number;

    @serializeAs('deliveryWarehouseId')
    @deserializeAs('deliveryWarehouseId')
    private _deliveryWarehouseId: number;

    @serializeAs('bankGroupId')
    @deserializeAs('bankGroupId')
    private _bankGroupId: number;

    @serializeAs('conversationRateCurrencyId')
    @deserializeAs('conversationRateCurrencyId')
    private _conversationRateCurrencyId: number;

    @serializeAs('conversationRate')
    @deserializeAs('conversationRate')
    private _conversationRate: number;

    @deserializeAs('convRate')
    private _convRate: number;

    @serializeAs('poImportDate')
    @deserializeAs('poImportDate')
    private _poImportDate: any;

    @deserializeAs('temp_date')
    private _temp_date: any;

    @serializeAs('poLimit')
    @deserializeAs('poLimit')
    private _poLimit: string;

    @serializeAs('purchaseOrder')
    @deserializeAs('purchaseOrder')
    private _purchaseOrder: string;

    @serializeAs('advancePayByCustomerCurrencyId')
    @deserializeAs('advancePayByCustomerCurrencyId')
    private _advancePayByCustomerCurrencyId: number;

    @serializeAs('advancePayByCustomer')
    @deserializeAs('advancePayByCustomer')
    private _advancePayByCustomer: number;

    @serializeAs('paymentTermsId')
    @deserializeAs('paymentTermsId')
    private _paymentTermsId: number;

    @serializeAs('shippingTypes')
    @deserializeAs('shippingTypes')
    private _shippingTypes: string;

    @serializeAs('cbmPriceCurrencyId')
    @deserializeAs('cbmPriceCurrencyId')
    private _cbmPriceCurrencyId: number;

    @serializeAs('poItemReq')
    @deserializeAs('poItemReq')
    private _poItemReq: POImportItem[];

    @serializeAs('cbmPrice')
    @deserializeAs('cbmPrice')
    private _cbmPrice: any;

    @serializeAs('tags')
    @deserializeAs('tags')
    private _tags: any;

    @deserializeAs('temp_tags')
    private _temp_tags: any;

    @serializeAs('notes')
    @deserializeAs('notes')
    private _notes: any;

    @serializeAs('status')
    @deserializeAs('status')
    private _status: any;

    @deserializeAs('poImportDocList')
    private _poImportDocList: any[];

    @serializeAs('deletedDocsID')
    @deserializeAs('deletedDocsID')
    private _deletedDocsID: any[];

    @serializeAs('docId')
    @deserializeAs('docId')
    private _docId: any[];

    @serializeAs('poCommentsReq')
    @deserializeAs('poCommentsReq')
    private _poCommentsReq: any[];

    @serializeAs('mobileExtensionId')
    @deserializeAs('mobileExtensionId')
    private _mobileExtensionId: number;

    @serializeAs('deletePoItem')
    @deserializeAs('deletePoItem')
    private _deletePoItem: number[];

    @serializeAs('linkPoImportItems')
    @deserializeAs('linkPoImportItems')
    private _linkPoImportItems: any;

    @serializeAs('isDraft')
    @deserializeAs('isDraft')
    private _isDraft: boolean;

    @serializeAs('draftPoItem')
    @deserializeAs('draftPoItem')
    private _draftPoItem: number[];

    @deserializeAs('subTotalRupees')
    private _subTotalRupees: number;

    @deserializeAs('subTotalRmb')
    private _subTotalRmb: number;

    @deserializeAs('gstAmountRupees')
    private _gstAmountRupees: number;

    @deserializeAs('gstAmountRmb')
    private _gstAmountRmb: number;

    @deserializeAs('grandTotalRupees')
    private _grandTotalRupees: number;

    @deserializeAs('grandTotalRmb')
    private _grandTotalRmb: number;

    constructor() {
        this.poImportDocList = [];
        this.deletedDocsID = [];
        this.poItemReq = [];
        this.poCommentsReq = [];
        this.deletePoItem = [];
        this.draftPoItem = []
        this.linkPoImportItems = null;
        this.isDraft = false;
        this.docId  = [];
    }

    /**
     * Getter docId
     * @return {any[]}
     */
	public get docId(): any[] {
		return this._docId;
	}

    /**
     * Setter docId
     * @param {any[]} value
     */
	public set docId(value: any[]) {
		this._docId = value;
	}


    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Getter poCreateId
     * @return {number}
     */
	public get poCreateId(): number {
		return this._poCreateId;
	}

    /**
     * Getter customerId
     * @return {number}
     */
	public get customerId(): number {
		return this._customerId;
	}

    /**
     * Getter supplierId
     * @return {number}
     */
	public get supplierId(): number {
		return this._supplierId;
	}

    /**
     * Getter mobileNo
     * @return {string}
     */
	public get mobileNo(): string {
		return this._mobileNo;
	}

    /**
     * Getter orderType
     * @return {string}
     */
	public get orderType(): string {
		return this._orderType;
	}

    /**
     * Getter mobileExtension
     * @return {number}
     */
	public get mobileExtension(): number {
		return this._mobileExtension;
	}

    /**
     * Getter deliveryWarehouseId
     * @return {number}
     */
	public get deliveryWarehouseId(): number {
		return this._deliveryWarehouseId;
	}

    /**
     * Getter bankGroupId
     * @return {number}
     */
	public get bankGroupId(): number {
		return this._bankGroupId;
	}

    /**
     * Getter conversationRateCurrencyId
     * @return {number}
     */
	public get conversationRateCurrencyId(): number {
		return this._conversationRateCurrencyId;
	}

    /**
     * Getter conversationRate
     * @return {number}
     */
	public get conversationRate(): number {
		return this._conversationRate;
	}

    /**
     * Getter convRate
     * @return {number}
     */
	public get convRate(): number {
		return this._convRate;
	}

    /**
     * Getter poImportDate
     * @return {any}
     */
	public get poImportDate(): any {
		return this._poImportDate;
	}

    /**
     * Getter temp_date
     * @return {any}
     */
	public get temp_date(): any {
		return this._temp_date;
	}

    /**
     * Getter poLimit
     * @return {string}
     */
	public get poLimit(): string {
		return this._poLimit;
	}

    /**
     * Getter purchaseOrder
     * @return {string}
     */
	public get purchaseOrder(): string {
		return this._purchaseOrder;
	}

    /**
     * Getter advancePayByCustomerCurrencyId
     * @return {number}
     */
	public get advancePayByCustomerCurrencyId(): number {
		return this._advancePayByCustomerCurrencyId;
	}

    /**
     * Getter advancePayByCustomer
     * @return {number}
     */
	public get advancePayByCustomer(): number {
		return this._advancePayByCustomer;
	}

    /**
     * Getter paymentTermsId
     * @return {number}
     */
	public get paymentTermsId(): number {
		return this._paymentTermsId;
	}

    /**
     * Getter shippingTypes
     * @return {string}
     */
	public get shippingTypes(): string {
		return this._shippingTypes;
	}

    /**
     * Getter cbmPriceCurrencyId
     * @return {number}
     */
	public get cbmPriceCurrencyId(): number {
		return this._cbmPriceCurrencyId;
	}

    /**
     * Getter poItemReq
     * @return {POImportItem[]}
     */
	public get poItemReq(): POImportItem[] {
		return this._poItemReq;
	}

    /**
     * Getter cbmPrice
     * @return {any}
     */
	public get cbmPrice(): any {
		return this._cbmPrice;
	}

    /**
     * Getter tags
     * @return {any}
     */
	public get tags(): any {
		return this._tags;
	}

    /**
     * Getter temp_tags
     * @return {any}
     */
	public get temp_tags(): any {
		return this._temp_tags;
	}

    /**
     * Getter notes
     * @return {any}
     */
	public get notes(): any {
		return this._notes;
	}

    /**
     * Getter status
     * @return {any}
     */
	public get status(): any {
		return this._status;
	}

    /**
     * Getter poImportDocList
     * @return {any[]}
     */
	public get poImportDocList(): any[] {
		return this._poImportDocList;
	}

    /**
     * Getter deletedDocsID
     * @return {any[]}
     */
	public get deletedDocsID(): any[] {
		return this._deletedDocsID;
	}

    /**
     * Getter poCommentsReq
     * @return {any[]}
     */
	public get poCommentsReq(): any[] {
		return this._poCommentsReq;
	}

    /**
     * Getter mobileExtensionId
     * @return {number}
     */
	public get mobileExtensionId(): number {
		return this._mobileExtensionId;
	}

    /**
     * Getter deletePoItem
     * @return {number[]}
     */
	public get deletePoItem(): number[] {
		return this._deletePoItem;
	}

    /**
     * Getter linkPoImportItems
     * @return {any}
     */
	public get linkPoImportItems(): any {
		return this._linkPoImportItems;
	}

    /**
     * Getter isDraft
     * @return {boolean}
     */
	public get isDraft(): boolean {
		return this._isDraft;
	}

    /**
     * Getter draftPoItem
     * @return {number[]}
     */
	public get draftPoItem(): number[] {
		return this._draftPoItem;
	}

    /**
     * Getter subTotalRupees
     * @return {number}
     */
	public get subTotalRupees(): number {
		return this._subTotalRupees;
	}

    /**
     * Getter subTotalRmb
     * @return {number}
     */
	public get subTotalRmb(): number {
		return this._subTotalRmb;
	}

    /**
     * Getter gstAmountRupees
     * @return {number}
     */
	public get gstAmountRupees(): number {
		return this._gstAmountRupees;
	}

    /**
     * Getter gstAmountRmb
     * @return {number}
     */
	public get gstAmountRmb(): number {
		return this._gstAmountRmb;
	}

    /**
     * Getter grandTotalRupees
     * @return {number}
     */
	public get grandTotalRupees(): number {
		return this._grandTotalRupees;
	}

    /**
     * Getter grandTotalRmb
     * @return {number}
     */
	public get grandTotalRmb(): number {
		return this._grandTotalRmb;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Setter poCreateId
     * @param {number} value
     */
	public set poCreateId(value: number) {
		this._poCreateId = value;
	}

    /**
     * Setter customerId
     * @param {number} value
     */
	public set customerId(value: number) {
		this._customerId = value;
	}

    /**
     * Setter supplierId
     * @param {number} value
     */
	public set supplierId(value: number) {
		this._supplierId = value;
	}

    /**
     * Setter mobileNo
     * @param {string} value
     */
	public set mobileNo(value: string) {
		this._mobileNo = value;
	}

    /**
     * Setter orderType
     * @param {string} value
     */
	public set orderType(value: string) {
		this._orderType = value;
	}

    /**
     * Setter mobileExtension
     * @param {number} value
     */
	public set mobileExtension(value: number) {
		this._mobileExtension = value;
	}

    /**
     * Setter deliveryWarehouseId
     * @param {number} value
     */
	public set deliveryWarehouseId(value: number) {
		this._deliveryWarehouseId = value;
	}

    /**
     * Setter bankGroupId
     * @param {number} value
     */
	public set bankGroupId(value: number) {
		this._bankGroupId = value;
	}

    /**
     * Setter conversationRateCurrencyId
     * @param {number} value
     */
	public set conversationRateCurrencyId(value: number) {
		this._conversationRateCurrencyId = value;
	}

    /**
     * Setter conversationRate
     * @param {number} value
     */
	public set conversationRate(value: number) {
		this._conversationRate = value;
	}

    /**
     * Setter convRate
     * @param {number} value
     */
	public set convRate(value: number) {
		this._convRate = value;
	}

    /**
     * Setter poImportDate
     * @param {any} value
     */
	public set poImportDate(value: any) {
		this._poImportDate = value;
	}

    /**
     * Setter temp_date
     * @param {any} value
     */
	public set temp_date(value: any) {
		this._temp_date = value;
	}

    /**
     * Setter poLimit
     * @param {string} value
     */
	public set poLimit(value: string) {
		this._poLimit = value;
	}

    /**
     * Setter purchaseOrder
     * @param {string} value
     */
	public set purchaseOrder(value: string) {
		this._purchaseOrder = value;
	}

    /**
     * Setter advancePayByCustomerCurrencyId
     * @param {number} value
     */
	public set advancePayByCustomerCurrencyId(value: number) {
		this._advancePayByCustomerCurrencyId = value;
	}

    /**
     * Setter advancePayByCustomer
     * @param {number} value
     */
	public set advancePayByCustomer(value: number) {
		this._advancePayByCustomer = value;
	}

    /**
     * Setter paymentTermsId
     * @param {number} value
     */
	public set paymentTermsId(value: number) {
		this._paymentTermsId = value;
	}

    /**
     * Setter shippingTypes
     * @param {string} value
     */
	public set shippingTypes(value: string) {
		this._shippingTypes = value;
	}

    /**
     * Setter cbmPriceCurrencyId
     * @param {number} value
     */
	public set cbmPriceCurrencyId(value: number) {
		this._cbmPriceCurrencyId = value;
	}

    /**
     * Setter poItemReq
     * @param {POImportItem[]} value
     */
	public set poItemReq(value: POImportItem[]) {
		this._poItemReq = value;
	}

    /**
     * Setter cbmPrice
     * @param {any} value
     */
	public set cbmPrice(value: any) {
		this._cbmPrice = value;
	}

    /**
     * Setter tags
     * @param {any} value
     */
	public set tags(value: any) {
		this._tags = value;
	}

    /**
     * Setter temp_tags
     * @param {any} value
     */
	public set temp_tags(value: any) {
		this._temp_tags = value;
	}

    /**
     * Setter notes
     * @param {any} value
     */
	public set notes(value: any) {
		this._notes = value;
	}

    /**
     * Setter status
     * @param {any} value
     */
	public set status(value: any) {
		this._status = value;
	}

    /**
     * Setter poImportDocList
     * @param {any[]} value
     */
	public set poImportDocList(value: any[]) {
		this._poImportDocList = value;
	}

    /**
     * Setter deletedDocsID
     * @param {any[]} value
     */
	public set deletedDocsID(value: any[]) {
		this._deletedDocsID = value;
	}

    /**
     * Setter poCommentsReq
     * @param {any[]} value
     */
	public set poCommentsReq(value: any[]) {
		this._poCommentsReq = value;
	}

    /**
     * Setter mobileExtensionId
     * @param {number} value
     */
	public set mobileExtensionId(value: number) {
		this._mobileExtensionId = value;
	}

    /**
     * Setter deletePoItem
     * @param {number[]} value
     */
	public set deletePoItem(value: number[]) {
		this._deletePoItem = value;
	}

    /**
     * Setter linkPoImportItems
     * @param {any} value
     */
	public set linkPoImportItems(value: any) {
		this._linkPoImportItems = value;
	}

    /**
     * Setter isDraft
     * @param {boolean} value
     */
	public set isDraft(value: boolean) {
		this._isDraft = value;
	}

    /**
     * Setter draftPoItem
     * @param {number[]} value
     */
	public set draftPoItem(value: number[]) {
		this._draftPoItem = value;
	}

    /**
     * Setter subTotalRupees
     * @param {number} value
     */
	public set subTotalRupees(value: number) {
		this._subTotalRupees = value;
	}

    /**
     * Setter subTotalRmb
     * @param {number} value
     */
	public set subTotalRmb(value: number) {
		this._subTotalRmb = value;
	}

    /**
     * Setter gstAmountRupees
     * @param {number} value
     */
	public set gstAmountRupees(value: number) {
		this._gstAmountRupees = value;
	}

    /**
     * Setter gstAmountRmb
     * @param {number} value
     */
	public set gstAmountRmb(value: number) {
		this._gstAmountRmb = value;
	}

    /**
     * Setter grandTotalRupees
     * @param {number} value
     */
	public set grandTotalRupees(value: number) {
		this._grandTotalRupees = value;
	}

    /**
     * Setter grandTotalRmb
     * @param {number} value
     */
	public set grandTotalRmb(value: number) {
		this._grandTotalRmb = value;
	}

}