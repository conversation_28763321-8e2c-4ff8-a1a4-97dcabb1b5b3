<div class="page-filters">
    <div class="page-filters-left">
        <div class="form-group form-group-sm filter-search">
            <div class="form-group-icon-start">
                <i class="th th-outline-search-normal-1 icon-broder "></i>
                <input (change)="onSearch($event)" [(ngModel)]="paginationRequest.purchaseOrderSearch" type="text"
                    class="form-control" placeholder="Search by ID">
            </div>
        </div>
        <div class="form-group form-group-sm filter-search">
            <div class="form-group-icon-start">
                <i class="th th-outline-search-normal-1 icon-broder "></i>
                <input (change)="onSearchPOItemComments($event)" [(ngModel)]="paginationRequest.searchPurchaseComments"
                    type="text" class="form-control" placeholder="Search by SKU & Comment">
            </div>
        </div>
        <div class="form-group form-group-sm date-range-filter">
            <div class="form-group-icon-end">
                <i (click)="open()" class="th th-outline-calendar ngx-daterangepicker-action"></i>
                <input #pickerDirective class="form-control" type="text" ngxDaterangepickerMd readonly
                    [formControl]="paginationRequest.dateRangeControl" [showCustomRangeLabel]="true" [alwaysShowCalendars]="true"
                    [ranges]="utilsService.ranges" [linkedCalendars]="false" [showClearButton]="false"
                    [autoApply]="true" [showRangeLabelOnInput]="true" startKey="start" placeholder="PO Date"
                    endKey="end">
            </div>
        </div>
        <div class="form-group theme-ngselect form-group-sm">
            <ng-select (change)="onChangeActive()" placeholder="Supplier" [multiple]="false" [clearable]="true"
                [items]="dropdown?.supplierDropdown" bindLabel="displayName" bindValue="id"
                [(ngModel)]="paginationRequest.supplierId">
            </ng-select>
        </div>
        <div class="form-group theme-ngselect form-group-sm">
            <ng-select (change)="onChangeActive()" placeholder="Customer" [multiple]="false" [clearable]="true"
                [items]="dropdown?.customerDropdown" bindLabel="displayName" bindValue="id"
                [(ngModel)]="paginationRequest.customerId">
            </ng-select>
        </div>
        <button (click)="onClear.emit()" class="btn btn-link btn-sm">Clear</button>
        <!-- <div class="form-group  form-group-sm">
            <input type="text" class="form-control" placeholder="Price From">
        </div>
        <div class="form-group  form-group-sm">
            <input type="text" class="form-control" placeholder="Price To">
        </div> -->

        <!-- <div class="form-group theme-ngselect form-group-sm">
            <ng-select placeholder="Status" [multiple]="false" [clearable]="false" [items]="demo" bindLabel="name"
                bindValue="id" [(ngModel)]="selectedDemo1">
            </ng-select>
        </div>
        <div class="form-group theme-ngselect form-group-sm">
            <ng-select placeholder="Payment Status" [multiple]="false" [clearable]="false" [items]="demo"
                bindLabel="name" bindValue="id" [(ngModel)]="selectedDemo1">
            </ng-select>
        </div> -->

    </div>
    <div class="page-filters-right">
        <!-- <app-table-column-filter-dropdown-new [allHeaderArr]="allHeaderArr" [columnArr]="columnArr"
            (saveCol)="saveCol.emit()" (checkIfAllSelected)="checkIfAllSelected.emit()" /> -->
    </div>
</div>

<div class="card card-theme card-table-sticky3">
    <div class="card-body p-0">
        <div class="table-responsive ">
            <table class="table-theme table-hover table table-bordered tbl-collapse">
                <thead class="border-less">
                    <tr class="">
                        <th>
                            # Shop No
                        </th>
                        <th>Supplier Name</th>
                        <th>Mobile No</th>
                        <th>Total Amount (RMB)</th>
                        <th class="text-end">Action</th>
                    </tr>
                </thead>


                <tbody>
                    <ng-container *ngFor="let item of poImportList; index as i; trackBy: trackBy">
                        <tr [ngClass]="{'tbl-bg-secondary': item.isExpand}" (click)="toggleExpand(i)">
                            <td class=" tbl-user">
                                <div class="tbl-user-checkbox-srno">
                                    <span>{{(i + 1) | padNum}}.</span>
                                    <b class="text-black">
                                        {{item.supplierShortCode}}
                                    </b>
                                </div>
                            </td>
                            <td>{{item.supplierName}}</td>
                            <td>{{item.countryExtension}} {{item.phone}}</td>
                            <td>{{item.supplierAmount ? (item.supplierAmount | indianCurrency) : '-'}}</td>
                            <td class="tbl-action" (click)="$event.stopPropagation()">
                                <div class="tbl-action-group justify-content-end">
                                    <button [pageAccess]="{page: utilsService.enumForPage.PO, action: utilsService.enumForPage.MOVE_TO_RC_CHINA}"
                                        (click)="openPoToChina(item, null, false)" class="btn btn-xs btn-light-primary btn-icon"
                                        ngbTooltip="Move To Receive China" placement="left" container="body" triggers="hover">
                                        <i class="th-outline-arrow-right-1"></i>
                                    </button>
                                    <button (click)="toggleExpand(i)" class="btn btn-xs text-color btn-icon btn-link"
                                        data-bs-toggle="collapse" [ngClass]="{'collapse-arrow': item.isExpand}"
                                        role="button" aria-expanded="false" [attr.data.target]="'#table-collapse-2'+ i"
                                        [attr.aria-controls]="'table-collapse-2'+ i">
                                        <i class="th th-outline-arrow-right-3"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>

                        <tr *ngIf="item.isExpand" class="collapse" [ngClass]="{'show': item.isExpand}"
                            [id]="'table-collapse-2' + i">
                            <td colspan="30" class=" tbl-collapse-child tbl-collapse-child-responsive">

                                <table class="table-theme table-hover table table-bordered">
                                    <thead class="border-less">
                                        <tr>
                                            <th> # Purchase Order ID </th>
                                            <th>PO Date</th>
                                            <th>Cartons</th>
                                            <th>Total Amount (RMB)</th>
                                            <th>Order Note</th>
                                            <th class="text-end">Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <ng-container
                                            *ngFor="let subItem of item.poImportCreate; index as j; trackBy: trackBySubItem">
                                            <tr class="tbl-bg-gray" (click)="toggleExpandChild(i, j)">
                                                <td class=" tbl-user ">
                                                    <div class="tbl-user-checkbox-srno">
                                                        <span>{{(j + 1) | padNum}}.</span>
                                                        <b class="text-black">
                                                            {{subItem.purchaseOrder}}
                                                        </b>
                                                    </div>
                                                </td>
                                                <td class="">{{subItem.poImportDate ? (subItem.poImportDate | date:
                                                    'dd/MM/yyyy') : '-'}}</td>
                                                <td class="">{{subItem.totalCartons ? subItem.totalCartons : '-'}}</td>
                                                <td class="">{{subItem.totalAmount ? (subItem.totalAmount | indianCurrency) : '-'}}</td>
                                                <td class="tbl-description">{{subItem.notes ? subItem.notes : '-'}}</td>
                                                <td class="tbl-action " (click)="$event.stopPropagation()">
                                                    <div class="tbl-action-group justify-content-end">
                                                        <button [pageAccess]="{page: utilsService.enumForPage.PO, action: utilsService.enumForPage.MOVE_TO_RC_CHINA}"
                                                            (click)="openPoToChina(item, subItem, false)" class="btn btn-xs btn-light-primary btn-icon"
                                                            ngbTooltip="Move To Receive China" placement="left" container="body" triggers="hover">
                                                            <i class="th-outline-arrow-right-1"></i>
                                                        </button>
                                                        <div class="dropdown"
                                                            *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.EDIT_PO, this.utilsService.enumForPage.DEL_PO])">
                                                            <button class="btn btn-xs btn-light-white btn-icon" id="actionDropDown" data-bs-toggle="dropdown"
                                                                aria-expanded="false" data-bs-popper-config='{"strategy":"fixed"}' ngbTooltip="More Option"
                                                                placement="bottom" container="body" triggers="hover">
                                                                <i class="th th-outline-more"></i>
                                                            </button>
                                                            <ul class="dropdown-menu" aria-labelledby="actionDropDown">
                                                                <ng-container >
                                                                    <li [pageAccess]="{page: utilsService.enumForPage.PO, action: utilsService.enumForPage.EDIT_PO}">
                                                                        <a class="dropdown-item" (click)="redirectToDetails.emit()"
                                                                            [routerLink]="'/users/purchases/po-import/edit-po-created/' + subItem.id"><i
                                                                                class="th th-outline-edit"></i>
                                                                            Edit
                                                                        </a>
                                                                    </li>
                                                                </ng-container>
                                                
                                                                <ng-container>
                                                                    <li [pageAccess]="{page: utilsService.enumForPage.PO, action: utilsService.enumForPage.DEL_PO}">
                                                                        <hr class="dropdown-divider">
                                                                    </li>
                                                                    <li [pageAccess]="{page: utilsService.enumForPage.PO, action: utilsService.enumForPage.DEL_PO}">
                                                                        <a (click)="openDeleteModal(subItem)" class="dropdown-item text-danger"><i
                                                                                class="th th-outline-trash"></i>Delete
                                                                        </a>
                                                                    </li>
                                                                </ng-container>
                                                            </ul>
                                                
                                                        </div>
                                                        <button (click)="toggleExpandChild(i, j)" class="btn btn-xs text-color btn-icon btn-link"
                                                            data-bs-toggle="collapse" [ngClass]="{'collapse-arrow': subItem.isExpand}" role="button"
                                                            aria-expanded="false" [attr.data.target]="'#table-collapse-2'+ i + j"
                                                            [attr.aria-controls]="'table-collapse-2'+ i + j">
                                                            <i class="th th-outline-arrow-right-3"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>

                                            <tr *ngIf="subItem.isExpand" class="collapse"
                                                [ngClass]="{'show': subItem.isExpand}"
                                                [id]="'table-collapse-2' + i + j">
                                                <td colspan="30"
                                                    class="tbl-collapse-child tbl-collapse-child-responsive">

                                                    <div class="table-responsive">
                                                        <table
                                                            class="table-theme table-hover table table-bordered table-sticky">
                                                            <thead class="border-less">
                                                                <tr class="tbl-bg-light-three">
                                                                    <ng-container
                                                                        *ngFor="let th of headerObj?.optionsArray | filterByShippingType: subItem?.shippingTypes?.value; index as i">
                                                                        <th *ngIf="th.show" [class]="th.class">
                                                                            {{th.header}}
                                                                        </th>
                                                                    </ng-container>
                                                                </tr>
                                                                <tr>
                                                                    <ng-container
                                                                        *ngFor="let th of headerObj?.optionsArray | filterByShippingType: subItem?.shippingTypes?.value; index as k">
                                                                        <th *ngIf="th.show" [class]="th.class"
                                                                            [innerHTML]="th.displayName"></th>
                                                                    </ng-container>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                <tr [ngClass]="{'tbl-bg-danger': child.purchaseRationFlag}"
                                                                    *ngFor="let child of subItem.poImportItemList; index as l; trackBy: trackByChild">
                                                                    <ng-container
                                                                        *ngFor="let column of headerObj.columnArr | filterByShippingType: subItem?.shippingTypes?.value;">
                                                                        <td class="tbl-user" *ngIf="column.show">
                                                                            <ng-container>
                                                                        
                                                                                <ng-container *ngIf="column.key =='item'">
                                                                                    <div class="tbl-user-checkbox-srno">
                                                                                        <span>{{(l + 1) | padNum}}.</span>
                                                                                        <div class="tbl-user-wrapper">
                                                                                            <div class="tbl-user-image" *ngIf="child?.item">
                                                                                                <img *ngIf="child.item?.formattedName" loading="lazy"
                                                                                                    [src]="child.item.formattedName ? (utilsService.imgPath + child.item.formattedName) : ''"
                                                                                                    alt="valamji">
                                                                                                <ng-container *ngIf="!child.item?.formattedName">{{
                                                                                                    child.displayName?.charAt(0).toUpperCase()
                                                                                                    }}
                                                                                                </ng-container>
                                                                                            </div>
                                                                                            <div class="tbl-user-text-action">
                                                                                                <div class="tbl-user-text">
                                                                                                    <p>{{child.item?.skuId}}
                                                                                                    </p>
                                                                                                    <span class="tbl-description">{{child.item.displayName}}</span>
                                                                                                </div>
                                                                                            </div>
                                                                                            <div class="dropdown" *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.VIEW_ITEM, this.utilsService.enumForPage.DEL_PO_ITEM])">
                                                                                                <button class="btn btn-xs btn-light-white btn-icon" id="actionDropDown"
                                                                                                    data-bs-toggle="dropdown" aria-expanded="false" data-bs-popper-config='{"strategy":"fixed"}'
                                                                                                    ngbTooltip="More Option" placement="bottom" container="body" triggers="hover"
                                                                                                    ngbTooltip="More Option" placement="bottom" container="body" triggers="hover">
                                                                                                    <i class="th th-outline-more"></i>
                                                                                                </button>
                                                                                                <ul class="dropdown-menu" aria-labelledby="actionDropDown">
                                                                                                    <li [pageAccess]="{page: utilsService.enumForPage.ITEM, action: utilsService.enumForPage.VIEW_ITEM}">
                                                                                                        <a class="dropdown-item" (click)="utilsService.openItemDetailsInNewTab(child.itemId)">
                                                                                                            <i class="th th-outline-eye"></i>View Item Details
                                                                                                        </a>
                                                                                                    </li>
                                                                                                    <!-- <li>
                                                                                                        <a class="dropdown-item" (click)="onStatusOpen(child, item)">
                                                                                                            <i class="th th-outline-status"></i>
                                                                                                            Change Status
                                                                                                        </a>
                                                                                                    </li> -->
                                                                                                    <ng-container>
                                                                                                        <hr class="dropdown-divider" *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.VIEW_ITEM, this.utilsService.enumForPage.DEL_PO_ITEM])">
                                                                                                        <li [pageAccess]="{page: utilsService.enumForPage.PO, action: utilsService.enumForPage.DEL_PO_ITEM}">
                                                                                                            <a (click)="onItemDel(child, subItem)" class="dropdown-item text-danger">
                                                                                                                <i class="th th-outline-trash"></i>Delete
                                                                                                            </a>
                                                                                                        </li>
                                                                                                    </ng-container>
                                                                                                    <!-- <li><a class="dropdown-item" data-bs-toggle="modal"
                                                                                                            data-bs-target="#ChangeStatusModal_4">
                                                                                                            <i class="th th-outline-status"></i>Change
                                                                                                            Status</a>
                                                                                                    </li>
                                                                                                    <li><a class="dropdown-item" href=""
                                                                                                            [routerLink]="['/users/purchases/new-po-import-expenses']"><i
                                                                                                                class="th th-outline-add-circle"></i>Add
                                                                                                            Expense</a></li>
                                                                                                    <li><a class="dropdown-item" href="" [routerLink]="['/users/audit-tickets/list']"><i
                                                                                                                class="th th-outline-box-1"></i>Audit
                                                                                                            Ticket</a>
                                                                                                    </li>
                                                                                                    <li><a class="dropdown-item" href="" [routerLink]="['/users/purchases/new-payments']"><i
                                                                                                                class="th th-outline-dollar-circle"></i>Make
                                                                                                            Payment</a></li>
                                                                                                    <li><a class="dropdown-item" href=""><i class="th th-outline-tick-circle"></i>
                                                                                                            Mark As Received</a>
                                                                                                    </li> -->
                                                                                                    <!-- <li>
                                                                                                        <hr class="dropdown-divider">
                                                                                                    </li>
                                                                                                    <li><a class="dropdown-item text-danger" href=""><i
                                                                                                                class="th th-outline-trash"></i>Delete</a>
                                                                                                    </li> -->
                                                                                                </ul>
                                                                        
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                </ng-container>
                                                                        
                                                                        
                                                                                <ng-container *ngIf="column.key =='purchaseBreachQty'">
                                                                                    <div>{{(child.item.levelBreachQtys
                                                                                        &&
                                                                                        child.item.levelBreachQtys.breachQtys)
                                                                                        ?
                                                                                        (child.item.levelBreachQtys.levelName
                                                                                        + ': ' +
                                                                                        child.item.levelBreachQtys.breachQtys)
                                                                                        : '-'}}
                                                                                    </div>
                                                                                </ng-container>
                                                                        
                                                                                <ng-container *ngIf="column.key =='toFrom'">
                                                                                    {{child?.item?.seasonMaster?.fromDate
                                                                                    ?
                                                                                    ((child.item.seasonMaster.fromDate |
                                                                                    date:
                                                                                    'dd-MM') + ' to ' +
                                                                                    (child.item.seasonMaster.toDate |
                                                                                    date:
                                                                                    'dd-MM')) : '-'}}
                                                                                </ng-container>
                                                                        
                                                                                <ng-container *ngIf="column.key =='advanceDate'">
                                                                                    <span class="text-center">
                                                                                        {{child.item?.seasonMaster?.advanceDate
                                                                                        ?
                                                                                        (child.item?.seasonMaster?.advanceDate
                                                                                        |
                                                                                        date:
                                                                                        'dd-MM') : '-'}}</span>
                                                                                </ng-container>
                                                                        
                                                                                <ng-container *ngIf="column.key ==4">
                                                                                    -
                                                                                </ng-container>
                                                                        
                                                                                <ng-container *ngIf="column.key =='salesPrice'">
                                                                                    <div>
                                                                                        {{child.item.itemPrice ?
                                                                                        child.item.itemPrice : '-'}}
                                                                                    </div>
                                                                                </ng-container>
                                                                        
                                                                                <ng-container *ngIf="column.key =='marka'">
                                                                                    <span class="d-flex flex-column align-items-start">
                                                                                        <div>{{child.marka ? child.marka
                                                                                            : '-'}}</div>
                                                                                        <div>
                                                                                            <ng-container>
                                                                                                {{child.cartonLength ?
                                                                                                child.cartonLength :
                                                                                                '-'}} X
                                                                                                {{child.cartonWidth ?
                                                                                                child.cartonWidth :
                                                                                                '-'}} X
                                                                                                {{child.cartonHeight ?
                                                                                                child.cartonHeight :
                                                                                                '-'}}
                                                                                                {{child?.cartonDimensionUnit ?
                                                                                                child?.cartonDimensionUnit?.shortCode :
                                                                                                ''}}
                                                                                            </ng-container>
                                                                                        </div>
                                                                                        <div>
                                                                                            <p>{{child.pricePerCarton ?
                                                                                                child.pricePerCarton :
                                                                                                '-'}}</p>
                                                                                        </div>
                                                                                        <div>
                                                                                            <p class="tbl-po-notes">{{child.chinaComment ?
                                                                                                child.chinaComment :
                                                                                                ''}}</p>
                                                                                        </div>
                                                                                    </span>
                                                                                </ng-container>
                                                                        
                                                                                <ng-container *ngIf="column.key =='color'">
                                                                                    <span class="w-100 d-block" *ngFor="let v of child.colorName">
                                                                                        {{!utilsService.isEmptyObjectOrNullUndefined(v) ? v : ''}}
                                                                                    </span>
                                                                                </ng-container>
                                                                        
                                                                                <ng-container *ngIf="column.key =='note'">
                                                                                    <div class="tbl-po-notes">
                                                                                        {{child.note ? child.note :
                                                                                        '-'}}
                                                                                    </div>
                                                                                </ng-container>
                                                                                
                                                                                <ng-container *ngIf="column.key =='englishComment'">
                                                                                    <div class="tbl-po-notes">
                                                                                        {{child.englishComment ?
                                                                                        child.englishComment : '-'}}
                                                                                    </div>
                                                                                </ng-container>
                                                                                
                                                                                <ng-container *ngIf="column.key =='chinaComment'">
                                                                                    <div class="tbl-po-notes">
                                                                                        {{child.chinaComment ?
                                                                                        child.chinaComment : '-'}}
                                                                                    </div>
                                                                                </ng-container>
                                                                        
                                                                                <ng-container *ngIf="column.key =='expectedDeliveryDate'">
                                                                                    <div>
                                                                                        {{child.expectedDeliveryDate ?
                                                                                        (child.expectedDeliveryDate |
                                                                                        date: 'dd/MM/yyyy') : '-'}}
                                                                                    </div>
                                                                                </ng-container>
                                                                        
                                                                                <ng-container *ngIf="column.key =='poCarton'">
                                                                                    {{child.poCarton ? child.poCarton :
                                                                                    '-'}}
                                                                                </ng-container>
                                                                        
                                                                                <ng-container *ngIf="column.key =='pricePerCarton'">
                                                                                    {{child.pricePerCarton ?
                                                                                    child.pricePerCarton : '-'}}
                                                                                </ng-container>
                                                                        
                                                                                <ng-container *ngIf="column.key =='totalPcsQty'">
                                                                                    {{(child.totalPcsQty) ?
                                                                                    (child.totalPcsQty)
                                                                                    : '-'}}
                                                                                </ng-container>
                                                                        
                                                                                <ng-container *ngIf="column.key =='pendingQty'">
                                                                                    <div>
                                                                                        {{child.pendingQtyCarton ?
                                                                                        (child.pendingQtyCarton) : '-'}}
                                                                                    </div>
                                                                                </ng-container>
                                                                        
                                                                                <ng-container *ngIf="column.key =='totalPendingQty'">
                                                                                    <div>
                                                                                        {{child.totalPendingQty ?
                                                                                        (child.totalPendingQty) : '-'}}
                                                                                    </div>
                                                                                </ng-container>
                                                                        
                                                                                <ng-container *ngIf="column.key =='pricePerItem'">
                                                                                    {{child.pricePerItem ?
                                                                                    (child.pricePerItem | indianCurrency) :
                                                                                    0}}
                                                                                </ng-container>
                                                                                <ng-container *ngIf="column.key =='totalAmount'">
                                                                                    {{child.Total_Price ? (child.Total_Price | indianCurrency) :
                                                                                    0}}
                                                                                </ng-container>
                                                                                <ng-container *ngIf="column.key =='expDeliveryCost'">
                                                                                    {{child.expDeliveryCost ?
                                                                                    child.expDeliveryCost : 0}}
                                                                                </ng-container>
                                                                                <ng-container *ngIf="column.key =='totalAmountWithExp'">
                                                                                    {{child.totalAmountWithExp ?
                                                                                    (child.totalAmountWithExp | indianCurrency) : 0}}
                                                                                </ng-container>
                                                                                <ng-container *ngIf="column.key =='conversationRate'">
                                                                                    <!-- {{subItem.conversationRateCurrencyName
                                                                                    ?
                                                                                    subItem.conversationRateCurrencyName
                                                                                    : ''}} -->
                                                                                    {{subItem.conversationRate ?
                                                                                    subItem.conversationRate : '-'}}
                                                                                </ng-container>
                                                                                <ng-container *ngIf="column.key =='totalAmountWithExpInINR'">
                                                                                    {{child.totalAmountWithExpInINR ?
                                                                                    (child.totalAmountWithExpInINR | indianCurrency) :
                                                                                    '-'}}
                                                                                </ng-container>
                                                                                <ng-container *ngIf="column.key =='chinaFinalExpextedCode'">
                                                                                    {{child.chinaFinalExpextedCode ?
                                                                                    child.chinaFinalExpextedCode : '-'}}
                                                                                </ng-container>
                                                                                <ng-container *ngIf="column.key =='shippingTypes'">
                                                                                    {{subItem.shippingTypes ?
                                                                                    (subItem.shippingTypes.value) :
                                                                                    '-'}}
                                                                                </ng-container>
                                                                                <ng-container *ngIf="column.key =='itemDim'">
                                                                                    {{child.cartonLength ?
                                                                                    child.cartonLength : '-'}} x
                                                                                    {{child.cartonWidth ?
                                                                                    child.cartonWidth : '-'}}
                                                                                    x
                                                                                    {{child.cartonHeight ?
                                                                                    child.cartonHeight : '-'}}
                                                                                    {{child?.cartonDimensionUnit ?
                                                                                    child?.cartonDimensionUnit?.shortCode :
                                                                                    ''}}
                                                                                </ng-container>
                                                                                <ng-container *ngIf="column.key =='dimAge'">
                                                                                    {{child.dimAge}}
                                                                                </ng-container>

                                                                                <ng-container>
                                                                                    <ng-container *ngIf="column.key =='CBM/Carton'">
                                                                                        {{child.cbmCarton ? child.cbmCarton : '-'}}
                                                                                    </ng-container>
                                                                                    <ng-container *ngIf="column.key =='Total CBM'">
                                                                                        {{child.totalCbm ? child.totalCbm :
                                                                                        '-'}}
                                                                                    </ng-container>
                                                                                    <ng-container *ngIf="column.key =='CBM Price'">
                                                                                        {{subItem.cbmPrice ?
                                                                                        (subItem.cbmPrice | indianCurrency) : 0}}
                                                                                    </ng-container>
                                                                                    <ng-container *ngIf="column.key =='TotalCBMExpense'">
                                                                                        {{child.totalCBMExpense ? (child.totalCBMExpense | indianCurrency) : 0}}
                                                                                    </ng-container>
                                                                                    <ng-container *ngIf="column.key =='ShippingExpense/PCS'">
                                                                                        {{child.shippingExpense ? (child.shippingExpense | indianCurrency) : 0}}
                                                                                    </ng-container>
                                                                                </ng-container>
                                                                        
                                                                                <ng-container *ngIf="column.key =='transportCharges'">
                                                                                    {{child.transportCharges ?
                                                                                    (child.transportCharges | indianCurrency) : 0}}
                                                                                </ng-container>
                                                                                <ng-container *ngIf="column.key =='totalTransportationChargesM2S'">
                                                                                    {{child.TotalTransportationCharges ?
                                                                                    (child.TotalTransportationCharges | indianCurrency) : 0}}
                                                                                </ng-container>
                                                                                <ng-container *ngIf="column.key =='transportationChargesM2SperPCS'">
                                                                                    {{child.Transportationcharges_Mumbai_to_Surat ?
                                                                                    (child.Transportationcharges_Mumbai_to_Surat | indianCurrency) : 0}}
                                                                                </ng-container>
                                                                                <ng-container *ngIf="column.key =='totalInsurance'">
                                                                                    {{child.TotalInsurance ?
                                                                                    (child.TotalInsurance | indianCurrency) : 0}}
                                                                                </ng-container>
                                                                                <ng-container *ngIf="column.key =='insurancePerPcs'">
                                                                                    {{child.insurancePCS ?
                                                                                    (child.insurancePCS | indianCurrency) : 0}}
                                                                                </ng-container>
                                                                                <ng-container *ngIf="column.key =='gstAmtPerPcs'">
                                                                                    {{child.GSTAmount_PCS ? (child.GSTAmount_PCS | indianCurrency) :
                                                                                    0}}
                                                                                </ng-container>
                                                                                <ng-container *ngIf="column.key =='craneExpense'">
                                                                                    {{child.craneExpense ?
                                                                                    (child.craneExpense | indianCurrency) :
                                                                                    0}}
                                                                                </ng-container>
                                                                                <ng-container *ngIf="column.key =='craneExpPcs'">
                                                                                    {{child.CraneExpense_PCS ? (child.CraneExpense_PCS | indianCurrency) :
                                                                                    0}}
                                                                                </ng-container>
                                                                                <ng-container *ngIf="column.key =='totalExp'">
                                                                                    {{child.Total_Expense ? (child.Total_Expense | indianCurrency) : 0}}
                                                                                </ng-container>
                                                                                <ng-container *ngIf="column.key =='chinaToSuratPadtar'">
                                                                                    {{child.ChinaToSuratFinalPrice ? (child.ChinaToSuratFinalPrice | indianCurrency) : 0}}
                                                                                </ng-container>
                                                                        
                                                                                <ng-container>
                                                                                    <ng-container *ngIf="column.key =='cartonWeight'">
                                                                                        {{child.Weight_Carton ?
                                                                                        child.Weight_Carton : '-'}}
                                                                                    </ng-container>
                                                                                    <ng-container *ngIf="column.key =='totalWeight'">
                                                                                        {{child.total_Weight ? child.total_Weight : '-'}}
                                                                                    </ng-container>
                                                                                    <ng-container *ngIf="column.key =='cartonWeightRu'">
                                                                                        {{child.Weight_kg ?
                                                                                        child.Weight_kg : '-'}}
                                                                                    </ng-container>
                                                                                    <ng-container *ngIf="column.key =='totalLoadAmt'">
                                                                                        {{child.total_load_amt_Ru ? child.total_load_amt_Ru : '-'}}
                                                                                    </ng-container>
                                                                                    <ng-container *ngIf="column.key =='shippingCostperPieceINR'">
                                                                                        {{child.shipingCost ? child.shipingCost : '-'}}
                                                                                    </ng-container>
                                                                                    <ng-container *ngIf="column.key =='totalShippingExpWeight'">
                                                                                        {{child.totalShippingExpense ? child.totalShippingExpense : '-'}}
                                                                                    </ng-container>
                                                                                </ng-container>

                                                                                <ng-container>

                                                                                    <ng-container *ngIf="column.key =='percentage'">
                                                                                        {{child.Percentage ? child.Percentage :
                                                                                        0}}
                                                                                    </ng-container>
                                                                                    <ng-container *ngIf="column.key =='totalExpPCSper'">
                                                                                        {{child.totalExpense ?
                                                                                        child.totalExpense : '-'}}
                                                                                    </ng-container>
                                                                                    <ng-container *ngIf="column.key =='totalFinalCostPCSper'">
                                                                                        {{child.totalFinalCost ?
                                                                                        child.totalFinalCost : '-'}}
                                                                                    </ng-container>

                                                                                </ng-container>


                                                                                <ng-container>
                                                                                    <ng-container *ngIf="column.key ==50">
                                                                                        {{child.item_Amount ?
                                                                                        (child.item_Amount | indianCurrency) : 0}}
                                                                                    </ng-container>
                                                                                    <ng-container *ngIf="column.key ==51">
                                                                                        {{child.item_Amount ?
                                                                                        (child.item_Amount | indianCurrency) : 0}}
                                                                                    </ng-container>
                                                                                </ng-container>
                                                                                
                                                                                <ng-container *ngIf="column.key =='expensePcs'">
                                                                                    {{child.Expense_PCS ?
                                                                                    (child.Expense_PCS | indianCurrency) :
                                                                                    0}}
                                                                                </ng-container>
                                                                                <ng-container *ngIf="column.key =='totalItemAmt'">
                                                                                    {{child.totalItemAmount ? (child.totalItemAmount | indianCurrency) :
                                                                                    0}}
                                                                                </ng-container>
                                                                                
                                                                                <ng-container *ngIf="column.key =='lastRecord'">
                                                                                    <ng-container *ngFor="let ls of child?.lastRecord">
                                                                                        {{ls?.supplierShortCode}},
                                                                                        {{ls?.pricePerCarton}},
                                                                                        {{ls?.poCarton}} <br />
                                                                                    </ng-container>
                                                                                </ng-container>
                                                                                <ng-container *ngIf="column.key ==55">
                                                                                    -
                                                                                </ng-container>
                                                                                <ng-container *ngIf="column.key ==56">
                                                                                    -
                                                                                </ng-container>
                                                                                <ng-container *ngIf="column.key ==57">
                                                                                    {{child.gst_amounts ? (child.gst_amounts | indianCurrency ): 0}}
                                                                                </ng-container>
                                                                                <ng-container *ngIf="column.key ==58">
                                                                                    {{child.gstPers ? (child.gstPers ): 0}}
                                                                                </ng-container>
                                                                                <ng-container *ngIf="column.key =='extraExpense'">
                                                                                    {{child.extraExpense ?
                                                                                    (child.extraExpense | indianCurrency) :
                                                                                    0}}
                                                                                </ng-container>
                                                                                <ng-container *ngIf="column.key =='purchaseRation'">
                                                                                    {{child.purchaseRatio ?
                                                                                    child.purchaseRatio : 0}}
                                                                                </ng-container>

                                                                            </ng-container>
                                                                        </td>
                                                                    </ng-container>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>

                                                </td>
                                            </tr>
                                        </ng-container>
                                    </tbody>
                                </table>

                            </td>
                        </tr>
                    </ng-container>
                    <ng-container>
                        <tr *ngIf="utilsService.isEmptyObjectOrNullUndefined(poImportList)">
                            <td colspan="20" class="text-center">
                                <span
                                    class="truncate">{{utilsService.serverVariableService.STRING_WHEN_NO_RECORDS_FOUND}}</span>
                            </td>
                        </tr>
                    </ng-container>
                </tbody>

            </table>
        </div>
    </div>
</div>

<div class="paginationbox pagination-fixed">
    <app-pagination (pagesizeData)="addPageSizeData($event)" (pageNumber)="pageNumber($event)"
        [page]="paginationRequest.pageNo" [pageSize]="paginationRequest.pageSize"
        [totalData]="paginationRequest.totalData"></app-pagination>
</div>