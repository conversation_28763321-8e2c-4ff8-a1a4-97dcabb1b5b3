<div class="page-content">
  <div class="page-title-wrapper">
    <div class="page-title-left">
      <h4>Group Code Master</h4>
    </div>
    <div class="page-title-right">
      <button (click)="redirectToDetails()" class="btn btn-sm btn-primary btn-icon-text"
        [routerLink]="['/users/inventory/group-code/new-group-code']">
        <i class="th th-outline-add-circle"></i>Add New
      </button>
      <button (click)="getAllGroupCode()" class="btn btn-sm btn-icon btn-outline-white" ngbTooltip="Refresh"
        placement="left" container="body" triggers="hover">
        <i class="th th-outline-refresh-2"></i>
      </button>
    </div>
  </div>
  <div class="content-area">
    <div class="page-filters">
      <div class="page-filters-left">
        <div class="form-group form-group-sm filter-search">
          <div class="form-group-icon-start">
            <i class="th th-outline-search-normal-1 icon-broder "></i>
            <input (change)="onSearch($event)" [(ngModel)]="paginationRequest.searchText" type="search"
              class="form-control" placeholder="Search by name / ID">
          </div>
        </div>
        <div class="form-group theme-ngselect form-group-sm theme-ngselect-group-list w-50 filter-search2">
          <ng-select (change)="onChangeActive()" placeholder="Select Category" [multiple]="false" [clearable]="true"
            [items]="flattenedParentCategory" bindLabel="categoryName" bindValue="id"
            [(ngModel)]="paginationRequest.categoryId">
            <ng-template ng-option-tmp let-item="item">
              <span class="fs-13" [title]="item.categoryName" [style.padding-left.px]="item.index * 25" [ngClass]="{'ng-option-child-label' : item.isChild}"
                [class]="item.className">
                {{ item.categoryName }}
              </span>
            </ng-template>
          </ng-select>
        </div>
        <div class="form-group theme-ngselect form-group-sm">
          <ng-select (change)="onChangeActive()" class="" placeholder="Status" [multiple]="false" [clearable]="true"
            [items]="activeInactiveStatus" bindLabel="label" bindValue="value" [(ngModel)]="paginationRequest.isActive"
            [hideSelected]="false">
          </ng-select>
        </div>
      </div>
      <div class="page-filters-right">
        <div class="form-group theme-ngselect form-group-sm form-group-export">
          <div class="dropdown export-dropdown">
            <button type="button" class="btn btn-sm btn-outline-white dropdown-toggle" data-bs-toggle="dropdown"
              aria-expanded="false">
              Export
            </button>
            <ul class="dropdown-menu">
              <li><a class="dropdown-item" (click)="exportReport()">Excel</a></li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    <div class="card card-theme card-table-sticky">
      <div class="card-body p-0">
        <div class="table-responsive">
          <table class="table-theme table-hover table table-bordered table-sticky">
            <thead class="border-less">
              <tr>
                <th *ngFor="let th of groupCodeTH; index as j" [class]="th.class"
                  [ngClass]="{'sorting-asc': paginationRequest.sortColumn==th.keyName && paginationRequest.sortOrder === enumForSortOrder.A, 
                              'sorting-desc': paginationRequest.sortColumn==th.keyName && paginationRequest.sortOrder === enumForSortOrder.D }"
                  (click)="!utilsService.isNullUndefinedOrBlank(th.keyName) ? onSortTH(th.keyName) : $event.preventDefault()">
                  <div (click)="$event.stopPropagation()" *ngIf="j === 0"
                    class="checkbox checkbox-primary checkbox-small">
                    <input [disabled]="utilsService.isEmptyObjectOrNullUndefined(groupCodeList)" (change)="selectAll()"
                      [(ngModel)]="paginationRequest.flagForSelectAll" type="checkbox" id="tbl-checkbox"
                      class="material-inputs filled-in" />
                    <label for="tbl-checkbox"></label>
                  </div>
                  {{th.displayName}}
                </th>
                <th class="tbl-switch">Status</th>
                <th class="text-center">Action</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let item of groupCodeList; index as i; trackBy: trackBy">
                <td class="tbl-user tbl-bold">
                  <div class="tbl-user-checkbox-srno">
                    <div class="checkbox checkbox-primary checkbox-small">
                      <input type="checkbox" id="tbl-checkbox-{{i}}" class="material-inputs filled-in"
                        (change)="selectUnselect(item.id, item.isSelected)" [(ngModel)]="item.isSelected" />
                      <label for="tbl-checkbox-{{i}}"></label>
                    </div>
                    <span class="tbl-user-srno">{{(i + 1) | padNum}}.</span>
                    <div class="tbl-user-wrapper">
                      <div class="tbl-user-image">
                        <img *ngIf="item.image" [src]="utilsService.imgPath + item.image" alt="Valamji"
                          loading="lazy" />
                      </div>
                      <div class="tbl-user-text">
                        <p>{{item.groupCodeName}}</p>
                      </div>
                    </div>
                  </div>
                </td>
                <td>{{item.groupCodeId}}</td>
                <td>{{item.formattedCategoryName}}</td>
                <td class="tbl-switch">
                  <div class="switch-box">
                    <label class="switch" htmlFor="switch-{{i}}">
                      <input type="checkbox" id='switch-{{i}}' [(ngModel)]="item.isActive"
                        (change)="onChangeStatus(item, item.isActive, i)" />
                      <div class="slider round"></div>
                    </label>
                  </div>
                </td>
                <td class="tbl-action">
                  <div class="tbl-action-group">
                    <button (click)="redirectToDetails()"
                      [routerLink]="['/users/inventory/group-code/edit-group-code/' + item.id]"
                      class="btn btn-xs btn-light-white btn-icon" ngbTooltip="Edit" placement="bottom" container="body"
                      triggers="hover">
                      <i class="th th-outline-edit"></i>
                    </button>
                    <button class="btn btn-xs btn-light-danger btn-icon" (click)="openDeleteGroupCodeModal(item)"
                      ngbTooltip="Delete" placement="left" container="body" triggers="hover">
                      <i class="th th-outline-trash"></i>
                    </button>
                  </div>
                </td>
              </tr>
              <tr *ngIf="utilsService.isEmptyObjectOrNullUndefined(groupCodeList)">
                <td colspan="20" class="text-center">
                  <span class="truncate">{{utilsService.serverVariableService.STRING_WHEN_NO_RECORDS_FOUND}}</span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
  <div class="paginationbox pagination-fixed">
    <app-pagination (pagesizeData)="addPageSizeData($event)" (pageNumber)="pageNumber($event)"
      [page]="paginationRequest.pageNo" [pageSize]="paginationRequest.pageSize"
      [totalData]="paginationRequest.totalData" />
  </div>
</div>

<!-- ----------------------------------------------------------------------- -->
<!--                           Delete Modal Start                          -->
<!-- ----------------------------------------------------------------------- -->
<div class="modal modal-theme modal-confirmation modal-reject fade" id="deleteGroupCodeModal" tabindex="-1"
  aria-labelledby="deleteGroupCodeModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
            class='th th-close'></i></button>
      </div>
      <div class="modal-body">

        <div class="modal-confirmation-container">
          <div class="modal-confirmation-icon">
            <i class="th th-bold-trash"></i>
          </div>
          <div class="modal-confirmation-content">
            <h5>Are You Sure?</h5>
            <p>You want to Delete <b>{{groupCodeObj.groupCodeName}}</b> Group Code.</p>
            <p><b>Note:</b> Items associated with this group code will be removed.</p>
          </div>
        </div>
        <div class="modal-button-group modal-full-width-btn">
          <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
          <button (click)="deleteGroupCode()" type="button" class="btn btn-primary btn-icon-text"> <i
              class="th th-outline-tick-circle"></i>
            Delete</button>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                           Delete Modal End                            -->
<!-- ----------------------------------------------------------------------- -->