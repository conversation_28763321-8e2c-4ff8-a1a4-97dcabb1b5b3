<div class="page-content">
  <div class="page-title-wrapper">
    <div class="page-title-left">
      <h4>{{expenseId ? 'Edit' : 'Add New'}} Tempo Expenses</h4>
    </div>
    <div class="page-title-right">
      <button class="btn btn-sm btn-icon btn-outline-white" (click)="onCloseCancel()"
        ngbTooltip="Close" placement="left" container="body" triggers="hover">
        <i class="th th-close"></i>
      </button>
    </div>
  </div>
  <div class="content-area">
    <div class="card card-theme card-forms">
      <div class="card-body" [formGroup]="expenseGroup">
        <div class="row">
          <div class="col-lg-5 col-md-6 col-sm-12">
            <div class="form-group form-group-inline-control single-date required">
              <label class="form-label">Date</label>
              <div class="form-control-wrapper">
                <div class="form-group-icon-end date-tempo-clear">
                  <i (click)="openDateModal()" class="th th-outline-calendar ngx-daterangepicker-action"></i>
                  <input #pickerDirectiveModal class="form-control" type="text" ngxDaterangepickerMd readonly
                    placeholder="Select Date" [autoApply]="true" [singleDatePicker]="true" [timePicker24Hour]="false"
                    [timePicker]="false" startKey="start" [closeOnAutoApply]="false" [maxDate]="tempoMaxDate"
                    [locale]="{format: 'DD/MM/YYYY h:mm A', displayFormat: 'DD/MM/YYYY'}" [(ngModel)]="tempoExpObj.temp_date"
                    formControlName="expenseDate">
                </div>
                <div class="message error-message" *ngIf="expenseGroup.controls['expenseDate'].touched && !tempoExpObj.temp_date">
                  {{utilsService.validationService.DATE_TIME_REQ}}
                </div>
              </div>
            </div>
            <div class="form-group theme-ngselect form-group-inline-control required">
              <label class="form-label">Tempo No.</label>
              <div class="form-control-wrapper">
                <ng-select (change)="onTempoChange()" placeholder="Select Tempo No." [multiple]="false"
                  [clearable]="false" [items]="dropdown.tempo" bindLabel="vehicleNo" bindValue="id"
                  formControlName="tempoID" [(ngModel)]="tempoExpObj.tempoID">
                </ng-select>
                <div class="message error-message"
                  *ngIf="expenseGroup.controls['tempoID'].hasError('required') &&  expenseGroup.controls['tempoID'].touched">
                  {{utilsService.validationService.TEMPO_NO_REQ}}
                </div>
              </div>
            </div>
            <div class="form-group theme-ngselect form-group-inline-control">
              <label class="form-label">Linked Containers</label>
              <div class="form-control-wrapper">
                <input type="text" class="form-control" placeholder="Linked Containers"
                  [(ngModel)]="tempoExpObj.containersName" formControlName="containersName">
              </div>
            </div>
            <div class="form-group form-group-inline-control" *ngIf="expenseId">
              <label class="form-label">Expense ID</label>
              <div class="form-control-wrapper">
                <input disabled type="text" class="form-control" placeholder="Expense ID"
                  [(ngModel)]="tempoExpObj.expenseID" [ngModelOptions]="{standalone: true}">
              </div>
            </div>
            <div class="form-group form-group-inline-control" (paste)="onSelectAttachments($event);doc.value"
              (drop)="onSelectAttachments($event);doc.value = ''" (dragover)="onSelectAttachments($event);doc.value = ''">
              <label class="form-label">Upload<i class="th th-outline-info-circle ms-1"
                  [ngbTooltip]="utilsService.validationService.DOC_INFO" placement="bottom" container="body"
                  triggers="hover"></i></label>
              <div class="form-control-wrapper">
                <div class='attachments-container h-100'>
                  <div class='attachments-content'>
                    <button class='btn btn-attachments'><i class="bi bi-upload"></i></button>
                    <p>Upload Photos & Document</p>
                  </div>
                  <input #doc type="file" ref={imageRef} multiple accept=".xls,.xlsx,.xlss,.csv,image/*,.pdf"
                    (change)="onSelectAttachments($event);doc.value = ''" />
                </div>
                <!-- <button class='btn btn-fileupload btn-fileupload-white'> <i class="bi bi-upload"></i>
                      Upload Photos & Document
                      <input #doc (paste)="onSelectAttachments($event);doc.value = ''" multiple
                        (change)="onSelectAttachments($event);doc.value = ''" type="file" ref={imageRef}
                        accept=".jpg,.jpeg,.png,.pdf,.doc,.docx,.jfif,.webp,.xls,.xlsx">
                    </button> -->
              </div>
            </div>
            <div class="form-group d-flex flex-column" (paste)="onSelectAttachments($event);doc.value"
              (drop)="onSelectAttachments($event);doc.value = ''" (dragover)="onSelectAttachments($event);doc.value = ''">
              <div class="attachments-wrapper">
                <div class='attachments-upload-grid-container attachments-upload-grid-container2'>
                  <div class='attachments-upload-row attachment-list'>
                    <div class='attachments-upload-col' *ngFor="let item of tempoExpObj.docs; index as i">
                      <div class='card-attachments-upload'>
                        <div class='attachments-image'>
            
                          <ng-container *ngIf="utilsService.isImage(item.file ? item.originalName : item.formattedName)">
                            <img (click)="openLink(item.file ? null : item.formattedName, item.file ? item.formattedName : null)"
                              loading="lazy" [src]="item.file ? item.formattedName : (utilsService.imgPath + item.formattedName)"
                              alt="valamji" />
                          </ng-container>
            
                          <ng-container *ngIf="utilsService.isDocument(item.file ? item.originalName : item.formattedName)">
                            <img (click)="openLink(item.file ? null : item.formattedName, item.file ? item.formattedName : null)"
                              src="assets/images/files/file-pdf.svg" alt="valamji" />
                          </ng-container>
            
                          <ng-container *ngIf="utilsService.isExcel(item.file ? item.originalName : item.formattedName)">
                            <img (click)="openLink(item.file ? null : item.formattedName, item.file ? item.formattedName : null)"
                              src="assets/images/files/file-excel.svg" alt="valamji" />
                          </ng-container>
            
                        </div>
                        <div class="attachments-text" [ngbTooltip]="item.fileName ? item.fileName : item.originalName"
                          placement="bottom" container="body" triggers="hover">
                          <h6 class="file-name">{{item.fileName ? item.fileName : item.originalName}}</h6>
                        </div>
                        <button (click)="removeAttachment(i, item)" class="btn-close" variant="close"><i class='th th-close'></i>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="form-group form-group-inline-control">
              <label class="form-label">Notes</label>
              <div class="form-control-wrapper">
                <textarea rows="3" formControlName="notes" [(ngModel)]="tempoExpObj.notes"
                  [maxlength]="utilsService.validationService.MAX_500" class="form-control"
                  placeholder="Enter Notes">
                </textarea>
              </div>
            </div>
          </div>
          <div class="col-lg-5 col-md-6 col-sm-12">
            <div class="table-responsive">
              <table class="table-theme table-hover table table-bordered" formArrayName="expenseTypeItems">
                <thead class="border-less">
                  <tr>
                    <th>Expense Type</th>
                    <th>Enter Amount (₹)</th>
                    <th class="text-center">Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr class="tbl-add-row tbl-bg-white" *ngFor="let contact of expenseTypeItems.controls; index as i"
                    [formGroupName]="i">
                    <td>
                      <div class="form-group theme-ngselect">
                        <ng-select [(ngModel)]="tempoExpObj.expenseTypeItems[i].expenseTypeId"
                          formControlName="expenseTypeId"
                          [ngClass]="{'required': contact.get('expenseTypeId').invalid && contact.get('expenseTypeId').touched}"
                          placeholder="Expense Type" [multiple]="false" [clearable]="false"
                          [items]="tempoExpObj.expenseTypeItems[i].expenseDropdown" bindValue="id" [appendTo]="'.theme-ngselect'">
                          <ng-template ng-label-tmp let-item="item">
                            <span class="ng-option-label tbl-description">{{item.expenseTypeName}}</span>
                          </ng-template>
                          <ng-template ng-option-tmp let-item="item">
                            <span class="ng-option-label">{{item.expenseTypeName}}</span>
                          </ng-template>
                        </ng-select>
                      </div>
                    </td>
                    <td>
                      <div class="form-group form-group-100"
                        [ngClass]="{'form-error': contact.get('amount').invalid && contact.get('amount').touched}">
                        <input mask="separator.2" thousandSeparator=""
                          [(ngModel)]="tempoExpObj.expenseTypeItems[i].amount" formControlName="amount" type="text"
                          class="form-control" placeholder="Enter Amount"
                          [maxLength]="utilsService.validationService.MAX_15">
                      </div>
                    </td>
                    <td class="tbl-action">
                      <div class="tbl-action-group">
                        <button *ngIf="expenseTypeItems.length > 1" (click)="openExpenseDeleteModal(i)" class="btn btn-xs btn-light-danger btn-icon"
                          ngbTooltip="Delete" placement="left" container="body" triggers="hover">
                          <i class="th th-outline-trash"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                  <tr *ngIf="utilsService.isEmptyObjectOrNullUndefined(tempoExpObj.expenseTypeItems)">
                    <td colspan="20" class="text-center">
                      <span class="truncate">{{utilsService.serverVariableService.STRING_WHEN_NO_RECORDS_FOUND}}</span>
                    </td>
                  </tr>
                </tbody>
                <tfoot>
                  <tr class="tbl-total-row" *ngIf="tempoExpObj.expenseTypeItems.length > 0">
                    <th>Total</th>
                    <th colspan="2">{{totalAmount ? (totalAmount | indianCurrency) : 0}}</th>
                  </tr>
                  <tr class="tbl-add-new">
                    <td colspan="100">
                      <button (click)="addExpenseTypeItem()"
                        class="btn btn-sm btn-link btn-icon-text text-primary"> <i class="th-bold-add-circle"></i>
                        Add New Row
                      </button>
                    </td>
                  </tr>
                </tfoot>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class='bottombar-wrapper bottom-fixed'>
      <div class='bottombar-container'>
        <div class='bottombar-left'>
          <button (click)="onSave()" type="button" class="btn btn-primary btn-icon-text btn-sm"> <i
              class="th th-outline-tick-circle"></i>{{expenseId ? 'Update' : 'Save'}}</button>
          <button (click)="onCloseCancel()" type="button" class="btn btn-outline-white btn-icon-text btn-sm"><i
              class="th th-outline-close-circle"></i>Cancel</button>
        </div>
        <div class='bottombar-right'>
      
        </div>
      </div>
    </div>
  </div>
</div>

<!-- ----------------------------------------------------------------------- -->
<!--                           Delete Modal Start                            -->
<!-- ----------------------------------------------------------------------- -->
<div class="modal modal-theme modal-confirmation modal-reject fade" id="deleteExpenseTModal" tabindex="-1"
  aria-labelledby="deleteExpenseTModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
            class='th th-close'></i></button>
      </div>
      <div class="modal-body">
        <div class="modal-confirmation-container">
          <div class="modal-confirmation-icon">
            <i class="th th-bold-trash"></i>
          </div>
          <div class="modal-confirmation-content">
            <h5>Are You Sure?</h5>
            <p>You want to Delete Expense Type.</p>
          </div>
        </div>
        <div class="modal-button-group modal-full-width-btn">
          <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
          <button (click)="onDeleteExpense()" type="button" class="btn btn-primary btn-icon-text"> <i
              class="th th-outline-tick-circle"></i>
            Delete</button>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                           Delete Modal End                            -->
<!-- ----------------------------------------------------------------------- -->