import { Component, OnInit, OnChanges, Input, Output, EventEmitter, forwardRef, ElementRef, ViewChild } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import * as moment from 'moment';

export type TimeFormat = '12-hour' | '24-hour';
export type OutputFormat = 'string' | 'moment';
export type DisplayFormat =
  | 'DD/MM/YYYY HH:mm'
  | 'DD-MM-YYYY HH:mm'
  | 'DD/MM/YYYY hh:mm A'
  | 'DD-MM-YYYY hh:mm A'
  | 'YYYY-MM-DD HH:mm'
  | 'YYYY-MM-DD hh:mm A'
  | 'DD/MM/YYYY'
  | 'DD-MM-YYYY'
  | 'MM/DD/YYYY'
  | 'MM-DD-YYYY'
  | 'YYYY-MM-DD'
  | 'YYYY/MM/DD'
  | 'HH:mm'
  | 'hh:mm A';

@Component({
  selector: 'app-date-time-picker',
  templateUrl: './date-time-picker.component.html',
  styleUrls: ['./date-time-picker.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => DateTimePickerComponent),
      multi: true
    }
  ]
})
export class DateTimePickerComponent implements OnInit, OnChanges, ControlValueAccessor {

  @Input() timer: boolean = true; // Show time picker when true, date picker only when false
  @Input() timeFormat: TimeFormat = '12-hour'; // 12-hour or 24-hour format
  @Input() outputFormat: OutputFormat = 'string';
  @Input() displayFormat: DisplayFormat = 'DD/MM/YYYY hh:mm A'; // Format for input field display
  @Input() disabled: boolean = false;
  @Input() placeholder: string = 'Select date & time';
  @Input() minDate: string = ''; // Format: 'DD-MM-YYYY' or 'DD-MM-YYYY HH:mm'
  @Input() maxDate: string = ''; // Format: 'DD-MM-YYYY' or 'DD-MM-YYYY HH:mm'
  @Input() closeOnOutsideClick: boolean = true;
  @Input() minuteStep: number = 1; // Step for minutes: 1 = all minutes (0-59), 5 = every 5 minutes (0,5,10...), 10 = every 10 minutes, etc.
  @Input() locale: string = 'en'; // Locale for moment.js formatting (e.g., 'en', 'fr', 'de', 'es', etc.)
  @Input() outputDateFormat: string = 'DD/MM/YYYY'; // Custom output format for date-only mode (e.g., 'YYYY-MM-DD', 'MM/DD/YYYY', etc.)
  @Input() timePosition: 'bottom' | 'right' = 'bottom'; // Position of time picker: 'bottom' (below date) or 'right' (beside date)

  // Event emitters
  @Output() onOpen = new EventEmitter<void>();
  @Output() onSave = new EventEmitter<string | moment.Moment>();
  @Output() onDateTimeChange = new EventEmitter<string | moment.Moment>();
  @Output() onDateChange = new EventEmitter<{ day: number; month: number; year: number }>();
  @Output() onTimeChange = new EventEmitter<{ hour: number; minute: number; period?: string }>();

  @ViewChild('inputField', { static: false }) inputField!: ElementRef;

  // Internal state
  selectedDate: moment.Moment | null = null;
  selectedDay: number | null = null;
  selectedMonth: number | null = null;
  selectedYear: number | null = null;
  selectedHour: number | null = null;
  selectedMinute: number | null = null;
  selectedPeriod: string = 'AM'; // For 12-hour format



  // View state
  viewingMonth: number = moment().month() + 1; // 1-12
  viewingYear: number = moment().year();
  showMonthSelection: boolean = false;
  showYearSelection: boolean = false;

  // Year pagination properties
  yearsPerPage: number = 16; // Display 16 years per page (4x4 grid for consistent height)
  currentYearPage: number = 0; // Current page index
  displayedYears: number[] = []; // Years currently displayed in the grid

  // Dropdown options - back to simple arrays for performance
  hours12: number[] = Array.from({ length: 12 }, (_, i) => i + 1); // 1-12
  hours24: number[] = Array.from({ length: 24 }, (_, i) => i); // 0-23
  minutes: number[] = []; // Will be generated based on minuteStep


  periods: string[] = ['AM', 'PM'];

  // Calendar data
  months = [
    { value: 1, name: 'Jan', fullName: 'January' },
    { value: 2, name: 'Feb', fullName: 'February' },
    { value: 3, name: 'Mar', fullName: 'March' },
    { value: 4, name: 'Apr', fullName: 'April' },
    { value: 5, name: 'May', fullName: 'May' },
    { value: 6, name: 'Jun', fullName: 'June' },
    { value: 7, name: 'Jul', fullName: 'July' },
    { value: 8, name: 'Aug', fullName: 'August' },
    { value: 9, name: 'Sep', fullName: 'September' },
    { value: 10, name: 'Oct', fullName: 'October' },
    { value: 11, name: 'Nov', fullName: 'November' },
    { value: 12, name: 'Dec', fullName: 'December' }
  ];

  years: number[] = [];

  // ControlValueAccessor
  private onChange = (value: any) => {};
  private onTouched = () => {};

  constructor() {
    // Generate years (current year ± 50 years)
    const currentYear = moment().year();
    for (let i = currentYear - 50; i <= currentYear + 50; i++) {
      this.years.push(i);
    }

    // Initialize year pagination
    this.initializeYearPagination();
  }

  ngOnInit() {
    // Generate minutes array based on minuteStep
    this.generateMinutesArray();

    // Don't set default time values on init - let user see placeholders
    // Time will be set when date is selected or when user manually selects time

    // Initialize time validation if timer is enabled and date is selected
    if (this.timer && this.selectedDate) {
      this.updateTimeValidation();
    }
  }

  // Generate minutes array based on minuteStep
  private generateMinutesArray(): void {
    this.minutes = [];
    if (this.minuteStep <= 0 || this.minuteStep > 60) {
      this.minuteStep = 1; // Default to 1 if invalid step
    }

    for (let i = 0; i < 60; i += this.minuteStep) {
      this.minutes.push(i);
    }


  }

  // Round minute to nearest step
  private roundToNearestStep(minute: number): number {
    return Math.floor(minute / this.minuteStep) * this.minuteStep;
  }

  // Handle changes to minuteStep input
  ngOnChanges(changes: any): void {
    if (changes.minuteStep && !changes.minuteStep.firstChange) {
      this.generateMinutesArray();
      // Adjust selected minute if it's no longer valid
      if (this.selectedMinute !== null && !this.minutes.includes(this.selectedMinute)) {
        this.selectedMinute = this.roundToNearestStep(this.selectedMinute);
        this.updateSelectedDate();
      }
    }
  }

  // ControlValueAccessor implementation
  writeValue(value: any): void {
    if (value) {
      let momentValue: moment.Moment;

      if (moment.isMoment(value)) {
        momentValue = value;
      } else {
        // Try to parse string with multiple formats
        const formats = [
          'DD/MM/YYYY HH:mm',
          'DD/MM/YYYY hh:mm A',
          'DD-MM-YYYY HH:mm',
          'DD-MM-YYYY hh:mm A',
          'DD/MM/YYYY',
          'DD-MM-YYYY',
          'MM/DD/YYYY',
          'MM-DD-YYYY',
          'YYYY-MM-DD',
          'YYYY/MM/DD',
          'HH:mm',
          'hh:mm A',
          'YYYY-MM-DD HH:mm:ss'
        ];

        momentValue = moment(value, formats, true);

        // If strict parsing fails, try flexible parsing
        if (!momentValue.isValid()) {
          momentValue = moment(value);
        }
      }

      if (momentValue.isValid()) {
        this.selectedDate = momentValue;
        this.selectedDay = momentValue.date();
        this.selectedMonth = momentValue.month() + 1;
        this.selectedYear = momentValue.year();
        this.viewingMonth = this.selectedMonth;
        this.viewingYear = this.selectedYear;

        if (this.timer) {
          this.selectedHour = this.timeFormat === '12-hour' ?
            (momentValue.hour() === 0 ? 12 : momentValue.hour() > 12 ? momentValue.hour() - 12 : momentValue.hour()) :
            momentValue.hour();
          this.selectedMinute = momentValue.minute();
          this.selectedPeriod = momentValue.hour() >= 12 ? 'PM' : 'AM';
        }
      }
    } else {
      this.selectedDate = null;
      this.selectedDay = null;
      this.selectedMonth = null;
      this.selectedYear = null;
      this.selectedHour = null;
      this.selectedMinute = null;
      this.selectedPeriod = 'AM';
    }
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }

  // Event handlers
  onDropdownOpenChange(isOpen: boolean): void {
    if (isOpen) {
      this.onOpen.emit();
      // Reset to date view when opening
      this.showMonthSelection = false;
      this.showYearSelection = false;

      // Always reset to selected date's month/year or current month/year when opening
      if (this.selectedDate && this.selectedDate.isValid()) {
        // If there's a selected date, show that month/year
        this.viewingMonth = this.selectedDate.month() + 1;
        this.viewingYear = this.selectedDate.year();
      } else {
        // If no selected date, show current month/year
        this.viewingMonth = moment().month() + 1;
        this.viewingYear = moment().year();
      }
    }
  }

  // Date selection methods
  onDaySelect(day: number, dropdown?: any): void {
    if (this.disabled || this.isDayDisabled(day)) return;

    this.selectedDay = day;
    this.selectedMonth = this.viewingMonth;
    this.selectedYear = this.viewingYear;
    this.onTouched();

    // If timer is enabled and NO time has been selected at all, set default time values
    if (this.timer && this.selectedHour === null && this.selectedMinute === null) {
      this.setDefaultValidTime();
    }

    this.onDateChange.emit({ day, month: this.viewingMonth, year: this.viewingYear });
    this.updateSelectedDate();

    // Update time validation when date changes
    if (this.timer) {
      this.updateTimeValidation();
    }

    if (!this.timer) {
      // If timer is disabled, complete selection and close dropdown
      this.emitValue();
      dropdown?.close();
    } else {
      // For timer mode, check if we can emit (need time too)
      this.checkAndEmitValue();
    }
  }

  // Enhanced day selection that handles previous/next month dates
  onDayObjSelect(dayObj: { day: number; isCurrentMonth: boolean; isPrevMonth: boolean; isNextMonth: boolean }, dropdown?: any): void {
    if (this.disabled) return;

    let targetMonth = this.viewingMonth;
    let targetYear = this.viewingYear;

    // Calculate the correct month and year for the selected date
    if (dayObj.isPrevMonth) {
      if (this.viewingMonth === 1) {
        const newYear = this.viewingYear - 1;
        const minYear = this.years[0]; // First year in the array
        if (newYear >= minYear) {
          targetMonth = 12;
          targetYear = newYear;
        } else {
          return; // Don't allow selection beyond valid year range
        }
      } else {
        targetMonth = this.viewingMonth - 1;
      }
    } else if (dayObj.isNextMonth) {
      if (this.viewingMonth === 12) {
        const newYear = this.viewingYear + 1;
        const maxYear = this.years[this.years.length - 1]; // Last year in the array
        if (newYear <= maxYear) {
          targetMonth = 1;
          targetYear = newYear;
        } else {
          return; // Don't allow selection beyond valid year range
        }
      } else {
        targetMonth = this.viewingMonth + 1;
      }
    }

    // Check if the target date is disabled
    if (this.isDayObjDisabled(dayObj)) return;

    this.selectedDay = dayObj.day;
    this.selectedMonth = targetMonth;
    this.selectedYear = targetYear;
    this.onTouched();

    // If selecting from previous/next month, update the viewing month to match
    if (!dayObj.isCurrentMonth) {
      this.viewingMonth = targetMonth;
      this.viewingYear = targetYear;
    }

    // If timer is enabled and NO time has been selected at all, set default time values
    if (this.timer && this.selectedHour === null && this.selectedMinute === null) {
      this.setDefaultValidTime();
    }

    this.onDateChange.emit({ day: dayObj.day, month: targetMonth, year: targetYear });
    this.updateSelectedDate();

    // Update time validation when date changes
    if (this.timer) {
      this.updateTimeValidation();
    }

    if (!this.timer) {
      // If timer is disabled, complete selection and close dropdown
      this.emitValue();
      dropdown?.close();
    } else {
      // For timer mode, check if we can emit (need time too)
      this.checkAndEmitValue();
    }
  }

  onMonthSelect(month: number): void {
    this.viewingMonth = month;
    this.showMonthSelection = false;
  }

  onYearSelect(year: number): void {
    if (this.disabled || this.isYearDisabled(year)) return;

    this.viewingYear = year;
    this.showYearSelection = false;
  }

  // Time selection methods
  onHourChange(hour: number | null): void {
    if (this.disabled) return;
    this.selectedHour = hour;
    this.onTouched();

    // Update minute validation when hour changes
    this.updateTimeValidation();

    this.updateSelectedDate();
    this.onTimeChange.emit({
      hour: hour || 0,
      minute: this.selectedMinute || 0,
      period: this.timeFormat === '12-hour' ? this.selectedPeriod : undefined
    });
    this.checkAndEmitValue();
  }

  onMinuteChange(minute: number | null): void {
    if (this.disabled) return;
    this.selectedMinute = minute;
    this.onTouched();
    this.updateSelectedDate();
    this.onTimeChange.emit({
      hour: this.selectedHour || 0,
      minute: minute || 0,
      period: this.timeFormat === '12-hour' ? this.selectedPeriod : undefined
    });
    this.checkAndEmitValue();
  }

  onPeriodChange(period: string): void {
    if (this.disabled) return;
    this.selectedPeriod = period;
    this.onTouched();

    // Update hour and minute validation when period changes
    this.updateTimeValidation();

    this.updateSelectedDate();
    this.onTimeChange.emit({
      hour: this.selectedHour || 0,
      minute: this.selectedMinute || 0,
      period
    });
    this.checkAndEmitValue();
  }

  // Toggle between AM and PM
  togglePeriod(): void {
    if (this.disabled) return;
    const newPeriod = this.selectedPeriod === 'AM' ? 'PM' : 'AM';
    this.onPeriodChange(newPeriod);
  }

  // Handle hour input keydown for quick selection
  onHourKeydown(event: KeyboardEvent): void {
    if (this.disabled) return;

    const key = event.key;

    // Only handle Enter key to confirm selection
    // Let ng-select handle all number input for proper search functionality
    if (key === 'Enter' && this.selectedHour !== null) {
      // Close dropdown on Enter
      event.preventDefault();
    }
    // For all other keys (including numbers), let ng-select handle the search
  }

  // Handle minute input keydown for quick selection
  onMinuteKeydown(event: KeyboardEvent): void {
    if (this.disabled) return;

    const key = event.key;

    // Only handle Enter key to confirm selection
    // Let ng-select handle all number input for proper search functionality
    if (key === 'Enter' && this.selectedMinute !== null) {
      // Close dropdown on Enter
      event.preventDefault();
    }
    // For all other keys (including numbers), let ng-select handle the search
  }

  // Check if all required fields are selected and emit value
  private checkAndEmitValue(): void {
    if (this.isTimeOnlyMode()) {
      // For time-only mode, just need time
      if (this.selectedHour !== null && this.selectedMinute !== null) {
        this.updateSelectedDateForTimeOnly();
        // Validate the time-only selection
        if (this.selectedDate && this.isDateTimeValid(this.selectedDate)) {
          this.emitValue();
        }
      }
    } else if (this.timer) {
      // For date-time mode, need both date and time
      if (this.selectedDay && this.selectedMonth && this.selectedYear &&
          this.selectedHour !== null && this.selectedMinute !== null) {
        // Create a temporary date-time to validate
        const tempDateTime = moment()
          .year(this.selectedYear)
          .month(this.selectedMonth - 1)
          .date(this.selectedDay)
          .hour(this.timeFormat === '12-hour' ? this.convertTo24Hour(this.selectedHour, this.selectedPeriod) : this.selectedHour)
          .minute(this.selectedMinute)
          .second(0);

        // Only emit if the date-time combination is valid
        if (this.isDateTimeValid(tempDateTime)) {
          this.emitValue();
        }
      }
    } else {
      // For date-only mode, just need date
      if (this.selectedDay && this.selectedMonth && this.selectedYear) {
        this.emitValue();
      }
    }
  }

  // Update selected date for time-only mode (use current date)
  private updateSelectedDateForTimeOnly(): void {
    if (this.isTimeOnlyMode() && this.selectedHour !== null && this.selectedMinute !== null) {
      let hour = 0;
      let minute = this.selectedMinute;

      if (this.timeFormat === '12-hour') {
        hour = this.selectedHour === 12 ?
          (this.selectedPeriod === 'AM' ? 0 : 12) :
          (this.selectedPeriod === 'PM' ? this.selectedHour + 12 : this.selectedHour);
      } else {
        hour = this.selectedHour;
      }

      // For time-only mode, use current date but with selected time
      this.selectedDate = moment()
        .hour(hour)
        .minute(minute)
        .second(0)
        .millisecond(0);
    }
  }

  // Navigation methods
  previousMonth(): void {
    if (this.isPreviousMonthDisabled()) return;

    if (this.viewingMonth === 1) {
      // Check if going to previous year would exceed the minimum year
      const newYear = this.viewingYear - 1;
      const minYear = this.years[0]; // First year in the array
      if (newYear >= minYear) {
        this.viewingMonth = 12;
        this.viewingYear = newYear;
      }
    } else {
      this.viewingMonth--;
    }
  }

  nextMonth(): void {
    if (this.isNextMonthDisabled()) return;

    if (this.viewingMonth === 12) {
      // Check if going to next year would exceed the maximum year
      const newYear = this.viewingYear + 1;
      const maxYear = this.years[this.years.length - 1]; // Last year in the array
      if (newYear <= maxYear) {
        this.viewingMonth = 1;
        this.viewingYear = newYear;
      }
    } else {
      this.viewingMonth++;
    }
  }

  // Year navigation methods for month selection view
  previousYear(): void {
    if (this.isPreviousYearDisabled()) return;

    const newYear = this.viewingYear - 1;
    const minYear = this.years[0];
    if (newYear >= minYear) {
      this.viewingYear = newYear;
    }
  }

  nextYear(): void {
    if (this.isNextYearDisabled()) return;

    const newYear = this.viewingYear + 1;
    const maxYear = this.years[this.years.length - 1];
    if (newYear <= maxYear) {
      this.viewingYear = newYear;
    }
  }

  // Year navigation validation for month selection view
  isPreviousYearDisabled(): boolean {
    const newYear = this.viewingYear - 1;
    const minYear = this.years[0];
    return newYear < minYear || this.isYearDisabled(newYear);
  }

  isNextYearDisabled(): boolean {
    const newYear = this.viewingYear + 1;
    const maxYear = this.years[this.years.length - 1];
    return newYear > maxYear || this.isYearDisabled(newYear);
  }

  toggleMonthSelection(): void {
    this.showMonthSelection = !this.showMonthSelection;
    this.showYearSelection = false;
  }

  toggleYearSelection(): void {
    this.showYearSelection = !this.showYearSelection;
    this.showMonthSelection = false;

    // When opening year selection, set the page to show relevant year
    if (this.showYearSelection) {
      this.setYearPageForTargetYear();
    }
  }

  // Year pagination methods
  initializeYearPagination(): void {
    this.setYearPageForTargetYear();
  }

  setYearPageForTargetYear(): void {
    // Determine target year: viewing year, selected date year, or current year
    const targetYear = this.viewingYear ||
                      (this.selectedDate ? this.selectedDate.year() : moment().year());

    // Find which page contains the target year
    const yearIndex = this.years.indexOf(targetYear);
    if (yearIndex !== -1) {
      this.currentYearPage = Math.floor(yearIndex / this.yearsPerPage);
    } else {
      // If target year not found, default to current year page
      const currentYear = moment().year();
      const currentYearIndex = this.years.indexOf(currentYear);
      this.currentYearPage = currentYearIndex !== -1 ? Math.floor(currentYearIndex / this.yearsPerPage) : 0;
    }

    this.updateDisplayedYears();
  }

  updateDisplayedYears(): void {
    const startIndex = this.currentYearPage * this.yearsPerPage;
    const endIndex = Math.min(startIndex + this.yearsPerPage, this.years.length);
    this.displayedYears = this.years.slice(startIndex, endIndex);
  }

  // Year navigation methods
  canNavigateToPreviousYearPage(): boolean {
    return this.currentYearPage > 0;
  }

  canNavigateToNextYearPage(): boolean {
    const nextPageStartIndex = (this.currentYearPage + 1) * this.yearsPerPage;
    return nextPageStartIndex < this.years.length;
  }

  navigateToPreviousYearPage(): void {
    if (this.canNavigateToPreviousYearPage()) {
      this.currentYearPage--;
      this.updateDisplayedYears();
    }
  }

  navigateToNextYearPage(): void {
    if (this.canNavigateToNextYearPage()) {
      this.currentYearPage++;
      this.updateDisplayedYears();

      // Safety check: if no years are displayed after navigation, go back
      if (this.displayedYears.length === 0) {
        this.currentYearPage--;
        this.updateDisplayedYears();
      }
    }
  }

  // Get current year range for display
  getCurrentYearRange(): string {
    if (this.displayedYears.length === 0) return '';
    const firstYear = this.displayedYears[0];
    const lastYear = this.displayedYears[this.displayedYears.length - 1];
    return `${firstYear} - ${lastYear}`;
  }

  // Exit year selection and return to date view
  exitYearSelection(): void {
    this.showYearSelection = false;
    this.showMonthSelection = false;
  }

  // Smart navigation disabling for year pages
  isPreviousYearPageDisabled(): boolean {
    if (!this.canNavigateToPreviousYearPage()) return true;

    // Check if all years in the previous page would be disabled
    const previousPageIndex = this.currentYearPage - 1;
    const startIndex = previousPageIndex * this.yearsPerPage;
    const endIndex = startIndex + this.yearsPerPage;
    const previousPageYears = this.years.slice(startIndex, endIndex);

    // If all years in the previous page are disabled, disable the navigation
    return previousPageYears.every(year => this.isYearDisabled(year));
  }

  isNextYearPageDisabled(): boolean {
    if (!this.canNavigateToNextYearPage()) return true;

    // Check if all years in the next page would be disabled
    const nextPageIndex = this.currentYearPage + 1;
    const startIndex = nextPageIndex * this.yearsPerPage;
    const endIndex = Math.min(startIndex + this.yearsPerPage, this.years.length);
    const nextPageYears = this.years.slice(startIndex, endIndex);

    // If no years exist in next page or all years are disabled, disable navigation
    return nextPageYears.length === 0 || nextPageYears.every(year => this.isYearDisabled(year));
  }

  // Utility methods
  updateSelectedDate(): void {
    if (this.selectedDay && this.selectedMonth && this.selectedYear) {
      let hour = 0;
      let minute = 0;

      if (this.timer && this.selectedHour !== null && this.selectedMinute !== null) {
        if (this.timeFormat === '12-hour') {
          hour = this.selectedHour === 12 ?
            (this.selectedPeriod === 'AM' ? 0 : 12) :
            (this.selectedPeriod === 'PM' ? this.selectedHour + 12 : this.selectedHour);
        } else {
          hour = this.selectedHour;
        }
        minute = this.selectedMinute;
      }

      const tempDate = moment()
        .year(this.selectedYear)
        .month(this.selectedMonth - 1)
        .date(this.selectedDay)
        .hour(hour)
        .minute(minute)
        .second(0)
        .millisecond(0);

      // Validate the date-time combination
      if (this.timer && (this.minDate || this.maxDate)) {
        if (!this.isDateTimeValid(tempDate)) {
          // If the current time selection is invalid, reset time to valid values
          this.resetToValidTime();
          return; // Exit early, resetToValidTime will call updateSelectedDate again
        }
      }

      this.selectedDate = tempDate;
    }
  }

  emitValue(): void {
    if (this.selectedDate) {
      let value: string | moment.Moment;

      if (this.outputFormat === 'moment') {
        // Set locale for moment object
        value = this.selectedDate.clone().locale(this.locale);
      } else {
        // Format as string with appropriate format and locale
        const momentWithLocale = this.selectedDate.clone().locale(this.locale);

        if (!this.timer) {
          // Date-only mode: use custom output format
          value = momentWithLocale.format(this.outputDateFormat);
        } else if (this.isTimeOnlyMode()) {
          // Time-only mode: use display format
          value = momentWithLocale.format(this.displayFormat);
        } else {
          // Date-time mode: use outputDateFormat if it includes time, otherwise use display format
          if (this.outputDateFormat.includes('HH') || this.outputDateFormat.includes('hh') ||
              this.outputDateFormat.includes('mm') || this.outputDateFormat.includes('ss')) {
            value = momentWithLocale.format(this.outputDateFormat);
          } else {
            value = momentWithLocale.format(this.displayFormat);
          }
        }
      }

      this.onChange(value);
      this.onDateTimeChange.emit(value);
      this.onSave.emit(value);
    } else {
      this.onChange(null);
      this.onDateTimeChange.emit(null as any);
      this.onSave.emit(null as any);
    }
  }

  // Validation methods
  isDayDisabled(day: number): boolean {
    if (!this.minDate && !this.maxDate) return false;

    const checkDate = moment()
      .year(this.viewingYear)
      .month(this.viewingMonth - 1)
      .date(day);

    if (this.minDate) {
      const min = this.parseMinMaxDate(this.minDate);
      if (checkDate.isBefore(min, 'day')) return true;
    }

    if (this.maxDate) {
      const max = this.parseMinMaxDate(this.maxDate);
      if (checkDate.isAfter(max, 'day')) return true;
    }

    return false;
  }

  isMonthDisabled(month: number): boolean {
    if (!this.minDate && !this.maxDate) return false;

    const checkDate = moment()
      .year(this.viewingYear)
      .month(month - 1)
      .date(1);

    if (this.minDate) {
      const min = this.parseMinMaxDate(this.minDate);
      if (checkDate.isBefore(min, 'month')) return true;
    }

    if (this.maxDate) {
      const max = this.parseMinMaxDate(this.maxDate);
      if (checkDate.isAfter(max, 'month')) return true;
    }

    return false;
  }

  isYearDisabled(year: number): boolean {
    if (!this.minDate && !this.maxDate) return false;

    const checkDate = moment()
      .year(year)
      .month(0) // January
      .date(1);

    if (this.minDate) {
      const min = this.parseMinMaxDate(this.minDate);
      if (checkDate.isBefore(min, 'year')) return true;
    }

    if (this.maxDate) {
      const max = this.parseMinMaxDate(this.maxDate);
      if (checkDate.isAfter(max, 'year')) return true;
    }

    return false;
  }

  // Navigation validation methods
  isPreviousMonthDisabled(): boolean {
    if (!this.minDate && !this.maxDate) return false;

    let prevMonth = this.viewingMonth - 1;
    let prevYear = this.viewingYear;

    if (prevMonth === 0) {
      prevMonth = 12;
      prevYear--;
    }

    // Check if the entire previous month is disabled
    return this.isEntireMonthDisabled(prevMonth, prevYear);
  }

  isNextMonthDisabled(): boolean {
    if (!this.minDate && !this.maxDate) return false;

    let nextMonth = this.viewingMonth + 1;
    let nextYear = this.viewingYear;

    if (nextMonth === 13) {
      nextMonth = 1;
      nextYear++;
    }

    // Check if the entire next month is disabled
    return this.isEntireMonthDisabled(nextMonth, nextYear);
  }

  // Helper method to check if entire month is disabled
  private isEntireMonthDisabled(month: number, year: number): boolean {
    if (!this.minDate && !this.maxDate) return false;

    const firstDayOfMonth = moment().year(year).month(month - 1).date(1);
    const lastDayOfMonth = moment().year(year).month(month - 1).endOf('month');

    if (this.minDate) {
      const min = this.parseMinMaxDate(this.minDate);
      // If the last day of the month is before min date, entire month is disabled
      if (lastDayOfMonth.isBefore(min, 'day')) return true;
    }

    if (this.maxDate) {
      const max = this.parseMinMaxDate(this.maxDate);
      // If the first day of the month is after max date, entire month is disabled
      if (firstDayOfMonth.isAfter(max, 'day')) return true;
    }

    return false;
  }

  // Helper method to parse min/max date with optional time
  private parseMinMaxDate(dateStr: string): moment.Moment {
    // Try parsing with time first (DD-MM-YYYY HH:mm)
    let parsed = moment(dateStr, 'DD-MM-YYYY HH:mm', true);

    if (!parsed.isValid()) {
      // Fall back to date only (DD-MM-YYYY)
      parsed = moment(dateStr, 'DD-MM-YYYY', true);

      if (!parsed.isValid()) {
        // If still invalid, return current moment as fallback
        console.warn(`Invalid date format: ${dateStr}. Expected DD-MM-YYYY or DD-MM-YYYY HH:mm`);
        return moment();
      }
    }

    return parsed;
  }

  // Check if a specific date-time combination is valid
  private isDateTimeValid(date: moment.Moment): boolean {
    if (!this.minDate && !this.maxDate) return true;

    if (this.minDate) {
      const min = this.parseMinMaxDate(this.minDate);
      if (date.isBefore(min)) return false;
    }

    if (this.maxDate) {
      const max = this.parseMinMaxDate(this.maxDate);
      if (date.isAfter(max)) return false;
    }

    return true;
  }

  // Time validation methods for date-time picker
  isHourDisabled(hour: number): boolean {
    if (!this.timer || (!this.minDate && !this.maxDate)) return false;
    if (!this.selectedDay || !this.selectedMonth || !this.selectedYear) return false;

    // Check if this hour would create an invalid date-time
    const testDateTime = moment()
      .year(this.selectedYear)
      .month(this.selectedMonth - 1)
      .date(this.selectedDay)
      .hour(this.timeFormat === '12-hour' ? this.convertTo24Hour(hour, this.selectedPeriod) : hour)
      .minute(this.selectedMinute || 0)
      .second(0);

    return !this.isDateTimeValid(testDateTime);
  }

  isMinuteDisabled(minute: number): boolean {
    if (!this.timer || (!this.minDate && !this.maxDate)) return false;
    if (!this.selectedDay || !this.selectedMonth || !this.selectedYear || this.selectedHour === null) return false;

    // Check if this minute would create an invalid date-time
    const testDateTime = moment()
      .year(this.selectedYear)
      .month(this.selectedMonth - 1)
      .date(this.selectedDay)
      .hour(this.timeFormat === '12-hour' ? this.convertTo24Hour(this.selectedHour, this.selectedPeriod) : this.selectedHour)
      .minute(minute)
      .second(0);

    return !this.isDateTimeValid(testDateTime);
  }

  isPeriodDisabled(period: string): boolean {
    if (!this.timer || this.timeFormat !== '12-hour' || (!this.minDate && !this.maxDate)) return false;
    if (!this.selectedDay || !this.selectedMonth || !this.selectedYear || this.selectedHour === null) return false;

    // Check if this period would create an invalid date-time
    const testDateTime = moment()
      .year(this.selectedYear)
      .month(this.selectedMonth - 1)
      .date(this.selectedDay)
      .hour(this.convertTo24Hour(this.selectedHour, period))
      .minute(this.selectedMinute || 0)
      .second(0);

    return !this.isDateTimeValid(testDateTime);
  }

  // Helper method to convert 12-hour to 24-hour format
  private convertTo24Hour(hour12: number, period: string): number {
    if (period === 'AM') {
      return hour12 === 12 ? 0 : hour12;
    } else {
      return hour12 === 12 ? 12 : hour12 + 12;
    }
  }

  // Update time validation (simplified for HTML select elements)
  updateTimeValidation(): void {
    // No need to update arrays - HTML select handles disabled options directly
  }

  // Set default valid time when no time is selected
  private setDefaultValidTime(): void {
    if (!this.timer) return;

    // Only set default time if we have a selected date
    if (this.selectedDay && this.selectedMonth && this.selectedYear) {
      // Try default times first (12:00 AM for 12-hour, 00:00 for 24-hour)
      let defaultHour = this.timeFormat === '12-hour' ? 12 : 0;
      let defaultPeriod = 'AM';
      let defaultMinute = 0;

      const testDateTime = moment()
        .year(this.selectedYear)
        .month(this.selectedMonth - 1)
        .date(this.selectedDay)
        .hour(this.timeFormat === '12-hour' ? this.convertTo24Hour(defaultHour, defaultPeriod) : defaultHour)
        .minute(defaultMinute)
        .second(0);

      if (this.isDateTimeValid(testDateTime)) {
        // Default time is valid, use it
        this.selectedHour = defaultHour;
        this.selectedMinute = defaultMinute;
        if (this.timeFormat === '12-hour') {
          this.selectedPeriod = defaultPeriod;
        }
        return;
      }

      // Default time is not valid, find the first valid time
      this.findFirstValidTime();
    }
    // If no date is selected, leave time as null to show placeholders
  }

  // Reset time to valid values when current selection is invalid
  private resetToValidTime(): void {
    if (!this.timer || !this.selectedDay || !this.selectedMonth || !this.selectedYear) return;
    this.findFirstValidTime();
  }

  // Find the first valid time combination
  private findFirstValidTime(): void {
    if (!this.selectedDay || !this.selectedMonth || !this.selectedYear) return;

    // Find the first valid time combination
    const hours = this.timeFormat === '12-hour' ? Array.from({length: 12}, (_, i) => i + 1) : Array.from({length: 24}, (_, i) => i);
    const periods = this.timeFormat === '12-hour' ? ['AM', 'PM'] : [''];

    for (const period of periods) {
      for (const hour of hours) {
        for (const minute of this.minutes) {
          const testDateTime = moment()
            .year(this.selectedYear)
            .month(this.selectedMonth - 1)
            .date(this.selectedDay)
            .hour(this.timeFormat === '12-hour' ? this.convertTo24Hour(hour, period) : hour)
            .minute(minute)
            .second(0);

          if (this.isDateTimeValid(testDateTime)) {
            // Found a valid time, set it
            this.selectedHour = hour;
            this.selectedMinute = minute;
            if (this.timeFormat === '12-hour') {
              this.selectedPeriod = period;
            }

            // Update the selected date with valid time
            this.selectedDate = testDateTime;
            return;
          }
        }
      }
    }

    // If no valid time found, clear time selection
    this.selectedHour = null;
    this.selectedMinute = null;
    if (this.timeFormat === '12-hour') {
      this.selectedPeriod = 'AM';
    }
  }

  // Display methods
  getSelectedDisplayValue(): string {
    if (!this.selectedDate) return '';

    const momentWithLocale = this.selectedDate.clone().locale(this.locale);

    if (!this.timer) {
      // Date only - use displayFormat for visual, but could be different from output
      return momentWithLocale.format(this.displayFormat.includes('HH:mm') || this.displayFormat.includes('hh:mm') ?
        this.displayFormat.split(' ')[0] : this.displayFormat);
    }

    // Date and time
    return momentWithLocale.format(this.displayFormat);
  }

  // Check if this is time-only mode (timer=true and displayFormat is time-only)
  isTimeOnlyMode(): boolean {
    return this.timer && (this.displayFormat === 'HH:mm' || this.displayFormat === 'hh:mm A');
  }

  // Format hour display with zero padding for all formats
  formatHourDisplay(hour: number): string {
    // Always use zero padding for both 12-hour and 24-hour formats
    return hour.toString().padStart(2, '0');
  }

  // Format minute display with zero padding
  formatMinuteDisplay(minute: number): string {
    return minute.toString().padStart(2, '0');
  }



  getCurrentMonthName(): string {
    const month = this.months.find(m => m.value === this.viewingMonth);
    return month ? month.fullName : '';
  }

  getDaysInMonth(): number[] {
    const daysInMonth = moment().year(this.viewingYear).month(this.viewingMonth - 1).daysInMonth();
    return Array.from({ length: daysInMonth }, (_, i) => i + 1);
  }

  // Get calendar grid with previous/next month dates
  getCalendarGrid(): { day: number; isCurrentMonth: boolean; isPrevMonth: boolean; isNextMonth: boolean }[] {
    const firstDay = moment().year(this.viewingYear).month(this.viewingMonth - 1).date(1);
    const daysInMonth = firstDay.daysInMonth();

    // Get the weekday of the first day (0=Sunday, 1=Monday, ..., 6=Saturday)
    // Convert to Monday=0, Tuesday=1, ..., Sunday=6 format
    let startWeekday = firstDay.day(); // 0=Sunday, 1=Monday, ..., 6=Saturday
    startWeekday = startWeekday === 0 ? 6 : startWeekday - 1; // Convert to Monday=0 format

    const grid: { day: number; isCurrentMonth: boolean; isPrevMonth: boolean; isNextMonth: boolean }[] = [];

    // Add previous month dates for empty cells
    if (startWeekday > 0) {
      const prevMonth = moment().year(this.viewingYear).month(this.viewingMonth - 1).subtract(1, 'month');
      const prevMonthDays = prevMonth.daysInMonth();

      for (let i = startWeekday - 1; i >= 0; i--) {
        grid.push({
          day: prevMonthDays - i,
          isCurrentMonth: false,
          isPrevMonth: true,
          isNextMonth: false
        });
      }
    }

    // Add all days of the current month
    for (let day = 1; day <= daysInMonth; day++) {
      grid.push({
        day: day,
        isCurrentMonth: true,
        isPrevMonth: false,
        isNextMonth: false
      });
    }

    // Fill remaining cells with next month dates (complete the 6-week grid)
    const totalCells = 42; // 6 weeks × 7 days
    let nextMonthDay = 1;
    while (grid.length < totalCells) {
      grid.push({
        day: nextMonthDay,
        isCurrentMonth: false,
        isPrevMonth: false,
        isNextMonth: true
      });
      nextMonthDay++;
    }

    return grid;
  }

  isToday(day: number): boolean {
    const today = moment();
    return today.year() === this.viewingYear &&
           today.month() === this.viewingMonth - 1 &&
           today.date() === day;
  }

  isCurrentMonth(month: number): boolean {
    const today = moment();
    return today.year() === this.viewingYear &&
           today.month() === month - 1;
  }

  isCurrentYear(year: number): boolean {
    const today = moment();
    return today.year() === year;
  }

  isSelected(day: number): boolean {
    return this.selectedDay === day &&
           this.selectedMonth === this.viewingMonth &&
           this.selectedYear === this.viewingYear;
  }

  // Enhanced selection check for day objects (handles prev/next month)
  isSelectedDayObj(dayObj: { day: number; isCurrentMonth: boolean; isPrevMonth: boolean; isNextMonth: boolean }): boolean {
    if (!this.selectedDay || !this.selectedMonth || !this.selectedYear) return false;

    let targetMonth = this.viewingMonth;
    let targetYear = this.viewingYear;

    if (dayObj.isPrevMonth) {
      if (this.viewingMonth === 1) {
        const newYear = this.viewingYear - 1;
        const minYear = this.years[0]; // First year in the array
        if (newYear >= minYear) {
          targetMonth = 12;
          targetYear = newYear;
        } else {
          return false; // Beyond valid range, can't be selected
        }
      } else {
        targetMonth = this.viewingMonth - 1;
      }
    } else if (dayObj.isNextMonth) {
      if (this.viewingMonth === 12) {
        const newYear = this.viewingYear + 1;
        const maxYear = this.years[this.years.length - 1]; // Last year in the array
        if (newYear <= maxYear) {
          targetMonth = 1;
          targetYear = newYear;
        } else {
          return false; // Beyond valid range, can't be selected
        }
      } else {
        targetMonth = this.viewingMonth + 1;
      }
    }

    return this.selectedDay === dayObj.day &&
           this.selectedMonth === targetMonth &&
           this.selectedYear === targetYear;
  }

  // Enhanced today check for day objects
  isTodayDayObj(dayObj: { day: number; isCurrentMonth: boolean; isPrevMonth: boolean; isNextMonth: boolean }): boolean {
    const today = moment();

    let targetMonth = this.viewingMonth;
    let targetYear = this.viewingYear;

    if (dayObj.isPrevMonth) {
      if (this.viewingMonth === 1) {
        const newYear = this.viewingYear - 1;
        const minYear = this.years[0]; // First year in the array
        if (newYear >= minYear) {
          targetMonth = 12;
          targetYear = newYear;
        } else {
          return false; // Beyond valid range, can't be today
        }
      } else {
        targetMonth = this.viewingMonth - 1;
      }
    } else if (dayObj.isNextMonth) {
      if (this.viewingMonth === 12) {
        const newYear = this.viewingYear + 1;
        const maxYear = this.years[this.years.length - 1]; // Last year in the array
        if (newYear <= maxYear) {
          targetMonth = 1;
          targetYear = newYear;
        } else {
          return false; // Beyond valid range, can't be today
        }
      } else {
        targetMonth = this.viewingMonth + 1;
      }
    }

    return today.year() === targetYear &&
           today.month() === targetMonth - 1 &&
           today.date() === dayObj.day;
  }

  // Enhanced disabled check for day objects
  isDayObjDisabled(dayObj: { day: number; isCurrentMonth: boolean; isPrevMonth: boolean; isNextMonth: boolean }): boolean {
    if (!this.minDate && !this.maxDate) return false;

    let targetMonth = this.viewingMonth;
    let targetYear = this.viewingYear;

    if (dayObj.isPrevMonth) {
      if (this.viewingMonth === 1) {
        const newYear = this.viewingYear - 1;
        const minYear = this.years[0]; // First year in the array
        if (newYear >= minYear) {
          targetMonth = 12;
          targetYear = newYear;
        } else {
          return true; // Beyond valid range, always disabled
        }
      } else {
        targetMonth = this.viewingMonth - 1;
      }
    } else if (dayObj.isNextMonth) {
      if (this.viewingMonth === 12) {
        const newYear = this.viewingYear + 1;
        const maxYear = this.years[this.years.length - 1]; // Last year in the array
        if (newYear <= maxYear) {
          targetMonth = 1;
          targetYear = newYear;
        } else {
          return true; // Beyond valid range, always disabled
        }
      } else {
        targetMonth = this.viewingMonth + 1;
      }
    }

    const checkDate = moment()
      .year(targetYear)
      .month(targetMonth - 1)
      .date(dayObj.day);

    if (this.minDate) {
      const min = this.parseMinMaxDate(this.minDate);
      if (checkDate.isBefore(min, 'day')) return true;
    }

    if (this.maxDate) {
      const max = this.parseMinMaxDate(this.maxDate);
      if (checkDate.isAfter(max, 'day')) return true;
    }

    return false;
  }

  isSelectedMonth(month: number): boolean {
    return this.selectedMonth === month;
  }

  isSelectedYear(year: number): boolean {
    return this.selectedYear === year;
  }



  // Track by functions for *ngFor
  trackByMonth(_: number, month: any): number {
    return month.value;
  }

  trackByDay(_: number, day: number): number {
    return day;
  }

  trackByDayObj(_: number, dayObj: { day: number; isCurrentMonth: boolean; isPrevMonth: boolean; isNextMonth: boolean }): string {
    return `${dayObj.day}-${dayObj.isCurrentMonth ? 'current' : dayObj.isPrevMonth ? 'prev' : 'next'}`;
  }

  trackByHour(_: number, hourObj: { value: number; disabled: boolean }): number {
    return hourObj.value;
  }

  trackByMinute(_: number, minuteObj: { value: number; disabled: boolean }): number {
    return minuteObj.value;
  }

  trackByNumber(_: number, item: number): number {
    return item;
  }
}
