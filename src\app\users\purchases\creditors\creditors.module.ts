import { RouterModule, Routes } from "@angular/router";
import { CreditorsComponent } from "./creditors.component";
import { CreditorsListComponent } from "./creditors-list/creditors-list.component";
import { CreditLimitApprovalComponent } from "./credit-limit-approval/credit-limit-approval.component";
import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { SharedModule } from "src/app/shared/shared.module";

const routes: Routes = [
    { path: '', component: CreditorsComponent },
]

@NgModule({
    imports: [
        CommonModule,
        RouterModule.forChild(routes),
        SharedModule.forRoot()
    ],
    declarations: [CreditorsComponent, CreditorsListComponent, CreditLimitApprovalComponent]
})
export class CreditorsModule { }
