import { deserializeAs, serializeAs } from 'cerialize';

export class POImageMapping {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('colors')
    @deserializeAs('colors')
    private _colors: number[];

    @serializeAs('images')
    @deserializeAs('images')
    private _images: any[];

    @serializeAs('tempImg')
    @deserializeAs('tempImg')
    private _tempImg: any[];

    @deserializeAs('poColors')
    private _poColors: any[];

    @deserializeAs('poImages')
    private _poImages: any[];

    @deserializeAs('colorDropdown')
    private _colorDropdown: any[];

    @deserializeAs('sortedColors')
    private _sortedColors: number[];

    constructor() {
        this.colors = [];
        this.images = [];
        this.tempImg = [];
        this.poColors = [];
        this.poImages = [];
        this.colorDropdown = [];
    }


    /**
     * Getter sortedColors
     * @return {number[]}
     */
	public get sortedColors(): number[] {
		return this._sortedColors;
	}

    /**
     * Setter sortedColors
     * @param {number[]} value
     */
	public set sortedColors(value: number[]) {
		this._sortedColors = value;
	}


    /**
     * Getter colorDropdown
     * @return {any[]}
     */
	public get colorDropdown(): any[] {
		return this._colorDropdown;
	}

    /**
     * Setter colorDropdown
     * @param {any[]} value
     */
	public set colorDropdown(value: any[]) {
		this._colorDropdown = value;
	}


    /**
     * Getter poImages
     * @return {any[]}
     */
	public get poImages(): any[] {
		return this._poImages;
	}

    /**
     * Setter poImages
     * @param {any[]} value
     */
	public set poImages(value: any[]) {
		this._poImages = value;
	}


    /**
     * Getter poColors
     * @return {any[]}
     */
	public get poColors(): any[] {
		return this._poColors;
	}

    /**
     * Setter poColors
     * @param {any[]} value
     */
	public set poColors(value: any[]) {
		this._poColors = value;
	}



    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Getter colors
     * @return {number[]}
     */
	public get colors(): number[] {
		return this._colors;
	}

    /**
     * Getter images
     * @return {any[]}
     */
	public get images(): any[] {
		return this._images;
	}

    /**
     * Getter tempImg
     * @return {any[]}
     */
	public get tempImg(): any[] {
		return this._tempImg;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Setter colors
     * @param {number[]} value
     */
	public set colors(value: number[]) {
		this._colors = value;
	}

    /**
     * Setter images
     * @param {any[]} value
     */
	public set images(value: any[]) {
		this._images = value;
	}

    /**
     * Setter tempImg
     * @param {any[]} value
     */
	public set tempImg(value: any[]) {
		this._tempImg = value;
	}



}