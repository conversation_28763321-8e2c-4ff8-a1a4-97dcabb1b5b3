import { serializeAs } from "cerialize";

export class qcChecklistItemPagination {

    @serializeAs('sortOrder')
    private _sortOrder: string;

    @serializeAs('sortColumn')
    private _sortColumn: string;

    // @serializeAs('pageNo')
    private _pageNo: number;

    // @serializeAs('pageSize')
    private _pageSize: string;

    @serializeAs('ids')
    private _ids: number[];

    private _totalData: number;

    private _pagination: any;

    constructor() {
        this.pageNo = 1;
        this.pageSize = "100"
        this.pagination = null;
        this.ids = []
    }


    /**
     * Getter sortOrder
     * @return {string}
     */
	public get sortOrder(): string {
		return this._sortOrder;
	}

    /**
     * Getter sortColumn
     * @return {string}
     */
	public get sortColumn(): string {
		return this._sortColumn;
	}

    /**
     * Getter pageNo
     * @return {number}
     */
	public get pageNo(): number {
		return this._pageNo;
	}

    /**
     * Getter pageSize
     * @return {string}
     */
	public get pageSize(): string {
		return this._pageSize;
	}

    /**
     * Getter ids
     * @return {number[]}
     */
	public get ids(): number[] {
		return this._ids;
	}

    /**
     * Getter totalData
     * @return {number}
     */
	public get totalData(): number {
		return this._totalData;
	}

    /**
     * Getter pagination
     * @return {any}
     */
	public get pagination(): any {
		return this._pagination;
	}

    /**
     * Setter sortOrder
     * @param {string} value
     */
	public set sortOrder(value: string) {
		this._sortOrder = value;
	}

    /**
     * Setter sortColumn
     * @param {string} value
     */
	public set sortColumn(value: string) {
		this._sortColumn = value;
	}

    /**
     * Setter pageNo
     * @param {number} value
     */
	public set pageNo(value: number) {
		this._pageNo = value;
	}

    /**
     * Setter pageSize
     * @param {string} value
     */
	public set pageSize(value: string) {
		this._pageSize = value;
	}

    /**
     * Setter ids
     * @param {number[]} value
     */
	public set ids(value: number[]) {
		this._ids = value;
	}

    /**
     * Setter totalData
     * @param {number} value
     */
	public set totalData(value: number) {
		this._totalData = value;
	}

    /**
     * Setter pagination
     * @param {any} value
     */
	public set pagination(value: any) {
		this._pagination = value;
	}


}