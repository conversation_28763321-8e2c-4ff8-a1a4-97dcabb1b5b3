<div class="page-content page-content-with-pagination-buttons">
  <div class="page-title-wrapper">
    <div class="page-title-left">
      <h4>Average Price</h4>
    </div>
    <div class="page-title-right">
      <!-- <div class="page-title-details">
        <p>Last Calculated on</p>
        <span>06 Aug 2024 • Monday • 9:00 AM</span>
      </div>
      <button class="btn btn-sm btn-primary btn-icon-text" data-bs-toggle="modal" data-bs-target="#calculateNowModal">
        <i class="th th-outline-calculator"></i>Calculate Now
      </button> -->
      <button (click)="onRefresh()" class="btn btn-sm btn-icon btn-outline-white" ngbTooltip="Refresh" placement="left"
        container="body" triggers="hover">
        <i class="th th-outline-refresh-2"></i>
      </button>
    </div>
  </div>
  <div class="content-area">
    <div class="page-filters">
      <div class="page-filters-left">
        <div class="form-group form-group-sm filter-search">
          <div class="form-group-icon-start">
            <i class="th th-outline-search-normal-1 icon-broder "></i>
            <input (change)="onChangeFilter('search', $event)"
              [ngModel]="paginationRequest().searchByItemName" type="text" class="form-control"
              placeholder="Search by name">
          </div>
        </div>
        <div class="form-group form-group-sm">
          <input mask="separator.2" thousandSeparator="" (change)="onChangeFilter('avgPriceFrom', $event)"
            [ngModel]="paginationRequest().avgPriceFrom" type="text" class="form-control"
            placeholder="Avg. Price From">
        </div>
        <div class="form-group form-group-sm">
          <input mask="separator.2" thousandSeparator="" (change)="onChangeFilter('avgPriceTo', $event)"
            [ngModel]="paginationRequest().avgPriceTo" type="text" class="form-control"
            placeholder="Avg. Price To">
        </div>
        <div class="form-group theme-ngselect form-group-sm">
          <ng-select (change)="onChangeFilter('containerId', $event)" class="" placeholder="Container" [multiple]="false"
            [clearable]="false" [ngModel]="paginationRequest().containerId"
            [items]="containerList()" bindLabel="containerName" bindValue="id">
          </ng-select>
        </div>
      </div>
      <div class="page-filters-right">
        <div class="dropdown export-dropdown">
          <button
            [disabled]="utilsService.isEmptyObjectOrNullUndefined(averagePriceList())"
            type="button" class="btn btn-sm btn-outline-white dropdown-toggle" data-bs-toggle="dropdown"
            aria-expanded="false">
            Export
          </button>
          <ul class="dropdown-menu">
            <li><a (click)="exportExcel()" class="dropdown-item">Excel</a></li>
          </ul>
        </div>
      </div>

    </div>
    <div class="card card-theme card-table-sticky2">
      <div class="card-body p-0">
        <div class="table-responsive">
          <table class="table-theme table-hover table table-bordered table-sticky">
            <thead class="border-less">
              <tr>
                @for(item of averagePrice; track $index; let i = $index) {
                <th [class]="item.class">
                  <div (click)="$event.stopPropagation()" *ngIf="i === 0" class="checkbox checkbox-primary checkbox-small">
                    <input type="checkbox" id="tbl-checkbox" class="material-inputs filled-in" />
                    <label for="tbl-checkbox"></label>
                  </div>
                  <div [class]="i === 0 ? 'mt-3' : ''" [innerHTML]="item.displayName"></div>
                </th>
                }
              </tr>
            </thead>
            <tbody>
              @for(item of averagePriceList(); track $index; let i = $index) {
              <tr>
                <td class="tbl-user">
                  <div class="tbl-user-checkbox-srno">
                    <div class="checkbox checkbox-primary checkbox-small">
                      <input type="checkbox" id="tbl-checkbox2" class="material-inputs filled-in" />
                      <label for="tbl-checkbox2"></label>
                    </div>
                    <span>{{i + 1 | padNum}}</span>
                    <div class="tbl-user-wrapper">
                      <div class="tbl-user-image">
                        <img [src]="utilsService.imgPath + item.item.formattedName" alt="Valamji" />
                      </div>
                      <div class="tbl-user-text">
                        <p>{{item.item.displayName}}</p>
                      </div>
                    </div>
                  </div>
                </td>
                <td>{{item.item.skuId}}</td>
                <td>{{item.containerName}}</td>
                <td>₹{{item.previousSp || 0}}</td>
                <td>
                  <div class="form-group form-group-sm form-group-200">
                    <input [(ngModel)]="item.newSP" [maxlength]="10" mask="separator.0" thousandSeparator="" type="text"
                      class="form-control">
                  </div>
                </td>
                <td>{{item.currentSp || 0}}</td>
                <td>-</td>
                <td>
                  <div class="form-group form-group-sm form-group-200">
                    <input [(ngModel)]="item.newSPStaff" [maxlength]="10" mask="separator.0" thousandSeparator="" type="text"
                      class="form-control">
                  </div>
                </td>
                <td>{{item.G_totalQty || 0}}</td>
                <td>₹{{item.H_totalAmountOldNewWithoutGST || 0}}</td>
                <td class="fw-600 text-primary">₹{{item.newAvgPriceWithoutGST || 0}}</td>
                <td class="fw-600 text-primary">₹{{item.newAvgPriceWithGST || 0}}</td>
                <td>{{item.averageOldStock.averageOldStock || 0}}</td>
                <td>₹{{item.averageOldStock.averagePriceWithoutGST || 0}}</td>
                <td>₹{{item.averageOldStock.totalAmountOldStockWithoutGST || 0}}</td>
                <td>{{item.D_newQty || 0}}</td>
                <td>₹{{item.E_WithoutGST || 0}}</td>
                <td>₹{{item.F_totalAmountNewWithoutGST || 0}}</td>
              </tr>
              }
              @if(utilsService.isEmptyObjectOrNullUndefined(averagePriceList())) {
                <tr>
                  <td colspan="20" class="text-center">
                    <span class="truncate">{{utilsService.serverVariableService.STRING_WHEN_NO_RECORDS_FOUND}}</span>
                  </td>
                </tr>
              }
            </tbody>
          </table>
        </div>
      </div>
    </div>
    <div class='bottombar-wrapper bottom-fixed'>
      <div class='bottombar-container'>
        <div class='bottombar-left'>
          <button
            [disabled]="utilsService.isEmptyObjectOrNullUndefined(averagePriceList())"
            (click)="onApproveModalOpen()" type="button" class="btn btn-primary btn-icon-text btn-sm">
            <i class="th th-outline-tick-circle"></i>
            Approve
          </button>
          
          <button (click)="onOpenRollBackModal()"
            [disabled]="utilsService.isEmptyObjectOrNullUndefined(averagePriceList())"
            type="button" class="btn btn-outline-secondary-two btn-icon-text btn-sm">
            <i class="th th-outline-close-circle"></i>
            Roll Back
          </button>
          
          <button
            [disabled]="utilsService.isEmptyObjectOrNullUndefined(averagePriceList())"
            (click)="onSaveSpStaffApprove('save')" type="button" class="btn btn-primary btn-icon-text btn-sm ms-3">
            <i class="th th-outline-tick-circle"></i>
            Save
          </button>
          
          <button (click)="onClearStaffB()"
            [disabled]="utilsService.isEmptyObjectOrNullUndefined(averagePriceList())"
            type="button" class="btn btn-outline-secondary-two btn-icon-text btn-sm">
            <i class="th th-outline-close-circle"></i>
            Reset
          </button>

        </div>
        <div class='bottombar-right'>

        </div>
      </div>
    </div>
  </div>
</div>

<!-- ----------------------------------------------------------------------- -->
<!--                        Calculate Now Modal Start                        -->
<!-- ----------------------------------------------------------------------- -->
<div class="modal modal-theme modal-confirmation modal-warning fade" id="calculateNowModal" tabindex="-1"
  aria-labelledby="calculateNowModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
            class='th th-close'></i></button>
      </div>
      <div class="modal-body">
        <div class="modal-confirmation-container">
          <div class="modal-confirmation-icon">
            <i class="th th-bold-dollar-circle"></i>
          </div>
          <div class="modal-confirmation-content">
            <h5>Are You Sure?</h5>
            <p>Do you want to calculate<br />
              average price right now!</p>
          </div>
        </div>
        <div class="modal-button-group modal-full-width-btn">
          <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">No</button>
          <button type="button" class="btn btn-primary btn-icon-text"> <i class="th th-outline-tick-circle"></i>
            Yes, Calculate</button>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                         Calculate Now Modal End                         -->
<!-- ----------------------------------------------------------------------- -->

<!-- ----------------------------------------------------------------------- -->
<!--                           Approve Modal Start                           -->
<!-- ----------------------------------------------------------------------- -->
<div class="modal modal-theme modal-confirmation modal-approve fade" id="approveAvgPriceModal" tabindex="-1"
  aria-labelledby="approveAvgPriceModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
            class='th th-close'></i></button>
      </div>
      <div class="modal-body">

        <div class="modal-confirmation-container">
          <div class="modal-confirmation-icon">
            <i class="th th-check"></i>
          </div>
          <div class="modal-confirmation-content">
            <h5>Are You Sure?</h5>
            <p>Do you want to approve new average rate ?</p>
          </div>
        </div>
        <div class="modal-button-group modal-full-width-btn">
          <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">No</button>
          <button (click)="onSaveSpStaffApprove('approve')" type="button" class="btn btn-primary btn-icon-text"> <i class="th th-outline-tick-circle"></i>
            Yes, Approve</button>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                            Approve Modal End                            -->
<!-- ----------------------------------------------------------------------- -->

<!-- ----------------------------------------------------------------------- -->
<!--                          Rollback Modal Start                           -->
<!-- ----------------------------------------------------------------------- -->
<div class="modal modal-theme modal-confirmation modal-warning fade" id="rollBackAvgPriceModal" tabindex="-1"
  aria-labelledby="rollBackAvgPriceModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
            class='th th-close'></i></button>
      </div>
      <div class="modal-body">
        <div class="modal-confirmation-container">
          <div class="modal-confirmation-icon">
            <i class="th th-bold-back-square"></i>
          </div>
          <div class="modal-confirmation-content">
            <h5>Are You Sure?</h5>
            <p>Do you want to rollback updated average rate ?</p>
          </div>
        </div>
        <div class="modal-button-group modal-full-width-btn">
          <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">No</button>
          <button (click)="onSaveSpStaffApprove('rollback')" type="button" class="btn btn-primary btn-icon-text"> <i class="th th-outline-tick-circle"></i>
            Yes, Rollback</button>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                           Rollback Modal End                            -->
<!-- ----------------------------------------------------------------------- -->