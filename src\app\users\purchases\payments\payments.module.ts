import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { NewPaymentsComponent } from "./new-payments/new-payments.component";
import { PaymentsComponent } from "./payments.component";
import { CommonModule } from "@angular/common";
import { SharedModule } from "src/app/shared/shared.module";

const routes: Routes = [
    { path: '', component: PaymentsComponent },
    { path: 'new-payments', component: NewPaymentsComponent },
    { path: 'edit-payments/:id', component: NewPaymentsComponent },
    { path: 'view-payment/:viewId', component: NewPaymentsComponent },
];

@NgModule({
    imports: [
        CommonModule,
        RouterModule.forChild(routes),
        SharedModule.forRoot()
    ],
    declarations: [PaymentsComponent, NewPaymentsComponent],
})
export class PaymentsModule { }