<div class="page-content" [pageAccess]="{page: utilsService.enumForPage.ITEM, action: utilsService.enumForPage.VIEW_ITEM, view: true}">
    <div class="page-title-wrapper">
        <div class="page-title-left">
            <h4>Item Master</h4>
        </div>
        <div class="page-title-right">
            <button [pageAccess]="{page: utilsService.enumForPage.ITEM, action: utilsService.enumForPage.ADD_ITEM}"
                (click)="redirectToDetails()" [routerLink]="'/users/inventory/items/new-item'"
                class="btn btn-sm btn-primary btn-icon-text">
                <i class="th th-outline-add-circle"></i>Add New
            </button>
            <button [pageAccess]="{page: utilsService.enumForPage.ITEM, action: utilsService.enumForPage.ADD_ITEM}"
                (click)="redirectToDetails()" [routerLink]="'/users/inventory/items/import-items'"
                class="btn btn-sm btn-outline-white btn-icon-text">
                <i class="bi bi-download"></i>Import Items
            </button>
            <div>
                <div class="dropdown export-dropdown">
                    <button [disabled]="utilsService.isEmptyObjectOrNullUndefined(itemList)" type="button"
                        class="btn btn-sm btn-outline-white dropdown-toggle" data-bs-toggle="dropdown"
                        aria-expanded="false">
                        Export
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" (click)="exportReport(false)">Excel</a></li>
                    </ul>
                </div>
            </div>
            <button (click)="getAllItems()" class="btn btn-sm btn-icon btn-outline-white" ngbTooltip="Refresh"
                placement="left" container="body" triggers="hover">
                <i class="th th-outline-refresh-2"></i>
            </button>
        </div>
    </div>

    <div class="content-area">
        <div class="page-filters">
            <div class="page-filters-left">
                <div class="form-group form-group-sm filter-search1">
                    <div class="form-group-icon-start">
                        <i class="th th-outline-search-normal-1 icon-broder "></i>
                        <input (change)="onSearch($event)" [(ngModel)]="paginationRequest.searchText" type="search"
                            class="form-control" placeholder="Search by name / SKU">
                    </div>
                </div>
                <div class="form-group theme-ngselect form-group-sm theme-ngselect-group-list filter-item-all">
                    <ng-select (change)="onChangeActive()" placeholder="Category" [multiple]="false" [clearable]="true"
                        [items]="flattenedParentCategory" [(ngModel)]="paginationRequest.categoryId"
                        bindLabel="categoryName" bindValue="id">
                        <ng-template ng-option-tmp let-item="item">
                            <span class="fs-13" [title]="item.categoryName" [style.padding-left.px]="item.index * 25"
                                [ngClass]="{'ng-option-child-label' : item.isChild}" [class]="item.className">
                                {{ item.categoryName }}
                            </span>
                        </ng-template>
                    </ng-select>
                </div>
                <!-- <div class="form-group  form-group-sm">
                    <input type="text" class="form-control" placeholder="Price From">
                </div>
                <div class="form-group  form-group-sm">
                    <input type="text" class="form-control" placeholder="Price To">
                </div> -->
                <div class="form-group theme-ngselect form-group-sm filter-group-code-item">
                    <ng-select (change)="onChangeActive()" class="" placeholder="Item Group" [multiple]="false"
                        [clearable]="true" [items]="itemPageDropdown?.itemGroup" bindLabel="label" bindValue="value"
                        [(ngModel)]="paginationRequest.itemGroupId">
                        <ng-template ng-option-tmp let-item="item">
                            <span class="fs-13" [title]="item.label">
                                {{ item.label }}
                            </span>
                        </ng-template>
                    </ng-select>
                </div>
                <div class="form-group theme-ngselect form-group-sm filter-market-type">
                    <ng-select (change)="onChangeActive()" class="" placeholder="Market Types" [multiple]="true"
                        [clearable]="true" [items]="itemPageDropdown?.marketType" bindLabel="label" bindValue="value"
                        [(ngModel)]="paginationRequest.marketTypes">
                        <ng-template ng-multi-label-tmp let-items="items" let-clear="clear">
                            <div class="ng-value" *ngFor="let item of items | slice:0:1">
                                <span class="ng-value-label fs-12">{{item['label']}}</span>
                                <span class="ng-value-icon right" (click)="clear(item)">×</span>
                            </div>
                            <div class="ng-value" *ngIf="items.length > 1">
                                <span class="ng-value-label fs-12">{{items.length - 1}} more...</span>
                            </div>
                        </ng-template>
                    </ng-select>
                </div>
                <div class="form-group theme-ngselect form-group-sm filter-group-code-item">
                    <ng-select (change)="onChangeActive()" class="" placeholder="Group Code" [multiple]="false"
                        [clearable]="true" [items]="itemPageDropdown?.groupCodes" bindLabel="groupCodeName" bindValue="id"
                        [(ngModel)]="paginationRequest.groupCodeId">
                        <ng-template ng-option-tmp let-item="item">
                            <span class="fs-13" [title]="item.groupCodeName">
                                {{ item.groupCodeName }}
                            </span>
                        </ng-template>
                    </ng-select>
                </div>
                <div class="form-group theme-ngselect form-group-sm">
                    <ng-select (change)="onChangeActive()" class="" placeholder="Status" [multiple]="false"
                        [clearable]="true" [items]="activeInactiveStatus" bindLabel="label" bindValue="value"
                        [(ngModel)]="paginationRequest.isActive">
                    </ng-select>
                </div>
            </div>
            <div class="page-filters-right">
                <div class="filter-tab">
                    <ul class="list-unstyled">
                        <li class="filter-item" (click)="changeView(enumForView.LIST)"
                            [ngClass]="{'active': selectedView === enumForView.LIST}">
                            <i class="bi bi-list-ul"></i> List

                        </li>
                        <li class="filter-item" (click)="changeView(enumForView.GRID)"
                            [ngClass]="{'active': selectedView === enumForView.GRID}">
                            <i class="th th-outline-grid-8"></i> Grid
                        </li>
                    </ul>
                </div>
                <ng-container *ngIf="selectedView === enumForView.LIST">
                    <app-table-column-filter-dropdown-new [allHeaderArr]="allHeaderArr" [columnArr]="columnArr"
                        (saveCol)="saveCol()" (checkIfAllSelected)="checkIfAllSelected()" (getHeader)="getHeader()"
                        [closeHeaderDropdown]="closeHeaderDropdown"/>
                </ng-container>

            </div>
        </div>
        <div class="card card-theme card-table-sticky ">
            <div class="card-body p-0">
                <div class="table-responsive">
                    <ng-container *ngIf="selectedView === enumForView.LIST">
                        <cdk-virtual-scroll-viewport class="item-viewport" [itemSize]="45" minBufferPx="250" maxBufferPx="500">
                            <table class="table-theme table-hover table table-bordered">
                                <thead class="border-less">
                                    <tr>
                                        <th class="text-center">
                                            <div (click)="$event.stopPropagation()" class="checkbox checkbox-primary checkbox-small">
                                                <input [disabled]="utilsService.isEmptyObjectOrNullUndefined(itemList)"
                                                    (change)="selectAllItem()" [(ngModel)]="paginationRequest.flagForSelectAll" type="checkbox"
                                                    id="tbl-checkbox" class="material-inputs filled-in" />
                                                <label for="tbl-checkbox"></label>
                                            </div>
                                        </th>
                                        <ng-container *ngFor="let th of optionsArray; index as i">
                                            <th *ngIf="th.show" class="{{th.class}}"
                                                [ngClass]="{
                                                'sorting-asc': paginationRequest?.sortColumn == th.keyName && paginationRequest?.sortOrder === enumForSortOrder.A,
                                                'sorting-desc': paginationRequest?.sortColumn == th.keyName && paginationRequest?.sortOrder === enumForSortOrder.D}"
                                                (click)="!utilsService.isNullUndefinedOrBlank(th.keyName) ? onSortTH(th.keyName) : $event.preventDefault()">
                                                {{th.displayName}}
                                            </th>
                                        </ng-container>
                                        <th class="tbl-switch" *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.EDIT_ITEM])">
                                            Status</th>
                                        <th class="text-center"
                                            *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.EDIT_MASTER, this.utilsService.enumForPage.DELETE_MASTER])">
                                            Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr *cdkVirtualFor="let item of itemList; index as i; trackBy: trackBy">
                                        <td class="item-checkbox">
                                            <div class="tbl-user-checkbox-srno">
                                                <div class="checkbox checkbox-primary checkbox-small">
                                                    <input (change)="selectUnselectItem(item.id, i, item.isSelected)"
                                                        [(ngModel)]="item.isSelected" type="checkbox" id="tbl-checkbox2-{{i}}"
                                                        class="material-inputs filled-in">
                                                    <label for="tbl-checkbox2-{{i}}"></label>
                                                </div>
                                            </div>
                                        </td>
                                        <ng-container *ngFor="let column of columnArr">
                                            <td class="tbl-user" *ngIf="column.show" [ngClass]="{'w-25': column.key == 'displayName'}">
                                                <ng-container [ngSwitch]="column.key">
                        
                                                    <!-- displayName -->
                                                    <ng-container *ngSwitchCase="'displayName'">
                                                        <div class="tbl-user-checkbox-srno ">
                                                            <!-- <div class="checkbox checkbox-primary checkbox-small">
                                                                                                                                                        <input
                                                                                                                                                            (change)="selectUnselectItem(item.id, i, item.isSelected)"
                                                                                                                                                            [(ngModel)]="item.isSelected" type="checkbox"
                                                                                                                                                            id="tbl-checkbox2-{{i}}" class="material-inputs filled-in">
                                                                                                                                                        <label for="tbl-checkbox2-{{i}}"></label>
                                                                                                                                                    </div>
                                                                                                                                                    <span>{{i + 1}}</span> -->
                                                            <div class="tbl-user-wrapper ">
                                                                <div class="tbl-user-image" (click)="openImagePreviewModal(item)">
                                                                    <img *ngIf="item.image" [src]="utilsService.imgPath + item.image"
                                                                        alt="Valamji" />
                                                                    <ng-container *ngIf="!item.image">{{
                                                                        item.displayName?.charAt(0).toUpperCase()
                                                                        }}</ng-container>
                                                                </div>
                        
                                                                <div (click)="redirectToItemDetails(item.id)"
                                                                    class="tbl-user-text cursor-pointer">
                                                                    <p>{{item.displayName}}</p>
                                                                </div>
                                                                <div class="tbl-user-new" *ngIf="item.isSaveAsDraft">
                                                                    <p class="text-danger" *ngIf="item.isSaveAsDraft">Draft</p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </ng-container>
                        
                                                    <!-- skuId -->
                                                    <ng-container *ngSwitchCase="'skuId'">
                                                        {{item.skuId}}
                                                    </ng-container>
                        
                                                    <!-- itemCarton  -->
                                                    <ng-container *ngSwitchCase="'carton'">
                                                        -
                                                    </ng-container>
                        
                                                    <!-- looseQty  -->
                                                    <ng-container *ngSwitchCase="'looseQty'">
                                                        -
                                                    </ng-container>
                        
                                                    <!-- TotalQty  -->
                                                    <ng-container *ngSwitchCase="'TotalQty'">
                                                        -
                                                    </ng-container>

                                                    <!-- categoryName  -->
                                                    <ng-container *ngSwitchCase="'categoryName'">
                                                        {{item?.categoryName ? item?.categoryName : '-'}}
                                                    </ng-container>
                        
                                                    <!-- LevelQty  -->
                                                    <ng-container *ngSwitchCase="'levelItemMaps'">
                                                        <div [innerHTML]="item.joinsLevels">
                                                        </div>
                                                    </ng-container>
                        
                                                    <!-- Type  -->
                                                    <ng-container *ngSwitchCase="'itemsMarketTypes'">
                                                        <div>
                                                            <span class="w-100 d-block" *ngFor="let v of item.marketType">
                                                                {{!utilsService.isEmptyObjectOrNullUndefined(v) ? v : ''}}
                                                            </span>
                                                        </div>
                                                    </ng-container>

                                                    <!-- Group Code  -->
                                                    <ng-container *ngSwitchCase="'groupCodeName'">
                                                        <div class="tbl-description">
                                                            {{item?.groupCodeName ? item?.groupCodeName : '-'}}
                                                        </div>
                                                    </ng-container>
                        
                                                    <!-- AveragePrice  -->
                                                    <ng-container *ngSwitchCase="'AveragePrice'">
                                                        -
                                                    </ng-container>
                        
                                                    <!-- AveragePrice (GST) -->
                                                    <ng-container *ngSwitchCase="'AveragePriceG'">
                                                        -
                                                    </ng-container>
                        
                                                    <!-- Sales Price/Piece -->
                                                    <ng-container *ngSwitchCase="'itemPrice'">
                                                        <div class="form-group  form-group-sm mb-0">
                                                            <input
                                                                [disabled]="!utilsService.checkPageAccess([this.utilsService.enumForPage.EDIT_SALES_PRICE])"
                                                                (change)="onChangeSalesPrice(true, item, $event)" [maxlength]="15" mask="separator.3"
                                                                thousandSeparator="" [ngModel]="item.itemPrice" type="text"
                                                                class="form-control" placeholder="Enter">
                                                        </div>
                                                    </ng-container>
                        
                                                    <!-- Sales Price/Carton -->
                                                    <ng-container *ngSwitchCase="'itemCarton'">
                                                        <div class="form-group  form-group-sm mb-0">
                                                            <input
                                                                [disabled]="!utilsService.checkPageAccess([this.utilsService.enumForPage.EDIT_SALES_PRICE])"
                                                                (change)="onChangeSalesPrice(false, item, $event)" [maxlength]="15" mask="separator.3"
                                                                thousandSeparator="" [ngModel]="item.itemCarton" type="text"
                                                                class="form-control" placeholder="Enter">
                                                        </div>
                                                    </ng-container>
                                                </ng-container>
                                            </td>
                                        </ng-container>
                                        <td class="tbl-switch" *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.EDIT_ITEM])">
                                            <div class="switch-box">
                                                <label class="switch" htmlFor="switch-{{i}}">
                                                    <input type="checkbox" id='switch-{{i}}' [(ngModel)]="item.isActive"
                                                        (change)="onChangeStatus(item, item.isActive, i)" />
                                                    <div class="slider round"></div>
                                                </label>
                                            </div>
                                        </td>
                                        <td class="tbl-action"
                                            *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.EDIT_ITEM, this.utilsService.enumForPage.DELETE_ITEM])">
                                            <div class="tbl-action-group">
                                                <button (click)="redirectToDetails()"
                                                    [pageAccess]="{page: this.utilsService.enumForPage.ITEM, action: this.utilsService.enumForPage.EDIT_ITEM}"
                                                    class="btn btn-xs btn-light-primary btn-icon"
                                                    [routerLink]="'/users/inventory/items/edit-item/' + item.id" ngbTooltip="Edit"
                                                    placement="bottom" container="body" triggers="hover"> <i
                                                        class="th th-outline-edit"></i></button>
                        
                                                <!-- <button class="btn btn-xs btn-light-warning btn-icon" data-bs-toggle="modal"
                                                                                                                                                data-bs-target="#stockModal" ngbTooltip="Adjust Stock" placement="bottom"
                                                                                                                                                container="body" triggers="hover"> <i
                                                                                                                                                    class="th th-outline-box-add"></i></button> -->
                        
                                                <button class="btn btn-xs btn-light-danger btn-icon"
                                                    [pageAccess]="{page: this.utilsService.enumForPage.ITEM, action: this.utilsService.enumForPage.DELETE_ITEM}"
                                                    (click)="openDeleteItem(item)" ngbTooltip="Delete" placement="left" container="body"
                                                    triggers="hover">
                                                    <i class="th th-outline-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr *ngIf="utilsService.isEmptyObjectOrNullUndefined(itemList)">
                                        <td colspan="20" class="text-center">
                                            <span>{{utilsService.serverVariableService.STRING_WHEN_NO_RECORDS_FOUND}}</span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </cdk-virtual-scroll-viewport>
                    </ng-container>
                
                    <ng-container *ngIf="selectedView === enumForView.GRID">
                        <cdk-virtual-scroll-viewport class="item-viewport" [itemSize]="45" appendOnly>
                            <div class="item-grid-view">
                                <div class="item-grid-row" *ngIf="!this.utilsService.isEmptyObjectOrNullUndefined(itemList)">
                                    <div class="item-grid-col" *cdkVirtualFor="let item of itemList; index as i; trackBy: trackBy">
                                        <div class="product-card card">
                                            <div class="card-image">
                                                <div class="product-checkbox  checkbox checkbox-primary checkbox-small">
                                                    <input [(ngModel)]="item.isSelected"
                                                        (change)="selectUnselectItem(item.id, i, item.isSelected)" type="checkbox"
                                                        id="checkbox2-grid-{{i}}" class="material-inputs filled-in" />
                                                    <label for="checkbox2-grid-{{i}}"> </label>
                                                </div>
                                                <!-- <img src="assets/images/product-card.png" alt="valamji"> -->
                                                <img (click)="redirectToItemDetails(item.id)" *ngIf="item.image"
                                                    [src]="utilsService.imgPath + item.image" alt="Valamji" />
                                                <!-- <img *ngIf="!item.image" src="assets/images/product-card.png" alt="valamji"> -->
                                            </div>
                                            <div class="card-body">
                                                <div class="title">
                                                    <a>
                                                        <h6>
                                                            <span>{{i + 1}}.</span> {{item.displayName}}
                                                        </h6>
                                                    </a>
                                                    <p>{{item.skuId}}</p>
                                                </div>
                                                <div class="details">
                                                    <ul class="list-unstyled">
                                                        <li>
                                                            <label>Stock In Hand</label>
                                                            <span>-</span>
                                                        </li>
                                                        <li>
                                                            <label>Pur- Breach Qty</label>
                                                            <span>-</span>
                                                        </li>
                                                        <li>
                                                            <label>Selling Price</label>
                                                            <span>{{item.itemPrice ? item.itemPrice : '-'}}</span>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div *ngIf="this.utilsService.isEmptyObjectOrNullUndefined(itemList)">
                                    <app-no-record />
                                </div>
                            </div>
                        </cdk-virtual-scroll-viewport>                        
                    </ng-container>
                </div>
            </div>
        </div>
        <div class="paginationbox pagination-fixed">
            <app-pagination (pagesizeData)="addPageSizeData($event)" (pageNumber)="pageNumber($event)"
                [page]="paginationRequest.pageNo" [pageSize]="paginationRequest.pageSize"
                [totalData]="paginationRequest.totalData"></app-pagination>
        </div>
    </div>
</div>

<!-- ----------------------------------------------------------------------- -->
<!--                     Add and Stock Edit Forms Modal Start                      -->
<!-- ----------------------------------------------------------------------- -->
<div class=" modal modal-theme fade" id="stockModal" tabindex="-1" aria-labelledby="stockModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">Adjust Stock</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
                        class='th th-close'></i></button>
            </div>
            <div class="modal-body">

                <div class="product-details-container">
                    <div class="product-details-left">
                        <a href="" data-bs-toggle="modal" data-bs-target="#productmodalSliderModal">
                            <div class="product-image">
                                <img src="assets/images/dummy-product.png" alt="valamji">
                            </div>
                        </a>
                        <div class="product-title">
                            <p class="text-black">Plastic Water Bottle</p>
                            <p>SKU #58545854585</p>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div class="form-group required">
                            <label class="form-label">Date</label>
                            <div class="form-group-icon-end">
                                <i class="th th-outline-calendar-1"></i>
                                <input type="text" class="form-control" placeholder="07/08/2024">
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="form-group theme-ngselect required">
                            <label class="form-label">Reason</label>
                            <ng-select class="" placeholder="Select reason" [multiple]="false" [clearable]="false"
                                [items]="Option" bindLabel="name" bindValue="id" [(ngModel)]="selectedOption">
                            </ng-select>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="form-group required">
                            <label class="form-label">Note</label>
                            <textarea class="form-control" rows="3" placeholder="Enter note here"></textarea>
                        </div>
                    </div>
                </div>
                <div class="modal-table">
                    <div class="inner-title-wrapper">
                        <div class="inner-title-left">
                            <div class="inner-title-text">
                                <h6 class="text-primary">Stock Adjustment</h6>
                            </div>
                        </div>
                        <div class="inner-title-rigth">
                        </div>
                    </div>
                    <div class="card card-theme  ">
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table-theme table-hover table table-bordered ">
                                    <thead class="border-less">
                                        <tr>
                                            <th class="d-flex align-items-center gap-2">
                                                Warehouse Name
                                            </th>
                                            <th>Aisle</th>
                                            <th>Rack</th>
                                            <th>Available Qty</th>
                                            <th>Actual Qty</th>
                                            <th>Adjusted Qty</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr *ngFor="let item of [1,2,3]">
                                            <td class="tbl-form-group-borderless">
                                                <div class="form-group theme-ngselect ">
                                                    <ng-select class="" placeholder="Warehouse 1" [multiple]="false"
                                                        [clearable]="false" [items]="Option" bindLabel="name"
                                                        bindValue="id" [(ngModel)]="selectedOption" appendTo="body">
                                                    </ng-select>
                                                </div>
                                            </td>
                                            <td class="tbl-form-group-borderless">
                                                <div class="form-group theme-ngselect ">
                                                    <ng-select class="" placeholder="A-05" [multiple]="false"
                                                        [clearable]="false" [items]="Option" bindLabel="name"
                                                        bindValue="id" [(ngModel)]="selectedOption" appendTo="body">
                                                    </ng-select>
                                                </div>
                                            </td>
                                            <td class="tbl-form-group-borderless">
                                                <div class="form-group theme-ngselect ">
                                                    <ng-select class="" placeholder="A-05-15" [multiple]="false"
                                                        [clearable]="false" [items]="Option" bindLabel="name"
                                                        bindValue="id" [(ngModel)]="selectedOption" appendTo="body">
                                                    </ng-select>
                                                </div>
                                            </td>
                                            <td>10</td>
                                            <td>12</td>
                                            <td>2</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal-footer">
                <div class="modal-footer-group full-width-btn">
                    <button type="button" class="btn btn-primary btn-icon-text"> <i
                            class="th th-outline-tick-circle"></i> Save</button>
                    <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                     Add and Stock Edit Forms Modal End                      -->
<!-- ----------------------------------------------------------------------- -->

<!-- ----------------------------------------------------------------------- -->
<!--                       product modal Slider Modal Start                            -->
<!-- ----------------------------------------------------------------------- -->
<div class="product-slider-modal modal modal-theme fade" id="productmodalSliderModal" tabindex="-1"
    aria-labelledby="productmodalSliderModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-header-details">
                    <div class="">
                        <h5 class="modal-title image-name"
                            [title]="currentItem?.originalName ? currentItem?.originalName : currentItem?.formattedName">
                            <div>
                                {{ currentItem?.originalName ? currentItem?.originalName :
                                currentItem?.formattedName }}
                            </div>
                        </h5>
                        <p>{{itemObj.skuId}}</p>
                    </div>
                    <button (click)="onCopy()" [disabled]="isCopied"
                        [copyText]="!currentItem?.isMediaLinks ? (utilsService.imgPath + currentItem?.formattedName) : currentItem?.formattedName"
                        type="button" class="btn btn-sm btn-light-white btn-icon-text">
                        <i class="th th-outline-tick-circle"></i> {{isCopied ? 'Copied' : 'Copy'}}
                    </button>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
                        class='th th-close'></i></button>
            </div>
            <div class="modal-body">
                <div class="product-modal-slider-wrapper">
                    <ngx-slick-carousel class="carousel product-modal-slider custom-slick-arrow " #galleryOne="slick-carousel"
                        (afterChange)="onSlideChange($event)" [config]="ProductModalImageSlider">
                        <div ngxSlickItem class="product-modal-item" *ngFor="let item of itemImagesList; index as i">

                            <ng-container *ngIf="item?.originalName && !item?.isMediaLinks">
                                <div class="product-image"
                                    *ngIf="utilsService.isImage(item.formattedName ? item.formattedName : item.originalName)">
                                    <img loading="lazy"
                                        [src]="item.formattedName ? (utilsService.imgPath + item.formattedName) : 'assets/images/avatar-default.svg'"
                                        alt="valamji">
                                </div>
                                <div class="product-image"
                                    *ngIf="utilsService.isMedia(item.formattedName ? item.formattedName : item.originalName)">
                                    <video class="" controls [autoplay]="false">
                                        <source loading="lazy" [src]="utilsService.imgPath + item.formattedName" type="video/mp4">
                                    </video>
                                </div>
                            </ng-container>

                            <ng-container *ngIf="item.formattedName && item?.isMediaLinks">
                                <div class="product-image" *ngIf="utilsService.isValidYouTubeLink(item.formattedName)">
                                    <iframe width="100%" height="550" [src]="item.linkUrl | safe" frameborder="0"
                                        allowfullscreen></iframe>
                                </div>
                                <div class="product-image" *ngIf="utilsService.isImage(item.formattedName)">
                                    <img loading="lazy"
                                        [src]="item.formattedName ? (item.formattedName) : 'assets/images/avatar-default.svg'"
                                        alt="valamji">
                                </div>
                                <!-- <div class="product-image">
                                    <iframe src="https://www.amazon.com" width="800" height="600">
                                        <meta http-equiv="X-Frame-Options" content="deny">
                                    </iframe>
                                    <iframe [src]="'https://www.amazon.in/vdp/16a3e3d022f142f0a571f2323efb7192' | safe" width="100%"
                                        height="550" sandbox="allow-same-origin allow-scripts allow-popups allow-forms">
                                    </iframe>
                                </div> -->
                            </ng-container>
                            
                        </div>
                    </ngx-slick-carousel>
                    <div class="product-modal-counter">
                        <span class="current-slide">{{selectedImageIndex}}</span> / <span
                            class="total-slides">{{itemImagesList?.length}}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                    product modal Slider Modal                             -->
<!-- ----------------------------------------------------------------------- -->

<!-- ---------------------------- Item Delete Modal ----------------------------- -->
<div class="modal modal-theme modal-confirmation modal-reject fade" id="deleteItemModal" tabindex="-1"
    aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
                        class='th th-close'></i></button>
            </div>
            <div class="modal-body">

                <div class="modal-confirmation-container">
                    <div class="modal-confirmation-icon">
                        <i class="th th-bold-trash"></i>
                    </div>
                    <div class="modal-confirmation-content">
                        <h5>Are You Sure?</h5>
                        <p>You want to Delete <b>{{itemObj.displayName}}</b> Item Master.</p>
                    </div>
                </div>
                <div class="modal-button-group modal-full-width-btn">
                    <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
                    <button (click)="deleteItem()" type="button" class="btn btn-primary btn-icon-text"> <i
                            class="th th-outline-tick-circle"></i> Delete</button>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- ---------------------------- Item Delete Modal ----------------------------- -->