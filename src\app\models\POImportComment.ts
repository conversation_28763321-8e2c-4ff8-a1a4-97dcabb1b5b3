import { deserializeAs, serializeAs } from 'cerialize';

export class POImportComment {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('userId')
    @deserializeAs('userId')
    private _userId: number;

    // @serializeAs('isAdd')
    @deserializeAs('isAdd')
    private _isAdd: boolean;

    // @serializeAs('isEdit')
    @deserializeAs('isEdit')
    private _isEdit: boolean;

    @serializeAs('comments')
    @deserializeAs('comments')
    private _comments: string;

    // @serializeAs('username')
    @deserializeAs('username')
    private _username: string;

    // @serializeAs('profile')
    @deserializeAs('profile')
    private _profile: string;

    @deserializeAs('originalProfileUrl')
    private _originalProfileUrl: string;

    @deserializeAs('profileUrl')
    private _profileUrl: string;
    
    @deserializeAs('name')
    private _name: string;

    constructor() {
        this.isAdd = false;
        this.isEdit = false;
    }


    /**
     * Getter name
     * @return {string}
     */
	public get name(): string {
		return this._name;
	}

    /**
     * Setter name
     * @param {string} value
     */
	public set name(value: string) {
		this._name = value;
	}


    /**
     * Getter originalProfileUrl
     * @return {string}
     */
	public get originalProfileUrl(): string {
		return this._originalProfileUrl;
	}

    /**
     * Getter profileUrl
     * @return {string}
     */
	public get profileUrl(): string {
		return this._profileUrl;
	}

    /**
     * Setter originalProfileUrl
     * @param {string} value
     */
	public set originalProfileUrl(value: string) {
		this._originalProfileUrl = value;
	}

    /**
     * Setter profileUrl
     * @param {string} value
     */
	public set profileUrl(value: string) {
		this._profileUrl = value;
	}



    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Getter userId
     * @return {number}
     */
	public get userId(): number {
		return this._userId;
	}

    /**
     * Getter isAdd
     * @return {boolean}
     */
	public get isAdd(): boolean {
		return this._isAdd;
	}

    /**
     * Getter isEdit
     * @return {boolean}
     */
	public get isEdit(): boolean {
		return this._isEdit;
	}

    /**
     * Getter comments
     * @return {string}
     */
	public get comments(): string {
		return this._comments;
	}

    /**
     * Getter username
     * @return {string}
     */
	public get username(): string {
		return this._username;
	}

    /**
     * Getter profile
     * @return {string}
     */
	public get profile(): string {
		return this._profile;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Setter userId
     * @param {number} value
     */
	public set userId(value: number) {
		this._userId = value;
	}

    /**
     * Setter isAdd
     * @param {boolean} value
     */
	public set isAdd(value: boolean) {
		this._isAdd = value;
	}

    /**
     * Setter isEdit
     * @param {boolean} value
     */
	public set isEdit(value: boolean) {
		this._isEdit = value;
	}

    /**
     * Setter comments
     * @param {string} value
     */
	public set comments(value: string) {
		this._comments = value;
	}

    /**
     * Setter username
     * @param {string} value
     */
	public set username(value: string) {
		this._username = value;
	}

    /**
     * Setter profile
     * @param {string} value
     */
	public set profile(value: string) {
		this._profile = value;
	}
   
    
}