import { deserializeAs, serializeAs } from 'cerialize';

export class AssociateItem {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('image')
    @deserializeAs('image')
    private _image: string;

    @serializeAs('itemName')
    @deserializeAs('itemName')
    private _itemName: string;

    @serializeAs('displayName')
    @deserializeAs('displayName')
    private _displayName: string;

    @serializeAs('hsnCode')
    @deserializeAs('hsnCode')
    private _hsnCode: string;

    @serializeAs('skuId')
    @deserializeAs('skuId')
    private _skuId: string;

    @serializeAs('isActive')
    @deserializeAs('isActive')
    private _isActive: boolean;

    @serializeAs('isAdd')
    @deserializeAs('isAdd')
    private _isAdd: boolean;

    @serializeAs('selectedValue')
    @deserializeAs('selectedValue')
    private _selectedValue: number;

    @deserializeAs('itemDropdown')
    private _itemDropdown: any[];

    constructor() {
        this.isActive = false;
        this.isAdd = false;
        this.itemDropdown = [];
    }


    /**
     * Getter itemDropdown
     * @return {any[]}
     */
	public get itemDropdown(): any[] {
		return this._itemDropdown;
	}

    /**
     * Setter itemDropdown
     * @param {any[]} value
     */
	public set itemDropdown(value: any[]) {
		this._itemDropdown = value;
	}


    /**
     * Getter skuId
     * @return {string}
     */
	public get skuId(): string {
		return this._skuId;
	}

    /**
     * Setter skuId
     * @param {string} value
     */
	public set skuId(value: string) {
		this._skuId = value;
	}


    /**
     * Getter selectedValue
     * @return {number}
     */
	public get selectedValue(): number {
		return this._selectedValue;
	}

    /**
     * Setter selectedValue
     * @param {number} value
     */
	public set selectedValue(value: number) {
		this._selectedValue = value;
	}
    

    /**
     * Getter isAdd
     * @return {boolean}
     */
	public get isAdd(): boolean {
		return this._isAdd;
	}

    /**
     * Setter isAdd
     * @param {boolean} value
     */
	public set isAdd(value: boolean) {
		this._isAdd = value;
	}


    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Getter image
     * @return {string}
     */
	public get image(): string {
		return this._image;
	}

    /**
     * Getter itemName
     * @return {string}
     */
	public get itemName(): string {
		return this._itemName;
	}

    /**
     * Getter displayName
     * @return {string}
     */
	public get displayName(): string {
		return this._displayName;
	}

    /**
     * Getter hsnCode
     * @return {string}
     */
	public get hsnCode(): string {
		return this._hsnCode;
	}

    /**
     * Getter isActive
     * @return {boolean}
     */
	public get isActive(): boolean {
		return this._isActive;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Setter image
     * @param {string} value
     */
	public set image(value: string) {
		this._image = value;
	}

    /**
     * Setter itemName
     * @param {string} value
     */
	public set itemName(value: string) {
		this._itemName = value;
	}

    /**
     * Setter displayName
     * @param {string} value
     */
	public set displayName(value: string) {
		this._displayName = value;
	}

    /**
     * Setter hsnCode
     * @param {string} value
     */
	public set hsnCode(value: string) {
		this._hsnCode = value;
	}

    /**
     * Setter isActive
     * @param {boolean} value
     */
	public set isActive(value: boolean) {
		this._isActive = value;
	}

}