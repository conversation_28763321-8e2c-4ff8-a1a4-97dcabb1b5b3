export const DRAFT_HEADER = [
    {
        index: 0,
        key: 'item',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Item Details',
        keyName: '',
    },
    {
        index: 1,
        key: 'purchaseBreachQty',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Purchase <br/> Breach Qty',
        keyName: '',
    },
    {
        index: 2,
        key: 'toFrom',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Season Date',
        keyName: '',
    },
    {
        index: 3,
        key: 'advanceDate',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Advance PO  <br/> Date',
        keyName: '',
    },
    {
        index: 4,
        key: 4,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Average Price <br/> (Surat)',
        keyName: '',
    },
    {
        index: 5,
        key: 'salesPrice',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Sale Price',
        keyName: '',
    },
    // {
    //     'index': 6, 'key': 'marka', 'show': true, 'isSelected': true, 'class': '', 'headerClass': 'tbl-bg-light-three', 'header': '-', 'displayName': 'Marka', 'keyName': '',
    // },
    {
        index: 7,
        key: 'color',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Color',
        keyName: '',
    },
    {
        index: 8,
        key: 'note',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Notes',
        keyName: '',
    },
    {
        index: 9,
        key: 'englishComment',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'English <br/> Comment',
        keyName: '',
    },
    {
        index: 10,
        key: 'chinaComment',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'China  <br/> Comment',
        keyName: '',
    },
    // {
    //     'index': 11, 'key': 11, 'show': true, 'isSelected': true, 'class': '', 'headerClass': 'tbl-bg-light-three', 'header': '-', 'displayName': 'Item Status', 'keyName': '',
    // },
    {
        index: 12,
        key: 'expectedDeliveryDate',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Expected <br/> Delivery Date',
        keyName: '',
    },
    {
        index: 13,
        key: 'poCarton',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: 'A',
        displayName: 'PO Carton',
        keyName: '',
    },
    {
        index: 14,
        key: 'pricePerCarton',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: 'B',
        displayName: 'PCS/Carton',
        keyName: '',
    },
    {
        index: 15,
        key: 'totalPcsQty',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: 'C = A*B',
        displayName: 'Total Qty',
        keyName: '',
    },
    {
        index: 16,
        key: 'pricePerItem',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: 'D',
        displayName: 'Price /PCS <br/> (RMB)',
        keyName: '',
    },
    {
        index: 17,
        key: 'totalAmount',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: 'E = C * D',
        displayName: 'Total Amount <br/> (RMB)',
        keyName: '',
    },
    {
        index: 18,
        key: 'expDeliveryCost',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: 'F',
        displayName: 'Expected <br/> Delivery <br/> (RMB)',
        keyName: '',
    },
    {
        index: 19,
        key: 'totalAmountWithExp',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: 'G = E + F',
        displayName: 'Total Amount <br/> (RMB) + (Ship. Exp.)',
        keyName: '',
    },
    {
        index: 20,
        key: 'conversationRate',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Conv. Rate <br/> (RS)',
        keyName: '',
    },
    {
        index: 21,
        key: 'totalAmountWithExpInINR',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: 'H = G*Conv',
        displayName: 'Total Conv. <br /> Amount (INR)',
        keyName: '',
    },
    {
        index: 22,
        key: 'chinaFinalExpextedCode',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: 'I = H/C',
        displayName: 'China Final <br />  Expected Cost <br /> (Padatar)/PCS',
        keyName: '',
    },
    {
        index: 23,
        key: 'shippingTypes',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Shipping Type',
        keyName: '',
    },
    {
        index: 24,
        key: 'itemDim',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Carton <br /> Dimension',
        keyName: '',
    },
    {
        index: 25,
        key: 'dimAge',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Dimensions <br /> Age',
        keyName: '',
    },
    {
        index: 41,
        key: 'cartonWeight',
        show: true,
        isSelected: true,
        class: '',
        headerClass: '',
        header: 'J2',
        displayName: 'Weight <br/> /Carton (Kg)',
        keyName: '',
    },
    {
        index: 26,
        key: 'CBM/Carton',
        show: true,
        isSelected: true,
        class: 'tbl-bg-secondary',
        headerClass: 'tbl-bg-light-three',
        header: 'J1',
        displayName: 'CBM/Carton',
        keyName: '',
    },
    {
        index: 27,
        key: 'Total CBM',
        show: true,
        isSelected: true,
        class: 'tbl-bg-secondary',
        headerClass: 'tbl-bg-light-three',
        header: 'K1=A*J1',
        displayName: 'Total CBM',
        keyName: '',
    },
    {
        index: 28,
        key: 'CBM Price',
        show: true,
        isSelected: true,
        class: 'tbl-bg-secondary',
        headerClass: 'tbl-bg-light-three',
        header: 'L1',
        displayName: 'CBM Price',
        keyName: '',
    },
    {
        index: 29,
        key: 'TotalCBMExpense',
        show: true,
        isSelected: true,
        class: 'tbl-bg-secondary',
        headerClass: 'tbl-bg-light-three',
        header: 'M1=K1*L1',
        displayName: 'Total CBM <br /> Expense (₹)',
        keyName: '',
    },
    {
        index: 30,
        key: 'ShippingExpense/PCS',
        show: true,
        isSelected: true,
        class: 'tbl-bg-secondary',
        headerClass: 'tbl-bg-light-three',
        header: 'N1=M1/C',
        displayName: 'Shipping <br /> Expense/PCS',
        keyName: '',
    },
    {
        index: 31,
        key: 'transportCharges',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: 'O',
        displayName:
            'Transportation <br /> Charges <br /> [Mumbai to Surat]/Carton',
        keyName: '',
    },
    {
        index: 32,
        key: 'totalTransportationChargesM2S',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: 'P=O*A',
        displayName:
            'Total <br /> Transportation <br /> Charges <br /> [Mumbai to Surat]',
        keyName: '',
    },
    {
        index: 33,
        key: 'transportationChargesM2SperPCS',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: 'Q=P/C',
        displayName: 'Transportation <br /> charges <br /> [Mumbai to Surat] / PCS',
        keyName: '',
    },
    {
        index: 34,
        key: 'totalInsurance',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: 'R=H*0.01',
        displayName: 'Total <br /> Insurance (₹)',
        keyName: '',
    },
    {
        index: 35,
        key: 'insurancePerPcs',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: 'S=R/C',
        displayName: 'Insurance <br /> (₹)/PCS',
        keyName: '',
    },
    {
        index: 36,
        key: 'gstAmtPerPcs',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: 'T=H*30/100*0.18/C',
        displayName: 'GST <br /> Amount/PCS',
        keyName: '',
    },
    {
        index: 37,
        key: 'craneExpense',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: 'U',
        displayName: 'Crane <br /> Expense [Total]',
        keyName: '',
    },
    {
        index: 38,
        key: 'craneExpPcs',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: 'V= U/C',
        displayName: 'Crane <br /> Expense/PCS',
        keyName: '',
    },
    {
        index: 39,
        key: 'totalExp',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: 'W=Q+S+T+V',
        displayName: 'Total Expense',
        keyName: '',
    },
    {
        index: 40,
        key: 'chinaToSuratPadtar',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: 'X=I+N1+W',
        displayName: 'China <br/> To Surat <br/> Final <br/> Price <br/> (Padatar)',
        keyName: '',
    },
    {
        index: 42,
        key: 'totalWeight',
        show: true,
        isSelected: true,
        class: 'tbl-bg-secondary-two',
        headerClass: '',
        header: 'K2=J2*A',
        displayName: 'Total Weight',
        keyName: '',
    },
    {
        index: 43,
        key: 'cartonWeightRu',
        show: true,
        isSelected: true,
        class: 'tbl-bg-secondary-two',
        headerClass: '',
        header: 'L2',
        displayName: 'Cost/ <br />kg (Rs)',
        keyName: '',
    },
    {
        index: 44,
        key: 'totalLoadAmt',
        show: true,
        isSelected: true,
        class: 'tbl-bg-secondary-two',
        headerClass: '',
        header: 'M2=K2*L2',
        displayName: 'Total Load. <br/> AMT [Weight] (₹)',
        keyName: '',
    },
    {
        index: 45,
        key: 'shippingCostperPieceINR',
        show: true,
        isSelected: true,
        class: 'tbl-bg-secondary-two',
        headerClass: '',
        header: 'N2=M2/C',
        displayName: 'Shipping <br/> Cost / Piece (₹)',
        keyName: '',
    },
    {
        index: 46,
        key: 'totalShippingExpWeight',
        show: true,
        isSelected: true,
        class: 'tbl-bg-secondary-two',
        headerClass: '',
        header: 'O2=I+W+N2',
        displayName: 'Total Shipping <br/> Expense / Weight',
        keyName: '',
    },
    {
        index: 47,
        key: 'percentage',
        show: true,
        isSelected: true,
        class: 'tbl-bg-success',
        headerClass: '',
        header: 'J3',
        displayName: 'Percentage - <br/> [%]',
        keyName: '',
    },
    {
        index: 48,
        key: 'totalExpPCSper',
        show: true,
        isSelected: true,
        class: 'tbl-bg-success',
        headerClass: '',
        header: 'K3=I*J3/100',
        displayName: 'Total Expense <br/> / Piece (₹) [%]',
        keyName: '',
    },
    {
        index: 49,
        key: 'totalFinalCostPCSper',
        show: true,
        isSelected: true,
        class: 'tbl-bg-success',
        headerClass: '',
        header: 'L3 = I+W+K3',
        displayName: 'Total Final <br/> Cost / <br /> Piece(₹) [%]',
        keyName: '',
    },
    {
        index: 50,
        key: 50,
        show: true,
        isSelected: true,
        class: 'tbl-bg-danger',
        headerClass: '',
        header: 'J4=I+W',
        displayName: 'Item Amount <br/> (₹) [Fixed]',
        keyName: '',
    },
    // {
    //     index: 51,
    //     key: 51,
    //     show: true,
    //     isSelected: true,
    //     class: 'tbl-bg-danger',
    //     headerClass: '',
    //     header: 'K4=Wrong!',
    //     displayName: 'Total Item <br/> Amount (₹) / <br /> [Fixed]',
    //     keyName: '',
    // },
    {
        index: 52,
        key: 'expensePcs',
        show: true,
        isSelected: true,
        class: 'tbl-bg-primary',
        headerClass: '',
        header: 'J5',
        displayName: 'Expense/PCS',
        keyName: '',
    },
    {
        index: 53,
        key: 'totalItemAmt',
        show: true,
        isSelected: true,
        class: 'tbl-bg-primary',
        headerClass: '',
        header: 'K5=I+W+J5',
        displayName: 'Total Item <br/> Amount (₹)',
        keyName: '',
    },
    {
        index: 54,
        key: 'lastRecord',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Last 3 <br/> Purchase <br/> [Shop No, Price, <br/> Cartons]',
        keyName: '',
    },
    {
        index: 55,
        key: 55,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Inquiry Shop <br/> with price',
        keyName: '',
    },
    {
        index: 56,
        key: 56,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName:
            'Inquiry <br/> (Cus Count) - <br/> [Inquiry <br/> (Total inq. Count)]',
        keyName: '',
    },
    {
        index: 58,
        key: 58,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'GST Tax %',
        keyName: '',
    },
    {
        index: 57,
        key: 57,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'GST Tax <br/> Amount (INR)',
        keyName: '',
    },
    {
        index: 59,
        key: 'extraExpense',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Extra Expense',
        keyName: '',
    },
    {
        index: 60,
        key: 'purchaseRation',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Purchase <br /> Ratio',
        keyName: '',
    },
];

export const PO_CREATED_HEADER = [
    {
        index: 0,
        key: 'item',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Item Details',
        keyName: '',
    },
    {
        index: 1,
        key: 'purchaseBreachQty',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Purchase <br/> Breach Qty',
        keyName: '',
    },
    {
        index: 2,
        key: 'toFrom',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Season Date',
        keyName: '',
    },
    {
        index: 3,
        key: 'advanceDate',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Advance PO  <br/> Date',
        keyName: '',
    },
    {
        index: 4,
        key: 4,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Average Price <br/> (Surat)',
        keyName: '',
    },
    {
        index: 5,
        key: 'salesPrice',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Sale Price',
        keyName: '',
    },
    {
        index: 6,
        key: 'marka',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Marka',
        keyName: '',
    },
    {
        index: 7,
        key: 'color',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Color',
        keyName: '',
    },
    {
        index: 8,
        key: 'note',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Notes',
        keyName: '',
    },
    {
        index: 9,
        key: 'englishComment',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'English <br/> Comment',
        keyName: '',
    },
    {
        index: 10,
        key: 'chinaComment',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'China  <br/> Comment',
        keyName: '',
    },
    {
        index: 61,
        key: 61,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Payment <br/> Status',
        keyName: '',
    },
    // {
    //     'index': 62, 'key': 62, 'show': true, 'isSelected': true, 'class': '', 'headerClass': 'tbl-bg-light-three', 'header': '-', 'displayName': 'Order Date', 'keyName': '',
    // },
    // {
    //     'index': 11, 'key': 11, 'show': true, 'isSelected': true, 'class': '', 'headerClass': 'tbl-bg-light-three', 'header': '-', 'displayName': 'Item Status', 'keyName': '',
    // },
    {
        index: 12,
        key: 'expectedDeliveryDate',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Expected <br/> Delivery Date',
        keyName: '',
    },
    {
        index: 13,
        key: 'poCarton',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: 'A',
        displayName: 'PO Carton',
        keyName: '',
    },
    {
        index: 14,
        key: 'pricePerCarton',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: 'B',
        displayName: 'PCS/Carton',
        keyName: '',
    },
    {
        index: 15,
        key: 'totalPcsQty',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: 'C = A*B',
        displayName: 'Total Qty',
        keyName: '',
    },
    {
        index: 61,
        key: 'pendingQty',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '',
        displayName: 'Pending Qty <br/> (Carton)',
        keyName: '',
    },
    {
        index: 62,
        key: 'totalPendingQty',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '',
        displayName: 'Total Pending <br/> Qty',
        keyName: '',
    },
    {
        index: 16,
        key: 'pricePerItem',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: 'D',
        displayName: 'Price /PCS <br/> (RMB)',
        keyName: '',
    },
    {
        index: 17,
        key: 'totalAmount',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: 'E = C * D',
        displayName: 'Total Amount <br/> (RMB)',
        keyName: '',
    },
    {
        index: 18,
        key: 'expDeliveryCost',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: 'F',
        displayName: 'Expected <br/> Delivery <br/> (RMB)',
        keyName: '',
    },
    {
        index: 19,
        key: 'totalAmountWithExp',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: 'G = E + F',
        displayName: 'Total Amount <br/> (RMB) + (Ship. Exp.)',
        keyName: '',
    },
    {
        index: 20,
        key: 'conversationRate',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Conv. Rate <br/> (RS)',
        keyName: '',
    },
    {
        index: 21,
        key: 'totalAmountWithExpInINR',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: 'H = G*Conv',
        displayName: 'Total Conv. <br /> Amount (INR)',
        keyName: '',
    },
    {
        index: 22,
        key: 'chinaFinalExpextedCode',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: 'I = H/C',
        displayName: 'China Final <br />  Expected Cost <br /> (Padatar)/PCS',
        keyName: '',
    },
    {
        index: 23,
        key: 'shippingTypes',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Shipping Type',
        keyName: '',
    },
    {
        index: 24,
        key: 'itemDim',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Carton <br /> Dimension',
        keyName: '',
    },
    {
        index: 25,
        key: 'dimAge',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Dimensions <br /> Age',
        keyName: '',
    },
    {
        index: 41,
        key: 'cartonWeight',
        show: true,
        isSelected: true,
        class: '',
        headerClass: '',
        header: 'J2',
        displayName: 'Weight <br/> /Carton (Kg)',
        keyName: '',
    },
    {
        index: 26,
        key: 'CBM/Carton',
        show: true,
        isSelected: true,
        class: 'tbl-bg-secondary',
        headerClass: 'tbl-bg-light-three',
        header: 'J1',
        displayName: 'CBM/Carton',
        keyName: '',
    },
    {
        index: 27,
        key: 'Total CBM',
        show: true,
        isSelected: true,
        class: 'tbl-bg-secondary',
        headerClass: 'tbl-bg-light-three',
        header: 'K1=A*J1',
        displayName: 'Total CBM',
        keyName: '',
    },
    {
        index: 28,
        key: 'CBM Price',
        show: true,
        isSelected: true,
        class: 'tbl-bg-secondary',
        headerClass: 'tbl-bg-light-three',
        header: 'L1',
        displayName: 'CBM Price',
        keyName: '',
    },
    {
        index: 29,
        key: 'TotalCBMExpense',
        show: true,
        isSelected: true,
        class: 'tbl-bg-secondary',
        headerClass: 'tbl-bg-light-three',
        header: 'M1=K1*L1',
        displayName: 'Total CBM <br /> Expense (₹)',
        keyName: '',
    },
    {
        index: 30,
        key: 'ShippingExpense/PCS',
        show: true,
        isSelected: true,
        class: 'tbl-bg-secondary',
        headerClass: 'tbl-bg-light-three',
        header: 'N1=M1/C',
        displayName: 'Shipping <br /> Expense/PCS',
        keyName: '',
    },
    {
        index: 31,
        key: 'transportCharges',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: 'O',
        displayName:
            'Transportation <br /> Charges <br /> [Mumbai to Surat]/Carton',
        keyName: '',
    },
    {
        index: 32,
        key: 'totalTransportationChargesM2S',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: 'P=O*A',
        displayName:
            'Total <br /> Transportation <br /> Charges <br /> [Mumbai to Surat]',
        keyName: '',
    },
    {
        index: 33,
        key: 'transportationChargesM2SperPCS',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: 'Q=P/C',
        displayName: 'Transportation <br /> charges <br /> [Mumbai to Surat] / PCS',
        keyName: '',
    },
    {
        index: 34,
        key: 'totalInsurance',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: 'R=H*0.01',
        displayName: 'Total <br /> Insurance (₹)',
        keyName: '',
    },
    {
        index: 35,
        key: 'insurancePerPcs',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: 'S=R/C',
        displayName: 'Insurance <br /> (₹)/PCS',
        keyName: '',
    },
    {
        index: 36,
        key: 'gstAmtPerPcs',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: 'T=H*30/100*0.18/C',
        displayName: 'GST <br /> Amount/PCS',
        keyName: '',
    },
    {
        index: 37,
        key: 'craneExpense',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: 'U',
        displayName: 'Crane <br /> Expense [Total]',
        keyName: '',
    },
    {
        index: 38,
        key: 'craneExpPcs',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: 'V= U/C',
        displayName: 'Crane <br /> Expense/PCS',
        keyName: '',
    },
    {
        index: 39,
        key: 'totalExp',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: 'W=Q+S+T+V',
        displayName: 'Total Expense',
        keyName: '',
    },
    {
        index: 40,
        key: 'chinaToSuratPadtar',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: 'X=I+N1+W',
        displayName: 'China <br/> To Surat <br/> Final <br/> Price <br/> (Padatar)',
        keyName: '',
    },
    {
        index: 42,
        key: 'totalWeight',
        show: true,
        isSelected: true,
        class: 'tbl-bg-secondary-two',
        headerClass: '',
        header: 'K2=J2*A',
        displayName: 'Total Weight',
        keyName: '',
    },
    {
        index: 43,
        key: 'cartonWeightRu',
        show: true,
        isSelected: true,
        class: 'tbl-bg-secondary-two',
        headerClass: '',
        header: 'L2',
        displayName: 'Cost/ <br />kg (Rs)',
        keyName: '',
    },
    {
        index: 44,
        key: 'totalLoadAmt',
        show: true,
        isSelected: true,
        class: 'tbl-bg-secondary-two',
        headerClass: '',
        header: 'M2=K2*L2',
        displayName: 'Total Load. <br/> AMT [Weight] (₹)',
        keyName: '',
    },
    {
        index: 45,
        key: 'shippingCostperPieceINR',
        show: true,
        isSelected: true,
        class: 'tbl-bg-secondary-two',
        headerClass: '',
        header: 'N2=M2/C',
        displayName: 'Shipping <br/> Cost / Piece (₹)',
        keyName: '',
    },
    {
        index: 46,
        key: 'totalShippingExpWeight',
        show: true,
        isSelected: true,
        class: 'tbl-bg-secondary-two',
        headerClass: '',
        header: 'O2=I+W+N2',
        displayName: 'Total Shipping <br/> Expense / Weight',
        keyName: '',
    },
    {
        index: 47,
        key: 'percentage',
        show: true,
        isSelected: true,
        class: 'tbl-bg-success',
        headerClass: '',
        header: 'J3',
        displayName: 'Percentage - <br/> [%]',
        keyName: '',
    },
    {
        index: 48,
        key: 'totalExpPCSper',
        show: true,
        isSelected: true,
        class: 'tbl-bg-success',
        headerClass: '',
        header: 'K3=I*J3/100',
        displayName: 'Total Expense <br/> / Piece (₹) [%]',
        keyName: '',
    },
    {
        index: 49,
        key: 'totalFinalCostPCSper',
        show: true,
        isSelected: true,
        class: 'tbl-bg-success',
        headerClass: '',
        header: 'L3 = I+W+K3',
        displayName: 'Total Final <br/> Cost / <br /> Piece(₹) [%]',
        keyName: '',
    },
    {
        index: 50,
        key: 50,
        show: true,
        isSelected: true,
        class: 'tbl-bg-danger',
        headerClass: '',
        header: 'J4=I+W',
        displayName: 'Item Amount <br/> (₹) [Fixed]',
        keyName: '',
    },
    // {
    //     index: 51,
    //     key: 51,
    //     show: true,
    //     isSelected: true,
    //     class: 'tbl-bg-danger',
    //     headerClass: '',
    //     header: 'K4=Wrong!',
    //     displayName: 'Total Item <br/> Amount (₹) / <br /> [Fixed]',
    //     keyName: '',
    // },
    {
        index: 52,
        key: 'expensePcs',
        show: true,
        isSelected: true,
        class: 'tbl-bg-primary',
        headerClass: '',
        header: 'J5',
        displayName: 'Expense/PCS',
        keyName: '',
    },
    {
        index: 53,
        key: 'totalItemAmt',
        show: true,
        isSelected: true,
        class: 'tbl-bg-primary',
        headerClass: '',
        header: 'K5=I+W+J5',
        displayName: 'Total Item <br/> Amount (₹)',
        keyName: '',
    },
    {
        index: 54,
        key: 'lastRecord',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Last 3 <br/> Purchase <br/> [Shop No, Price, <br/> Cartons]',
        keyName: '',
    },
    {
        index: 55,
        key: 55,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Inquiry Shop <br/> with price',
        keyName: '',
    },
    {
        index: 56,
        key: 56,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName:
            'Inquiry <br/> (Cus Count) - <br/> [Inquiry <br/> (Total inq. Count)]',
        keyName: '',
    },
    {
        index: 58,
        key: 58,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'GST Tax %',
        keyName: '',
    },
    {
        index: 57,
        key: 57,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'GST Tax <br/> Amount (INR)',
        keyName: '',
    },
    {
        index: 59,
        key: 'extraExpense',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Extra Expense',
        keyName: '',
    },
    {
        index: 60,
        key: 'purchaseRation',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Purchase <br /> Ratio',
        keyName: '',
    },
];

export const RECEIVED_CHINA_TH = [
    {
        index: 0,
        key: 0,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Item Details',
        keyName: '',
    },
    {
        index: 1,
        key: 1,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'PO No',
        keyName: '',
    },
    {
        index: 2,
        key: 2,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Marka',
        keyName: '',
    },
    {
        index: 3,
        key: 3,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Color',
        keyName: '',
    },
    {
        index: 4,
        key: 4,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Payment <br/> Status',
        keyName: '',
    },
    {
        index: 5,
        key: 5,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Order Date',
        keyName: '',
    },
    {
        index: 6,
        key: 6,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Expected <br/> Delivery Date',
        keyName: '',
    },
    {
        index: 7,
        key: 7,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'PO Carton',
        keyName: '',
    },
    {
        index: 8,
        key: 8,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'PCS/Carton',
        keyName: '',
    },
    {
        index: 9,
        key: 9,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Total PO Qty',
        keyName: '',
    },
    {
        index: 10,
        key: 10,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Received <br/> Cartons [CH]',
        keyName: '',
    },
    {
        index: 11,
        key: 11,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Loaded <br/> Cartons',
        keyName: '',
    },
    {
        index: 12,
        key: 12,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Loaded <br/> Pending Qty <br/> (Carton)',
        keyName: '',
    },
    {
        index: 13,
        key: 13,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Total Pending  <br/> Loaded Qty',
        keyName: '',
    },
    {
        index: 14,
        key: 14,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Price /PCS <br/> (RMB)',
        keyName: '',
    },
    {
        index: 15,
        key: 15,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Total Amount <br/> (RMB)',
        keyName: '',
    },
    {
        index: 30,
        key: 30,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Total Received <br/> Amount (RMB)',
        keyName: '',
    },
    {
        index: 90,
        key: 'cartonWeight',
        show: true,
        isSelected: true,
        class: '',
        headerClass: '',
        header: 'J2',
        displayName: 'Weight <br/> /Carton (Kg)',
        keyName: '',
    },
    {
        index: 27,
        key: 'shippingTypes',
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Shipping Type',
        keyName: '',
    },
    {
        index: 16,
        key: 16,
        show: true,
        isSelected: true,
        class: 'tbl-bg-secondary',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'CBM/Carton',
        keyName: '',
    },
    {
        index: 17,
        key: 17,
        show: true,
        isSelected: true,
        class: 'tbl-bg-secondary',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Total CBM',
        keyName: '',
    },
    {
        index: 18,
        key: 18,
        show: true,
        isSelected: true,
        class: 'tbl-bg-secondary',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'CBM Price',
        keyName: '',
    },
    {
        index: 19,
        key: 19,
        show: true,
        isSelected: true,
        class: 'tbl-bg-secondary',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Total CBM <br/> Expense (₹)',
        keyName: '',
    },
    {
        index: 20,
        key: 20,
        show: true,
        isSelected: true,
        class: 'tbl-bg-secondary',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Shipping <br/> Expense/PCS',
        keyName: '',
    },
    {
        index: 91,
        key: 'totalWeight',
        show: true,
        isSelected: true,
        class: 'tbl-bg-secondary-two',
        headerClass: '',
        header: 'K2=J2*A',
        displayName: 'Total Weight',
        keyName: '',
    },
    {
        index: 92,
        key: 'cartonWeightRu',
        show: true,
        isSelected: true,
        class: 'tbl-bg-secondary-two',
        headerClass: '',
        header: 'L2',
        displayName: 'Cost/ <br />kg (Rs)',
        keyName: '',
    },
    {
        index: 93,
        key: 'totalLoadAmt',
        show: true,
        isSelected: true,
        class: 'tbl-bg-secondary-two',
        headerClass: '',
        header: 'M2=K2*L2',
        displayName: 'Total Load. <br/> AMT [Weight] (₹)',
        keyName: '',
    },
    {
        index: 94,
        key: 'shippingCostperPieceINR',
        show: true,
        isSelected: true,
        class: 'tbl-bg-secondary-two',
        headerClass: '',
        header: 'N2=M2/C',
        displayName: 'Shipping <br/> Cost / Piece (₹)',
        keyName: '',
    },
    {
        index: 95,
        key: 'totalShippingExpWeight',
        show: true,
        isSelected: true,
        class: 'tbl-bg-secondary-two',
        headerClass: '',
        header: 'O2=I+W+N2',
        displayName: 'Total Shipping <br/> Expense / Weight',
        keyName: '',
    },
    {
        index: 100,
        key: 'percentage',
        show: true,
        isSelected: true,
        class: 'tbl-bg-success',
        headerClass: '',
        header: 'J3',
        displayName: 'Percentage - <br/> [%]',
        keyName: '',
    },
    {
        index: 101,
        key: 'totalExpPCSper',
        show: true,
        isSelected: true,
        class: 'tbl-bg-success',
        headerClass: '',
        header: 'K3=I*J3/100',
        displayName: 'Total Expense <br/> / Piece (₹) [%]',
        keyName: '',
    },
    {
        index: 102,
        key: 'totalFinalCostPCSper',
        show: true,
        isSelected: true,
        class: 'tbl-bg-success',
        headerClass: '',
        header: 'L3 = I+W+K3',
        displayName: 'Total Final <br/> Cost / <br /> Piece(₹) [%]',
        keyName: '',
    },
    {
        index: 110,
        key: 50,
        show: true,
        isSelected: true,
        class: 'tbl-bg-danger',
        headerClass: '',
        header: 'J4=I+W',
        displayName: 'Item Amount <br/> (₹) [Fixed]',
        keyName: '',
    },
    // {
    //     index: 111,
    //     key: 51,
    //     show: true,
    //     isSelected: true,
    //     class: 'tbl-bg-danger',
    //     headerClass: '',
    //     header: 'K4=Wrong!',
    //     displayName: 'Total Item <br/> Amount (₹) / <br /> [Fixed]',
    //     keyName: '',
    // },
    {
        index: 112,
        key: 'expensePcs',
        show: true,
        isSelected: true,
        class: 'tbl-bg-primary',
        headerClass: '',
        header: 'J5',
        displayName: 'Expense/PCS',
        keyName: '',
    },
    {
        index: 113,
        key: 'totalItemAmt',
        show: true,
        isSelected: true,
        class: 'tbl-bg-primary',
        headerClass: '',
        header: 'K5=I+W+J5',
        displayName: 'Total Item <br/> Amount (₹)',
        keyName: '',
    },
    // {
    //     'index': 114,
    //     'key': 114,
    //     'show': true,
    //     'isSelected': true,
    //     'class': '',
    //     'headerClass': 'tbl-bg-light-three',
    //     'header': '-',
    //     'displayName': 'Last 3 <br/> Purchase <br/> [Shop No, <br /> Price, <br/> Cartons]',
    //     'keyName': '',
    // },
    // {
    //     index: 21,
    //     key: 21,
    //     show: true,
    //     isSelected: true,
    //     class: '',
    //     headerClass: 'tbl-bg-light-three',
    //     header: '-',
    //     displayName: 'Inquiry Shop <br/> with price',
    //     keyName: '',
    // },
    // {
    //     index: 22,
    //     key: 22,
    //     show: true,
    //     isSelected: true,
    //     class: '',
    //     headerClass: 'tbl-bg-light-three',
    //     header: '-',
    //     displayName:
    //         'Inquiry (Cus <br /> Count) - <br/> [Inquiry (Total <br /> inq. Count)]',
    //     keyName: '',
    // },
    {
        index: 24,
        key: 24,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'GST Tax %',
        keyName: '',
    },
    {
        index: 23,
        key: 23,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'GST Tax <br/> Amount (INR)',
        keyName: '',
    },
    {
        index: 25,
        key: 25,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Extra Expense',
        keyName: '',
    },
    {
        index: 26,
        key: 26,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Purchase <br/> Ratio',
        keyName: '',
    },
];

export const LOADED_TH = [
    {
        index: 0,
        key: 0,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Item Details',
        keyName: '',
    },
    {
        index: 1,
        key: 1,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Marka',
        keyName: '',
    },
    {
        index: 2,
        key: 2,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Color',
        keyName: '',
    },
    {
        index: 3,
        key: 3,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Payment <br/> Status',
        keyName: '',
    },
    {
        index: 4,
        key: 4,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Order Date',
        keyName: '',
    },
    // {
    //     index: 5,
    //     key: 5,
    //     show: true,
    //     isSelected: true,
    //     class: '',
    //     headerClass: 'tbl-bg-light-three',
    //     header: '-',
    //     displayName: 'Expected <br/> Delivery Date',
    //     keyName: '',
    // },
    {
        index: 6,
        key: 6,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'PO Carton',
        keyName: '',
    },
    {
        index: 7,
        key: 7,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Loaded <br/> Cartons',
        keyName: '',
    },
    {
        index: 35,
        key: 35,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Received <br/> Cartons [CH]',
        keyName: '',
    },
    {
        index: 8,
        key: 8,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Received ID <br/> [CH]',
        keyName: '',
    },
    {
        index: 9,
        key: 9,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'PCS/Carton',
        keyName: '',
    },
    {
        index: 10,
        key: 10,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Total PO Qty',
        keyName: '',
    },
    {
        index: 11,
        key: 11,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Price /PCS <br/> (RMB)',
        keyName: '',
    },
    {
        index: 12,
        key: 12,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Total Amount <br/> (RMB)',
        keyName: '',
    },
    {
        index: 40,
        key: 40,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Total Loaded <br/> Amount (RMB)',
        keyName: '',
    },
    // {
    //     index: 13,
    //     key: 13,
    //     show: true,
    //     isSelected: true,
    //     class: '',
    //     headerClass: 'tbl-bg-light-three',
    //     header: '-',
    //     displayName: 'Expected Delivery <br/> (RMB)',
    //     keyName: '',
    // },
    {
        index: 14,
        key: 14,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Total Amount <br/> (RMB) + (Ship. <br/> Exp.)',
        keyName: '',
    },
    {
        index: 15,
        key: 15,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Conv. Rate <br/> (RS)',
        keyName: '',
    },
    {
        index: 16,
        key: 16,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Total Conv <br/> Amount (INR)',
        keyName: '',
    },
    {
        index: 17,
        key: 17,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'China Final <br/> Expected Cost <br/> (Padatar)/ <br/> PCS',
        keyName: '',
    },
    {
        index: 18,
        key: 18,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Shipping Type',
        keyName: '',
    },
    {
        index: 19,
        key: 19,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Carton <br/> Dimension',
        keyName: '',
    },
    {
        index: 20,
        key: 20,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Dimension <br/> Age',
        keyName: '',
    },
    {
        index: 90,
        key: 'cartonWeight',
        show: true,
        isSelected: true,
        class: '',
        headerClass: '',
        header: 'J2',
        displayName: 'Weight <br/> /Carton (Kg)',
        keyName: '',
    },
    {
        index: 21,
        key: 21,
        show: true,
        isSelected: true,
        class: 'tbl-bg-secondary',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'CBM/Carton',
        keyName: '',
    },
    {
        index: 22,
        key: 22,
        show: true,
        isSelected: true,
        class: 'tbl-bg-secondary',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Total CBM',
        keyName: '',
    },
    {
        index: 23,
        key: 23,
        show: true,
        isSelected: true,
        class: 'tbl-bg-secondary',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'CBM Price',
        keyName: '',
    },
    {
        index: 24,
        key: 24,
        show: true,
        isSelected: true,
        class: 'tbl-bg-secondary',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Total CBM <br/> Expense (₹)',
        keyName: '',
    },
    {
        index: 25,
        key: 25,
        show: true,
        isSelected: true,
        class: 'tbl-bg-secondary',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Shipping <br/> Expense/PCS',
        keyName: '',
    },
    {
        index: 91,
        key: 'totalWeight',
        show: true,
        isSelected: true,
        class: 'tbl-bg-secondary-two',
        headerClass: '',
        header: 'K2=J2*A',
        displayName: 'Total Weight',
        keyName: '',
    },
    {
        index: 92,
        key: 'cartonWeightRu',
        show: true,
        isSelected: true,
        class: 'tbl-bg-secondary-two',
        headerClass: '',
        header: 'L2',
        displayName: 'Cost/ <br />kg (Rs)',
        keyName: '',
    },
    {
        index: 93,
        key: 'totalLoadAmt',
        show: true,
        isSelected: true,
        class: 'tbl-bg-secondary-two',
        headerClass: '',
        header: 'M2=K2*L2',
        displayName: 'Total Load. <br/> AMT [Weight] (₹)',
        keyName: '',
    },
    {
        index: 94,
        key: 'shippingCostperPieceINR',
        show: true,
        isSelected: true,
        class: 'tbl-bg-secondary-two',
        headerClass: '',
        header: 'N2=M2/C',
        displayName: 'Shipping <br/> Cost / Piece (₹)',
        keyName: '',
    },
    {
        index: 95,
        key: 'totalShippingExpWeight',
        show: true,
        isSelected: true,
        class: 'tbl-bg-secondary-two',
        headerClass: '',
        header: 'O2=I+W+N2',
        displayName: 'Total Shipping <br/> Expense / Weight',
        keyName: '',
    },
    {
        index: 100,
        key: 'percentage',
        show: true,
        isSelected: true,
        class: 'tbl-bg-success',
        headerClass: '',
        header: 'J3',
        displayName: 'Percentage - <br/> [%]',
        keyName: '',
    },
    {
        index: 101,
        key: 'totalExpPCSper',
        show: true,
        isSelected: true,
        class: 'tbl-bg-success',
        headerClass: '',
        header: 'K3=I*J3/100',
        displayName: 'Total Expense <br/> / Piece (₹) [%]',
        keyName: '',
    },
    {
        index: 102,
        key: 'totalFinalCostPCSper',
        show: true,
        isSelected: true,
        class: 'tbl-bg-success',
        headerClass: '',
        header: 'L3 = I+W+K3',
        displayName: 'Total Final <br/> Cost / <br /> Piece(₹) [%]',
        keyName: '',
    },
    {
        index: 110,
        key: 50,
        show: true,
        isSelected: true,
        class: 'tbl-bg-danger',
        headerClass: '',
        header: 'J4=I+W',
        displayName: 'Item Amount <br/> (₹) [Fixed]',
        keyName: '',
    },
    // {
    //     index: 111,
    //     key: 51,
    //     show: true,
    //     isSelected: true,
    //     class: 'tbl-bg-danger',
    //     headerClass: '',
    //     header: 'K4=Wrong!',
    //     displayName: 'Total Item <br/> Amount (₹) / <br /> [Fixed]',
    //     keyName: '',
    // },
    {
        index: 112,
        key: 'expensePcs',
        show: true,
        isSelected: true,
        class: 'tbl-bg-primary',
        headerClass: '',
        header: 'J5',
        displayName: 'Expense/PCS',
        keyName: '',
    },
    {
        index: 113,
        key: 'totalItemAmt',
        show: true,
        isSelected: true,
        class: 'tbl-bg-primary',
        headerClass: '',
        header: 'K5=I+W+J5',
        displayName: 'Total Item <br/> Amount (₹)',
        keyName: '',
    },
    // {
    //     'index': 26,
    //     'key': 26,
    //     'show': true,
    //     'isSelected': true,
    //     'class': '',
    //     'headerClass': 'tbl-bg-light-three',
    //     'header': '-',
    //     'displayName': 'Last 3 <br/> Purchase <br/> [Shop No, <br /> Price, <br/> Cartons]',
    //     'keyName': ''
    // },
    // {
    //     index: 27,
    //     key: 27,
    //     show: true,
    //     isSelected: true,
    //     class: '',
    //     headerClass: 'tbl-bg-light-three',
    //     header: '-',
    //     displayName: 'Inquiry Shop <br/> with price',
    //     keyName: '',
    // },
    // {
    //     index: 28,
    //     key: 28,
    //     show: true,
    //     isSelected: true,
    //     class: '',
    //     headerClass: 'tbl-bg-light-three',
    //     header: '-',
    //     displayName:
    //         'Inquiry (Cus <br /> Count) - <br/> [Inquiry (Total <br /> inq. Count)]',
    //     keyName: '',
    // },
    {
        index: 30,
        key: 30,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'GST Tax %',
        keyName: '',
    },
    {
        index: 29,
        key: 29,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'GST Tax <br/> Amount (INR)',
        keyName: '',
    },
    {
        index: 31,
        key: 31,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Extra Expense',
        keyName: '',
    },
    {
        index: 32,
        key: 32,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Purchase <br/> Ratio',
        keyName: '',
    },
];

export const RELEASE_TH = [
    {
        index: 0,
        key: 0,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Item Details',
        keyName: '',
    },
    {
        index: 1,
        key: 1,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Marka',
        keyName: '',
    },
    {
        index: 2,
        key: 2,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Color',
        keyName: '',
    },
    {
        index: 3,
        key: 3,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Notes',
        keyName: '',
    },
    {
        index: 4,
        key: 4,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Payment <br/> Status',
        keyName: '',
    },
    {
        index: 5,
        key: 5,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Order Date',
        keyName: '',
    },
    // {
    //     index: 6,
    //     key: 6,
    //     show: true,
    //     isSelected: true,
    //     class: '',
    //     headerClass: 'tbl-bg-light-three',
    //     header: '-',
    //     displayName: 'Expected <br/> Delivery Date',
    //     keyName: '',
    // },
    {
        index: 7,
        key: 7,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'PO Number',
        keyName: '',
    },
    {
        index: 8,
        key: 8,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'PO Carton',
        keyName: '',
    },
    {
        index: 9,
        key: 9,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Received <br/> Cartons [CH]',
        keyName: '',
    },
    {
        index: 10,
        key: 10,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Receive ID <br /> [CH]',
        keyName: '',
    },
    {
        index: 11,
        key: 11,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'PCS/Carton',
        keyName: '',
    },
    {
        index: 12,
        key: 12,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Total Released <br/> Carton',
        keyName: '',
    },
    {
        index: 13,
        key: 13,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Pending Qty <br/> (Carton)',
        keyName: '',
    },
    {
        index: 14,
        key: 14,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Total Pending <br/> Qty',
        keyName: '',
    },
    {
        index: 15,
        key: 15,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'GRN <br/> (Carton)',
        keyName: '',
    },
    {
        index: 16,
        key: 16,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Total GRN <br/> Qty',
        keyName: '',
    },
    {
        index: 42,
        key: 42,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Pending GRN <br/> (Carton)',
        keyName: '',
    },
    {
        index: 41,
        key: 41,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'CHA Carton',
        keyName: '',
    },
    {
        index: 17,
        key: 17,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Price /PCS <br/> (RMB)',
        keyName: '',
    },
    {
        index: 18,
        key: 18,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Total Amount <br/> (RMB)',
        keyName: '',
    },
    {
        index: 19,
        key: 19,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Expected <br/> Shipment Exp <br/> (RMB)',
        keyName: '',
    },
    {
        index: 20,
        key: 20,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Total Amount <br/> (RMB) + (Ship. <br/> Exp.)',
        keyName: '',
    },
    {
        index: 21,
        key: 21,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Total Conv. <br/> Amount (INR)',
        keyName: '',
    },
    {
        index: 22,
        key: 22,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'China Final <br/> Expected Cost <br/> (Padatar)/ <br/> PCS',
        keyName: '',
    },
    {
        index: 23,
        key: 23,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Shipping Type',
        keyName: '',
    },
    {
        index: 24,
        key: 24,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Carton <br/> Dimension',
        keyName: '',
    },
    {
        index: 25,
        key: 25,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Dimensions <br/> Age',
        keyName: '',
    },
    {
        index: 90,
        key: 'cartonWeight',
        show: true,
        isSelected: true,
        class: '',
        headerClass: '',
        header: 'J2',
        displayName: 'Weight <br/> /Carton (Kg)',
        keyName: '',
    },
    {
        index: 26,
        key: 26,
        show: true,
        isSelected: true,
        class: 'tbl-bg-secondary',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'CBM/Carton',
        keyName: '',
    },
    {
        index: 27,
        key: 27,
        show: true,
        isSelected: true,
        class: 'tbl-bg-secondary',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Total CBM',
        keyName: '',
    },
    {
        index: 28,
        key: 28,
        show: true,
        isSelected: true,
        class: 'tbl-bg-secondary',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'CBM Price',
        keyName: '',
    },
    {
        index: 29,
        key: 29,
        show: true,
        isSelected: true,
        class: 'tbl-bg-secondary',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Total CBM <br/> Expense (₹)',
        keyName: '',
    },
    {
        index: 30,
        key: 30,
        show: true,
        isSelected: true,
        class: 'tbl-bg-secondary',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Shipping <br/> Expense/PCS',
        keyName: '',
    },
    {
        index: 91,
        key: 'totalWeight',
        show: true,
        isSelected: true,
        class: 'tbl-bg-secondary-two',
        headerClass: '',
        header: 'K2=J2*A',
        displayName: 'Total Weight',
        keyName: '',
    },
    {
        index: 92,
        key: 'cartonWeightRu',
        show: true,
        isSelected: true,
        class: 'tbl-bg-secondary-two',
        headerClass: '',
        header: 'L2',
        displayName: 'Cost/ <br />kg (Rs)',
        keyName: '',
    },
    {
        index: 93,
        key: 'totalLoadAmt',
        show: true,
        isSelected: true,
        class: 'tbl-bg-secondary-two',
        headerClass: '',
        header: 'M2=K2*L2',
        displayName: 'Total Load. <br/> AMT [Weight] (₹)',
        keyName: '',
    },
    {
        index: 94,
        key: 'shippingCostperPieceINR',
        show: true,
        isSelected: true,
        class: 'tbl-bg-secondary-two',
        headerClass: '',
        header: 'N2=M2/C',
        displayName: 'Shipping <br/> Cost / Piece (₹)',
        keyName: '',
    },
    {
        index: 95,
        key: 'totalShippingExpWeight',
        show: true,
        isSelected: true,
        class: 'tbl-bg-secondary-two',
        headerClass: '',
        header: 'O2=I+W+N2',
        displayName: 'Total Shipping <br/> Expense / Weight',
        keyName: '',
    },
    {
        index: 100,
        key: 'percentage',
        show: true,
        isSelected: true,
        class: 'tbl-bg-success',
        headerClass: '',
        header: 'J3',
        displayName: 'Percentage - <br/> [%]',
        keyName: '',
    },
    {
        index: 101,
        key: 'totalExpPCSper',
        show: true,
        isSelected: true,
        class: 'tbl-bg-success',
        headerClass: '',
        header: 'K3=I*J3/100',
        displayName: 'Total Expense <br/> / Piece (₹) [%]',
        keyName: '',
    },
    {
        index: 102,
        key: 'totalFinalCostPCSper',
        show: true,
        isSelected: true,
        class: 'tbl-bg-success',
        headerClass: '',
        header: 'L3 = I+W+K3',
        displayName: 'Total Final <br/> Cost / <br /> Piece(₹) [%]',
        keyName: '',
    },
    {
        index: 110,
        key: 50,
        show: true,
        isSelected: true,
        class: 'tbl-bg-danger',
        headerClass: '',
        header: 'J4=I+W',
        displayName: 'Item Amount <br/> (₹) [Fixed]',
        keyName: '',
    },
    // {
    //     index: 111,
    //     key: 51,
    //     show: true,
    //     isSelected: true,
    //     class: 'tbl-bg-danger',
    //     headerClass: '',
    //     header: 'K4=Wrong!',
    //     displayName: 'Total Item <br/> Amount (₹) / <br /> [Fixed]',
    //     keyName: '',
    // },
    {
        index: 112,
        key: 'expensePcs',
        show: true,
        isSelected: true,
        class: 'tbl-bg-primary',
        headerClass: '',
        header: 'J5',
        displayName: 'Expense/PCS',
        keyName: '',
    },
    {
        index: 113,
        key: 'totalItemAmt',
        show: true,
        isSelected: true,
        class: 'tbl-bg-primary',
        headerClass: '',
        header: 'K5=I+W+J5',
        displayName: 'Total Item <br/> Amount (₹)',
        keyName: '',
    },
    {
        index: 31,
        key: 31,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName:
            'Transportation <br/> charges <br/> [Mumbai to <br/> Surat]/Carton',
        keyName: '',
    },
    {
        index: 32,
        key: 32,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName:
            'Total <br/> Transportation <br/> charges <br/> [Mumbai to <br/> Surat]',
        keyName: '',
    },
    {
        index: 33,
        key: 33,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName:
            'Transportation <br/> charges <br/> [Mumbai to <br/> Surat] / PCS',
        keyName: '',
    },
    {
        index: 34,
        key: 34,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Total <br/> Insurance (₹)',
        keyName: '',
    },
    {
        index: 35,
        key: 35,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Insurance (₹)/ <br/> PCS',
        keyName: '',
    },
    {
        index: 36,
        key: 36,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'GST Amount/ <br/> PCS',
        keyName: '',
    },
    {
        index: 45,
        key: 45,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'GST Tax %',
        keyName: '',
    },
    {
        index: 46,
        key: 46,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'GST Tax <br/> Amount (INR)',
        keyName: '',
    },
    {
        index: 37,
        key: 37,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Crane <br/> Expense <br/> [Total]',
        keyName: '',
    },
    {
        index: 38,
        key: 38,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Crane <br/> Expense/PCS',
        keyName: '',
    },
    {
        index: 39,
        key: 39,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Total Expense',
        keyName: '',
    },
    {
        index: 40,
        key: 40,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'China To <br/> Surat Final <br/> Price <br/> (Padatar)',
        keyName: '',
    },
    {
        index: 43,
        key: 43,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Purchase <br/> Ratio',
        keyName: '',
    },
    // {
    //     index: 41,
    //     key: 41,
    //     show: true,
    //     isSelected: true,
    //     class: '',
    //     headerClass: 'tbl-bg-light-three',
    //     header: '-',
    //     displayName: 'CHA - <br/> Importer',
    //     keyName: '',
    // },
    // {
    //     'index': 42,
    //     'key': 42,
    //     'show': true,
    //     'isSelected': true,
    //     'class': '',
    //     'headerClass': 'tbl-bg-light-three',
    //     'header': '-',
    //     'displayName': 'Received ID',
    //     'keyName': ''
    // },
];

export const GRN_TH = [
    {
        index: 0,
        key: 0,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Item Details',
        keyName: '',
    },
    {
        index: 1,
        key: 1,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Marka',
        keyName: '',
    },
    {
        index: 2,
        key: 2,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'GRN <br/> Cartons',
        keyName: '',
    },
    {
        index: 3,
        key: 3,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Container <br/> Number',
        keyName: '',
    },
    {
        index: 4,
        key: 4,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'PO Number',
        keyName: '',
    },
    {
        index: 5,
        key: 5,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Order Date',
        keyName: '',
    },
    {
        index: 6,
        key: 6,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'PO Carton',
        keyName: '',
    },
    {
        index: 7,
        key: 7,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'PCS/Carton',
        keyName: '',
    },
    {
        index: 8,
        key: 8,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Total Qty',
        keyName: '',
    },
    {
        index: 9,
        key: 9,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Total Received <br/> Qty',
        keyName: '',
    },
    {
        index: 10,
        key: 10,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Carton <br/> Dimensions',
        keyName: '',
    },
    {
        index: 11,
        key: 11,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: 'A',
        displayName: 'Real Carton <br/> Dimensions',
        keyName: '',
    },
    {
        index: 12,
        key: 12,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Real <br/> CBM/Carton',
        keyName: '',
    },
    {
        index: 13,
        key: 13,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Total CBM',
        keyName: '',
    },
    {
        index: 14,
        key: 14,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Real Weight/ <br/> Carton (kg)',
        keyName: '',
    },
];

export const COMPLETED_TH = [
    {
        index: 0,
        key: 0,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Item Details',
        keyName: '',
    },
    {
        index: 1,
        key: 1,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Average Price <br/> (Surat)',
        keyName: '',
    },
    {
        index: 2,
        key: 2,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Marka',
        keyName: '',
    },
    {
        index: 3,
        key: 3,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Color',
        keyName: '',
    },
    {
        index: 4,
        key: 4,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Notes',
        keyName: '',
    },
    {
        index: 5,
        key: 5,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'English <br/> Comment',
        keyName: '',
    },
    {
        index: 6,
        key: 6,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'China <br/> Comment',
        keyName: '',
    },
    {
        index: 7,
        key: 7,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Group Code',
        keyName: '',
    },
    {
        index: 8,
        key: 8,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Payment <br/> Status',
        keyName: '',
    },
    {
        index: 9,
        key: 9,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Order Date',
        keyName: '',
    },
    {
        index: 10,
        key: 10,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'PO Carton',
        keyName: '',
    },
    {
        index: 11,
        key: 11,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'PCS/Carton',
        keyName: '',
    },
    {
        index: 12,
        key: 12,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Total Qty',
        keyName: '',
    },
    {
        index: 13,
        key: 13,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Pending Qty <br/> (Carton)',
        keyName: '',
    },
    {
        index: 14,
        key: 14,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Total Pending <br/> Qty',
        keyName: '',
    },
    {
        index: 15,
        key: 15,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Price /PCS <br/> (RMB)',
        keyName: '',
    },
    {
        index: 16,
        key: 16,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Total Amount <br/> (RMB)',
        keyName: '',
    },
    {
        index: 17,
        key: 17,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Expected <br/> Delivery <br/> (RMB)',
        keyName: '',
    }, 
    {
        index: 18,
        key: 18,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Total Amount <br/> (RMB) + (Ship. <br/> Exp.)',
        keyName: '',
    },
    {
        index: 19,
        key: 19,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Conv. Rate <br/> (RS)',
        keyName: '',
    },
    {
        index: 20,
        key: 20,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Total Conv. <br/> Amount (INR)',
        keyName: '',
    },
    {
        index: 21,
        key: 21,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'China Final <br/> Expected Cost <br/> (Padatar)/ <br/> PCS',
        keyName: '',
        isTotal: true,
    },
    {
        index: 22,
        key: 22,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Shipping Type',
        keyName: '',
    },
    {
        index: 23,
        key: 23,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Carton <br/> Dimension',
        keyName: '',
    },
    {
        index: 24,
        key: 24,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Dimensions <br/> Age',
        keyName: '',
    },
    {
        index: 25,
        key: 25,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Total Expense',
        keyName: '',
    },
    // {
    //     index: 26,
    //     key: 26,
    //     show: true,
    //     isSelected: true,
    //     class: '',
    //     headerClass: 'tbl-bg-light-three',
    //     header: '-',
    //     displayName: 'Container No.',
    //     keyName: '',
    // },
    {
        index: 27,
        key: 27,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Real Carton <br/> Dimensions',
        keyName: '',
    },
    {
        index: 28,
        key: 28,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Real <br/> CBM/Carton',
        keyName: '',
    },
    {
        index: 29,
        key: 29,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Total Real <br/> CBM',
        keyName: '',
    },
    {
        index: 30,
        key: 30,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Real Weight/ <br/> Carton (kg)',
        keyName: '',
    },
    {
        index: 31,
        key: 31,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'QC Checklist',
        keyName: '',
    },
    // {
    //     index: 32,
    //     key: 32,
    //     show: true,
    //     isSelected: true,
    //     class: '',
    //     headerClass: 'tbl-bg-light-three',
    //     header: '-',
    //     displayName: 'CHA - <br/> Importer',
    //     keyName: '',
    //     isTotal: true,
    // },
    {
        index: 34,
        key: 34,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Actual <br/> Total Expense',
        keyName: '',
    },
    {
        index: 33,
        key: 33,
        show: true,
        isSelected: true,
        class: '',
        headerClass: 'tbl-bg-light-three',
        header: '-',
        displayName: 'Purchase <br/> Ratio',
        keyName: '',
    },

]
