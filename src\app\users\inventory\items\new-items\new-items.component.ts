import { Component, OnInit, ViewChild, ElementRef } from "@angular/core";
import { FormGroup, FormBuilder, FormArray, Validators, ValidatorFn, AbstractControl, ValidationErrors } from "@angular/forms";
import { ActivatedRoute } from "@angular/router";
import { EnumForItemTabs } from "@enums/EnumForItemTabs";
import { Category } from "@modal/Category";
import { HsnCode } from "@modal/HsnCode";
import { Item, BreachQty, DiscountSetting, PackingInfo, ItemMediaLink, BulletPoint } from "@modal/Item";
import { ItemGroup, ItemGroupDocs } from "@modal/ItemGroup";
import { Level } from "@modal/Level";
import { POImageMapping } from "@modal/POImageMapping";
import { StockDetails } from "@modal/StockDetails";
import { Unit } from "@modal/Unit";
import { UtilsService } from "@service/utils.service";
import { Serialize, Deserialize } from "cerialize";
import moment from "moment";
import { ItemDropdown } from "src/app/shared/constants/interface";
declare var window: any;

@Component({
  selector: 'app-new-items',
  templateUrl: './new-items.component.html',
  styleUrls: ['./new-items.component.scss']
})
export class NewItemsComponent implements OnInit {

  @ViewChild('doc') doc: ElementRef;
  @ViewChild('colorPo') colorPo: ElementRef;

  //item add/edit
  itemId: number;
  itemForm: FormGroup;
  itemObj = new Item();
  //
  breachQty = new BreachQty();
  //
  discountSettingFG: FormGroup;
  discountSettingList: DiscountSetting[] = []
  selectedDiscountIndex: number;
  deleteDiscountGrp: any;
  //
  packetInfoFG: FormGroup;
  deletePackingInfo: any;
  packingInfoList: PackingInfo[] = []
  //
  levelList: Level[] = []
  enumForTab = EnumForItemTabs;
  selectedTab: string = this.enumForTab.DESCRIPTION;
  // PO mapping
  pOImageMapping: POImageMapping[] = []
  poImageFGP: FormGroup;
  deletePoMapping: any;

  dropdown: ItemDropdown = null;

  //HSN
  showHsnModal: boolean = false
  hsnGroup: FormGroup;
  hsnObj = new HsnCode();
  gstSlabDropdown: any[] = [];
  itemAddHsnMasterModal: any;
  allCategories: any[] = [];

  //Unit
  itemAddUnitMaster: any;
  showUnitModal: boolean = false
  unitGroup: FormGroup;
  unitObj = new Unit();

  //Item Grp
  itemGroupAddModal: any;
  showItemGroupModal: boolean = false;
  itemGroupForm: FormGroup;
  flattenedParentCategory: any[] = [];
  itemGroupObj = new ItemGroup();
  categoryList: Category[] = []
  hsnCodeDropdown: any[] = [];

  filteredGroupCode: any[] = [];

  constructor(public utilsService: UtilsService, private fb: FormBuilder, private route: ActivatedRoute) { }

  ngOnInit(): void {

    //hsn
    this.hsnFormGroup();
    this.itemAddHsnMasterModal = new window.bootstrap.Modal(
      document.getElementById('itemAddHsnMasterModal')
    );

    //unit
    this.unitFormGroup();
    this.itemAddUnitMaster = new window.bootstrap.Modal(
      document.getElementById('itemAddUnitMaster')
    );

    this.itemGroupAddModal = new window.bootstrap.Modal(
      document.getElementById('itemGroupAddModal')
    );

    //itemGroup
    this.itemGroupModal();

    this.itemFormGroup();
    this.discountSettingGroup();
    this.packetInfoGroup();
    this.poImageMapping();

    this.itemId = Number(this.route.snapshot.paramMap.get('id'));
    if (!this.itemId) {
      this.itemObj.isActive = true;
      this.itemObj.itemsSeason = 'REGULAR'
      this.itemObj.isSaveAsDraft = true;
    }

    if (this.itemId) {
      this.getDataByID();
    }
    else {
      this.addPOMapping();
      this.addMediaLink();
      this.addBullet();
      this.getRequiredData();
    }

    this.deleteDiscountGrp = new window.bootstrap.Modal(
      document.getElementById('deleteDiscountGrp')
    );

    this.deletePackingInfo = new window.bootstrap.Modal(
      document.getElementById('deletePackingInfo')
    );

    this.deletePoMapping = new window.bootstrap.Modal(
      document.getElementById('deletePoMapping')
    );

    // auto calculate field
    this.itemForm.get('singlePieceVolume').disable();
    this.itemForm.get('singlePieceVolume').clearValidators()
    this.itemForm.get('singlePieceVolume').updateValueAndValidity()

  }

  getRequiredData() {
    this.utilsService.getMethodAPI(false, this.utilsService.serverVariableService.ITEM_REQ_DATA, null, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.categoryList = [];
        this.dropdown = response;
        this.dropdown.itemDimUnit = Serialize((this.dropdown.unitMaster || []));
        this.dropdown.itemWeightDimUnit = Serialize(this.dropdown.unitMaster);
        this.dropdown.boxUnit = Serialize((this.dropdown.unitMaster || []));
        this.dropdown.boxWeightUnit = Serialize(this.dropdown.unitMaster);
        Object.keys(this.dropdown).filter(v => v !== 'itemsBelong').forEach(key => {
          if (this.dropdown[key] && Array.isArray(this.dropdown[key])) {
            this.dropdown[key] = this.utilsService.transformDropdownItems(this.dropdown[key]);
          }
        });
        setTimeout(() => {

          // filter unit dropdown acc to unit type
          if (this.dropdown?.itemDimUnit) {
            this.dropdown.itemDimUnit = (this.dropdown.itemDimUnit.filter(d => d?.unitMasterCategory?.value == 'LENGTH'))
          }
          if (this.dropdown?.boxUnit) {
            this.dropdown.boxUnit = this.dropdown.boxUnit.filter(d => d?.unitMasterCategory?.value == 'LENGTH')
          }
          if (this.dropdown?.itemWeightDimUnit) {
            this.dropdown.itemWeightDimUnit = this.dropdown.itemWeightDimUnit.filter(d => d?.unitMasterCategory?.value == 'WEIGHT')
          }
          if (this.dropdown?.boxWeightUnit) {
            this.dropdown.boxWeightUnit = this.dropdown.boxWeightUnit.filter(d => d?.unitMasterCategory?.value == 'WEIGHT')
          }

          //setting default value
          if (!this.itemId) {
            if (!this.itemId) {
              if (this.dropdown?.itemDimUnit && !this.itemObj.itemsDimId) {
                this.itemObj.itemsDimId = this.dropdown.itemDimUnit.filter(d => d.shortCode == 'CM')[0]?.id;
              }
              if (this.dropdown?.boxUnit && !this.itemObj.boxDimId) {
                this.itemObj.boxDimId = this.dropdown.boxUnit.filter(d => d.shortCode == 'CM')[0]?.id;
              }
              if (this.dropdown?.itemWeightDimUnit && !this.itemObj.itemsWeightDimId) {
                this.itemObj.itemsWeightDimId = this.dropdown.itemWeightDimUnit.filter(d => d.shortCode == 'KG')[0]?.id;
              }
              if (this.dropdown?.boxWeightUnit && !this.itemObj.boxWeightDimId) {
                this.itemObj.boxWeightDimId = this.dropdown.boxWeightUnit.filter(d => d.shortCode == 'KG')[0]?.id;
              }
            }
          }

          this.getParentCategory();
          this.dropdown.itemGroup = this.utilsService.filterIsActiveLV(this.dropdown?.itemGroup, this.itemObj.itemGroupId ? this.itemObj.itemGroupId : null);
          this.dropdown.sizeMaster = this.utilsService.filterIsActiveLV(this.dropdown?.sizeMaster, this.itemObj.sizeMasterId ? this.itemObj.sizeMasterId : null);
          this.dropdown.mountingType = this.utilsService.filterIsActiveLV(this.dropdown?.mountingType, this.itemObj.mountingTypeId ? this.itemObj.mountingTypeId : null);
          this.dropdown.qcChecklist = this.utilsService.filterIsActiveLV(this.dropdown?.qcChecklist, this.itemObj.qcChecklistId ? this.itemObj.qcChecklistId : null);
          this.dropdown.packOf = this.utilsService.filterIsActiveLV(this.dropdown?.packOf, this.itemObj.packOfId ? this.itemObj.packOfId : null);
          this.dropdown.hsnCodeMaster = this.utilsService.filterIsActive(this.dropdown?.hsnCodeMaster, this.itemObj.hsnCodeId ? this.itemObj.hsnCodeId : null);
          this.dropdown.unitMaster = this.utilsService.filterIsActive(this.dropdown?.unitMaster, this.itemObj.unitId ? this.itemObj.unitId : null);
          this.dropdown.seasonMaster = this.utilsService.filterIsActiveLV(this.dropdown?.seasonMaster, this.itemObj.seasonMasterId ? this.itemObj.seasonMasterId : null);
          this.dropdown.batteryType = this.utilsService.filterIsActiveLV(this.dropdown?.batteryType, this.itemObj.batteryTypeId ? this.itemObj.batteryTypeId : null);
          this.dropdown.marketType = this.utilsService.filterIsActiveMultipleLV((this.dropdown?.marketType || []), this.itemObj.marketTypeIds ? this.itemObj.marketTypeIds : null);

          //
          this.dropdown.groupCodes = this.utilsService.filterIsActive(this.dropdown?.groupCodes, this.itemObj.groupCodeId ? this.itemObj.groupCodeId : null);
          this.filteredGroupCode = this.dropdown?.groupCodes?.filter(v => v.categoryId === this.itemObj.categoryId);
          // units
          this.dropdown.itemDimUnit = this.utilsService.filterIsActive(this.dropdown?.itemDimUnit, this.itemObj.itemsDimId ? this.itemObj.itemsDimId : null);
          this.dropdown.boxUnit = this.utilsService.filterIsActive(this.dropdown?.boxUnit, this.itemObj.boxDimId ? this.itemObj.boxDimId : null);
          this.dropdown.boxWeightUnit = this.utilsService.filterIsActive(this.dropdown?.boxWeightUnit, this.itemObj.boxWeightDimId ? this.itemObj.boxWeightDimId : null);
          this.dropdown.itemWeightDimUnit = this.utilsService.filterIsActive(this.dropdown?.itemWeightDimUnit, this.itemObj.itemsWeightDimId ? this.itemObj.itemsWeightDimId : null);

          // color dropdown check
          this.pOImageMapping.forEach(v => {
            v.colorDropdown = Serialize(this.utilsService.filterIsActiveMultipleLV((this.dropdown?.colorMaster || []), v.colors ? v.colors : null))
          });

          // packing type dropdown
          this.packingInfoList.forEach(v => {
            v.packingDropdown = Serialize(this.utilsService.filterIsActiveLV((this.dropdown?.packingMaster || []), v.packingTypeId ? v.packingTypeId : null))
          });
        }, 250);

        if (response.levels && !this.itemId) {
          this.levelList = Deserialize(response.levels, Level)
        }
      }
    })
  }

  getDataByID() {
    this.utilsService.getMethodAPI(false, this.utilsService.serverVariableService.ITEM_BY_ID + `?id=${this.itemId}`, {}, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.itemObj = Deserialize(response.item, Item);
        this.itemObj.itemDocs = Deserialize((response.item?.itemDocs || []), ItemGroupDocs);

        if (this.itemObj?.category) {
          this.itemObj.categoryId = (this.itemObj?.category?.id)
        }

        if (response.breachQty) {
          this.breachQty = Deserialize(response.breachQty, BreachQty)
        }

        if (response.levels) {
          this.levelList = Deserialize(response.levels, Level)
        }

        if (response.discountSettings) {
          this.discountSettingList = Deserialize(response.discountSettings, DiscountSetting)
          const fa = (this.discountSettingFG.get('setting') as FormArray);
          this.discountSettingList.map(v => {
            fa.push(this.fb.group({
              orderQtyFrom: [v.orderQtyFrom, Validators.required],
              orderQtyTo: [v.orderQtyTo, Validators.required],
              discount: [v.discount, Validators.compose([Validators.required, Validators.max(100)])]
            }, { validators: this.rangeValidatorDiscount() }));
          })
        }

        if (response.packingInfos) {
          this.packingInfoList = Deserialize(response.packingInfos, PackingInfo)
          this.packingInfoList.forEach(v => {
            v.packingTypeId = Serialize(v.packingMaster?.id)
          })
          const fa = (this.packetInfoFG.get('packetInfo') as FormArray);
          this.packingInfoList.map(v => {
            fa.push(this.fb.group({
              name: [v.name, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.ONLY_SPACE_NOT_ALLOW)])],
              qtyRangeFrom: [v.qtyRangeFrom, Validators.required],
              qtyRangeTo: [v.qtyRangeTo, Validators.required],
              packingTypeId: [v.packingMaster?.id, Validators.required]
            }, { validators: this.rangeValidatorPacking() }));
          })
        }

        //PO color mapping
        if (response.poColorImageMappings) {
          this.pOImageMapping = Deserialize(response.poColorImageMappings, POImageMapping);
          this.pOImageMapping.forEach(v => {
            v.colors = v.poColors.map(color => color.colorMaster?.id);
            v.images = v.poImages.map(image => ({
              id: image.id,
              formattedName: image.formattedName,
              originalName: image.originalName
            }));
          });
          const fa = this.poImageFGP.get('imageMapping') as FormArray;
          this.pOImageMapping.map(v => {
            fa.push(this.fb.group({
              color: [v.colors, Validators.required],
              image: [v.images],
            }))
          })
        }

        if(response.itemStock) {
          this.itemObj.itemStock = Deserialize(response.itemStock, StockDetails);
        }

        // Bullet Points
        if (response.bulletPoints) {
          this.itemObj.bulletPoints = Serialize(response.bulletPoints);
          const bullet = (this.itemForm.get('bulletPts') as FormArray);
          bullet.clear();
          this.itemObj.bulletPoints?.map(v => {
            if (v) {
              bullet.push(this.fb.group({
                point: [v.points, Validators.compose([Validators.pattern(this.utilsService.validationService.ONLY_SPACE_NOT_ALLOW)])],
              }));
            }
          })
        } else {
          this.addBullet();
        }

        // Media links
        if (response.mediaLinks) {
          this.itemObj.mediaLinks = Deserialize(response.mediaLinks, ItemMediaLink);
          const mediaLinks = (this.itemForm.get('mediaLinks') as FormArray);
          mediaLinks.clear();
          this.itemObj.mediaLinks?.map(v => {
            if (v) {
              mediaLinks.push(this.fb.group({
                link: [v?.link, Validators.compose([Validators.pattern(this.utilsService.validationService.ONLY_SPACE_NOT_ALLOW)])],
              }));
            }
          })
        } else {
          this.addMediaLink();
        }

        if (this.itemObj?.boxDim) {
          this.itemObj.boxDimId = (this.itemObj.boxDim.id)
        }

        if (this.itemObj?.boxWeightDim) {
          this.itemObj.boxWeightDimId = (this.itemObj.boxWeightDim.id)
        }

        if (this.itemObj?.itemsDim) {
          this.itemObj.itemsDimId = (this.itemObj.itemsDim.id)
        }

        if (this.itemObj?.itemsWeightDim) {
          this.itemObj.itemsWeightDimId = (this.itemObj.itemsWeightDim.id)
        }

        if (this.itemObj?.hsnCodeMaster?.id) {
          this.itemObj.hsnCodeId = (this.itemObj.hsnCodeMaster.id)
        }
        if (this.itemObj?.itemGroup?.id) {
          this.itemObj.itemGroupId = (this.itemObj.itemGroup.id)
        }
        if (this.itemObj?.unitMaster?.id) {
          this.itemObj.unitId = (this.itemObj.unitMaster.id)
        }
        if (this.itemObj?.sizeMaster?.id) {
          this.itemObj.sizeMasterId = (this.itemObj.sizeMaster.id)
        }
        if (this.itemObj?.itemsMarketTypes) {
          this.itemObj.marketTypeIds = (this.itemObj.itemsMarketTypes || []).map(v => v.market?.id)
        }
        if (this.itemObj?.seasonMaster?.id) {
          this.itemObj.seasonMasterId = (this.itemObj.seasonMaster.id)
        }
        if (this.itemObj?.qcChecklist?.id) {
          this.itemObj.qcChecklistId = (this.itemObj.qcChecklist.id)
        }
        if (this.itemObj?.packOf) {
          this.itemObj.packOfId = (this.itemObj.packOf.id)
        }
        if (this.itemObj?.mountingType) {
          this.itemObj.mountingTypeId = (this.itemObj.mountingType.id)
        }
        if (this.itemObj?.batteryType) {
          this.itemObj.batteryTypeId = (this.itemObj.batteryType.id)
        }
        if (this.itemObj?.itemGroupCode) {
          this.itemObj.groupCodeId = (this.itemObj.itemGroupCode.id)
        }

        if (this.itemObj?.itemsSeason) {
          this.itemObj.itemsSeason = (this.itemObj.itemsSeason?.value);
          if (this.itemObj.itemsSeason?.value === 'SEASONAL') {
            this.itemForm.controls['seasonMasterId'].reset();
            this.itemForm.controls['seasonMasterId'].clearValidators();
            this.itemForm.controls['seasonMasterId'].updateValueAndValidity();
          }
          this.itemObj.tempAdvPODate = moment(this.dropdown?.seasonMaster.find(v => v.id === this.itemObj.seasonMasterId).advanceDate).format('DD/MM/YYYY')
        }

        setTimeout(() => {
          this.getRequiredData();
        }, 150);
      }
    })
  }

  itemFormGroup() {
    this.itemForm = this.fb.group({
      itemName: [null, Validators.compose([Validators.required])],
      displayName: [null, Validators.compose([Validators.required])],
      marketTypeIds: [null, Validators.compose([Validators.required])],
      isSellCarton: [false],
      isVisible: [false],
      isAlert: [false],
      days: [null],
      isActive: [true],
      itemGroupId: [null, Validators.compose([Validators.required])],
      groupCodeId: [null],
      categoryId: [null, Validators.compose([Validators.required])],
      unitId: [null, Validators.compose([Validators.required])],
      hsnCodeId: [null, Validators.compose([Validators.required])],
      qcChecklistId: [null],
      itemLength: [null],
      itemWidth: [null],
      itemHeight: [null],
      itemsDim: [null],
      BoxDim: [null],
      boxLength: [null],
      boxWidth: [null],
      boxHeight: [null],
      youtubeLink: [null],
      itemsBelongTo: [null],
      itemsWeight: [null],
      boxWeight: [null],
      itemsWeightDim: [null],
      boxWeightDim: [null],
      singlePieceVolume: [null],
      purchaseRation: [null],
      itemPrice: [null],
      gstItemPrice: [null],
      itemCarton: [null],
      gstItemCarton: [null],
      discounterCoins: [null],
      loosesGoodsCoins: [null],
      seasonMasterId: [null],
      itemsSeason: [false],
      title: [null, Validators.compose([Validators.required])],
      searchKeyWords: [null, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.ONLY_SPACE_NOT_ALLOW)])],
      material: [null],
      subMaterial: [null],
      sizeMasterId: [null],
      accessories: [null],
      capacity: [null],
      batteryType: [null],
      numberOfBattery: [null],
      isRemoteControl: [false],
      voltage: [null],
      wattage: [null],
      mountingType: [null],
      packOf: [null],
      packQty: [null],
      itemsDescription: [null],
      specialFeatures: [null],
      branchTransferAlert: [null, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.ONLY_SPACE_NOT_ALLOW)])],
      branchReorderQty: [null, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.ONLY_SPACE_NOT_ALLOW)])],
      blockBranchTransferQty: [null, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.ONLY_SPACE_NOT_ALLOW)])],
      blockSalesQty: [null, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.ONLY_SPACE_NOT_ALLOW)])],
      mainWarehouseAlert: [null, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.ONLY_SPACE_NOT_ALLOW)])],
      warehouseReorderQty: [null, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.ONLY_SPACE_NOT_ALLOW)])],
      rackBreachQty: [null, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.ONLY_SPACE_NOT_ALLOW)])],
      rackRefillQty: [null, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.ONLY_SPACE_NOT_ALLOW)])],
      bulletPts: this.fb.array([]),
      itemsDimId: [null],
      boxDimId: [null],
      itemsWeightDimId: [null],
      boxWeightDimId: [null],
      mediaLinks: this.fb.array([]),
    });
  }

  // On Change category
  onChangeCategory() {
    this.itemObj.groupCodeId = null;
    this.itemForm.get('groupCodeId').reset();
    this.filteredGroupCode = this.dropdown?.groupCodes?.filter(v => v.categoryId === this.itemObj.categoryId);
  }

  get bulletPts() {
    return (this.itemForm.get('bulletPts') as FormArray);
  }

  getBulletPoints(): FormGroup {
    return this.fb.group({
      point: [null, Validators.compose([Validators.pattern(this.utilsService.validationService.ONLY_SPACE_NOT_ALLOW)])],
    });
  }

  addBullet() {
    this.bulletPts.push(this.getBulletPoints());
    this.itemObj.bulletPoints.push(Serialize(new BulletPoint()))
  }

  removeBullet(index: number) {
    if (this.bulletPts.length > 1) {
      if (this.itemObj.bulletPoints[index]?.id) {
        this.itemObj.deletedBulletPointsID.push(this.itemObj.bulletPoints[index].id)
      }
      this.bulletPts.removeAt(index);
      this.itemObj.bulletPoints.splice(index, 1);
    }
  }

  /// Media Links

  get mediaLinks() {
    return (this.itemForm.get('mediaLinks') as FormArray);
  }

  getMediaLinks(): FormGroup {
    return this.fb.group({
      link: [null, Validators.compose([Validators.pattern(this.utilsService.validationService.ONLY_SPACE_NOT_ALLOW)])],
    });
  }

  addMediaLink() {
    if (this.mediaLinks.length > 4) {
      this.utilsService.toasterService.error(`Maximum 5 media links allowed.`, '', {
        positionClass: 'toast-top-right',
        closeButton: true,
        timeOut: 10000
      });
      return;
    }
    this.mediaLinks.push(this.getMediaLinks());
    this.itemObj.mediaLinks.push(Serialize(new ItemMediaLink))
  }

  removeMediaLink(index: number) {
    if (this.mediaLinks.length > 1) {
      if (this.itemObj.mediaLinks[index].id) {
        this.itemObj.deletedMediaLinksID.push(this.itemObj.mediaLinks[index].id)
      }
      this.mediaLinks.removeAt(index);
      this.itemObj.mediaLinks.splice(index, 1);
    }
  }

  // discount setting formarray
  discountSettingGroup() {
    this.discountSettingFG = this.fb.group({
      setting: this.fb.array([])
    });
  }

  get setting() {
    return (this.discountSettingFG.get('setting') as FormArray);
  }

  addSetting() {
    const itemGroup = this.fb.group({
      discount: [null, Validators.compose([Validators.required, Validators.max(100)])],
      orderQtyFrom: [null, Validators.required],
      orderQtyTo: [null, Validators.required]
    }, { validators: this.rangeValidatorDiscount() });

    this.setting.push(itemGroup);
    this.discountSettingList.push(Serialize(new DiscountSetting))
  }

  openRemoveDiscountModal(index: number) {
    this.selectedDiscountIndex = index;
    this.deleteDiscountGrp.show();
  }

  removeDiscountSetting() {
    this.setting.removeAt(this.selectedDiscountIndex);
    this.discountSettingList.splice(this.selectedDiscountIndex, 1);
    this.deleteDiscountGrp.hide();
  }

  // packing info
  packetInfoGroup() {
    this.packetInfoFG = this.fb.group({
      packetInfo: this.fb.array([])
    });
  }

  get packetInfo() {
    return (this.packetInfoFG.get('packetInfo') as FormArray);
  }

  rangeValidatorPacking(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const formGroup = control as FormGroup;
      const qtyRangeFrom = formGroup.get('qtyRangeFrom')?.value;
      const qtyRangeTo = formGroup.get('qtyRangeTo')?.value;
      if (qtyRangeFrom !== null && qtyRangeTo !== null && Number(qtyRangeTo) <= Number(qtyRangeFrom)) {
        return { 'packing': true };
      }
      return null;
    };
  }

  rangeValidatorDiscount(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const formGroup = control as FormGroup;
      const qtyRangeFrom = formGroup.get('orderQtyFrom')?.value;
      const qtyRangeTo = formGroup.get('orderQtyTo')?.value;
      if (qtyRangeFrom !== null && qtyRangeTo !== null && Number(qtyRangeTo) <= Number(qtyRangeFrom)) {
        return { 'discount': true };
      }
      return null;
    };
  }

  addPacketInfo() {
    const itemGroup = this.fb.group({
      name: [null, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.ONLY_SPACE_NOT_ALLOW)])],
      qtyRangeTo: ['', Validators.required],
      qtyRangeFrom: ['', Validators.required],
      packingTypeId: [null, Validators.required]
    }, { validators: this.rangeValidatorPacking() });

    this.packetInfo.push(itemGroup);
    // this.packingInfoList.push(Serialize(new PackingInfo))
    const obj = new PackingInfo()
    obj.packingDropdown = this.utilsService.filterIsActiveLV(this.dropdown?.packingMaster, null);
    this.packingInfoList.push(obj)
  }

  openRemovePIModal(index: number) {
    this.selectedDiscountIndex = index;
    this.deletePackingInfo.show();
  }

  removePI() {
    this.packetInfo.removeAt(this.selectedDiscountIndex);
    this.packingInfoList.splice(this.selectedDiscountIndex, 1);
    this.deletePackingInfo.hide();
  }

  // PO Image Mapping
  poImageMapping() {
    this.poImageFGP = this.fb.group({
      imageMapping: this.fb.array([])
    });
  }

  get imageMapping() {
    return (this.poImageFGP.get('imageMapping') as FormArray);
  }

  addPOMapping() {
    const itemGroup = this.fb.group({
      color: [null, Validators.compose([Validators.required])],
      image: [null],
    });

    this.imageMapping.push(itemGroup);
    const obj = new POImageMapping()
    obj.colorDropdown = this.utilsService.filterIsActiveLV(this.dropdown?.colorMaster, null);
    this.pOImageMapping.push(obj)
  }

  openRemoveCPOModal(index: number) {
    this.selectedDiscountIndex = index;
    this.deletePoMapping.show();
  }

  removePOMapping() {
    this.itemObj.deletePoImageId.push(this.pOImageMapping.at(this.selectedDiscountIndex).id)
    this.imageMapping.removeAt(this.selectedDiscountIndex);
    this.pOImageMapping.splice(this.selectedDiscountIndex, 1);
    this.deletePoMapping.hide();
  }

  onColorImageChange(event: any, index: number): void {
    if (event.target.files && event.target.files[0]) {
      let fileData = null;
      const max_file_size = 5242880;
      let selectedFile = Array.from(event.target.files || []);;
      if (selectedFile.length) {
        const obj = selectedFile.map((file: File) => {
          const ext = file.name.substr(file.name.lastIndexOf('.') + 1);
          const ext1 = (ext).toLowerCase();
          if (ext1 === 'jpeg' || ext1 === 'png' || ext1 === 'jpg' || ext1 === 'jfif') {
            if (file.size > max_file_size) {
              this.utilsService.toasterService.error(this.utilsService.validationService.IMAGE_MAX_FILE_SIZE)
            } else {
              // const fileUrl = URL.createObjectURL(file);
              const fileUrl = URL.createObjectURL(file);
              fileData = {
                id: null,
                originalname: fileUrl,
                fileName: file.name,
                file: file,
              };
              this.pOImageMapping[index]?.images.push(fileData);
            }
          }
          else {
            this.utilsService.toasterService.error(this.utilsService.validationService.IMAGE_INVALID_EXTENSION)
          }
        });
      }
    }

    if (this.colorPo) {
      this.colorPo.nativeElement.value = "";
    }
  }

  removePOColorAttachment(i: number, file, childI: number) {
    if (this.colorPo?.nativeElement) {
      this.colorPo.nativeElement.value = "";
    }
    this.pOImageMapping[i].images.splice(childI, 1)
    if (file.id) {
      this.itemObj.deletePoImageImageId.push(file.id)
    }
  }

  ///

  onSelectPOImage(event): void {

    if (event.target.files && event.target.files[0]) {
      let fileData = null;
      const max_file_size = 5242880;
      let selectedFile = Array.from(event.target.files || []);;
      if (selectedFile.length) {
        const obj = selectedFile.map((file: File) => {
          const ext = file.name.substr(file.name.lastIndexOf('.') + 1);
          const ext1 = (ext).toLowerCase();
          if (ext1 === 'jpeg' || ext1 === 'png' || ext1 === 'jpg' || ext1 === 'jfif') {
            if (file.size > max_file_size) {
              this.utilsService.toasterService.error(this.utilsService.validationService.IMAGE_MAX_FILE_SIZE)
            } else {
              const fileUrl = URL.createObjectURL(file);
              // fileData = {
              //   id: null,
              //   originalname: fileUrl,
              //   fileName: file.name,
              //   file,
              //   isFlag: false,
              // };
            }
          }
          else {
            this.utilsService.toasterService.error(this.utilsService.validationService.IMAGE_INVALID_EXTENSION)
          }
        });
      }
    }
  }

  onChangePerisableAlert(type: boolean) {
    this.itemObj.days = null;
    this.itemForm.controls['days'].reset();
    this.itemForm.controls['days'].clearValidators();
    if (type) {
      this.itemForm.controls['days'].setValidators([Validators.compose([Validators.required])]);
    }
    this.itemForm.controls['days'].updateValueAndValidity();
  }

  onChangeRegularSeason(type: boolean) {
    this.itemObj.seasonMasterId = null;
    this.itemObj.tempAdvPODate = null;
    this.itemForm.controls['seasonMasterId'].reset();
    this.itemForm.controls['seasonMasterId'].clearValidators();
    if (type) {
      this.itemForm.controls['seasonMasterId'].setValidators([Validators.compose([Validators.required])]);
    }
    this.itemForm.controls['seasonMasterId'].updateValueAndValidity();
  }

  // documents

  onSelectAttachments(event): void {

    let selectedFiles: FileList | null = null;
    if (event.type === 'drop') {
      event.preventDefault();
      selectedFiles = event.dataTransfer?.files;
    }

    if (event.type === 'dragover') {
      event.preventDefault();
    }

    if (event.type === 'change') {
      selectedFiles = event.target.files;
    }

    if (event.type === 'paste') {
      const items = (event.clipboardData.items);
      const dataTransfer = new DataTransfer();

      for (const item of items) {
        if (item.type.indexOf('image') === 0) {
          const blob = item.getAsFile();
          const fileName = blob?.name;
          if (blob !== null && fileName) {
            const fileName = `${Date.now()}-image.${item.type.split('/')[1]}`;
            const fileFromBlob = new File([blob], fileName, { type: item.type });
            dataTransfer.items.add(fileFromBlob);
          }
        }
      }
      selectedFiles = dataTransfer.files;
    }

    const max_file_size = 5242880;

    if (selectedFiles) {
      Array.from(selectedFiles).forEach((file: File, i: number) => {
        const ext = file.name.substr(file.name.lastIndexOf('.') + 1).toLowerCase();

        if (['jpeg', 'png', 'jpg', 'jfif', 'm4v', 'mp4', 'webp', 'avif'].includes(ext)) {
          if (file.size > max_file_size) {
            this.utilsService.toasterService.error(this.utilsService.validationService.IMAGE_MAX_FILE_SIZE);
          } else {
            const fileUrl = URL.createObjectURL(file);
            let fileData = null;
            if (this.utilsService.isImage(file.name)) {
              fileData = {
                id: null,
                file: file,
                isMarkDefault: (this.itemObj.itemDocs.every(doc => doc.isMarkDefault === false)) ? true : false,
                originalName: file.name,
                formattedName: fileUrl,
              };
            }
            if (this.utilsService.isMedia(file.name)) {
              fileData = {
                id: null,
                file: file,
                isMarkDefault: false,
                originalName: file.name,
                formattedName: fileUrl,
              };
            }

            if(this.itemObj?.itemDocs.length >= 10) {
              this.utilsService.toasterService.error('You have exceeded the file upload limit of 10', '', {
                positionClass: 'toast-top-right',
                closeButton: true,
                timeOut: 10000
              });
              selectedFiles = null;
              return;
            }

            this.itemObj?.itemDocs.push(fileData);
            selectedFiles = null;
          }
        } else {
          this.utilsService.toasterService.error(this.utilsService.validationService.IMAGE_INVALID_EXTENSION);
        }
      });
    }

  }

  removeAttachment(i: number, file) {
    this.itemObj?.itemDocs?.splice(i, 1)
    if (file.id) {
      this.itemObj.deletedDocsID.push(file.id)
    }
  }

  onPrimaryChange(currentIndex: number) {
    const obj = this.itemObj?.itemDocs?.map((request, index) => {
      request.isMarkDefault = (index === currentIndex);
    });
  }

  // tab change
  onChangeTab(value: string) {
    this.selectedTab = value;
  }

  //Save

  onSaveItem(isDraft: boolean) {

    const formData = new FormData();

    if (isDraft) {
      let firstStepFields = [];
      firstStepFields = ['displayName', 'itemName'];
      firstStepFields.map(field => this.itemForm.get(field).markAsTouched());
      const firstStepValid = firstStepFields.every(field => this.itemForm.get(field).valid)

      if (!firstStepValid) {
        return;
      }
    }

    if (!isDraft) {
      const formGroups = [this.itemForm, this.discountSettingFG, this.packetInfoFG, this.poImageFGP];
      let hasInvalidFormGroup = false;
      let hasOtherDetails = false;
      let hasBreachQty = false;
      let hasPOImageError = false;
      let hasPackingInfoError = false;
      let hasDiscountError = false;
      let hasDescriptionError = false;

      for (let formGroup of formGroups) {
        if (formGroup.invalid) {
          formGroup.markAllAsTouched();

          let descriptionTab = ['hsnCodeId']
          let otherDetails = ['searchKeyWords', 'title']
          let breachQty = ['branchTransferAlert', 'branchReorderQty', 'blockBranchTransferQty', 'blockSalesQty', 'mainWarehouseAlert', 'warehouseReorderQty', 'rackBreachQty', 'rackRefillQty']

          hasDescriptionError = descriptionTab.some(field =>
            this.itemForm.get(field)?.invalid && this.itemForm.get(field)?.touched
          );

          hasOtherDetails = otherDetails.some(field =>
            this.itemForm.get(field)?.invalid && this.itemForm.get(field)?.touched
          );

          hasBreachQty = breachQty.some(field =>
            this.itemForm.get(field)?.invalid && this.itemForm.get(field)?.touched
          );

          hasPOImageError = this.poImageFGP.invalid && this.poImageFGP.touched
          hasPackingInfoError = this.packetInfoFG.invalid && this.packetInfoFG.touched
          hasDiscountError = this.discountSettingFG.invalid && this.discountSettingFG.touched

          hasInvalidFormGroup = true;
        }
      }

      if (hasInvalidFormGroup) {
        if (hasDescriptionError) {
          this.selectedTab = this.enumForTab.DESCRIPTION
          return;
        } else if (hasOtherDetails) {
          this.selectedTab = this.enumForTab.OTHER_DETAILS
          return;
        } else if (hasBreachQty) {
          this.selectedTab = this.enumForTab.BREACH_QTY
          return;
        } else if (hasPOImageError) {
          this.selectedTab = this.enumForTab.PO_IMAGE_MAPPING
          return;
        } else if (hasPackingInfoError) {
          this.selectedTab = this.enumForTab.PACKING_INFO
          return;
        } else if (hasDiscountError) {
          this.selectedTab = this.enumForTab.DISCOUNT_SETTING
          return;
        }
        return;
      }

      const noMarkAsPrimary = this.itemObj.itemDocs.every(v => !v.isMarkDefault)

      if (this.itemObj.itemDocs.length === 0) {
        this.utilsService.toasterService.error('Minimum one image is required to save item.', '', {
          positionClass: 'toast-top-right',
          closeButton: true,
          timeOut: 10000
        });
        return;
      }

      if (noMarkAsPrimary) {
        this.utilsService.toasterService.error('Minimum one image with Marked As default is required', '', {
          positionClass: 'toast-top-right',
          closeButton: true,
          timeOut: 10000
        });
        return;
      }
    }

    let fileRes = {}

    const obj = this.itemObj.itemDocs.filter(a => !a.id).map((v, i) => {
      if (v.file) {
        if (v.isMarkDefault) {
          fileRes[i] = v.file.name;
          formData.set('itemsReqs', JSON.stringify(fileRes));
        }
        formData.append('itemImg', v.file);
      }
    })

    this.itemObj.itemDocs.map((v, i) => {
      if (!v.file) {
        if (v.isMarkDefault) {
          formData.set('itemsId', JSON.stringify(v.id));
        }
      }
    })

    if (!this.isDiscountRangeValid()) {
      this.utilsService.toasterService.error('Discount Setting should not allow a duplicate range.', '', {
        positionClass: 'toast-top-right',
        closeButton: true,
        timeOut: 10000
      });
      return;
    }

    if (this.discountSettingList) {
      formData.set('discountSetting', JSON.stringify(Serialize(this.discountSettingList)))
    }

    if (!this.isPackingInfoListValid()) {
      this.utilsService.toasterService.error('Packing Information should not allow a duplicate range.', '', {
        positionClass: 'toast-top-right',
        closeButton: true,
        timeOut: 10000
      });
      return;
    }
    
    if (this.packingInfoList) {
      formData.set('packingInfoReq', JSON.stringify(Serialize(this.packingInfoList)))
    }

    if (this.levelList) {
      let level = this.levelList.map(v => (
        {
          levelId: v.id,
          breachQtys: v.breachQtys
        }
      ));
      formData.set('breachMappings', JSON.stringify(Serialize(level)))
    }

    //// PO IMAGE MAPPING 

    let errorItemLink = false;
    this.pOImageMapping.forEach(a => {
      // a.sortedColors = a.colors.sort((a, b) => a - b);
      const hashArray = (array) => {
        return array.slice().sort().join(',');
      };
      const duplicate = this.pOImageMapping.some((item, index) => { return index !== this.pOImageMapping.indexOf(a) && hashArray(item.colors) === hashArray(a.colors);});
      if (duplicate) {
        errorItemLink = true;
        this.selectedTab = this.enumForTab.PO_IMAGE_MAPPING
        this.utilsService.toasterService.error('Colors should be unique.', '', {
          positionClass: 'toast-top-right',
          closeButton: true,
          timeOut: 10000
        });
      }
    });
    if (errorItemLink) {
      return;
    }
    
    if (this.pOImageMapping.length >= 1) {
      if (this.pOImageMapping.some(a => !a.images || a.images.length === 0)) {
        this.selectedTab = this.enumForTab.PO_IMAGE_MAPPING
        this.utilsService.toasterService.error('Minimum one PO Color Image is required for PO Image Mapping', '', {
          positionClass: 'toast-top-right',
          closeButton: true,
          timeOut: 10000
        });
        return;
      }
    }

    this.pOImageMapping.map(v => {
      v.images.map(a => {
        if (a.file) {
          formData.append('poImage', a.file)
        }

      })
    })

    let POImages = []
    let imageIndex = 0;
    const imgObj = this.pOImageMapping.map(mapping => {
      const transformedMapping: any = {
        id: mapping.id ? mapping.id : null,
        colorIds: mapping.colors,
        poImages: mapping.images.filter(a => !a.id).map((image) => {
          const currentImageIndex = imageIndex++;
          return {
            url: image.fileName ? image.fileName : image.originalName,
            imageIndex: image.file ? currentImageIndex : null,
          };
        })
      };
      POImages.push(transformedMapping);
    });


    const imgMapping =
    {
      // colorIds: this.pOImageMapping.map(v => v.colors).flatMap(v => v),
      mappingReq: POImages
    }

    formData.set('poColorImageRequest', JSON.stringify(POImages))
    formData.set('breachQty', JSON.stringify(Serialize(this.breachQty)))
    ////////

    // this.itemObj.breachQty = Serialize(this.breachQty);
    if (isDraft) {
      this.itemObj.isSaveAsDraft = true
    }
    else {
      this.itemObj.isSaveAsDraft = false;
    };

    const param = Serialize(this.itemObj) as Item;

    // media links
    if (param.mediaLinks) {
      param.mediaLinks = param.mediaLinks.filter(a => {
        if (a.link === undefined || a.link === null || a.link === '') {
          if (a.id) {
            param.deletedMediaLinksID.push(a.id);
          }
          return false;
        }
        return true;
      });
    }

    // bullet points
    if (param.bulletPoints) {
      const bulletPoints = param.bulletPoints.filter(a => {
        if (a.points === undefined || a.points === null || a.points === '') {
          if (a.id) {
            param.deletedBulletPointsID.push(a.id);
          }
          return false;
        }
        return true;
      }).map(a => {
        const obj = {
          points: a?.points,
          id: a?.id ? a.id : null,
        }
        return obj;
      })
      param.bulletPoints = Serialize(bulletPoints);
    }
    
    formData.set('itemInfo', JSON.stringify(param));

    this.utilsService.postMethodAPI(true, this.utilsService.serverVariableService.ITEM_SAVE_EDIT_DELETE, formData, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.utilsService.redirectTo('/users/inventory/items/')
      }
    })

  }

  isPackingInfoListValid(): boolean {
    const list = this.packingInfoList.map(item => ({
      from: Number(item.qtyRangeFrom),
      to: Number(item.qtyRangeTo)
    }));

    for (let i = 0; i < list.length; i++) {
      const current = list[i];

      for (let j = 0; j < list.length; j++) {
        if (i === j) continue;

        const compare = list[j];
        if (current.from < compare.to && current.to >= compare.from) {
          this.selectedTab = this.enumForTab.PACKING_INFO
          return false;
        }
      }
    }
    return true;
  }

  isDiscountRangeValid(): boolean {
    const list = this.discountSettingList.map(item => ({
      from: Number(item.orderQtyFrom),
      to: Number(item.orderQtyTo)
    }));

    for (let i = 0; i < list.length; i++) {
      const current = list[i];

      for (let j = 0; j < list.length; j++) {
        if (i === j) continue;

        const compare = list[j];
        if (current.from < compare.to && current.to >= compare.from) {
          this.selectedTab = this.enumForTab.DISCOUNT_SETTING
          return false;
        }
      }
    }
    return true;
  }
  
  //HSN

  hsnFormGroup() {
    this.hsnGroup = this.fb.group({
      hsn_code: ['', Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.PATTERN_FOR_ALPHABATES_NUMBER_AND_SPACE_DOT)])],
      slab: ['', Validators.compose([Validators.required])],
      description: [''],
    })
  }

  getHSNRequiredData() {

    this.gstSlabDropdown = []

    this.utilsService.getMethodAPI(false, this.utilsService.serverVariableService.HSN_DROPDOWN_DATA, null, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.gstSlabDropdown = response.gst_slab;
        this.gstSlabDropdown = this.gstSlabDropdown ? this.utilsService.transformDropdownItems(this.gstSlabDropdown) : [];
        this.gstSlabDropdown = this.utilsService.filterIsActiveLV(this.gstSlabDropdown, null);
      }
    })
  }

  openAddEditModal() {
    this.showHsnModal = true;
    this.hsnObj = new HsnCode();
    this.hsnGroup.reset();
    this.getHSNRequiredData();
    setTimeout(() => {
      this.hsnObj.isActive = true;
    }, 100);

    this.itemAddHsnMasterModal.show();
  }

  onSaveHsnCode() {

    if (this.hsnGroup.invalid) {
      this.hsnGroup.markAllAsTouched();
      return;
    }

    this.hsnObj.taxesComplianceId = Serialize(this.hsnObj.taxId)
    let param = this.utilsService.trimObjectValues(Serialize(this.hsnObj));
    param['isActive'] = true
    this.utilsService.postMethodAPI(true, this.utilsService.serverVariableService.HSN_SAVE_EDIT_DELETE, param, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.itemAddHsnMasterModal.hide();
        this.getRequiredData();
        this.itemObj.hsnCodeId = response.id
      }
    })

  }

  // UNit 

  unitFormGroup() {
    this.unitGroup = this.fb.group({
      name: ['', Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.PATTERN_FOR_ALPHANUMERIC_WITH_SPECIAL_CHAR)])],
      short_code: ['', Validators.compose([Validators.required])],
      status: [true],
      category : [true, Validators.compose([Validators.required])],
      conversionToMeter : [true, Validators.compose([Validators.required])]
    })
  }

  openAddEditModalUnit() {
    this.showUnitModal = true;
    this.unitObj = new Unit();
    this.unitGroup.reset();
    this.getAllUnitCategory()
    setTimeout(() => {
      this.unitObj.isActive = true;
    }, 100);

    this.itemAddUnitMaster.show();
  }

  onSaveUnit() {

    if (this.unitGroup.invalid) {
      this.unitGroup.markAllAsTouched();
      return;
    }

    let param = this.utilsService.trimObjectValues(Serialize(this.unitObj));
    this.utilsService.postMethodAPI(true, this.utilsService.serverVariableService.UNIT_SAVE_EDIT_DELETE, param, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.itemAddUnitMaster.hide();
        this.getRequiredData();
        this.itemObj.unitId = response.id
      }
    })

  }

  getAllUnitCategory() {
    this.utilsService.getMethodAPI(false, this.utilsService.serverVariableService.UNIT_ALL_CATEGORY, null, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.allCategories = response;
      }
    });
  }

  // Item Group Addd

  itemGroupModal() {
    this.itemGroupForm = this.fb.group({
      name: ['', Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.PATTERN_FOR_ALPHANUMERIC_WITH_SPECIAL_CHAR)])],
      category: [null, Validators.required],
      hsn: [null],
    });
  }

  openAddEditModalIG() {
    this.showItemGroupModal = true;
    this.itemGroupObj = new ItemGroup();
    this.itemGroupForm.reset();
    setTimeout(() => {
      this.itemGroupObj.isActive = true;
      this.getRequiredDataItemGroup();
    }, 100);

    this.itemGroupAddModal.show();
  }

  // category dropdown
  modifyParentResponse(category: any[]) {
    let flatList = [];
    for (let parent of (category || [])) {
      parent.className = "ng-select-option-" + (parent.index + 1);
      flatList.push(parent);
      if (parent.categoryList && parent.categoryList.length > 0) {
        let data = (parent.categoryList || []).filter(p => p.index < 4);
        for (let d of data) {
          d.className = "ng-select-option-" + (d.index + 1);
          d.isChild = true;
        }
        flatList = flatList.concat(this.modifyParentResponse(data));
      }
    }
    return flatList;
  }

  getParentCategory() {
    let data = this.assignCategoryIndex(this.dropdown.category)
    data = (data || []).filter(list => list.index == 0);
    this.flattenedParentCategory = this.modifyParentResponse(data);
    this.flattenedParentCategory = this.flattenedParentCategory ? this.utilsService.transformDropdownItems(this.flattenedParentCategory) : [];
    this.flattenedParentCategory = this.utilsService.filterIsActive(this.flattenedParentCategory, null);
  }

  assignCategoryIndex(categories: Category[], depth: number = 0): any[] {
    return categories.map((category) => {
      category.index = depth;
      if (category.categoryList && category.categoryList.length > 0) {
        category.categoryList = this.assignCategoryIndex(category.categoryList, depth + 1);
      }
      return category;
    });
  }

  getRequiredDataItemGroup() {
    this.categoryList = [];
    this.utilsService.getMethodAPI(false, this.utilsService.serverVariableService.IG_DROPDOWN, null, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.hsnCodeDropdown = response.hsn_code;
        this.hsnCodeDropdown = this.hsnCodeDropdown ? this.utilsService.transformDropdownItems(this.hsnCodeDropdown) : [];
        this.hsnCodeDropdown = this.utilsService.filterIsActiveLV(this.hsnCodeDropdown, null);
        this.categoryList = (response?.category);
        setTimeout(() => {
          this.getParentCategory();
        }, 150);
      }
    })
  }

  openLink(link, newUpload: any) {
    const filePreview = link ? `${this.utilsService.imgPath}${link}` : null

    if (!this.utilsService.isEmptyObjectOrNullUndefined(newUpload)) {
      window.open(newUpload, "_blank");
      return;
    }

    if (!this.utilsService.isEmptyObjectOrNullUndefined(filePreview)) {
      window.open(filePreview, "_blank");
      return;
    }
  }

  onClearGroupCode = () => {
    this.itemForm.get('groupCodeId').setValue(null);
    this.itemObj.groupCodeId = null;
  }

}
