<div class="page-content">
    <div class="page-title-wrapper">
        <div class="page-title-left">
            <h4>Add New Ticket </h4>
        </div>
        <div class="page-title-right">
            <Button class="btn btn-sm btn-icon btn-outline-white" [routerLink]="['/users/audit-tickets/list']"
                ngbTooltip="Close" placement="left" container="body" triggers="hover">
                <i class="th th-close"></i>
            </Button>
        </div>
    </div>
    <div class="content-area">
        <div class="card card-theme card-forms">
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-lg-4 col-md-4 col-sm-12">
                        <div class="form-group theme-ngselect form-group-inline-control required">
                            <label class="form-label">Ticket Subject</label>
                            <div class="form-control-wrapper ">
                                <ng-select class="" placeholder="Select " [multiple]="false" [clearable]="false"
                                    [items]="Option" bindLabel="name" bindValue="id" [(ngModel)]="selectedOption">
                                </ng-select>
                            </div>
                        </div>
                        <div class="form-group theme-ngselect form-group-inline-control required">
                            <label class="form-label">Ticket Type</label>
                            <div class="form-control-wrapper ">
                                <ng-select class="" placeholder="Select " [multiple]="false" [clearable]="false"
                                    [items]="Option" bindLabel="name" bindValue="id" [(ngModel)]="selectedOption">
                                </ng-select>
                            </div>
                        </div>
                        <div class="form-group theme-ngselect form-group-inline-control required">
                            <label class="form-label">Branch</label>
                            <div class="form-control-wrapper ">
                                <ng-select class="" placeholder="Select " [multiple]="false" [clearable]="false"
                                    [items]="Option" bindLabel="name" bindValue="id" [(ngModel)]="selectedOption">
                                </ng-select>
                            </div>
                        </div>
                        <div class="form-group theme-ngselect form-group-inline-control required">
                            <label class="form-label">Warehouse</label>
                            <div class="form-control-wrapper ">
                                <ng-select class="" placeholder="Select " [multiple]="false" [clearable]="false"
                                    [items]="Option" bindLabel="name" bindValue="id" [(ngModel)]="selectedOption">
                                </ng-select>
                            </div>
                        </div>
                        <div class="form-group theme-ngselect form-group-inline-control ">
                            <label class="form-label">Assign User</label>
                            <div class="form-control-wrapper ">
                                <ng-select class="" placeholder="Select " [multiple]="false" [clearable]="false"
                                    [items]="Option" bindLabel="name" bindValue="id" [(ngModel)]="selectedOption">
                                </ng-select>
                            </div>
                        </div>
                        <div class="form-group theme-ngselect form-group-inline-control ">
                            <label class="form-label">Subject Status</label>
                            <div class="form-control-wrapper ">
                                <ng-select class="" placeholder="Select " [multiple]="false" [clearable]="false"
                                    [items]="Option" bindLabel="name" bindValue="id" [(ngModel)]="selectedOption">
                                </ng-select>
                            </div>
                        </div>

                        <div class="form-group form-group-inline-control ">
                            <label class="form-label">Ticket Note</label>
                            <div class="form-control-wrapper ">
                                <textarea placeholder="Enter Note" class="form-control"></textarea>
                            </div>
                        </div>

                        <div class="form-group form-group-inline-control ">
                            <label class="form-label"></label>
                            <div class="form-control-wrapper ">
                                <div class="checkbox checkbox-primary checkbox-small">
                                    <input type="checkbox" id="photos" class="material-inputs filled-in" />
                                    <label for="photos"> Need photos when close ticket </label>
                                </div>
                            </div>
                        </div>
                    </div>


                    <div class="col-lg-4 col-md-4 col-sm-12 ">
                        <div class="attachments-wrapper">
                            <div class='attachments-container h-100'>
                                <div class='attachments-content'>
                                    <button class='btn btn-attachments'><i class="bi bi-upload"></i></button>
                                    <p>Drag and Drop Images & Videos here or <span class='text-primary'>Choose
                                            file</span></p>
                                </div>
                                <input type="file" ref={imageRef} multiple />
                            </div>
                            <div class='attachments-upload-grid-container attachments-upload-grid-container2'>
                                <div class='attachments-upload-row'>
                                    <div class='attachments-upload-col'>
                                        <div class='card-attachments-upload'>
                                            <div class='attachments-image'>
                                                <img src="assets/images/avatar.jpg" alt="valamji" />
                                            </div>
                                            <div class="attachments-text">
                                                <h6 class="file-name">Filename.jpg</h6>
                                                <p class="file-size">Size: 5mb</p>
                                            </div>
                                            <button class="btn-close" variant="close"><i
                                                    class='th th-close'></i></button>
                                        </div>

                                    </div>
                                    <div class='attachments-upload-col'>
                                        <div class='card-attachments-upload'>
                                            <div class='attachments-image'>
                                                <img src="assets/images/avatar.jpg" alt="valamji" />
                                            </div>
                                            <div class="attachments-text">
                                                <h6 class="file-name">Filename.jpg</h6>
                                                <p class="file-size">Size: 5mb</p>
                                            </div>
                                            <button class="btn-close" variant="close"><i
                                                    class='th th-close'></i></button>
                                        </div>

                                    </div>
                                    <div class='attachments-upload-col'>
                                        <div class='attachments-container attachments-container2'>
                                            <div class='attachments-content'>
                                                <button class='btn btn-primary btn-icon btn-sm btn-round'><i
                                                        class="th th-outline-add"></i></button>
                                            </div>
                                            <input type="file" ref={imageRef} multiple />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>


                </div>
                <hr>
                <div class="row mb-2">
                    <div class="col-lg-12 col-md-12 col-sm-12 mt-3">
                        <div class="inner-title-wrapper">
                            <div class="inner-title-left flex-column align-items-start gap-2 ">
                                <div class="inner-title-text">
                                    <h6 class="">Associated Item*</h6>
                                </div>
                                <div
                                    class="form-group form-group-inline justify-content-start gap-2 align-items-center mb-0">
                                    <div class="form-label">By Item</div>
                                    <div class="switch-box switch-box-warning">
                                        <label class="switch" htmlFor="switchWarning">
                                            <input type="checkbox" id='switchWarning' checked />
                                            <div class="slider round"></div>
                                        </label>
                                    </div>
                                    <div class="form-label">By Location</div>
                                </div>
                            </div>
                            <div class="inner-title-rigth">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12">
                        <div class="table-responsive">
                            <table class="table-theme table-hover table table-bordered table-sticky">
                                <thead class="border-less">
                                    <tr>
                                        <th>Item</th>
                                        <th>Current Location</th>
                                        <th>Available Carton</th>
                                        <th>Total Carton Qty</th>
                                        <th>Loose Qty</th>
                                        <th>Total Qty</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr *ngFor="let item of [1,2,]">
                                        <td class="tbl-user">
                                            <div class="tbl-user-checkbox-srno">
                                                <span>01.</span>
                                                <div class="tbl-user-wrapper">
                                                    <div class="tbl-user-image"><img
                                                            src="assets/images/dummy-product.png" alt="valamji"></div>
                                                    <div class="tbl-user-text-action">
                                                        <div class="tbl-user-text">
                                                            <p>Ink Pen</p>
                                                            <span>SKU #58545854585</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>Z-11, A11, A-12</td>
                                        <td>
                                            <div class="dropdown dropdown-with-tables">
                                                <button class="btn btn-link" type="button" data-bs-toggle="dropdown"
                                                    aria-expanded="false" data-bs-auto-close="outside"
                                                    data-bs-popper-config='{"strategy":"fixed"}'>
                                                    50
                                                </button>
                                                <div class="dropdown-menu">
                                                    <div class="table-responsive ">
                                                        <table
                                                            class="table-theme table-hover table table-bordered table-sticky ">
                                                            <thead class="border-less">
                                                                <tr>
                                                                    <th class="">
                                                                        Marko
                                                                    </th>
                                                                    <th>Carton
                                                                        Qty
                                                                    </th>
                                                                    <th>Item/Carton
                                                                    </th>
                                                                    <th>Total C.
                                                                        Qty
                                                                    </th>
                                                                    <th>Loose
                                                                    </th>
                                                                    <th>Total
                                                                    </th>
                                                                    <th>Location
                                                                    </th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                <tr *ngFor="let item of [1,2,3,4,5]">
                                                                    <td class="tbl-user">
                                                                        <div class="tbl-user-checkbox-srno">
                                                                            <div class="tbl-user-wrapper">
                                                                                <div class="tbl-user-image">
                                                                                    <img src="assets/images/dummy-product.png"
                                                                                        alt="valamji">
                                                                                </div>
                                                                                <div class="tbl-user-text">
                                                                                    <p>ZA-50-585-12
                                                                                    </p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </td>
                                                                    <td> 5</td>
                                                                    <td>50</td>
                                                                    <td>250</td>
                                                                    <td>250</td>
                                                                    <td>250</td>
                                                                    <td>Z-11
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                            <tfoot>
                                                                <tr class="tbl-total">
                                                                    <td>Total
                                                                    </td>
                                                                    <td>20</td>
                                                                    <td>-</td>
                                                                    <td>100</td>
                                                                    <td>1000
                                                                    </td>
                                                                    <td>1000
                                                                    </td>
                                                                    <td>-</td>
                                                                </tr>
                                                            </tfoot>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>5000</td>
                                        <td>500</td>
                                        <td>5500</td>
                                    </tr>
                                    <tr class="tbl-add-row">
                                        <td class=" tbl-user">
                                            <div class="tbl-user-wrapper">
                                                <div class="tbl-user-image"><img src="assets/images/dummy-product.png"
                                                        alt="valamji">
                                                </div>
                                                <div class="tbl-user-text tbl-form-group-borderless">
                                                    <div class="form-group theme-ngselect">
                                                        <ng-select class="" placeholder="Select" [multiple]="false"
                                                            [clearable]="false" [items]="demo2" bindLabel="name"
                                                            bindValue="id" [(ngModel)]="selectedDemo2" appendTo="body">
                                                        </ng-select>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="tbl-form-group-borderless">
                                            <div class="form-group">
                                                <input type="text" class="form-control" placeholder="-">
                                            </div>
                                        </td>

                                        <td class="tbl-form-group-borderless">
                                            <div class="form-group">
                                                <input type="text" class="form-control" placeholder="-">
                                            </div>
                                        </td>

                                        <td class="tbl-form-group-borderless">
                                            <div class="form-group">
                                                <input type="text" class="form-control" placeholder="-">
                                            </div>
                                        </td>
                                        <td class="tbl-form-group-borderless">
                                            <div class="form-group">
                                                <input type="text" class="form-control" placeholder="-">
                                            </div>
                                        </td>
                                        <td class="tbl-form-group-borderless">
                                            <div class="form-group">
                                                <input type="text" class="form-control" placeholder="-">
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                                <tfoot>
                                    <tr class="tbl-add-new">
                                        <td colspan="100">
                                            <button class="btn btn-sm btn-link btn-icon-text text-primary"> <i
                                                    class="th-bold-add-circle"></i>
                                                Add New Row
                                            </button>
                                        </td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>

                <hr>
                <div class="row mb-2">
                    <div class="col-lg-12 col-md-12 col-sm-12 mt-3">
                        <div class="inner-title-wrapper">
                            <div class="inner-title-left flex-column align-items-start gap-2 ">
                                <div class="inner-title-text">
                                    <h6 class="">Associated Item*</h6>
                                </div>
                                <div
                                    class="form-group form-group-inline justify-content-start gap-2 align-items-center mb-0">
                                    <div class="form-label">By Item</div>
                                    <div class="switch-box ">
                                        <label class="switch" htmlFor="switchWarning">
                                            <input type="checkbox" id='switchWarning' checked />
                                            <div class="slider round"></div>
                                        </label>
                                    </div>
                                    <div class="form-label">By Location</div>
                                </div>
                            </div>
                            <div class="inner-title-rigth">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12">
                        <div class="table-responsive">
                            <table class="table-theme table-hover table table-bordered table-sticky">
                                <thead class="border-less">
                                    <tr>
                                        <th>Location</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr *ngFor="let item of [1,2]">
                                        <td class="tbl-form-group-borderless">
                                            <div class="form-group theme-ngselect">
                                                <ng-select class="" placeholder="Z-11, A11, A-12" [multiple]="false"
                                                    [clearable]="false" [items]="demo2" bindLabel="name" bindValue="id"
                                                    [(ngModel)]="selectedDemo2" appendTo="body">
                                                </ng-select>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class='bottombar-wrapper bottom-fixed'>
            <div class='bottombar-container'>
                <div class='bottombar-left'>
                    <button type="button" class="btn btn-primary btn-icon-text btn-sm"> <i
                            class="th th-outline-tick-circle"></i>
                        Save</button>
                    <button type="button" class="btn btn-outline-white btn-icon-text btn-sm"><i
                            class="th th-outline-close-circle"
                            [routerLink]="['/users/audit-tickets/list']"></i>Cancel</button>
                </div>
                <div class='bottombar-right'>

                </div>
            </div>
        </div>
    </div>
</div>