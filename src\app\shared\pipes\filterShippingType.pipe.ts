import { Pipe, PipeTransform } from '@angular/core';
import { EnumForShippingType } from '@enums/EnumForShippingType.enum';
import { UtilsService } from '@service/utils.service';

@Pipe({
  name: 'filterByShippingType'
})

export class FilterShippingTypePipe implements PipeTransform {

  enumForShippingType = EnumForShippingType

  constructor(private utilsService: UtilsService) {}

  transform(value: any[], shippingType: string): any[] {
    if (!value) {
      return value;
    }

    if(this.utilsService.isEmptyObjectOrNullUndefined(shippingType)) {
      return value.filter(a => a.class !== 'tbl-bg-secondary' && a.class !== 'tbl-bg-success' && a.class !== 'tbl-bg-danger' && a.class !== 'tbl-bg-secondary-two' && a.class !== 'tbl-bg-primary');
    }

    switch (shippingType) {
      case this.enumForShippingType.CBM:
        return value.filter(a => a.class !== 'tbl-bg-secondary-two' && a.class !== 'tbl-bg-success' && a.class !== 'tbl-bg-danger' && a.class !== 'tbl-bg-primary');
      case this.enumForShippingType.PERCENTAGE:
        return value.filter(a => a.class !== 'tbl-bg-secondary' && a.class !== 'tbl-bg-secondary-two' && a.class !== 'tbl-bg-primary' && a.class !== 'tbl-bg-danger');
      case this.enumForShippingType.WEIGHT:
        return value.filter(a => a.class !== 'tbl-bg-secondary' && a.class !== 'tbl-bg-success' && a.class !== 'tbl-bg-primary' && a.class !== 'tbl-bg-danger');
      case this.enumForShippingType.DONE:
        return value.filter(a => a.class !== 'tbl-bg-secondary' && a.class !== 'tbl-bg-success' && a.class !== 'tbl-bg-primary' && a.class !== 'tbl-bg-secondary-two');
      case this.enumForShippingType.PIECE:
        return value.filter(a => a.class !== 'tbl-bg-secondary' && a.class !== 'tbl-bg-success' && a.class !== 'tbl-bg-danger' && a.class !== 'tbl-bg-secondary-two');
      default:
      return value.filter(a => a.class !== 'tbl-bg-secondary' && a.class !== 'tbl-bg-success' && a.class !== 'tbl-bg-danger' && a.class !== 'tbl-bg-secondary-two' && a.class !== 'tbl-bg-primary');
    }
  }

}
