import { Component, EventEmitter, Input, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormArray, FormGroup } from '@angular/forms';
import { Item } from 'src/app/models/Item';
import { ItemDropdown } from 'src/app/shared/constants/interface';
import { UtilsService } from 'src/app/shared/services/utils.service';

@Component({
  selector: 'app-item-description',
  templateUrl: './item-description.component.html',
  styleUrls: ['./item-description.component.scss']
})
export class ItemDescriptionComponent implements OnInit {

  @Input({ alias: 'itemForm', required: true }) itemForm: FormGroup;
  @Input({ alias: 'itemObj', required: true }) itemObj: Item;
  @Input({ alias: 'dropdown', required: true }) dropdown: ItemDropdown;

  @Output() openAddEditModal: EventEmitter<any> = new EventEmitter<any>();

  @Output() addMediaLink: EventEmitter<any> = new EventEmitter<any>();
  @Output() removeMediaLink: EventEmitter<any> = new EventEmitter<any>();

  constructor(public utilsService: UtilsService) { }

  ngOnInit(): void {
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (!this.itemObj.id) {
      if (this.dropdown?.itemDimUnit && !this.itemObj.itemsDimId) {
        this.itemObj.itemsDimId = this.dropdown.itemDimUnit.filter(d => d.shortCode == 'CM')[0]?.id;
      }
      if (this.dropdown?.boxUnit && !this.itemObj.boxDimId) {
        this.itemObj.boxDimId = this.dropdown.boxUnit.filter(d => d.shortCode == 'CM')[0]?.id;
      }
      if (this.dropdown?.itemWeightDimUnit && !this.itemObj.itemsWeightDimId) {
        this.itemObj.itemsWeightDimId = this.dropdown.itemWeightDimUnit.filter(d => d.shortCode == 'KG')[0]?.id;
      }
      if (this.dropdown?.boxWeightUnit && !this.itemObj.boxWeightDimId) {
        this.itemObj.boxWeightDimId = this.dropdown.boxWeightUnit.filter(d => d.shortCode == 'KG')[0]?.id;
      }
    }
  }

  // unit change 
  onChangeUnit(keyName: string, item) {
    if(item.isActive) {
      this.itemObj[keyName] = item.value
      this.itemObj[keyName] = item.label
    }
  }

  openHsnModal() {
    this.openAddEditModal.emit();
  }

  onClearQC() {
    this.itemObj.qcChecklistId = null;
  }

  onChangeBoxDim() {
    if (this.itemObj.boxLength && this.itemObj.boxWidth && this.itemObj.boxHeight && this.itemObj.boxDimId) {
      let unit = this.dropdown.boxUnit.find(u => u.id == this.itemObj.boxDimId);
      let volume = unit.conversionToMeter;
      this.itemObj.singlePieceVolume = ((this.itemObj.boxLength * volume) * (this.itemObj.boxWidth * volume) * (this.itemObj.boxHeight * volume)) / 5000;
    }
    else {
      this.itemObj.singlePieceVolume = null;
    }
  }

  ///

  get mediaLinks() {
    return (this.itemForm.get('mediaLinks') as FormArray);
  }

  removeMedia(i: number) {
    this.removeMediaLink.emit(i)
  }

  // Search

  customSearchFn(term: string, item: any) {
    term = term.toLocaleLowerCase();
    return item.hsnCode.toLocaleLowerCase().indexOf(term) > -1 ||
      item?.description?.toLocaleLowerCase().indexOf(term) > -1 ||
      (item.hsnCode + " - " + item.description).toLocaleLowerCase().indexOf(term) > -1;
  }

}
