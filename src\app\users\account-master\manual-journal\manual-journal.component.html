<div class="page-content"
  [pageAccess]="{page: utilsService.enumForPage.ACCOUNTS, action: utilsService.enumForPage.VIEW_ACCOUNTS, view: true}">
  <div class="page-title-wrapper">
    <div class="page-title-left">
      <h4>Manual Journal Report</h4>
    </div>
    <div class="page-title-right">
      <button (click)="redirectToDetails()" class="btn btn-sm btn-primary btn-icon-text"
        [routerLink]="['/users/accounts/manual-journal/new-manual-journal']">
        <i class="th th-outline-add-circle"></i>Add New
      </button>
      <button (click)="getAllMJ()" class="btn btn-sm btn-icon btn-outline-white" ngbTooltip="Refresh" placement="left"
        container="body" triggers="hover">
        <i class="th th-outline-refresh-2"></i>
      </button>
    </div>
  </div>
  <div class="content-area">
    <div class="page-filters">
      <div class="page-filters-left">
        <div class="form-group form-group-sm">
          <div class="form-group-icon-start">
            <i class="th th-outline-search-normal-1 icon-broder "></i>
            <input (input)="onSearch($event)" [(ngModel)]="paginationRequest.searchText" type="search"
              class="form-control" placeholder="Search">
          </div>
        </div>
        <div class="form-group form-group-sm date-range-filter">
          <div class="form-group-icon-end">
            <i (click)="open()" class="th th-outline-calendar ngx-daterangepicker-action"></i>
            <input (change)="onChangeDateRange()" class="form-control" type="text" ngxDaterangepickerMd [(ngModel)]="dateRange"
              [showCustomRangeLabel]="true" [alwaysShowCalendars]="true" [ranges]="utilsService.ranges" [linkedCalendars]="false"
              [showClearButton]="false" placeholder="Date Range" [autoApply]="true" [showRangeLabelOnInput]="true"
              startKey="start" endKey="end">
          </div>
        </div>
        <button (click)="onClear()" class="btn btn-link btn-sm">Clear</button>
      </div>
      <div class="page-filters-right">
      </div>
    </div>
    <div class="card card-theme card-table-sticky">
      <div class="card-body p-0">
        <div class="table-responsive">
          <table class="table-theme table-hover table table-bordered ">
            <thead class="border-less">
              <tr>
                <th *ngFor="let th of mjTH; index as j" [class]="th.class"
                  [ngClass]="{'sorting-asc': paginationRequest.sortColumn==th.keyName && paginationRequest.sortOrder === enumForSortOrder.A, 
                              'sorting-desc': paginationRequest.sortColumn==th.keyName && paginationRequest.sortOrder === enumForSortOrder.D }"
                  (click)="!utilsService.isNullUndefinedOrBlank(th.keyName) ? onSortTH(th.keyName) : $event.preventDefault()">
                  {{th.displayName}}
                </th>
                <th class="text-center"
                  *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.EDIT_ACCOUNTS, this.utilsService.enumForPage.DELETE_ACCOUNTS])">
                  Action
                </th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let item of mjList; index as i; trackBy: trackBy">
                <td class="tbl-user tbl-bold">
                  <div class="tbl-user-checkbox-srno">
                    <span>{{item.date | date: 'dd/MM/YYYY'}}</span>
                  </div>
                </td>
                <td class="text-primary fw-600">#{{item.journalNumber}}</td>
                <!-- <td>545554</td> -->
                <td>₹{{item.amount}}</td>
                <td class="tbl-description">
                  <div>{{item.note ? item.note : '-'}}</div>
                </td>
                <td>{{item.created ? item.created : '-'}}</td>
                <td>{{item.createdDate ? (item.createdDate | date: 'dd/MM/YYYY') : '-'}}</td>
                <td class="tbl-action"
                  *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.EDIT_ACCOUNTS, this.utilsService.enumForPage.DELETE_ACCOUNTS])">
                  <div class="tbl-action-group">
                    <button (click)="redirectToDetails()"
                      [pageAccess]="{page: this.utilsService.enumForPage.ACCOUNTS, action: this.utilsService.enumForPage.EDIT_ACCOUNTS}"
                      [routerLink]="['/users/accounts/manual-journal/edit-manual-journal/' + item.id]"
                      class="btn btn-xs btn-light-primary btn-icon" ngbTooltip="Edit" placement="bottom"
                      container="body" triggers="hover"> <i class="th th-outline-edit"></i></button>
                    <button (click)="openDeleteMJModal(item)" class="btn btn-xs btn-light-danger btn-icon"
                      [pageAccess]="{page: this.utilsService.enumForPage.ACCOUNTS, action: this.utilsService.enumForPage.DELETE_ACCOUNTS}"
                      ngbTooltip="Delete" placement="left" container="body" triggers="hover">
                      <i class="th th-outline-trash"></i>
                    </button>
                  </div>
                </td>
              </tr>
              <tr *ngIf="utilsService.isEmptyObjectOrNullUndefined(mjList)">
                <td colspan="20" class="text-center">
                  <span class="truncate">{{utilsService.serverVariableService.STRING_WHEN_NO_RECORDS_FOUND}}</span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
    <div class="paginationbox pagination-fixed">
      <app-pagination (pagesizeData)="addPageSizeData($event)" (pageNumber)="pageNumber($event)"
        [page]="paginationRequest.pageNo" [pageSize]="paginationRequest.pageSize"
        [totalData]="paginationRequest.totalData"></app-pagination>
    </div>
  </div>
</div>

<!-- ----------------------------------------------------------------------- -->
<!--                           Delete Modal Start                            -->
<!-- ----------------------------------------------------------------------- -->
<div class="modal modal-theme modal-confirmation modal-reject fade" id="deleteMJModal" tabindex="-1"
  aria-labelledby="deleteMJModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
            class='th th-close'></i></button>
      </div>
      <div class="modal-body">
        <div class="modal-confirmation-container">
          <div class="modal-confirmation-icon">
            <i class="th th-bold-trash"></i>
          </div>
          <div class="modal-confirmation-content">
            <h5>Are You Sure?</h5>
            <p>You want to Delete <b>#{{mjObj.journalNumber}}</b> Manual Journal Report.</p>
          </div>
        </div>
        <div class="modal-button-group modal-full-width-btn">
          <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
          <button (click)="deleteMJ()" type="button" class="btn btn-primary btn-icon-text"> <i
              class="th th-outline-tick-circle"></i>
            Delete</button>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                           Delete Modal End                            -->
<!-- ----------------------------------------------------------------------- -->