<div class="page-content" [pageAccess]="{page: utilsService.enumForPage.BRANCH, action: utilsService.enumForPage.VIEW_BRANCH, view: true}">
  <div class="page-title-wrapper">
    <div class="page-title-left">
      <h4>Branches</h4>
    </div>
    <div class="page-title-right">
      <button [pageAccess]="{page: utilsService.enumForPage.BRANCH, action: utilsService.enumForPage.ADD_BRANCH}"
        [routerLink]="'/users/branch-management/new-branch'" class="btn btn-sm btn-primary btn-icon-text">
        <i class="th th-outline-add-circle"></i>Add New
      </button>
      <button ngbTooltip="Refresh" placement="left" container="body" triggers="hover" (click)="getBranchList()"
        class="btn btn-sm btn-icon btn-outline-white">
        <i class="th th-outline-refresh-2"></i>
      </button>
    </div>
  </div>

  <div class="content-area" *ngIf="!utilsService.isEmptyObjectOrNullUndefined(branchList)">
    <div class="page-content-wrapper">
      <div class="row row-gap-2" cdkDropList [cdkDropListData]="branchList" cdkDropListOrientation="mixed" (cdkDropListDropped)="drop($event)">
        <div class="col-md-4" *ngFor="let item of branchList; index as i; trackBy: trackBy" cdkDrag [cdkDragData]="item" cdkDragPreviewContainer="parent">
          <div class="card card-grid card-theme2">
            <div class="card-header">
              <div class="card-header-left">
                <div class="card-grid-icon">
                  <span>{{(i + 1) | padNum}}.</span>
                </div>
                <div class="card-grid-title">
                  <h6>{{item.branchName}}</h6>
                  <p>{{item.representativeName ? item.representativeName : '-'}}</p>
                </div>
              </div>
              <div class="card-header-right">
                <button class="btn btn-primary btn-xs btn-icon-text"
                  [pageAccess]="{page: utilsService.enumForPage.BRANCH, action: utilsService.enumForPage.EDIT_BRANCH}"
                  (click)="utilsService.redirectTo('/users/branch-management/edit-branch/' + item.id)"> <i
                    class="th th-outline-edit"></i> Edit </button>
                <ng-container
                  *ngIf="utilsService.checkPageAccess([utilsService.enumForPage.EDIT_BRANCH, utilsService.enumForPage.DELETE_BRANCH])">
                  <div class="dropdown" *ngIf="!(item.isActive && item.isMainBranch)">
                    <button id="actionDropDown" data-bs-toggle="dropdown" aria-expanded="false"
                      class="btn btn-xs btn-outline-white" data-bs-popper-config='{"strategy":"fixed"}'>
                      <i class="th th-outline-more"></i>
                    </button>
                    <ul aria-labelledby="actionDropDown" class="dropdown-menu">
                      <li [pageAccess]="{page: utilsService.enumForPage.BRANCH, action: utilsService.enumForPage.EDIT_BRANCH}"
                        (click)="openMarkAsPrimary(item, true, i)" *ngIf="!item.isMainBranch && item.isActive"><a
                          class="dropdown-item">
                          <i class="th th-outline-star"></i> Mark as Org. Primary </a></li>
                      <li [pageAccess]="{page: utilsService.enumForPage.BRANCH, action: utilsService.enumForPage.EDIT_BRANCH}"
                        (click)="onChangeStatus(item, true, i)" *ngIf="item.isActive"><a class="dropdown-item"> <i
                            class="th th th-outline-slash"></i> Mark as Inactive </a></li>
                      <li [pageAccess]="{page: utilsService.enumForPage.BRANCH, action: utilsService.enumForPage.EDIT_BRANCH}"
                        (click)="onChangeStatus(item, false, i)" *ngIf="!item.isActive && !item.isMainBranch"><a
                          class="dropdown-item">
                          <i class="th th-outline-tick-circle"></i> Mark as Active </a></li>
                      <hr class="m-0"
                        *ngIf="utilsService.checkPageAccess([utilsService.enumForPage.DELETE_BRANCH]) && utilsService.checkPageAccess([utilsService.enumForPage.EDIT_BRANCH])">
                      <li [pageAccess]="{page: utilsService.enumForPage.BRANCH, action: utilsService.enumForPage.DELETE_BRANCH}"
                        *ngIf=" !item.isMainBranch" (click)="openDeleteBranchModal(item)"><a class="dropdown-item text-danger">
                          <i class="th th-outline-trash"></i> Delete</a>
                      </li>
                    </ul>
                  </div>
                </ng-container>
              </div>
            </div>
            <div class="card-body">
              <div class="card-grid-details">
                <ul>
                  <li>
                    <label>Branch Code</label>
                    <span>{{item.shortCode}}</span>
                  </li>
                  <li>
                    <label>Email</label>
                    <span>{{item.branchEmail ? item.branchEmail : '-'}}</span>
                  </li>
                  <li>
                    <label>Mobile No</label>
                    <span>{{item.branchPhone ? item.branchPhone : '-'}}</span>
                  </li>
                  <li>
                    <label>State</label>
                    <span>{{item.stateName ? item.stateName : '-'}}</span>
                  </li>
                  <li>
                    <label>City</label>
                    <span>{{item.cityName ? item.cityName : '-'}}</span>
                  </li>
                  <li>
                    <label>Address</label>
                    <span>{{item.address ? item.address : '-'}}</span>
                  </li>
                  <li>
                    <label>Location Link</label>
                    <span><a (click)="item.locationLink ? utilsService.openURL(item.locationLink) : null"
                        class="text-link">{{item.locationLink ? 'Link' : '-'}}</a></span>
                  </li>
                </ul>
              </div>
            </div>
            <div class="card-footer">
              <div class="card-grid-footer-wrapper">
                <div *ngIf="item.isMainBranch" class="badge rounded-pill badge-warning "><i class="th th-bold-star"></i>
                  Main Branch</div>
                <div *ngIf="item.isActive" class="badge rounded-pill badge-primary-light "><i class="th th-bold-tick-circle"></i>
                  Active</div>
                <div *ngIf="!item.isActive" class="badge rounded-pill badge-white-light "><i class="th th-bold-slash"></i>
                  Inactive</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <ng-container *ngIf="utilsService.isEmptyObjectOrNullUndefined(branchList)">
    <app-no-record />
  </ng-container>
</div>



<!-- ----------------------------------------------------------------------- -->
<!--                           Delete Modal Start                            -->
<!-- ----------------------------------------------------------------------- -->
<div class="modal modal-theme modal-confirmation modal-reject fade" id="deleteBranchModal" tabindex="-1"
  aria-labelledby="deleteBranchModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
            class='th th-close'></i></button>
      </div>
      <div class="modal-body">
        <div class="modal-confirmation-container">
          <div class="modal-confirmation-icon">
            <i class="th th-bold-trash"></i>
          </div>
          <div class="modal-confirmation-content">
            <h5>Are You Sure?</h5>
            <p>You want to Delete <b>{{branchObj.branchName}}</b> Branch.</p>
          </div>
        </div>
        <div class="modal-button-group modal-full-width-btn">
          <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
          <button (click)="deleteBranch()" type="button" class="btn btn-primary btn-icon-text"> <i
              class="th th-outline-tick-circle"></i>
            Delete</button>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                           Delete Modal End                            -->
<!-- ----------------------------------------------------------------------- -->


<div class="modal modal-theme modal-confirmation modal-warning-two fade" id="markAsPrimaryBModal" tabindex="-1"
  aria-labelledby="markAsPrimaryBModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
            class='th th-close'></i></button>
      </div>
      <div class="modal-body">

        <div class="modal-confirmation-container">
          <div class="modal-confirmation-icon">
            <i class="th th-outline-info-circle"></i>
          </div>
          <div class="modal-confirmation-content">
            <h5>Are You Sure?</h5>
            <p>Do you want make this branch as main branch? </p>
            <p><b>Note:</b> Existing main branch will not be treated as main branch</p>
          </div>
        </div>

        <div class="modal-button-group modal-full-width-btn">
          <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
          <button (click)="onMarkAsPrimary()" type="button" class="btn btn-primary btn-icon-text"> <i
              class="th th-outline-tick-circle"></i>Confirm</button>
        </div>
      </div>
    </div>
  </div>
</div>