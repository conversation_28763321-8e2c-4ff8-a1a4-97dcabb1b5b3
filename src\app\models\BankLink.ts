import { deserializeAs, serializeAs } from 'cerialize';

export class BankList {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('bankLinkName')
    @deserializeAs('bankLinkName')
    private _bankLinkName: string;

    @serializeAs('accountHolderName')
    @deserializeAs('accountHolderName')
    private _accountHolderName: string;

    @serializeAs('accountNo')
    @deserializeAs('accountNo')
    private _accountNo: string;

    @serializeAs('ifscCode')
    @deserializeAs('ifscCode')
    private _ifscCode: string;

    @serializeAs('termAndCondition')
    @deserializeAs('termAndCondition')
    private _termAndCondition: string;

    @serializeAs('isActive')
    @deserializeAs('isActive')
    private _isActive: boolean;

    @serializeAs('isPrimary')
    @deserializeAs('isPrimary')
    private _isPrimary: boolean;

    @serializeAs('signatureImage')
    @deserializeAs('signatureImage')
    private _signatureImage: string;

    @serializeAs('signatureImageOrg')
    @deserializeAs('signatureImageOrg')
    private _signatureImageOrg: string;

    @serializeAs('qrImageOrg')
    @deserializeAs('qrImageOrg')
    private _qrImageOrg: string;

    @serializeAs('qrImage')
    @deserializeAs('qrImage')
    private _qrImage: string;

    @serializeAs('sigFile')
    @deserializeAs('sigFile')
    private _sigFile: any;

    @serializeAs('qrFile')
    @deserializeAs('qrFile')
    private _qrFile: any;

    constructor() {
        this.isActive = true;
        this.isPrimary = false;
        this.signatureImage = null;
        this.qrImage = null;
        this.sigFile = null;
        this.qrFile = null
    }


    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}


    /**
     * Getter signatureImageOrg
     * @return {string}
     */
	public get signatureImageOrg(): string {
		return this._signatureImageOrg;
	}

    /**
     * Getter qrImageOrg
     * @return {string}
     */
	public get qrImageOrg(): string {
		return this._qrImageOrg;
	}

    /**
     * Setter signatureImageOrg
     * @param {string} value
     */
	public set signatureImageOrg(value: string) {
		this._signatureImageOrg = value;
	}

    /**
     * Setter qrImageOrg
     * @param {string} value
     */
	public set qrImageOrg(value: string) {
		this._qrImageOrg = value;
	}


    /**
     * Getter sigFile
     * @return {any}
     */
	public get sigFile(): any {
		return this._sigFile;
	}

    /**
     * Getter qrFile
     * @return {any}
     */
	public get qrFile(): any {
		return this._qrFile;
	}

    /**
     * Setter sigFile
     * @param {any} value
     */
	public set sigFile(value: any) {
		this._sigFile = value;
	}

    /**
     * Setter qrFile
     * @param {any} value
     */
	public set qrFile(value: any) {
		this._qrFile = value;
	}


    /**
     * Getter signatureImage
     * @return {string}
     */
	public get signatureImage(): string {
		return this._signatureImage;
	}

    /**
     * Getter qrImage
     * @return {string}
     */
	public get qrImage(): string {
		return this._qrImage;
	}

    /**
     * Setter signatureImage
     * @param {string} value
     */
	public set signatureImage(value: string) {
		this._signatureImage = value;
	}

    /**
     * Setter qrImage
     * @param {string} value
     */
	public set qrImage(value: string) {
		this._qrImage = value;
	}


    /**
     * Getter bankLinkName
     * @return {string}
     */
	public get bankLinkName(): string {
		return this._bankLinkName;
	}

    /**
     * Getter accountHolderName
     * @return {string}
     */
	public get accountHolderName(): string {
		return this._accountHolderName;
	}

    /**
     * Getter accountNo
     * @return {string}
     */
	public get accountNo(): string {
		return this._accountNo;
	}

    /**
     * Getter ifscCode
     * @return {string}
     */
	public get ifscCode(): string {
		return this._ifscCode;
	}

    /**
     * Getter termAndCondition
     * @return {string}
     */
	public get termAndCondition(): string {
		return this._termAndCondition;
	}

    /**
     * Getter isActive
     * @return {boolean}
     */
	public get isActive(): boolean {
		return this._isActive;
	}

    /**
     * Getter isPrimary
     * @return {boolean}
     */
	public get isPrimary(): boolean {
		return this._isPrimary;
	}

    /**
     * Setter bankLinkName
     * @param {string} value
     */
	public set bankLinkName(value: string) {
		this._bankLinkName = value;
	}

    /**
     * Setter accountHolderName
     * @param {string} value
     */
	public set accountHolderName(value: string) {
		this._accountHolderName = value;
	}

    /**
     * Setter accountNo
     * @param {string} value
     */
	public set accountNo(value: string) {
		this._accountNo = value;
	}

    /**
     * Setter ifscCode
     * @param {string} value
     */
	public set ifscCode(value: string) {
		this._ifscCode = value;
	}

    /**
     * Setter termAndCondition
     * @param {string} value
     */
	public set termAndCondition(value: string) {
		this._termAndCondition = value;
	}

    /**
     * Setter isActive
     * @param {boolean} value
     */
	public set isActive(value: boolean) {
		this._isActive = value;
	}

    /**
     * Setter isPrimary
     * @param {boolean} value
     */
	public set isPrimary(value: boolean) {
		this._isPrimary = value;
	}

}