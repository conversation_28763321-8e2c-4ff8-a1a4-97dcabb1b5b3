import { deserializeAs, serializeAs } from 'cerialize';

export class Screens {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('isSelected')
    @deserializeAs('isSelected')
    private _isSelected: boolean;

    @serializeAs('name')
    @deserializeAs('name')
    private _name: string;

    @serializeAs('screenOperations')
    @deserializeAs('screenOperations')
    private _screenOperations: ScreenOperation[];
    
    
    @serializeAs('totalPrivs')
    @deserializeAs('totalPrivs')
    private _totalPrivs: number;

    constructor() {
        this.screenOperations = [];
        this.isSelected = false;
    }


    /**
     * Getter totalPrivs
     * @return {number}
     */
	public get totalPrivs(): number {
		return this._totalPrivs;
	}

    /**
     * Setter totalPrivs
     * @param {number} value
     */
	public set totalPrivs(value: number) {
		this._totalPrivs = value;
	}


    /**
     * Getter id
     * @return {number}
     */
    public get id(): number {
        return this._id;
    }

    /**
     * Getter isSelected
     * @return {boolean}
     */
    public get isSelected(): boolean {
        return this._isSelected;
    }

    /**
     * Getter name
     * @return {string}
     */
    public get name(): string {
        return this._name;
    }

    /**
     * Getter screenOperations
     * @return {ScreenOperation[]}
     */
    public get screenOperations(): ScreenOperation[] {
        return this._screenOperations;
    }

    /**
     * Setter id
     * @param {number} value
     */
    public set id(value: number) {
        this._id = value;
    }

    /**
     * Setter isSelected
     * @param {boolean} value
     */
    public set isSelected(value: boolean) {
        this._isSelected = value;
    }

    /**
     * Setter name
     * @param {string} value
     */
    public set name(value: string) {
        this._name = value;
    }

    /**
     * Setter screenOperations
     * @param {ScreenOperation[]} value
     */
    public set screenOperations(value: ScreenOperation[]) {
        this._screenOperations = value;
    }


}

export class ScreenOperation {

    @serializeAs('operation')
    @deserializeAs('operation')
    private _operation: string;

    @serializeAs('isSelected')
    @deserializeAs('isSelected')
    private _isSelected: boolean;
   
    @serializeAs('isDisable')
    @deserializeAs('isDisable')
    private _isDisable: boolean;

    @serializeAs('privileges')
    @deserializeAs('privileges')
    private _privileges: ScreenPrivileges[];

    constructor() {
        this.privileges = [];
        this.isSelected = false;
        this.isDisable = false;
    }


    /**
     * Getter operation
     * @return {string}
     */
    public get operation(): string {
        return this._operation;
    }

    /**
     * Setter operation
     * @param {string} value
     */
    public set operation(value: string) {
        this._operation = value;
    }


    /**
     * Getter isSelected
     * @return {boolean}
     */
    public get isSelected(): boolean {
        return this._isSelected;
    }

    /**
     * Setter isSelected
     * @param {boolean} value
     */
    public set isSelected(value: boolean) {
        this._isSelected = value;
    }


    /**
     * Getter privileges
     * @return {ScreenPrivileges[]}
     */
    public get privileges(): ScreenPrivileges[] {
        return this._privileges;
    }

    /**
     * Setter privileges
     * @param {ScreenPrivileges[]} value
     */
    public set privileges(value: ScreenPrivileges[]) {
        this._privileges = value;
    }


    /**
     * Getter isDisable
     * @return {boolean}
     */
	public get isDisable(): boolean {
		return this._isDisable;
	}

    /**
     * Setter isDisable
     * @param {boolean} value
     */
	public set isDisable(value: boolean) {
		this._isDisable = value;
	}
    

}

export class ScreenPrivileges {

    @serializeAs('linkedPrivId')
    @deserializeAs('linkedPrivId')
    private _linkedPrivId: number[];

    @serializeAs('screenPrivID')
    @deserializeAs('screenPrivID')
    private _screenPrivID: number;

    @serializeAs('screenPrivName')
    @deserializeAs('screenPrivName')
    private _screenPrivName: string;

    @serializeAs('userPrivilegeID')
    @deserializeAs('userPrivilegeID')
    private _userPrivilegeID: number;

    @serializeAs('isSelected')
    @deserializeAs('isSelected')
    private _isSelected: boolean;

    @serializeAs('isDisable')
    @deserializeAs('isDisable')
    private _isDisable: boolean;

    @serializeAs('isDelete')
    @deserializeAs('isDelete')
    private _isDelete: boolean;

    constructor() {
        this.isSelected = false;
        this.linkedPrivId = [];
        this.isDisable = false;
        this.isDelete = false;
    }


    /**
     * Getter userPrivilegeID
     * @return {number}
     */
	public get userPrivilegeID(): number {
		return this._userPrivilegeID;
	}

    /**
     * Setter userPrivilegeID
     * @param {number} value
     */
	public set userPrivilegeID(value: number) {
		this._userPrivilegeID = value;
	}
    

    /**
     * Getter isDisable
     * @return {boolean}
     */
	public get isDisable(): boolean {
		return this._isDisable;
	}

    /**
     * Setter isDisable
     * @param {boolean} value
     */
	public set isDisable(value: boolean) {
		this._isDisable = value;
	}


    /**
     * Getter isDelete
     * @return {boolean}
     */
	public get isDelete(): boolean {
		return this._isDelete;
	}

    /**
     * Setter isDelete
     * @param {boolean} value
     */
	public set isDelete(value: boolean) {
		this._isDelete = value;
	}


    /**
     * Getter linkedPrivId
     * @return {number[]}
     */
    public get linkedPrivId(): number[] {
        return this._linkedPrivId;
    }

    /**
     * Setter linkedPrivId
     * @param {number[]} value
     */
    public set linkedPrivId(value: number[]) {
        this._linkedPrivId = value;
    }



    /**
     * Getter screenPrivID
     * @return {number}
     */
    public get screenPrivID(): number {
        return this._screenPrivID;
    }

    /**
     * Setter screenPrivID
     * @param {number} value
     */
    public set screenPrivID(value: number) {
        this._screenPrivID = value;
    }


    /**
     * Getter screenPrivName
     * @return {string}
     */
    public get screenPrivName(): string {
        return this._screenPrivName;
    }

    /**
     * Setter screenPrivName
     * @param {string} value
     */
    public set screenPrivName(value: string) {
        this._screenPrivName = value;
    }

    /**
     * Getter isSelected
     * @return {boolean}
     */
    public get isSelected(): boolean {
        return this._isSelected;
    }

    /**
     * Setter isSelected
     * @param {boolean} value
     */
    public set isSelected(value: boolean) {
        this._isSelected = value;
    }


}