<div class="page-content"
  [pageAccess]="{page: utilsService.enumForPage.ACCOUNTS, action: utilsService.enumForPage.VIEW_ACCOUNTS, view: true}">
  <div class="page-title-wrapper">
    <div class="page-title-left">
      <h4>Account Type</h4>
    </div>
    <div class="page-title-right">
      <button class="btn btn-sm btn-primary btn-icon-text" (click)="openAddEditModal(null, 'Add')"
        [pageAccess]="{page: utilsService.enumForPage.ACCOUNTS, action: utilsService.enumForPage.ADD_ACCOUNTS}">
        <i class="th th-outline-add-circle"></i>Add New
      </button>
      <Button class="btn btn-sm btn-icon btn-outline-white" (click)="getAccountTypes()" ngbTooltip="Refresh"
        placement="left" container="body" triggers="hover">
        <i class="th th-outline-refresh-2"></i>
      </Button>
    </div>
  </div>
  <div class="content-area">
    <div class="page-filters">
      <div class="page-filters-left">
        <div class="form-group form-group-sm">
          <div class="form-group-icon-start">
            <i class="th th-outline-search-normal-1 icon-broder "></i>
            <input (input)="onSearch($event)" type="search" class="form-control" placeholder="Enter Account Type"
              [(ngModel)]="searchText">
          </div>
        </div>
        <div class="form-group theme-ngselect form-group-sm">
          <ng-select (change)="onChangeActive()" class="" placeholder="Account Group" [multiple]="false"
            [clearable]="true" [items]="accountGroups" bindLabel="label" bindValue="value"
            [(ngModel)]="selectedAccountGroup" [hideSelected]="false">
          </ng-select>
        </div>
        <div class="form-group theme-ngselect form-group-sm">
          <ng-select (change)="onChangeActive()" class="" placeholder="Status" [multiple]="false" [clearable]="true"
            [items]="activeInactiveStatus" bindLabel="label" bindValue="value" [(ngModel)]="activeFlag"
            [hideSelected]="false">
          </ng-select>
        </div>
      </div>
      <div class="page-filters-right">
      </div>
    </div>
    <div class="card card-theme card-table-sticky">
      <div class="card-body p-0">
        <div class="table-responsive">
          <table class="table-theme table-hover table table-bordered ">
            <thead class="border-less">
              <tr>
                <th *ngFor="let th of accountTypeTH; index as j" [class]="th.class"
                  [ngClass]="{'sorting-asc': sortColumn==th.keyName && sortOrder === enumForSortOrder.A, 
                                            'sorting-desc': sortColumn==th.keyName && sortOrder === enumForSortOrder.D }"
                  (click)="!utilsService.isNullUndefinedOrBlank(th.keyName) ? onSortTH(th.keyName) : $event.preventDefault()">
                  {{th.displayName}}
                </th>
                <th class="tbl-switch"
                  *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.EDIT_ACCOUNTS])">Status</th>
                <th class="tbl-radio-wt-text"
                  *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.EDIT_ACCOUNTS])">Default</th>
                <th class="text-center"
                  *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.EDIT_ACCOUNTS, this.utilsService.enumForPage.DELETE_ACCOUNTS])">
                  Action</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let item of accountTypeList; index as i; trackBy: trackBy">

                <td class="tbl-user tbl-bold">
                  <div class="tbl-color-checkbox">
                    <span>{{item.typeName}}</span>
                  </div>
                </td>
                <td>{{item.accountGroup.label}}</td>
                <td>{{item.lastModifiedDate | date: 'dd/MM/YYYY h:mm a'}}</td>
                <td class="tbl-description">
                  <div>{{item.description}}</div>
                </td>
                <td class="tbl-switch"
                  *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.EDIT_ACCOUNTS])">
                  <div class="switch-box">
                    <label class="switch" htmlFor="switch-{{i}}">
                      <input (change)="onChangeStatus(item, item.isActive, i)" type="checkbox" id='switch-{{i}}'
                        [(ngModel)]="item.isActive" />
                      <div class="slider round"></div>
                    </label>
                  </div>
                </td>
                <td class="tbl-radio-wt-text"
                  *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.EDIT_ACCOUNTS])">
                  <div class="radio radio-primary form-check-inline">
                    <input [disabled]="item.markAsDefault" (click)="onChangeMarkAsDefault(item, item.markAsDefault, i)" [value]="true"
                      [checked]="item.markAsDefault" type="radio" id="accountdefault-{{i}}" name='accounttype{{i}}'
                      [(ngModel)]="item.markAsDefault" />
                    <label for="accountdefault-{{i}}"></label>
                  </div>
                </td>
                <td class="tbl-action"
                  *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.EDIT_ACCOUNTS, this.utilsService.enumForPage.DELETE_ACCOUNTS])">
                  <div class="tbl-action-group">
                    <button class="btn btn-xs btn-light-white btn-icon" (click)="openAddEditModal(item, 'Edit')"
                      [pageAccess]="{page: this.utilsService.enumForPage.ACCOUNTS, action: this.utilsService.enumForPage.EDIT_ACCOUNTS}"
                      ngbTooltip="Edit" placement="bottom" container="body" triggers="hover">
                      <i class="th th-outline-edit"></i>
                    </button>
                    <button class="btn btn-xs btn-light-danger btn-icon" (click)="openDeleteColorModal(item)"
                      [pageAccess]="{page: this.utilsService.enumForPage.ACCOUNTS, action: this.utilsService.enumForPage.DELETE_ACCOUNTS}"
                      ngbTooltip="Delete" placement="left" container="body" triggers="hover">
                      <i class="th th-outline-trash"></i>
                    </button>
                  </div>
                </td>
              </tr>
              <tr *ngIf="utilsService.isEmptyObjectOrNullUndefined(accountTypeList)">
                <td colspan="20" class="text-center">
                  <span class="truncate">{{utilsService.serverVariableService.STRING_WHEN_NO_RECORDS_FOUND}}</span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
    <div class="paginationbox pagination-fixed">
      <app-pagination (pagesizeData)="addPageSizeData($event)" (pageNumber)="pageNumber($event)" [page]="pageNo"
        [pageSize]="pageSize" [totalData]="totalData"></app-pagination>
    </div>
  </div>
</div>

<!-- ----------------------------------------------------------------------- -->
<!--                     Add and Edit Forms Modal Start                      -->
<!-- ----------------------------------------------------------------------- -->
<div class="modal modal-theme fade" id="accountTypeModal" tabindex="-1" aria-labelledby="colorModalLabel"
  aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel">{{statusForModal === 'Add' ? 'Add New' : 'Edit'}} Account Type
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
            class='th th-close'></i></button>
      </div>
      <div class="modal-body" [formGroup]="accountTypeFormGroup">
        <div class="row">
          <div class="col-12">
            <div class="form-group required">
              <label class="form-label">Account Type</label>
              <input id="name" [maxlength]="utilsService.validationService.MAX_50" type="text"
                [(ngModel)]="accountTypeObj.typeName" formControlName="name" type="text" class="form-control"
                placeholder="Enter Account Type">
              <div class="message error-message"
                *ngIf="accountTypeFormGroup.controls['name'].hasError('required') &&  accountTypeFormGroup.controls['name'].touched">
                {{utilsService.validationService.ACCOUNT_TYPE_NAME_REQ}}
              </div>
              <div class="message error-message"
                *ngIf="!accountTypeFormGroup.controls['name'].hasError('required') && !accountTypeFormGroup.controls['name'].valid && accountTypeFormGroup.controls['name'].touched">
                {{utilsService.validationService.ACCOUNT_TYPE_NAME_INVALID}}
              </div>
            </div>
          </div>
          <div class="col-12">
            <div class="form-group theme-ngselect required">
              <label class="form-label">Account Group</label>
              <ng-select formControlName="account_group" placeholder="Account Group" [multiple]="false"
                [clearable]="false" [items]="accountGroups" bindLabel="label" bindValue="value"
                [(ngModel)]="accountTypeObj.accountGroup" [maxlength]="utilsService.validationService.MAX_50">
              </ng-select>
              <div class="message error-message"
                *ngIf="accountTypeFormGroup.controls['account_group'].hasError('required') &&  accountTypeFormGroup.controls['account_group'].touched">
                {{utilsService.validationService.ACCOUNT_GROUP_REQUIRED}}
              </div>
            </div>
          </div>
          <div class="col-12">
            <div class="form-group">
              <label class="form-label">Description</label>
              <textarea class="form-control" [maxlength]="utilsService.validationService.MAX_1000"
                formControlName="description" [(ngModel)]="accountTypeObj.description"
                placeholder="Enter Description"></textarea>
            </div>
          </div>
          <div class="col-12">
            <div class="form-group">
              <div class=" checkbox checkbox-primary checkbox-small">
                <input type="checkbox" id="markasdefault" class="material-inputs filled-in"
                  formControlName="markAsDefault" [(ngModel)]="accountTypeObj.markAsDefault" />
                <label for="markasdefault">Mark As Default</label>
              </div>
            </div>
          </div>
          <div class="col-12">
            <div class="form-group required d-flex justify-content-between">
              <label class="form-label">Status</label>
              <div class="switch-box">
                <label class="switch" htmlFor="switch">
                  <input type="checkbox" id='switch' checked formControlName="status"
                    [(ngModel)]="accountTypeObj.isActive" />
                  <div class="slider round"></div>
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <div class="modal-footer-group full-width-btn">
          <button type="button" (click)="onSaveColor()" class="btn btn-primary btn-icon-text"> <i
              class="th th-outline-tick-circle"></i>
            {{statusForModal === 'Add' ? 'Save' : 'Update'}}</button>
          <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                      Add and Edit Forms Modal End                       -->
<!-- ----------------------------------------------------------------------- -->

<!-- ----------------------------------------------------------------------- -->
<!--                           Delete Modal Start                            -->
<!-- ----------------------------------------------------------------------- -->
<div class="modal modal-theme modal-confirmation modal-reject fade" id="deleteModel" tabindex="-1"
  aria-labelledby="deleteModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
            class='th th-close'></i></button>
      </div>
      <div class="modal-body">
        <div class="modal-confirmation-container">
          <div class="modal-confirmation-icon">
            <i class="th th-bold-trash"></i>
          </div>
          <div class="modal-confirmation-content">
            <h5>Are You Sure?</h5>
            <p>You want to Delete <b>{{accountTypeObj.typeName}}</b> Account Type.</p>
          </div>
        </div>
        <div class="modal-button-group modal-full-width-btn">
          <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
          <button (click)="deleteObj()" type="button" class="btn btn-primary btn-icon-text"> <i
              class="th th-outline-tick-circle"></i>
            Delete</button>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                           Delete Modal End                            -->
<!-- ----------------------------------------------------------------------- -->