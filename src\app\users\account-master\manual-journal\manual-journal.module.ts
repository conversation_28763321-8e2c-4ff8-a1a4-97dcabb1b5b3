import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { SharedModule } from 'src/app/shared/shared.module';
import { ManualJournalComponent } from './manual-journal.component';
import { NewManualJournalComponent } from './new-manual-journal/new-manual-journal.component';

const routes: Routes = [
  { path: '', redirectTo: 'manual-journal', pathMatch: 'full' },
  { path: '', component: ManualJournalComponent },
  { path: 'new-manual-journal', component: NewManualJournalComponent },
  { path: 'edit-manual-journal/:id', component: NewManualJournalComponent },
];

@NgModule({
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    SharedModule.forRoot()
  ],
  declarations: [ManualJournalComponent, NewManualJournalComponent]
})

export class ManualJournalModule { }
