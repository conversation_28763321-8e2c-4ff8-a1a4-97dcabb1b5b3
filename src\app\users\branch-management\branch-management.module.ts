import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BranchManagementComponent } from './branch-management.component';
import { RouterModule, Routes } from '@angular/router';
import { BranchListComponent } from './branch-list/branch-list.component';
import { NewBranchComponent } from './new-branch/new-branch.component';
import { SharedModule } from 'src/app/shared/shared.module';

const routes: Routes = [
  { path: '', redirectTo: 'branch-management', pathMatch: 'full' },
  { path: '', component: BranchListComponent },
  { path: 'new-branch', component: NewBranchComponent },
  { path: 'edit-branch/:id', component: NewBranchComponent },
];

@NgModule({
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    SharedModule.forRoot()
  ],
  declarations: [BranchManagementComponent, BranchListComponent, NewBranchComponent]
})
export class BranchManagementModule { }
