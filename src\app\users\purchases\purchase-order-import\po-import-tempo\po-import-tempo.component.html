<div class="page-filters">
    <div class="page-filters-left">
        <div class="form-group form-group-sm filter-search">
            <div class="form-group-icon-start">
                <i class="th th-outline-search-normal-1 icon-broder "></i>
                <input (change)="onSearch($event)" [(ngModel)]="paginationRequest.searchText" type="search"
                    class="form-control" placeholder="Search by tempo no">
            </div>
        </div>
        <div class="form-group form-group-sm date-range-filter">
            <div class="form-group-icon-end">
                <i (click)="open()" class="th th-outline-calendar ngx-daterangepicker-action"></i>
                <input pickerDirective class="form-control" type="text" ngxDaterangepickerMd
                    [formControl]="paginationRequest.dateRangeControl" [showCustomRangeLabel]="true"
                    [alwaysShowCalendars]="true" [ranges]="utilsService.ranges" [linkedCalendars]="false"
                    [showClearButton]="false" placeholder="Loaded Date" [autoApply]="true" [showRangeLabelOnInput]="true"
                    startKey="start" endKey="end">
            </div>
        </div>
        <div class="form-group theme-ngselect form-group-sm">
            <ng-select (change)="onChangeStatus()" placeholder="Tempo Status" [multiple]="false" [clearable]="true"
                [items]="dropdown?.tempoStatus" bindLabel="label" bindValue="value"
                [(ngModel)]="paginationRequest.tempoStatus">
            </ng-select>
        </div>
        <button (click)="onClear.emit()" class="btn btn-link btn-sm">Clear</button>

    </div>
    <div class="page-filters-right">
        <!-- <button class="btn btn-sm btn-outline-white btn-icon-text">
            View Tempo History
            <div class="vr my-1"></div> <i class="th th-outline-arrow-right-3"></i>
        </button>
        <button class="btn btn-sm btn-outline-white btn-icon-text">
            Close Tempo History
            <div class="vr my-1"></div> <i class="th th-outline-close-circle"></i>
        </button> -->

    </div>
</div>

<div class="card card-theme card-table-sticky3">
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table-theme table-hover table table-bordered tbl-collapse table-sticky">
                <thead class="border-less">
                    <tr>
                        <th>
                            <div class="d-flex align-items-center gap-2">
                                # Vehicle No
                            </div>
                        </th>
                        <th>Containers</th>
                        <th>Loaded Date</th>
                        <th>No Of Carton</th>
                        <th>GRN Cartons</th>
                        <th>Status</th>
                        <th class="text-end">Action</th>
                    </tr>
                </thead>
                <tbody>
                    <ng-container *ngFor="let item of poImportList; index as i; trackBy: trackBy">
                        <tr>
                            <td class=" tbl-user">
                                <div class="tbl-user-checkbox-srno">
                                    <span>{{(i + 1) | padNum}}</span>
                                    <b class="text-black">
                                        {{item.vehicleNo}}
                                    </b>
                                </div>
                            </td>
                            <td>{{item.containersName}}</td>
                            <td>{{item.date ? (item.date | date: 'dd/MM/YYYY h:mm a') : '-'}}</td>
                            <td>{{item.cartonQtyVehicle ? item.cartonQtyVehicle : '-'}}</td>
                            <td>{{item.grnReceivedCartons}}</td>
                            <td>
                                <span class="fw-600"
                                    [ngClass]="{'text-secondary': item?.status?.value === enumForTempoStatus.REC_SURAT,
                                                'text-warning': item?.status?.value === enumForTempoStatus.WAY_TO_SURAT,
                                                'text-success': item?.status?.value === enumForTempoStatus.COMPLETED,
                                                'text-primary': item?.status?.value === enumForTempoStatus.GRN_COMPLETED}">
                                    {{item?.status?.label}}
                                </span>
                            </td>
                            <td class="tbl-action">
                                <div class="tbl-action-group justify-content-end">
                                    <button *ngIf="item?.status?.value === enumForTempoStatus.WAY_TO_SURAT"
                                        [pageAccess]="{page: this.utilsService.enumForPage.PO, action: this.utilsService.enumForPage.EDIT_TEMPO}"
                                        (click)="openAddEditTempoModal(item, 'Edit')" class="btn btn-xs btn-light-white btn-icon" ngbTooltip="Edit"
                                        placement="bottom" container="body" triggers="hover">
                                        <i class="th th-outline-edit"></i>
                                    </button>
                                
                                    <div class="dropdown" *ngIf="hasVisibleDropdownItems(item)">
                                        <button class="btn btn-xs btn-light-white btn-icon" id="actionDropDown" data-bs-toggle="dropdown"
                                            aria-expanded="false" data-bs-popper-config='{"strategy":"fixed"}' ngbTooltip="More Option" placement="left"
                                            container="body" triggers="hover">
                                            <i class="th th-outline-more"></i>
                                        </button>
                                
                                        <ul class="dropdown-menu" aria-labelledby="actionDropDown">
                                            <li *ngIf="item?.status?.value !== enumForTempoStatus.COMPLETED"
                                                [pageAccess]="{page: this.utilsService.enumForPage.EXPENSES, action: this.utilsService.enumForPage.ADD_TEMPO_EXP}">
                                                <a class="dropdown-item" (click)="redirectToExp(item.id)"><i class="th th-outline-add-circle"></i>
                                                    Add Expense
                                                </a>
                                            </li>
                                
                                            <li [pageAccess]="{page: this.utilsService.enumForPage.PO, action: this.utilsService.enumForPage.MARK_AS_COMPLETED_TEMPO}"
                                                *ngIf="item?.status?.value === enumForTempoStatus.GRN_COMPLETED">
                                                <a (click)="tempoToCompleteOpen(item)" class="dropdown-item"><i class="th th-outline-tick-circle"></i>
                                                    Mark As Completed
                                                </a>
                                            </li>
                                            <!-- <li *ngIf="(item?.status?.value === enumForTempoStatus.WAY_TO_SURAT)">
                                                <a (click)="onStartGRN(item)" class="dropdown-item">
                                                    <i class="th th-outline-box-tick"></i>
                                                    Start GRN
                                                </a>
                                            </li> -->
                                            <li *ngIf="item?.status?.value === enumForTempoStatus.WAY_TO_SURAT">
                                                <hr class="dropdown-divider">
                                            </li>
                                            <li *ngIf="item?.status?.value === enumForTempoStatus.WAY_TO_SURAT"
                                                [pageAccess]="{page: this.utilsService.enumForPage.PO, action: this.utilsService.enumForPage.DELETE_TEMPO}">
                                                <a (click)="tempoDel(item)" class="dropdown-item text-danger"><i
                                                        class="th th-outline-trash"></i>Delete</a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    </ng-container>
                    <ng-container>
                        <tr *ngIf="utilsService.isEmptyObjectOrNullUndefined(poImportList)">
                            <td colspan="20" class="text-center">
                                <span
                                    class="truncate">{{utilsService.serverVariableService.STRING_WHEN_NO_RECORDS_FOUND}}</span>
                            </td>
                        </tr>
                    </ng-container>
                </tbody>
            </table>
        </div>
    </div>
</div>

<div class="paginationbox pagination-fixed">
    <app-pagination (pagesizeData)="addPageSizeData($event)" (pageNumber)="pageNumber($event)"
        [page]="paginationRequest.pageNo" [pageSize]="paginationRequest.pageSize"
        [totalData]="paginationRequest.totalData">
    </app-pagination>
</div>