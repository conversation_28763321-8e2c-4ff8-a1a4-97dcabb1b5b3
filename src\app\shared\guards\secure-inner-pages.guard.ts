import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { UtilsService } from '../services/utils.service';

@Injectable({
  providedIn: 'root'
})

export class SecureInnerPagesGuard {

  constructor(private utilsService: UtilsService, private router: Router) { }

  canActivate(): boolean {
    if (!this.utilsService.isEmptyObjectOrNullUndefined(this.utilsService.getLoggedInUser())) {
      this.router.navigate(['/users/dashboard']);
    }
    // Allow the route activation
    return true;
  }

}
