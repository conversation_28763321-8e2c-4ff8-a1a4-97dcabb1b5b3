import { serializeAs, deserializeAs } from 'cerialize';

export class POImportHeader {

    @serializeAs('class')
    @deserializeAs('class')
    private _class: string;

    @serializeAs('displayName')
    @deserializeAs('displayName')
    private _displayName: string;

    @serializeAs('keyName')
    @deserializeAs('keyName')
    private _keyName: string;

    @serializeAs('key')
    @deserializeAs('key')
    private _key: string;

    @serializeAs('header')
    @deserializeAs('header')
    private _header: string;

    @serializeAs('headerClass')
    @deserializeAs('headerClass')
    private _headerClass: string;

    @serializeAs('show')
    @deserializeAs('show')
    private _show: boolean;

    @serializeAs('isSelected')
    @deserializeAs('isSelected')
    private _isSelected: boolean;

    @serializeAs('index')
    @deserializeAs('index')
    private _index: number;

    constructor() {}


    /**
     * Getter class
     * @return {string}
     */
	public get class(): string {
		return this._class;
	}

    /**
     * Getter displayName
     * @return {string}
     */
	public get displayName(): string {
		return this._displayName;
	}

    /**
     * Getter keyName
     * @return {string}
     */
	public get keyName(): string {
		return this._keyName;
	}

    /**
     * Getter key
     * @return {string}
     */
	public get key(): string {
		return this._key;
	}

    /**
     * Getter header
     * @return {string}
     */
	public get header(): string {
		return this._header;
	}

    /**
     * Getter headerClass
     * @return {string}
     */
	public get headerClass(): string {
		return this._headerClass;
	}

    /**
     * Getter show
     * @return {boolean}
     */
	public get show(): boolean {
		return this._show;
	}

    /**
     * Getter isSelected
     * @return {boolean}
     */
	public get isSelected(): boolean {
		return this._isSelected;
	}

    /**
     * Getter index
     * @return {number}
     */
	public get index(): number {
		return this._index;
	}

    /**
     * Setter class
     * @param {string} value
     */
	public set class(value: string) {
		this._class = value;
	}

    /**
     * Setter displayName
     * @param {string} value
     */
	public set displayName(value: string) {
		this._displayName = value;
	}

    /**
     * Setter keyName
     * @param {string} value
     */
	public set keyName(value: string) {
		this._keyName = value;
	}

    /**
     * Setter key
     * @param {string} value
     */
	public set key(value: string) {
		this._key = value;
	}

    /**
     * Setter header
     * @param {string} value
     */
	public set header(value: string) {
		this._header = value;
	}

    /**
     * Setter headerClass
     * @param {string} value
     */
	public set headerClass(value: string) {
		this._headerClass = value;
	}

    /**
     * Setter show
     * @param {boolean} value
     */
	public set show(value: boolean) {
		this._show = value;
	}

    /**
     * Setter isSelected
     * @param {boolean} value
     */
	public set isSelected(value: boolean) {
		this._isSelected = value;
	}

    /**
     * Setter index
     * @param {number} value
     */
	public set index(value: number) {
		this._index = value;
	}

}