import { AfterViewInit, Component, ElementRef, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { WarehouseService } from '../warehouse.service';
import { DROPOFF_LOC } from 'src/app/shared/constants/constant';
import { debounceTime, distinctUntilChanged, Subject } from 'rxjs';
import { Validators } from '@angular/forms';
declare var window: any;

@Component({
  selector: 'app-warehouse-details',
  templateUrl: './warehouse-details.component.html',
  styleUrls: ['./warehouse-details.component.css']
})
export class WarehouseDetailsComponent extends WarehouseService implements OnInit, AfterViewInit, OnDestroy {

  destroy$ = new Subject();

  ngOnInit() {

    this.flagForPasswordHideShow = true;
    this.passwordControl = this.fb.control(null, [Validators.required]);
    this.warehouseFormGroup();
    this.dropOffForm();

    // triple drop modals, delete
    this.deleteWarehouseDetailModal = new window.bootstrap.Modal(
      document.getElementById('deleteWarehouseDetailModal')
    );

    this.deleteRackModal = new window.bootstrap.Modal(
      document.getElementById('deleteRackModal')
    );

    this.deleteAisleModal = new window.bootstrap.Modal(
      document.getElementById('deleteAisleModal')
    );
    //
    this.addNewWareHouseDetails = new window.bootstrap.Modal(
      document.getElementById('addNewWareHouseDetails')
    );

    document.getElementById('addNewWareHouseDetails').addEventListener('shown.bs.modal', () => {
      document.getElementById('f').focus();
    });

    document.getElementById('addNewWareHouseDetails').addEventListener('hidden.bs.modal', () => {
      this.showAddEditModal = false;
    });
    //
    //
    this.addEditDropOff = new window.bootstrap.Modal(
      document.getElementById('addEditDropOff')
    );

    this.deleteDropOff = new window.bootstrap.Modal(
      document.getElementById('deleteDropOff')
    );

    this.markAsPrimaryDetailModal = new window.bootstrap.Modal(
      document.getElementById('markAsPrimaryDetailModal')
    );

    this.warehouseRemoveModal = new window.bootstrap.Modal(
      document.getElementById('warehouseRemoveModal')
    );

    document.getElementById('addEditDropOff').addEventListener('shown.bs.modal', () => {
      document.getElementById('f1').focus();
    });

    document.getElementById('addEditDropOff').addEventListener('hidden.bs.modal', () => {
      this.showAddEditDropOff = false;
    });
    //
    this.warehouseId = Number(this.route.snapshot.paramMap.get('id'));

    if (this.warehouseId) {
      this.getWarehouseDetails(null, this.warehouseId);
    }

    this.searchDOSubject.pipe(debounceTime(200), distinctUntilChanged()).subscribe((res: string) => {
      this.dropOffParam.searchText = null;
      this.dropOffParam.searchText = res;
      this.getWarehouseDetails(this.selectedWarehouseIndex, this.warehouseId);
    }); 

    this.searchARSubject.pipe(debounceTime(200), distinctUntilChanged()).subscribe((res: string) => {
      this.aisleRackName = null;
      this.aisleRackName = res;
      this.getWarehouseDetails(this.selectedWarehouseIndex, this.warehouseId);
    }); 

    this.dropOffTH = DROPOFF_LOC;
  }

  ngAfterViewInit(): void {

  }

  ngOnDestroy() {
    this.elementRef?.nativeElement?.remove();
    if (this.routeSub) {
      this.routeSub.unsubscribe();
    }
    this.destroy$.next(null);
    this.destroy$.complete();
  }
  
}
