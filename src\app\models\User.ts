import { deserializeAs, serializeAs } from 'cerialize';

export class User {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('idOfRole')
    @deserializeAs('idOfRole')
    private _idOfRole: number;

    @serializeAs('idOfDepartment')
    @deserializeAs('idOfDepartment')
    private _idOfDepartment: number;

    @serializeAs('idOfBranch')
    @deserializeAs('idOfBranch')
    private _idOfBranch: number[];

    @serializeAs('idOfWarehouse')
    @deserializeAs('idOfWarehouse')
    private _idOfWarehouse: number[];

    @serializeAs('idOfCity')
    @deserializeAs('idOfCity')
    private _idOfCity: number;

    @serializeAs('idOfState')
    @deserializeAs('idOfState')
    private _idOfState: number;

    @serializeAs('idOfCountry')
    @deserializeAs('idOfCountry')
    private _idOfCountry: number;

    @serializeAs('zipCode')
    @deserializeAs('zipCode')
    private _zipCode: string;

    @serializeAs('password')
    @deserializeAs('password')
    private _password: string;

    @deserializeAs('lastModifiedDate')
    private _lastModifiedDate: string;

    @serializeAs('isActive')
    @deserializeAs('isActive')
    private _isActive: boolean;

    @deserializeAs('isAbsent')
    private _isAbsent: boolean;

    @deserializeAs('isDefault')
    private _isDefault: boolean;

    @serializeAs('isShareOnWhatsapp')
    @deserializeAs('isShareOnWhatsapp')
    private _isShareOnWhatsapp: boolean;

    @deserializeAs('isSelected')
    private _isSelected: boolean;
    
    @serializeAs('name')
    @deserializeAs('name')
    private _name: string;

    @deserializeAs('userName')
    private _userName: string;

    @serializeAs('username')
    @deserializeAs('username')
    private _username: string;

    @serializeAs('gender')
    @deserializeAs('gender')
    private _gender: any;

    @deserializeAs('mobileNo')
    private _mobileNo: string;

    @serializeAs('mobile')
    @deserializeAs('mobile')
    private _mobile: string;

    @serializeAs('email')
    @deserializeAs('email')
    private _email: string;

    @serializeAs('aadharNo')
    @deserializeAs('aadharNo')
    private _aadharNo: string;

    @serializeAs('panNo')
    @deserializeAs('panNo')
    private _panNo: string;

    @serializeAs('address')
    @deserializeAs('address')
    private _address: string;

    @deserializeAs('departmentName')
    private _departmentName: string;

    @deserializeAs('roleName')
    private _roleName: string;

    @deserializeAs('wareHouseName')
    private _wareHouseName: string;

    @deserializeAs('branchName')
    private _branchName: string;

    @deserializeAs('loggedInTime')
    private _loggedInTime: any;

    @deserializeAs('loggedOutTime')
    private _loggedOutTime: any;

    @deserializeAs('profileUrl')
    private _profileUrl: any;
   
    @serializeAs('deleteProfileUrl')
    private _deleteProfileUrl: any;

    @deserializeAs('state')
    private _state: any;
    
    @serializeAs('startTime')
    @deserializeAs('startTime')
    private _startTime: any;

    @serializeAs('endTime')
    @deserializeAs('endTime')
    private _endTime: any;

    @serializeAs('deletedUserDocs')
    @deserializeAs('deletedUserDocs')
    private _deletedUserDocs: any[];

    // @serializeAs('temp_startTime')
    @deserializeAs('temp_startTime')
    private _temp_startTime: any;

    // @serializeAs('temp_endTime')
    @deserializeAs('temp_endTime')
    private _temp_endTime: any;

    @deserializeAs('userDocs')
    private _userDocs: any[];

    private _stateName: any[];

    private _cityName: any[];

    @deserializeAs('originalProfileUrl')
    private _originalProfileUrl: any;

    @deserializeAs('branchNames')
    private _branchNames: any;

    @serializeAs('contactCountryExtensionId')
    @deserializeAs('contactCountryExtensionId')
    private _contactCountryExtensionId: number;

    @deserializeAs('contactExtension')
    private _contactExtension: any;

    constructor() {
        this.isActive = false;
        this.isSelected = false;
        this.isAbsent = false;
        this.isDefault = false;
        this.isShareOnWhatsapp = false;
        this.userDocs = [];
        this.deletedUserDocs = [];
    }


    /**
     * Getter isDefault
     * @return {boolean}
     */
	public get isDefault(): boolean {
		return this._isDefault;
	}

    /**
     * Setter isDefault
     * @param {boolean} value
     */
	public set isDefault(value: boolean) {
		this._isDefault = value;
	}


    /**
     * Getter contactExtension
     * @return {any}
     */
	public get contactExtension(): any {
		return this._contactExtension;
	}

    /**
     * Setter contactExtension
     * @param {any} value
     */
	public set contactExtension(value: any) {
		this._contactExtension = value;
	}


    /**
     * Getter contactCountryExtensionId
     * @return {number}
     */
	public get contactCountryExtensionId(): number {
		return this._contactCountryExtensionId;
	}

    /**
     * Setter contactCountryExtensionId
     * @param {number} value
     */
	public set contactCountryExtensionId(value: number) {
		this._contactCountryExtensionId = value;
	}


    /**
     * Getter branchNames
     * @return {any}
     */
	public get branchNames(): any {
		return this._branchNames;
	}

    /**
     * Setter branchNames
     * @param {any} value
     */
	public set branchNames(value: any) {
		this._branchNames = value;
	}


    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Getter idOfRole
     * @return {number}
     */
	public get idOfRole(): number {
		return this._idOfRole;
	}

    /**
     * Getter idOfDepartment
     * @return {number}
     */
	public get idOfDepartment(): number {
		return this._idOfDepartment;
	}

    /**
     * Getter idOfBranch
     * @return {number[]}
     */
	public get idOfBranch(): number[] {
		return this._idOfBranch;
	}

    /**
     * Getter idOfWarehouse
     * @return {number[]}
     */
	public get idOfWarehouse(): number[] {
		return this._idOfWarehouse;
	}

    /**
     * Getter idOfCity
     * @return {number}
     */
	public get idOfCity(): number {
		return this._idOfCity;
	}

    /**
     * Getter idOfState
     * @return {number}
     */
	public get idOfState(): number {
		return this._idOfState;
	}

    /**
     * Getter idOfCountry
     * @return {number}
     */
	public get idOfCountry(): number {
		return this._idOfCountry;
	}

    /**
     * Getter zipCode
     * @return {string}
     */
	public get zipCode(): string {
		return this._zipCode;
	}

    /**
     * Getter password
     * @return {string}
     */
	public get password(): string {
		return this._password;
	}

    /**
     * Getter lastModifiedDate
     * @return {string}
     */
	public get lastModifiedDate(): string {
		return this._lastModifiedDate;
	}

    /**
     * Getter isActive
     * @return {boolean}
     */
	public get isActive(): boolean {
		return this._isActive;
	}

    /**
     * Getter isAbsent
     * @return {boolean}
     */
	public get isAbsent(): boolean {
		return this._isAbsent;
	}

    /**
     * Getter isShareOnWhatsapp
     * @return {boolean}
     */
	public get isShareOnWhatsapp(): boolean {
		return this._isShareOnWhatsapp;
	}

    /**
     * Getter isSelected
     * @return {boolean}
     */
	public get isSelected(): boolean {
		return this._isSelected;
	}

    /**
     * Getter name
     * @return {string}
     */
	public get name(): string {
		return this._name;
	}

    /**
     * Getter userName
     * @return {string}
     */
	public get userName(): string {
		return this._userName;
	}

    /**
     * Getter username
     * @return {string}
     */
	public get username(): string {
		return this._username;
	}

    /**
     * Getter gender
     * @return {any}
     */
	public get gender(): any {
		return this._gender;
	}

    /**
     * Getter mobileNo
     * @return {string}
     */
	public get mobileNo(): string {
		return this._mobileNo;
	}

    /**
     * Getter mobile
     * @return {string}
     */
	public get mobile(): string {
		return this._mobile;
	}

    /**
     * Getter email
     * @return {string}
     */
	public get email(): string {
		return this._email;
	}

    /**
     * Getter aadharNo
     * @return {string}
     */
	public get aadharNo(): string {
		return this._aadharNo;
	}

    /**
     * Getter panNo
     * @return {string}
     */
	public get panNo(): string {
		return this._panNo;
	}

    /**
     * Getter address
     * @return {string}
     */
	public get address(): string {
		return this._address;
	}

    /**
     * Getter departmentName
     * @return {string}
     */
	public get departmentName(): string {
		return this._departmentName;
	}

    /**
     * Getter roleName
     * @return {string}
     */
	public get roleName(): string {
		return this._roleName;
	}

    /**
     * Getter wareHouseName
     * @return {string}
     */
	public get wareHouseName(): string {
		return this._wareHouseName;
	}

    /**
     * Getter branchName
     * @return {string}
     */
	public get branchName(): string {
		return this._branchName;
	}

    /**
     * Getter loggedInTime
     * @return {any}
     */
	public get loggedInTime(): any {
		return this._loggedInTime;
	}

    /**
     * Getter loggedOutTime
     * @return {any}
     */
	public get loggedOutTime(): any {
		return this._loggedOutTime;
	}

    /**
     * Getter profileUrl
     * @return {any}
     */
	public get profileUrl(): any {
		return this._profileUrl;
	}

    /**
     * Getter state
     * @return {any}
     */
	public get state(): any {
		return this._state;
	}

    /**
     * Getter startTime
     * @return {any}
     */
	public get startTime(): any {
		return this._startTime;
	}

    /**
     * Getter endTime
     * @return {any}
     */
	public get endTime(): any {
		return this._endTime;
	}

    /**
     * Getter deletedUserDocs
     * @return {any[]}
     */
	public get deletedUserDocs(): any[] {
		return this._deletedUserDocs;
	}

    /**
     * Getter temp_startTime
     * @return {any}
     */
	public get temp_startTime(): any {
		return this._temp_startTime;
	}

    /**
     * Getter temp_endTime
     * @return {any}
     */
	public get temp_endTime(): any {
		return this._temp_endTime;
	}

    /**
     * Getter userDocs
     * @return {any[]}
     */
	public get userDocs(): any[] {
		return this._userDocs;
	}

    /**
     * Getter stateName
     * @return {any[]}
     */
	public get stateName(): any[] {
		return this._stateName;
	}

    /**
     * Getter cityName
     * @return {any[]}
     */
	public get cityName(): any[] {
		return this._cityName;
	}

    /**
     * Getter originalProfileUrl
     * @return {any}
     */
	public get originalProfileUrl(): any {
		return this._originalProfileUrl;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Setter idOfRole
     * @param {number} value
     */
	public set idOfRole(value: number) {
		this._idOfRole = value;
	}

    /**
     * Setter idOfDepartment
     * @param {number} value
     */
	public set idOfDepartment(value: number) {
		this._idOfDepartment = value;
	}

    /**
     * Setter idOfBranch
     * @param {number[]} value
     */
	public set idOfBranch(value: number[]) {
		this._idOfBranch = value;
	}

    /**
     * Setter idOfWarehouse
     * @param {number[]} value
     */
	public set idOfWarehouse(value: number[]) {
		this._idOfWarehouse = value;
	}

    /**
     * Setter idOfCity
     * @param {number} value
     */
	public set idOfCity(value: number) {
		this._idOfCity = value;
	}

    /**
     * Setter idOfState
     * @param {number} value
     */
	public set idOfState(value: number) {
		this._idOfState = value;
	}

    /**
     * Setter idOfCountry
     * @param {number} value
     */
	public set idOfCountry(value: number) {
		this._idOfCountry = value;
	}

    /**
     * Setter zipCode
     * @param {string} value
     */
	public set zipCode(value: string) {
		this._zipCode = value;
	}

    /**
     * Setter password
     * @param {string} value
     */
	public set password(value: string) {
		this._password = value;
	}

    /**
     * Setter lastModifiedDate
     * @param {string} value
     */
	public set lastModifiedDate(value: string) {
		this._lastModifiedDate = value;
	}

    /**
     * Setter isActive
     * @param {boolean} value
     */
	public set isActive(value: boolean) {
		this._isActive = value;
	}

    /**
     * Setter isAbsent
     * @param {boolean} value
     */
	public set isAbsent(value: boolean) {
		this._isAbsent = value;
	}

    /**
     * Setter isShareOnWhatsapp
     * @param {boolean} value
     */
	public set isShareOnWhatsapp(value: boolean) {
		this._isShareOnWhatsapp = value;
	}

    /**
     * Setter isSelected
     * @param {boolean} value
     */
	public set isSelected(value: boolean) {
		this._isSelected = value;
	}

    /**
     * Setter name
     * @param {string} value
     */
	public set name(value: string) {
		this._name = value;
	}

    /**
     * Setter userName
     * @param {string} value
     */
	public set userName(value: string) {
		this._userName = value;
	}

    /**
     * Setter username
     * @param {string} value
     */
	public set username(value: string) {
		this._username = value;
	}

    /**
     * Setter gender
     * @param {any} value
     */
	public set gender(value: any) {
		this._gender = value;
	}

    /**
     * Setter mobileNo
     * @param {string} value
     */
	public set mobileNo(value: string) {
		this._mobileNo = value;
	}

    /**
     * Setter mobile
     * @param {string} value
     */
	public set mobile(value: string) {
		this._mobile = value;
	}

    /**
     * Setter email
     * @param {string} value
     */
	public set email(value: string) {
		this._email = value;
	}

    /**
     * Setter aadharNo
     * @param {string} value
     */
	public set aadharNo(value: string) {
		this._aadharNo = value;
	}

    /**
     * Setter panNo
     * @param {string} value
     */
	public set panNo(value: string) {
		this._panNo = value;
	}

    /**
     * Setter address
     * @param {string} value
     */
	public set address(value: string) {
		this._address = value;
	}

    /**
     * Setter departmentName
     * @param {string} value
     */
	public set departmentName(value: string) {
		this._departmentName = value;
	}

    /**
     * Setter roleName
     * @param {string} value
     */
	public set roleName(value: string) {
		this._roleName = value;
	}

    /**
     * Setter wareHouseName
     * @param {string} value
     */
	public set wareHouseName(value: string) {
		this._wareHouseName = value;
	}

    /**
     * Setter branchName
     * @param {string} value
     */
	public set branchName(value: string) {
		this._branchName = value;
	}

    /**
     * Setter loggedInTime
     * @param {any} value
     */
	public set loggedInTime(value: any) {
		this._loggedInTime = value;
	}

    /**
     * Setter loggedOutTime
     * @param {any} value
     */
	public set loggedOutTime(value: any) {
		this._loggedOutTime = value;
	}

    /**
     * Setter profileUrl
     * @param {any} value
     */
	public set profileUrl(value: any) {
		this._profileUrl = value;
	}

    /**
     * Setter state
     * @param {any} value
     */
	public set state(value: any) {
		this._state = value;
	}

    /**
     * Setter startTime
     * @param {any} value
     */
	public set startTime(value: any) {
		this._startTime = value;
	}

    /**
     * Setter endTime
     * @param {any} value
     */
	public set endTime(value: any) {
		this._endTime = value;
	}

    /**
     * Setter deletedUserDocs
     * @param {any[]} value
     */
	public set deletedUserDocs(value: any[]) {
		this._deletedUserDocs = value;
	}

    /**
     * Setter temp_startTime
     * @param {any} value
     */
	public set temp_startTime(value: any) {
		this._temp_startTime = value;
	}

    /**
     * Setter temp_endTime
     * @param {any} value
     */
	public set temp_endTime(value: any) {
		this._temp_endTime = value;
	}

    /**
     * Setter userDocs
     * @param {any[]} value
     */
	public set userDocs(value: any[]) {
		this._userDocs = value;
	}

    /**
     * Setter stateName
     * @param {any[]} value
     */
	public set stateName(value: any[]) {
		this._stateName = value;
	}

    /**
     * Setter cityName
     * @param {any[]} value
     */
	public set cityName(value: any[]) {
		this._cityName = value;
	}

    /**
     * Setter originalProfileUrl
     * @param {any} value
     */
	public set originalProfileUrl(value: any) {
		this._originalProfileUrl = value;
	}



    /**
     * Getter deleteProfileUrl
     * @return {any}
     */
	public get deleteProfileUrl(): any {
		return this._deleteProfileUrl;
	}

    /**
     * Setter deleteProfileUrl
     * @param {any} value
     */
	public set deleteProfileUrl(value: any) {
		this._deleteProfileUrl = value;
	}

}