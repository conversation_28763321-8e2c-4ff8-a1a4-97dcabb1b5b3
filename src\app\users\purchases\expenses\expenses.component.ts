import { Component, OnInit, OnDestroy, ViewChild, inject } from '@angular/core';
import { EnumForExpensesTab } from '@enums/EnumForExpensesTab.enum';
import { EnumForExpenseStatus } from '@enums/EnumForExpenseStatus';
import { ContainerExpense } from '@modal/ContainerExpense';
import { TempoExpensePagination } from '@modal/request/TempoExpensePagination';
import { TempoExpense } from '@modal/TempoExpense';
import { UtilsService } from '@service/utils.service';
import { Serialize, Deserialize } from 'cerialize';
import dayjs from 'dayjs';
import { saveAs } from 'file-saver';
import { DaterangepickerDirective } from 'ngx-daterangepicker-material';
import { Subscription } from 'rxjs';
import { TEMPO_EXP, CONTAINER_EXP } from 'src/app/shared/constants/constant';
declare var window: any;

@Component({
  selector: 'app-expenses',
  templateUrl: './expenses.component.html',
  styleUrls: ['./expenses.component.css']
})
export class ExpensesComponent implements OnInit, OnDestroy {

  @ViewChild(DaterangepickerDirective, { static: true }) pickerDirective: DaterangepickerDirective;
  utilsService = inject(UtilsService);

  selectedTab: string;
  enumForTabs = EnumForExpensesTab;
  enumForExpenseStatus = EnumForExpenseStatus;
  expensesTH: any[] = [];
  selectedIds: any[] = [];

  enumForSortOrder = this.utilsService.enumForSortOrder;
  paginationRequest = new TempoExpensePagination();
  tempoExpensesList: (TempoExpense | ContainerExpense)[] = [];
  tempoObj = new TempoExpense() || new ContainerExpense();
  // Tempo Expense
  selectedDocIndex: number;

  dateSub: Subscription;
  approveFlag: boolean = false;

  // Modal
  deleteTempoExpenseModal: any;
  approveDeclineModal: any;

  constructor() {
    if(this.utilsService.checkPageAccess([this.utilsService.enumForPage.VIEW_CONTAINER_EXP])) {
      this.selectedTab = this.enumForTabs.CONTAINER_EXPENSES;
    }
    else if(this.utilsService.checkPageAccess([this.utilsService.enumForPage.VIEW_TEMPO_EXP])) {
      this.selectedTab = this.enumForTabs.TEMPO_EXPENSES;
    }
  }

  ngOnInit() {

    this.dateSub = new Subscription();

    this.dateSub = this.paginationRequest.dateRangeControl.valueChanges.subscribe((a: any) => {
      if (a['start'] && a['end']) {
        this.paginationRequest.fromDate = dayjs(a['start']).format('YYYY-MM-DD');
        this.paginationRequest.toDate = dayjs(a['end']).format('YYYY-MM-DD');
        this.getAllExpenses()
      }
    });

    this.deleteTempoExpenseModal = new window.bootstrap.Modal(
      document.getElementById('deleteTempoExpenseModal')
    );

    document.getElementById('deleteTempoExpenseModal').addEventListener('hidden.bs.modal', () => {
      this.selectedDocIndex = null;
    });

    this.approveDeclineModal = new window.bootstrap.Modal(
      document.getElementById('approveDeclineModal')
    );

    this.getAllExpenses()
  }

  ngOnDestroy() {
    this.dateSub?.unsubscribe();
  }

  getAllExpenses = () => {

    let ls_param = null
    ls_param = JSON.parse(localStorage.getItem('param'))

    if (!this.utilsService.isNullUndefinedOrBlank(ls_param)) {
      if (ls_param.pageName === 'tempo-expenses') {
        this.paginationRequest.pageNo = ls_param.pageNo,
          this.paginationRequest.pageSize = ls_param.pageSize,
          this.selectedTab = ls_param.selectedTab;
      }
    }

    if (this.selectedTab === this.enumForTabs.TEMPO_EXPENSES) {
      this.expensesTH = TEMPO_EXP;
    }
    else if (this.selectedTab === this.enumForTabs.CONTAINER_EXPENSES) {
      this.expensesTH = CONTAINER_EXP;
    }

    this.paginationRequest.flagForSelectAll = false;
    this.selectedIds = []

    const API = this.selectedTab === this.enumForTabs.TEMPO_EXPENSES ? this.utilsService.serverVariableService.TEMPO_EXP_LISTING : this.utilsService.serverVariableService.CONTAINER_EXP_LISTING;

    this.utilsService.postMethodAPI(false, API, Serialize(this.paginationRequest), (response => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.tempoExpensesList = Deserialize(response.content, TempoExpense || ContainerExpense);
        this.paginationRequest.totalData = response.totalElements;
        this.paginationRequest.pagination = response;
        localStorage.removeItem('param')
      } else {
        this.tempoExpensesList = [];
      }
    }))
  }

  addPageSizeData = (event) => {
    this.paginationRequest.pageNo = 1;
    this.paginationRequest.pageSize = event;
    this.getAllExpenses();
  }

  pageNumber = (event) => {
    this.paginationRequest.pageNo = event
    this.getAllExpenses();
  }

  trackBy = (index: number, name: TempoExpense | ContainerExpense): number => {
    return name.id;
  }

  // sorting 
  onSortTH = (key: string) => {

    if (this.utilsService.isEmptyObjectOrNullUndefined(this.tempoExpensesList)) {
      return;
    }

    if (key === this.paginationRequest.sortColumn) {
      if (this.paginationRequest.sortOrder === this.enumForSortOrder.A) {
        this.paginationRequest.sortOrder = this.enumForSortOrder.D;
      } else if (this.paginationRequest.sortOrder === this.enumForSortOrder.D) {
        this.paginationRequest.sortOrder = this.enumForSortOrder.A;
      }
    } else {
      this.paginationRequest.sortOrder = this.enumForSortOrder.D;
    }

    this.paginationRequest.sortColumn = key;
    this.getAllExpenses();
  }

  selectAll = () => {
    this.selectedIds = [];

    if (this.paginationRequest.flagForSelectAll) {
      this.tempoExpensesList.forEach(val => {
        val.isSelected = true;
        this.selectedIds.push(val.id);
      });
    } else {
      this.tempoExpensesList.forEach(val => {
        val.isSelected = false;
      });
    }
  }

  selectUnselect = (id: number, value) => {
    const isSelected = this.selectedIds.includes(id);
    if (value && !isSelected) {
      this.selectedIds.push(id);
    } else if (!value && isSelected) {
      const assetIndex = this.selectedIds.indexOf(id);
      this.selectedIds.splice(assetIndex, 1);
    }
    this.paginationRequest.flagForSelectAll = this.checkIfAllSelected();
  }

  checkIfAllSelected = (): boolean => {
    return this.tempoExpensesList.every(val => val.isSelected);
  }

  onChangeTab = (value: string) => {
    if (this.selectedTab === value) return;

    this.selectedTab = value;
    this.tempoExpensesList = [];

    switch (this.selectedTab) {
      case this.enumForTabs.CONTAINER_EXPENSES:
        this.expensesTH = CONTAINER_EXP
        this.onClearFilters();
        break;
      case this.enumForTabs.TEMPO_EXPENSES:
        this.expensesTH = TEMPO_EXP
        this.onClearFilters();
        break;
      default:
        break;
    }
  }

  // Delete Document
  openDeleteDocsModal = (index: number, obj: TempoExpense | ContainerExpense) => {
    this.tempoObj = (obj)
    this.selectedDocIndex = index;
    this.deleteTempoExpenseModal.show();
  }

  deleteDocFromList = () => {
    this.utilsService.deleteMethodAPI(true, this.utilsService.serverVariableService.TEMPO_EXP_DOC_DELETE + `?documentId=${this.tempoObj.documents[this.selectedDocIndex]?.id}`, {}, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.deleteTempoExpenseModal.hide();
        this.getAllExpenses();
      }
    })
  }

  // Redirect
  redirectToDetails = () => {
    let param = null;
    param = {
      pageName: 'tempo-expenses',
      selectedTab: this.selectedTab,
      pageNo: this.paginationRequest.pageNo,
      pageSize: this.paginationRequest.pageSize,
    }
    localStorage.setItem('param', JSON.stringify(param))
  }

  // Delete Expense
  openDeleteExpenseModal = (obj: TempoExpense | ContainerExpense) => {
    this.tempoObj = obj;
    this.deleteTempoExpenseModal.show();
  }

  deleteExpense = () => {

    this.utilsService.deleteMethodAPI(true, this.utilsService.serverVariableService.TEMPO_EXP_SAVE_DEL + `?expenseId=${this.tempoObj.id}`, {}, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.deleteTempoExpenseModal.hide();
        if (!this.paginationRequest.pagination?.first && this.paginationRequest.pagination?.last && this.paginationRequest.pagination?.numberOfElements === 1) {
          this.paginationRequest.pageNo = this.paginationRequest.pageNo - 1
        }
        this.getAllExpenses();
      }
    })
  }

  // Search / Filters

  onSearch = (event: any) => {
    if (this.utilsService.isNullUndefinedOrBlank(event)) {
      this.paginationRequest.searchText = '';
    } else {
      this.paginationRequest.searchText = event.target.value.trim();
    }
    this.paginationRequest.pageNo = 1;
    this.getAllExpenses();
  }

  onChangeFromToPrice = (from: number, to: number) => {
    const fromField = this.utilsService.isNullUndefinedOrBlank(from) ? null : +from.toString().replace(/^\./, '');;
    const toField = this.utilsService.isNullUndefinedOrBlank(to) ? null : +to.toString().replace(/^\./, '');;
    this.paginationRequest.priceFrom = fromField;
    this.paginationRequest.priceTo = toField;
    this.paginationRequest.pageNo = 1;
    this.getAllExpenses();
  }

  open = (): void => {
    if (!this.pickerDirective.picker.isShown) {
      this.pickerDirective.open();
    } else this.pickerDirective.hide()
  }

  // Clear Filters
  onClearFilters = () => {
    this.paginationRequest.fromDate = null;
    this.paginationRequest.toDate = null;
    this.paginationRequest.priceFrom = null;
    this.paginationRequest.priceTo = null;
    this.paginationRequest.searchText = null;
    this.pickerDirective?.clear()
    this.getAllExpenses();
  }

  //export excel
  exportReport = () => {

    const API = this.selectedTab === this.enumForTabs.TEMPO_EXPENSES ? this.utilsService.serverVariableService.TEMPO_EXP_EXPORT : this.utilsService.serverVariableService.CONTAINER_EXP_EXPORT;

    const param = {
      ids: this.selectedIds ? this.selectedIds : [],
      searchText: this.paginationRequest.searchText,
      sortOrder: this.paginationRequest.sortOrder,
      sortColumn: this.paginationRequest.sortColumn,
      fromDate: this.paginationRequest.fromDate,
      toDate: this.paginationRequest.toDate,
      priceFrom: this.paginationRequest.priceFrom,
      priceTo: this.paginationRequest.priceTo
    }

    this.utilsService.exportReport(param, API).subscribe((data: any) => {
      saveAs(new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' }), `${this.selectedTab === this.enumForTabs.TEMPO_EXPENSES ? 'Tempo Expenses' : 'Container Expenses'} Sheet`);
    });
  }

  //Refresh
  onRefresh = () => {
    switch (this.selectedTab) {
      case this.enumForTabs.CONTAINER_EXPENSES:
        this.expensesTH = CONTAINER_EXP;
        this.getAllExpenses();
        break;
      case this.enumForTabs.TEMPO_EXPENSES:
        this.expensesTH = TEMPO_EXP;
        this.getAllExpenses();
        break;
      default:
        break;
    }
  }

  redirectToEdit = (id: number) => {
    this.redirectToDetails()
    switch (this.selectedTab) {
      case this.enumForTabs.CONTAINER_EXPENSES:
        this.utilsService.redirectTo(`/users/purchases/expenses/edit-expense/${id}`);
        break;
      case this.enumForTabs.TEMPO_EXPENSES:
        this.utilsService.redirectTo(`/users/purchases/expenses/edit-tempo-expense/${id}`);
        break;
      default:
        break;
    }
  }

  getExpenseTypeItems(item: TempoExpense | ContainerExpense): any[] {
    return item.expenseTypeItems || [];
  }

  // Approve/Decline
  openApproveDeclineModal = (approveFlag: boolean, item: TempoExpense | ContainerExpense) => {
    this.tempoObj = item;
    this.approveFlag = approveFlag;
    this.approveDeclineModal.show();
  }

  onApproveDeclineContainer = () => {

    let API = null;

    if (this.approveFlag) {
      API = `${this.utilsService.serverVariableService.CONTAINER_EXP_APPROVE}?expenseId=${this.tempoObj.id}`;
    } else {
      API = `${this.utilsService.serverVariableService.CONTAINER_EXP_DECLINE}?expenseId=${this.tempoObj.id}`;
    }

    this.utilsService.putMethodAPI(true, API, null, null, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.approveDeclineModal.hide();
        this.getAllExpenses();
      }
    })
  }

}
