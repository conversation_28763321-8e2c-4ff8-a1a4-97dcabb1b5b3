
import { Injectable, ViewChild, ChangeDetectorRef } from '@angular/core';
import { FormControl } from '@angular/forms';
import { DomSanitizer } from '@angular/platform-browser';
import { ActivatedRoute } from '@angular/router';
import { EnumForGridList } from '@enums/EnumForGridList';
import { EnumForItemDetails } from '@enums/EnumForItemDetails';
import { Category } from '@modal/Category';
import { Item } from '@modal/Item';
import { ItemImageObj } from '@modal/ItemImageObj';
import { ItemPagination } from '@modal/request/ItemPagination';
import { StockDetailsList } from '@modal/StockDetails';
import { TableHeader } from '@modal/TableHeader';
import { UtilsService } from '@service/utils.service';
import { Deserialize, Serialize } from 'cerialize';
import { saveAs } from 'file-saver';
import { Subject, Subscription } from 'rxjs';
import { activeInactiveStatus, ITEM_HEADER } from 'src/app/shared/constants/constant';
import { ItemDetailsPurchaseHistoryComponent } from './items-details/item-details-purchase-history/item-details-purchase-history.component';

@Injectable({
  providedIn: 'root'
})
export class ItemsService {

  @ViewChild(ItemDetailsPurchaseHistoryComponent) ItemDetailsPurchaseHistoryComponent: ItemDetailsPurchaseHistoryComponent;

  enumForSortOrder = this.utilsService.enumForSortOrder;
  deleteItemModal: any;
  deleteItemModalDetails: any;
  //headers
  optionsArray: TableHeader[] = [];
  columnArr: TableHeader[] = [];
  allHeaderArr: TableHeader[] = [];
  draggingColumnIndex: number = null;
  selectedId: number;
  flagforAllSelectH: boolean = false;

  selectedIds: any[] = [];
  paginationRequest = new ItemPagination();
  activeInactiveStatus = activeInactiveStatus;
  itemList: Item[] = [];
  itemObj = new Item();
  itemPageDropdown: any;
  flattenedParentCategory: any[];
  productmodalSliderModal: any;
  showSliderModal: boolean;
  itemImagesList: ItemImageObj[];
  currentItem = new ItemImageObj();
  selectedImageIndex: number;
  isCopied: boolean = false;
  timer: any = null;
  closeHeaderDropdown: boolean = true;

  //grid
  enumForView = EnumForGridList;
  selectedView: string = this.enumForView.LIST

  //ITEM DETAILS
  activeFlagDetail: boolean = null;
  itemId: number = null;
  itemDetailsList: Item[] = [];
  itemObjDetail = new Item();
  selectedItemIndex: number = 0;
  enumForTab = EnumForItemDetails
  selectedTab: string = this.enumForTab.OVERVIEW;
  searchDetail: string;
  searchDSubject: Subject<string> = new Subject<string>();

  getListFlag: boolean = false;

  searchDetailSupplier: string;
  supplierFilter: boolean = null;
  supplierFilterSubject: Subject<boolean> = new Subject<boolean>();
  searchSSubject: Subject<string> = new Subject<string>();

  searchReqPurchaseHistory = {
    supplier: null,
    status: null,
    searchText: null,
    dateRangeControl: new FormControl(null),
    fromDate: null,
    toDate: null,
    supplierDropdown: []
  }
  dateSub: Subscription;

  isExpandedIDs: number[] = [];
  markaDetailsStock: any;
  stockDetailObj = new StockDetailsList();
  deleteDocIds: number[] = [];
  searchText: string

  constructor(public utilsService: UtilsService, public route: ActivatedRoute, private cdr: ChangeDetectorRef, private sanitizer: DomSanitizer) { }

  getHeader() {

    // this.selectedId = null;
    // this.columnArr = [];
    this.utilsService.getMethodAPI(false, this.utilsService.serverVariableService.HEADER_GET_SAVE_API + `?pageName=${'Items'}`, null, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response && (JSON.parse(response.data)).length === 0)) {
        this.columnArr = Deserialize(ITEM_HEADER, TableHeader);
        const keys = (JSON.parse(response.data)).filter(v => !v.show).map(a => a.key)
        const indexArr = (JSON.parse(response.data)).map(a => a.index) as number[]
        const flag = indexArr.some(item => (item === null) || (item === undefined))
        this.columnArr.forEach(item => {
          item.isSelected = !keys.includes(item.key);
          item.show = !keys.includes(item.key);
        });
        if (!flag) {
          this.columnArr = indexArr.map(index => this.columnArr[index]);
        }
        this.selectedId = response.id ? response.id : null
      }
      else {
        this.columnArr = Deserialize(ITEM_HEADER, TableHeader)
        this.flagforAllSelectH = true;
      }
      //
      this.optionsArray = Serialize(this.columnArr)
      this.allHeaderArr = Serialize(this.columnArr)
    })
  }

  changeView(value: string) {

    if(this.selectedView === value) return;

    this.selectedView = value;
    this.paginationRequest.pageNo = 1;
    this.selectedIds = [];
    this.paginationRequest.flagForSelectAll = false;
    this.getAllItems();
  }

  // show hide columns

  saveCol() {
    this.closeHeaderDropdown = true;
    let newColArr = Serialize(this.allHeaderArr) as TableHeader[];
    const updatedData = newColArr.map((item, i) => ({
      ...item,
      show: item.isSelected,
    }));

    const sortedData = updatedData.sort((a, b) => {
      return (a.show === b.show) ? 0 : a.show ? -1 : 1;
    });


    const flag = newColArr.filter(v => v.isSelected).length < 4
    if (flag) {
      this.closeHeaderDropdown = false;
      this.utilsService.toasterService.error('Minimum 4 columns are required to display data', '', { closeButton: true });
      return;
    }

    const param = {
      id: this.selectedId,
      pageName: 'Items',
      data: JSON.stringify(sortedData)
      // data: JSON.stringify(this.columnArr)
    }
    this.utilsService.postMethodAPI(false, this.utilsService.serverVariableService.HEADER_GET_SAVE_API, param, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.getHeader();
        this.draggingColumnIndex = null;
      }
    })
  }

  // select ALL for header listing
  checkIfAllSelected() {
    let flag = true;
    this.allHeaderArr.filter((val) => {
      if (val['isSelected'] === false) {
        flag = false;
        return;
      }
    });
    this.flagforAllSelectH = flag;
    return flag;
  }
  ///

  getAllItems() {

    let ls_param = null
    ls_param = JSON.parse(localStorage.getItem('param'))

    if (!this.utilsService.isNullUndefinedOrBlank(ls_param)) {
      if (ls_param.pageName === 'item') {
        this.paginationRequest.pageNo = ls_param.pageNo,
        this.paginationRequest.pageSize = ls_param.pageSize,
        this.paginationRequest.sortOrder = ls_param.sortOrder
        this.paginationRequest.sortColumn = ls_param.sortColumn,
        this.paginationRequest.searchText = ls_param.searchText,
        this.paginationRequest.isActive = ls_param.isActive
        this.paginationRequest.categoryId = ls_param.categoryId
        this.paginationRequest.itemGroupId = ls_param.itemGroupId
        this.selectedView = ls_param.selectedView
      }
    }

    this.paginationRequest.flagForSelectAll = false;
    this.selectedIds = []
    this.utilsService.postMethodAPI(false, this.utilsService.serverVariableService.ITEMS_LISTING, Serialize(this.paginationRequest), (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.itemList = Deserialize(response.content, Item);

        this.itemList.forEach(v => {
          if (v.itemsMarketTypes) {
            v.marketType = (v.itemsMarketTypes || []).map(v => v.market?.marketName)
          }
        })
        this.paginationRequest.totalData = response.totalElements;
        this.paginationRequest.pagination = response;
        localStorage.removeItem('param')
      } else {
        this.itemList = [];
      }
    })
  }

  getPaginationReqData() {
    this.itemPageDropdown = null;
    this.utilsService.getMethodAPI(false, this.utilsService.serverVariableService.ITEM_LISTING_DROPDOWN, null, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.itemPageDropdown = response;
        setTimeout(() => {
          this.getParentCategory();
        }, 150);
      }
    })
  }

  // category dropdown
  modifyParentResponse(category: any[]) {
    let flatList = [];
    for (let parent of (category || [])) {
      parent.className = "ng-select-option-" + (parent.index + 1);
      flatList.push(parent);
      if (parent.categoryList && parent.categoryList.length > 0) {
        let data = (parent.categoryList || []).filter(p => p.index < 4);
        for (let d of data) {
          d.className = "ng-select-option-" + (d.index + 1);
          d.isChild = true;
        }
        flatList = flatList.concat(this.modifyParentResponse(data));
      }
    }
    return flatList;
  }

  getParentCategory() {
    let data = this.assignCategoryIndex(this.itemPageDropdown?.category || [])
    data = (data || []).filter(list => list.index == 0);
    this.flattenedParentCategory = this.itemPageDropdown?.category ? this.modifyParentResponse(data) : [];
  }

  assignCategoryIndex(categories: Category[], depth: number = 0): any[] {
    return categories.map((category) => {
      category.index = depth;
      if (category.categoryList && category.categoryList.length > 0) {
        category.categoryList = this.assignCategoryIndex(category.categoryList, depth + 1);
      }
      return category;
    });
  }

  //pagination
  addPageSizeData(event) {
    this.paginationRequest.pageNo = 1;
    this.paginationRequest.pageSize = event;
    this.getAllItems();
  }

  pageNumber(event) {
    this.paginationRequest.pageNo = event
    this.getAllItems();
  }

  trackBy(index: number, item: Item): number {
    return item.id;
  }

  redirectToDetails() {
    let param = null;
    param = {
      pageNo: this.paginationRequest.pageNo,
      pageSize: this.paginationRequest.pageSize,
      sortOrder: this.paginationRequest.sortOrder,
      sortColumn: this.paginationRequest.sortColumn,
      searchText: this.paginationRequest.searchText,
      isActive: this.paginationRequest.isActive,
      itemGroupId: this.paginationRequest.itemGroupId,
      categoryId: this.paginationRequest.categoryId,
      selectedView: this.selectedView,
      pageName: 'item'
    }
    localStorage.setItem('param', JSON.stringify(param))
  }

  redirectToItemDetails = (id: number) => {
    this.utilsService.redirectTo('/users/inventory/items/item-details/' + id)
    this.redirectToDetails()
  }

  // Select Deselect 
  selectAllItem() {
    if (this.paginationRequest.flagForSelectAll === true) {
      this.selectedIds = new Array<string>();
    }
    const obj = this.itemList.filter((val, index) => {
      if (this.paginationRequest.flagForSelectAll === true) {
        val['isSelected'] = true;
        this.selectedIds.push(val.id);
      } else {
        val['isSelected'] = false;
        this.selectedIds.splice(index, 1);
      }
    });
    if (this.paginationRequest.flagForSelectAll === false) {
      this.selectedIds = new Array<string>();
    }
  }

  selectUnselectItem(id: number, index, value) {

    const isSelected = this.selectedIds.includes(id);

    if (value && !isSelected) {

      this.selectedIds.push(id);

    } else if (!value && isSelected) {

      const assetIndex = this.selectedIds.indexOf(id);
      this.selectedIds.splice(assetIndex, 1);
    }
    this.paginationRequest.flagForSelectAll = this.checkIfAllSelectedItem();
  }

  checkIfAllSelectedItem() {
    let flag = true;
    this.itemList.filter((val, index) => {
      if (val['isSelected'] === false) {
        flag = false;
        return;
      }
    });
    return flag;
  }

  // sorting 
  onSortTH(key) {

    if (this.utilsService.isEmptyObjectOrNullUndefined(this.itemList)) {
      return;
    }

    if (key === this.paginationRequest.sortColumn) {
      if (this.paginationRequest.sortOrder === this.enumForSortOrder.A) {
        this.paginationRequest.sortOrder = this.enumForSortOrder.D;
      } else if (this.paginationRequest.sortOrder === this.enumForSortOrder.D) {
        this.paginationRequest.sortOrder = this.enumForSortOrder.A;
      }
    } else {
      this.paginationRequest.sortOrder = this.enumForSortOrder.D;
    }

    this.paginationRequest.sortColumn = key;
    this.getAllItems();
  }

  // status 
  onChangeStatus(item: Item, value, index) {

    this.utilsService.putMethodAPI(true, this.utilsService.serverVariableService.ITEMS_STATUS + `${item.id}/${value} `, {}, '', (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response.data)) {
        this.itemList[index].isActive = value
      } else {
        this.itemList[index].isActive = !value
      }
      if (!this.paginationRequest.pagination?.first && this.paginationRequest.pagination?.last && this.paginationRequest.pagination?.numberOfElements === 1 && this.paginationRequest.isActive) {
        this.paginationRequest.pageNo = this.paginationRequest.pageNo - 1
      }
      this.getAllItems()
    }, true);
  }

  //Search
  onSearch(event: any) {
    this.paginationRequest.searchText = event.target.value;
    this.getAllItems();
  }

  // active/inactive
  onChangeActive() {
    this.paginationRequest.pageNo = 1;
    this.getAllItems();
  }

  // export 
  exportReport(isDetail: boolean) {

    let param = null;

    if(isDetail) {
      param = {
        isActive: this.activeFlagDetail,
        searchText: this.searchDetail,
      }
    }

    if(!isDetail) {
      param = {
        ids: this.selectedIds ? this.selectedIds : [],
        isActive: this.paginationRequest.isActive,
        searchText: this.paginationRequest.searchText,
        sortOrder: this.paginationRequest.sortOrder,
        sortColumn: this.paginationRequest.sortColumn,
        itemGroupId: this.paginationRequest.itemGroupId,
        categoryId: this.paginationRequest.categoryId,
        groupCodeId: this.paginationRequest.groupCodeId,
        marketTypes : this.paginationRequest.marketTypes
      }
    }

    this.utilsService.exportReport(param, this.utilsService.serverVariableService.ITEMS_EXPORT).subscribe((data: any) => {
      saveAs(new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' }), 'Item Sheet');
    });
  }

  //Update sales price

  onChangeSalesPrice(isPiece: boolean, item: Item) {

    let param = null;

    if (isPiece) {
      param = {
        id: item.id,
        salesPricePiece: item.itemPrice ? item.itemPrice : 0
      }
    }
    if (!isPiece) {
      param = {
        id: item.id,
        salesPriceCarton: item.itemCarton ? item.itemCarton : 0
      }
    }

    this.utilsService.postMethodAPI(true, this.utilsService.serverVariableService.ITEMS_TABLE_SALES_PRICE, param, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.getAllItems();
      }
    })

  }

  // delete 
  openDeleteItem(item: Item) {
    this.itemObj = Serialize(item)
    this.deleteItemModal.show()
  }

  deleteItem() {
    this.utilsService.deleteMethodAPI(true, this.utilsService.serverVariableService.ITEM_SAVE_EDIT_DELETE + `?itemtId=${this.itemObj.id}`, {}, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.deleteItemModal.hide();
        if(!this.paginationRequest.pagination?.first && this.paginationRequest.pagination?.last && this.paginationRequest.pagination?.numberOfElements === 1) {
          this.paginationRequest.pageNo = this.paginationRequest.pageNo - 1
        }
        this.getAllItems();
      }
    })
  }

  // ITEM DETAILS APIs

  getItemDetails(index: number, itemId: number) {

    this.selectedItemIndex = index ? index : 0;
    this.itemId = itemId ? itemId : null;
    this.itemObjDetail = new Item();

    const param = {
      id: this.itemId ? this.itemId : null,
      isActive: this.activeFlagDetail,
      searchText: this.searchDetail ? this.searchDetail : null,
      searchName: this.searchDetailSupplier ? this.searchDetailSupplier : null,
      supplierIsActive: this.supplierFilter,
      isFlag: this.getListFlag,
      searchByMarka: this.searchText ? this.searchText : null
    }

    this.utilsService.postMethodAPI(false, this.utilsService.serverVariableService.ITEM_DETAILS, param, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        if(response.itemList) {
          this.itemDetailsList = Deserialize(response.itemList, Item);
          this.selectedItemIndex = this.itemDetailsList.findIndex(item => item.id === this.itemId)
        }
        if(response.item) {
          this.itemObjDetail = Deserialize(response.item, Item);
          this.itemObjDetail.itemDocs.map(a => {
            a.isMediaLinks = a.isMediaLinks ? a.isMediaLinks : false
            if (a.isMediaLinks && this.utilsService.isValidYouTubeLink(a.formattedName)) {
              a.linkUrl = this.utilsService.convertToEmbedYouTubeLink(a.formattedName)
            }
            return a;
          })
          this.searchReqPurchaseHistory.supplierDropdown = this.itemObjDetail.supplier ? this.itemObjDetail.supplier : [];
          if (this.itemObjDetail.itemsMarketTypes) {
            this.itemObjDetail.marketType = (this.itemObjDetail.itemsMarketTypes || []).map(v => v.market?.marketName)
          }
          if (this.itemObjDetail.stockDetails.length > 0) {
            this.itemObjDetail.stockDetails[0].isExpand = true
          }
        }
        else {
          this.itemObjDetail = null;
        }
      }
    })
    
  }

  onSearchMarka(event: any) {
    this.searchText = event.target.value;
    this.getItemDetails(this.selectedItemIndex, this.itemId);
    this.getListFlag = true;
  }

  onSearchDetail(event: any) {
    this.searchDetail = event.target.value;
    this.getItemDetails(this.selectedItemIndex, this.itemId);
    this.getListFlag = false;
    // this.searchDSubject.next(event.target.value);
  }

  onChangeActiveDetail() {
    this.selectedItemIndex = 0;
    this.getListFlag = false;
    this.getItemDetails(this.selectedItemIndex, null);
    // this.getItemDetails(this.selectedItemIndex, this.itemId);
  }

  changeItem(index: number, id: number) {
    this.getListFlag = true;
    this.utilsService.redirectTo('/users/inventory/items/item-details/' + id)
    this.getItemDetails(index, id);
  }

  changeItemDetailsTab(value: string) {
    this.selectedTab = value;
  }

  changeActiveInactive() {
    this.utilsService.putMethodAPI(true, this.utilsService.serverVariableService.ITEMS_STATUS + `${this.itemId}/${!this.itemObjDetail.isActive}`, {}, '', (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.getListFlag = false;
        this.getItemDetails(this.selectedItemIndex, this.itemId)
      }
    });
  }

  onSearchSupplier(event) {
    this.searchSSubject.next(event.target.value);
  }

  onChangeSupplierActive(event) {
    if(event) {
      this.supplierFilterSubject.next(event.value);
    } else this.supplierFilterSubject.next(null);
  }

  // delete From Detials
  openDeleteDetailsItem(item: Item) {
    this.itemObj = Serialize(item)
    this.deleteItemModalDetails.show()
  }

  deleteItemDetails() {
    this.utilsService.deleteMethodAPI(true, this.utilsService.serverVariableService.ITEM_SAVE_EDIT_DELETE + `?itemtId=${this.itemObj.id}`, {}, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.deleteItemModalDetails.hide();
        this.utilsService.redirectTo('/users/inventory/items/')
      }
    })
  }

  //image list modal
  openImagePreviewModal(item: Item) {
    if (this.timer) {
      clearTimeout(this.timer);
    }
    this.isCopied = false;
    this.itemImagesList = [];
    this.showSliderModal = true;
    this.itemObj = Serialize(item)
    setTimeout(() => {
      this.getImageByItem(item.id)
    }, 50);
  }

  getImageByItem(id: number) {
    this.utilsService.getMethodAPI(false, this.utilsService.serverVariableService.ITEM_IMAGE_BY_ID + `?itemId=${id}`, null, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.itemImagesList = response;
        this.itemImagesList.forEach(a => {
          if (a.isMediaLinks && this.utilsService.isValidYouTubeLink(a.formattedName)) {
            a.linkUrl = this.utilsService.convertToEmbedYouTubeLink(a.formattedName)
          }
        });
        if (this.itemImagesList) {
          const markedImages = this.itemImagesList.filter(item => item.isMarkDefault);
          const unmarkedImages = this.itemImagesList.filter(item => !item.isMarkDefault);
          const sortedImages = [...markedImages, ...unmarkedImages];

          this.itemImagesList = Serialize(sortedImages);
          this.currentItem = this.itemImagesList[0];
          this.selectedImageIndex = 1;
          this.productmodalSliderModal.show();
        }
      }
    })
  }

  //
  onSlideChange(event: any): void {
    if (this.timer) {
      clearTimeout(this.timer);
    }
    this.isCopied = false;
    const activeIndex = event.slick.currentSlide;
    this.currentItem = this.itemImagesList[activeIndex];
    this.selectedImageIndex = activeIndex + 1; 
  }

  onCopy() {
    this.isCopied = true;
    if (this.timer) {
      clearTimeout(this.timer);
    }
    this.timer = setTimeout(() => {
      this.isCopied = false;
    }, 5000);
  }

  // On Clear Purchase History Filters
  onClear = () => {
    
  }

  // Stock Details get Image

  openStockDetailEditModal = (item: StockDetailsList) => {
    this.deleteDocIds = [];
    this.itemImagesList = [];
    this.stockDetailObj = Serialize(item)
    this.getImageByStockId();
  }

  getImageByStockId() {
    this.utilsService.getMethodAPI(false, this.utilsService.serverVariableService.GET_ITEM_IMAGE_FROMGRN + `?marka=${this.stockDetailObj.marka}`, null, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.markaDetailsStock.show()
        this.itemImagesList = Deserialize(response, ItemImageObj);
        for(const item of this.itemImagesList) {
          if(item.grnDocGroupLinkId) {
            item.isLink = true;
          }
        }
      }
      else {
        this.utilsService.toasterService.error('No Images Found', '', {
          positionClass: 'toast-top-right',
          closeButton: true,
          timeOut: 10000
        });
      }
    })
  }

  onSaveCreateLink = () => {

    const formData = new FormData();

    const param = this.itemImagesList.filter(item => item.id).map(item => ({ isFlag: item.isLink, id: item.id }));

    const req = {
      marka: this.stockDetailObj.marka,
      grnGroupLinkId: this.stockDetailObj.grnGroupLinkId ?? null,
      grnDocs: param,
      deleteDocIds: this.deleteDocIds || []
    }

    for (let img of this.itemImagesList) {
      if (img.file) {
        formData.append('files', img.file);
      }
    }

    formData.set('info', JSON.stringify(req));

    this.utilsService.postMethodAPI(true, this.utilsService.serverVariableService.SAVE_ITEM_LINK_IMG, formData, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.markaDetailsStock.hide()
        this.getItemDetails(this.selectedItemIndex, this.itemId)
      }
    })
  }

  removeAttachment(i: number, file: any) {
    this.itemImagesList.splice(i, 1)
    if (file.id) {
      (this.deleteDocIds || []).push(file.id)
    }
  }

  // documents
  onSelectAttachments(event): void {

    let selectedFiles: FileList | null = null;
    if (event.type === 'drop') {
      event.preventDefault();
      selectedFiles = event.dataTransfer?.files;
    }

    if (event.type === 'dragover') {
      event.preventDefault();
    }

    if (event.type === 'change') {
      selectedFiles = event.target.files;
    }

    if (event.type === 'paste') {
      const items = (event.clipboardData.items);
      const dataTransfer = new DataTransfer();

      for (const item of items) {
        if (item.type.indexOf('image') === 0) {
          const blob = item.getAsFile();
          const fileName = blob?.name;
          if (blob !== null && fileName) {
            const fileName = `${Date.now()}-image.${item.type.split('/')[1]}`;
            const fileFromBlob = new File([blob], fileName, { type: item.type });
            dataTransfer.items.add(fileFromBlob);
          }
        }
      }
      selectedFiles = dataTransfer.files;
    }

    const max_file_size = 5242880;

    if (selectedFiles) {
      Array.from(selectedFiles).forEach((file: File, i: number) => {
        const ext = file.name.substr(file.name.lastIndexOf('.') + 1).toLowerCase();

        if (['jpeg', 'png', 'jpg', 'jfif', 'webp'].includes(ext)) {
          if (file.size > max_file_size) {
            this.utilsService.toasterService.error(this.utilsService.validationService.IMAGE_MAX_FILE_SIZE);
          } else {
            const fileUrl = URL.createObjectURL(file);
            let fileData = null;
            fileData = {
              id: null,
              file: file,
              originalName: file.name,
              formattedName: fileUrl,
            };
            if (this.itemImagesList.length >= 10) {
              this.utilsService.toasterService.error('You have exceeded the file upload limit of 10', '', {
                positionClass: 'toast-top-right',
                closeButton: true,
                timeOut: 10000
              });
              selectedFiles = null;
              return;
            }

            this.itemImagesList.push(fileData);
            selectedFiles = null;
          }
        } else {
          this.utilsService.toasterService.error(this.utilsService.validationService.IMAGE_INVALID_EXTENSION);
        }
      });
    }

  }

  openLink(link, newUpload: any) {
    const filePreview = link ? `${this.utilsService.imgPath}${link}` : null

    if (!this.utilsService.isEmptyObjectOrNullUndefined(newUpload)) {
      window.open(newUpload, "_blank");
      return;
    }

    if (!this.utilsService.isEmptyObjectOrNullUndefined(filePreview)) {
      window.open(filePreview, "_blank");
      return;
    }
  }
}
