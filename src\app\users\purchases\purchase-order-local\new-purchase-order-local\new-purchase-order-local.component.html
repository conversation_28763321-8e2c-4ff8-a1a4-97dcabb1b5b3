<div class="page-content">
  <div class="page-title-wrapper">
    <div class="page-title-left">
      <h4>Add New Local Purchase Order</h4>
    </div>
    <div class="page-title-right">
      <Button class="btn btn-sm btn-icon btn-outline-white" [routerLink]="['/users/purchases/po-local']"
        ngbTooltip="Close" placement="left" container="body" triggers="hover">
        <i class="th th-close"></i>
      </Button>
    </div>
  </div>
  <div class="content-area">
    <div class="card card-theme card-forms">
      <div class="card-body">
        <div class="row">
          <div class="col-lg-4 col-md-6 col-sm-12">
            <div class="form-group form-group-inline-control required">
              <label class="form-label">Purchase Order#</label>
              <div class="form-control-wrapper">
                <input type="text" class="form-control" placeholder="Purchase Order#" value="#***********" disabled>
              </div>
            </div>
            <div class="form-group theme-ngselect form-group-inline-control required">
              <label class="form-label">Supplier</label>
              <div class="form-control-wrapper">
                <ng-select placeholder="Supplier" [multiple]="false" [clearable]="false" [items]="demo" bindLabel="name"
                  bindValue="id" [(ngModel)]="selectedDemo1" appendTo="body">
                </ng-select>
              </div>
            </div>
            <div class="form-group theme-ngselect form-group-inline-control required">
              <label class="form-label">Bank Group</label>
              <div class="form-control-wrapper">
                <ng-select placeholder="Bank Group" [multiple]="false" [clearable]="false" [items]="demo"
                  bindLabel="name" bindValue="id" [(ngModel)]="selectedDemo1" appendTo="body">
                </ng-select>
              </div>
            </div>
            <div class="form-group theme-ngselect form-group-inline-control required">
              <label class="form-label">Order For</label>
              <div class="form-control-wrapper">
                <div class="radio radio-primary form-check-inline">
                  <input type="radio" id="stock" name='orderfor' checked />
                  <label for="stock">Stock</label>
                </div>
                <div class="radio radio-primary form-check-inline">
                  <input type="radio" id="customer" name='orderfor' />
                  <label for="customer">Customer</label>
                </div>
                <div class="mt-1">
                  <ng-select placeholder="Customer" [multiple]="false" [clearable]="false" [items]="demo"
                    bindLabel="name" bindValue="id" [(ngModel)]="selectedDemo1" appendTo="body">
                  </ng-select>
                </div>
              </div>
            </div>
            <div class="form-group theme-ngselect form-group-inline-control">
              <label class="form-label">Pickup Person</label>
              <div class="form-control-wrapper">
                <ng-select placeholder="Pickup Person" [multiple]="false" [clearable]="false" [items]="demo"
                  bindLabel="name" bindValue="id" [(ngModel)]="selectedDemo1" appendTo="body">
                </ng-select>
              </div>
            </div>
            <div class="form-group theme-ngselect form-group-inline-control">
              <label class="form-label">Submitted To</label>
              <div class="form-control-wrapper">
                <ng-select placeholder="Submitted To" [multiple]="false" [clearable]="false" [items]="demo"
                  bindLabel="name" bindValue="id" [(ngModel)]="selectedDemo1" appendTo="body">
                </ng-select>
              </div>
            </div>
            <div class="form-group form-group-inline-control">
              <label class="form-label">Expected Delivery Date</label>
              <div class="form-control-wrapper">
                <div class="form-group-icon-end">
                  <i class="th th-outline-calendar"></i>
                  <input type="text" class="form-control" placeholder="Select Date">
                </div>
              </div>
            </div>
            <div class="form-group theme-ngselect form-group-inline-control">
              <label class="form-label">Payment Terms</label>
              <div class="form-control-wrapper">
                <ng-select placeholder="Payment Terms" [multiple]="false" [clearable]="false" [items]="demo"
                  bindLabel="name" bindValue="id" [(ngModel)]="selectedDemo1" appendTo="body">
                </ng-select>
              </div>
            </div>
          </div>
          <div class="col-lg-4 col-md-6 col-sm-12">
            <div class="form-group form-group-inline-control required">
              <label class="form-label">PO Date</label>
              <div class="form-control-wrapper">
                <div class="form-group-icon-end">
                  <i class="th th-outline-calendar"></i>
                  <input type="text" class="form-control" placeholder="Select Date">
                </div>
              </div>
            </div>
            <div class="form-group theme-ngselect form-group-inline-control">
              <label class="form-label">Contact Person</label>
              <div class="form-control-wrapper">
                <div class="form-group-button">
                  <ng-select placeholder="Contact Person" [multiple]="false" [clearable]="false" [items]="demo"
                    bindLabel="name" bindValue="id" [(ngModel)]="selectedDemo1" appendTo="body">
                  </ng-select>
                  <button class="btn btn-outline-white"><i class="th th-outline-add-circle text-primary"></i></button>
                </div>
              </div>
            </div>
            <div class="form-group form-group-inline-control">
              <label class="form-label">Contact Mobile No</label>
              <div class="form-control-wrapper">
                <input type="text" class="form-control" placeholder="Enter mobile no">
              </div>
            </div>
            <div class="form-group form-group-inline-control">
              <label class="form-label">Pickup Location</label>
              <div class="form-control-wrapper">
                <input type="text" class="form-control" placeholder="Enter Pickup Location">
              </div>
            </div>
            <div class="form-group form-group-inline-control">
              <label class="form-label">Advance Payment</label>
              <div class="form-control-wrapper">
                <input type="text" class="form-control" placeholder="Advance Payment">
              </div>
            </div>
            <div class="form-group theme-ngselect form-group-inline-control">
              <label class="form-label">Transporter</label>
              <div class="form-control-wrapper">
                <ng-select placeholder="Transporter" [multiple]="false" [clearable]="false" [items]="demo"
                  bindLabel="name" bindValue="id" [(ngModel)]="selectedDemo1" appendTo="body">
                </ng-select>
              </div>
            </div>
            <div class="form-group theme-ngselect form-group-inline-control required">
              <label class="form-label">Delivery Address</label>
              <div class="form-control-wrapper">
                <ng-select placeholder="Delivery Address" [multiple]="false" [clearable]="false" [items]="demo"
                  bindLabel="name" bindValue="id" [(ngModel)]="selectedDemo1" appendTo="body">
                </ng-select>
              </div>
            </div>
            <div class="form-group theme-ngselect form-group-inline-control required">
              <label class="form-label">Delivery Type</label>
              <div class="form-control-wrapper">
                <div class="radio radio-primary form-check-inline">
                  <input type="radio" id="door" name='deliverytype' checked />
                  <label for="door">Door</label>
                </div>
                <div class="radio radio-primary form-check-inline">
                  <input type="radio" id="self" name='deliverytype' />
                  <label for="self">Self</label>
                </div>
              </div>
            </div>
            <div class="form-group form-group-inline-control">
              <label class="form-label">Notes</label>
              <div class="form-control-wrapper">
                <textarea class="form-control" placeholder="Enter Note"></textarea>
              </div>
            </div>
          </div>
          <div class="col-lg-4 col-md-6 col-sm-12">
            <div class="d-flex flex-column h-100">
              <div class="attachments-wrapper">
                <div class='attachments-container h-100'>
                  <div class='attachments-content'>
                    <button class='btn btn-attachments'><i class="bi bi-upload"></i></button>
                    <p>Drag and Drop file here or <span class='text-primary'>Choose file</span></p>
                  </div>
                  <input type="file" ref={imageRef} multiple />
                </div>
                <div class='attachments-upload-grid-container attachments-upload-grid-container2'>
                  <div class='attachments-upload-row'>
                    <div class='attachments-upload-col'>
                      <div class='card-attachments-upload'>
                        <div class='attachments-image'>
                          <img src="assets/images/avatar.jpg" alt="valamji" />
                        </div>
                        <div class="attachments-text">
                          <h6 class="file-name">Filename.jpg</h6>
                          <p class="file-size">Size: 5mb</p>
                        </div>
                        <button class="btn-close" variant="close"><i class='th th-close'></i></button>
                      </div>
                      <div class="radio radio-primary">
                        <input type="radio" id="thumb" name="thumb" checked="">
                        <label for="thumb">Mark default</label>
                      </div>
                    </div>
                    <div class='attachments-upload-col'>
                      <div class='card-attachments-upload'>
                        <div class='attachments-image'>
                          <img src="assets/images/avatar.jpg" alt="valamji" />
                        </div>
                        <div class="attachments-text">
                          <h6 class="file-name">Filename.jpg</h6>
                          <p class="file-size">Size: 5mb</p>
                        </div>
                        <button class="btn-close" variant="close"><i class='th th-close'></i></button>
                      </div>
                      <div class="radio radio-primary">
                        <input type="radio" id="thumb2" name="thumb" checked="">
                        <label for="thumb2">Mark default</label>
                      </div>
                    </div>
                    <div class='attachments-upload-col'>
                      <div class='attachments-container attachments-container2'>
                        <div class='attachments-content'>
                          <button class='btn btn-primary btn-icon btn-sm btn-round'><i
                              class="th th-outline-add"></i></button>
                        </div>
                        <input type="file" ref={imageRef} multiple />
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="card-forms-total">
                <table class="table table-theme table-hover">
                  <tbody>
                    <tr>
                      <td>Sub Total</td>
                      <th class="text-black">₹ 50,000.00</th>
                    </tr>
                    <tr>
                      <td>GST AMT</td>
                      <th class="text-black">₹ 50,000.00</th>
                    </tr>
                    <tr>
                      <td>Discount</td>
                      <td>
                        <div class="form-group">
                          <div class="input-group">
                            <input type="text" class="form-control" aria-label="Discount" placeholder="Discount">
                            <button type="button" class="btn dropdown-toggle dropdown-toggle-split"
                              data-bs-toggle="dropdown" aria-expanded="false">
                              %<span class="visually-hidden">Toggle Dropdown</span>
                            </button>
                            <ul class="dropdown-menu">
                              <li><a class="dropdown-item" href="#">%</a></li>
                              <li><a class="dropdown-item" href="#">%</a></li>
                            </ul>
                          </div>
                        </div>
                      </td>
                    </tr>
                    <tr>
                      <td>Adjustment</td>
                      <td>
                        <div class="form-group">
                          <div class="input-group">
                            <input type="text" class="form-control" aria-label="Adjustment"
                              placeholder="Eg: +10 or -10">
                            <button type="button" class="btn dropdown-toggle dropdown-toggle-split"
                              data-bs-toggle="dropdown" aria-expanded="false">
                              Rs<span class="visually-hidden">Toggle Dropdown</span>
                            </button>
                            <ul class="dropdown-menu">
                              <li><a class="dropdown-item" href="#">Rs</a></li>
                              <li><a class="dropdown-item" href="#">$</a></li>
                            </ul>
                          </div>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                  <tfoot>
                    <tr>
                      <td>Total</td>
                      <th class="text-black fs-14">₹75,150.00</th>
                    </tr>
                  </tfoot>
                </table>
              </div>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-lg-12 col-md-12 col-sm-12">
            <hr />
          </div>
        </div>
        <div class="row">
          <div class="col-lg-12 col-md-12 col-sm-12">
            <div class="inner-title-wrapper">
              <div class="inner-title-left">
                <div class="inner-title-text">
                  <h6 class="">Add Items*</h6>
                </div>
              </div>
              <div class="inner-title-rigth">
              </div>
            </div>
          </div>
          <div class="col-lg-12 col-md-12 col-sm-12">
            <div class="table-responsive">
              <table class="table-theme table-hover table table-bordered ">
                <thead class="border-less">
                  <tr>
                    <th>Item Details</th>
                    <th>HSN Code</th>
                    <th>Purchase <br />Breach Qty</th>
                    <th>Available Qty</th>
                    <th>Difference <br />(Formula[B])</th>
                    <th>Sale Price/<br />PCS</th>
                    <th>Sale Price/<br />Carton</th>
                    <th>Last Sale Price <br />(Carton)</th>
                    <th>Last Purchase <br />(Purchase By/<br />CTN)</th>
                    <th>Other Supplier Price</th>
                    <th>Order Type</th>
                    <th>Cartons <br />(PO)</th>
                    <th>PCS/Carton <br />(PO)</th>
                    <th>Total Quantity <br />(PO)</th>
                    <th>Purchase <br />Price</th>
                    <th>Total Amount</th>
                    <th>GST %</th>
                    <th>GST AMT</th>
                    <th>Total Amount <br />(With TAX)</th>
                    <th>Note</th>
                    <th>Inquiry (Cus <br />Count) - (Total <br />inq. Count)</th>
                    <th>Item Rating</th>
                    <th>Market Type</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let item of [1,2,3,4,5]">
                    <td class="tbl-user ">
                      <div class="tbl-user-checkbox-srno">
                        <div class="checkbox checkbox-primary checkbox-small">
                          <input type="checkbox" id="tbl-checkbox2" class="material-inputs filled-in" />
                          <label for="tbl-checkbox2"></label>
                        </div>
                        <span>01.</span>
                        <div class="tbl-user-wrapper">
                          <div class="tbl-user-image"><img src="assets/images/dummy-product.png" alt="valamji">
                          </div>
                          <div class="tbl-user-text">
                            <p>Ink Pen</p>
                            <span>VO #1550</span>
                          </div>
                        </div>
                      </div>
                    </td>
                    <td>458555</td>
                    <td>
                      <a class="btn-link text-primary" data-bs-toggle="modal"
                        data-bs-target="#poStockDetailsModal">20</a>
                    </td>
                    <td>25</td>
                    <td>20</td>
                    <td>110</td>
                    <td>100</td>
                    <td>105,106,104</td>
                    <td>
                      <p class="mb-0">Kavya, 50 </p>
                      <p class="mb-0">Nirmal,45</p>
                      <p class="mb-0">Ashopalav,45</p>
                      <div class="dropdown dropdown-with-tables">
                        <button class="btn btn-link text-primary" type="button" data-bs-toggle="dropdown"
                          aria-expanded="false" data-bs-auto-close="outside"
                          data-bs-popper-config='{"strategy":"fixed"}'>
                          View more
                        </button>
                        <div class="dropdown-menu">
                          <div class="table-responsive">
                            <table class="table-theme table-hover table table-bordered table-sticky ">
                              <thead class="border-less">
                                <tr>
                                  <th>Supplier Name</th>
                                  <th>Carton Qty</th>
                                  <th>Qty/Carton</th>
                                  <th>Total Qty</th>
                                  <th>Price</th>
                                  <th>Dimension</th>
                                </tr>
                              </thead>
                              <tbody>
                                <tr *ngFor="let item of [1,2,3,4,5]">
                                  <td class="tbl-user">
                                    <div class="tbl-user-checkbox-srno">
                                      <div class="tbl-user-wrapper">
                                        <div class="tbl-user-image">
                                          <i class="th th-outline-user"></i>
                                        </div>
                                        <div class="tbl-user-text">
                                          <p>Alpeshbhai</p>
                                          <span>alpeshbhai56&#64;gmail.com</span>
                                        </div>
                                      </div>
                                    </div>
                                  </td>
                                  <td>15</td>
                                  <td>20</td>
                                  <td>300</td>
                                  <td>85</td>
                                  <td>20 x 30 x 49</td>
                                </tr>
                              </tbody>
                            </table>
                          </div>
                        </div>
                      </div>
                    </td>
                    <td>
                      <div class="d-flex align-items-center">
                        <div class="dropdown dropdown-with-tables">
                          <button class="btn btn-link text-primary" type="button" data-bs-toggle="dropdown"
                            aria-expanded="false" data-bs-auto-close="outside"
                            data-bs-popper-config='{"strategy":"fixed"}'>
                            Amish, 45
                          </button>
                          <div class="dropdown-menu">
                            <div class="table-responsive">
                              <table class="table-theme table-hover table table-bordered table-sticky ">
                                <thead class="border-less">
                                  <tr>
                                    <th>Supplier Name</th>
                                    <th>Qty/Carton</th>
                                    <th>Last Purchased on</th>
                                    <th>Price</th>
                                  </tr>
                                </thead>
                                <tbody>
                                  <tr *ngFor="let item of [1,2,3,4,5]">
                                    <td class="tbl-user">
                                      <div class="tbl-user-checkbox-srno">
                                        <div class="tbl-user-wrapper">
                                          <div class="tbl-user-image">
                                            <i class="th th-outline-user"></i>
                                          </div>
                                          <div class="tbl-user-text">
                                            <p>Alpeshbhai</p>
                                            <span>alpeshbhai56&#64;gmail.com</span>
                                          </div>
                                        </div>
                                      </div>
                                    </td>
                                    <td>15</td>
                                    <td>12/05/2024</td>
                                    <td>85</td>
                                  </tr>
                                </tbody>
                              </table>
                            </div>
                          </div>
                        </div>
                        <i class="th th-outline-info-circle ms-auto text-danger" ngbTooltip="Other Supplier Price	"
                          placement="bottom" container="body" triggers="hover"></i>
                      </div>
                    </td>
                    <td class="tbl-form-group-borderless">
                      <div class="form-group theme-ngselect">
                        <ng-select placeholder="Order Type" [multiple]="false" [clearable]="false" [items]="demo"
                          bindLabel="name" bindValue="id" [(ngModel)]="selectedDemo1" appendTo="body">
                        </ng-select>
                      </div>
                    </td>
                    <td>5</td>
                    <td>10</td>
                    <td>50</td>
                    <td>100</td>
                    <td>5000</td>
                    <td>18</td>
                    <td>900</td>
                    <td>5900</td>
                    <td>kale mokalshe-gujrat transport ma</td>
                    <td>3 [15]</td>
                    <td>2.1</td>
                    <td class="tbl-form-group-borderless">
                      <div class="form-group theme-ngselect">
                        <ng-select placeholder="Market Type" [multiple]="false" [clearable]="false" [items]="demo"
                          bindLabel="name" bindValue="id" [(ngModel)]="selectedDemo1" appendTo="body">
                        </ng-select>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
        <div class="row mt-3">
          <div class="col-lg-12 col-md-12 col-sm-12">
            <div class="inner-title-wrapper">
              <div class="inner-title-left">
                <div class="inner-title-text">
                  <h6 class="">Other Items by supplier</h6>
                </div>
              </div>
              <div class="inner-title-rigth">
              </div>
            </div>
          </div>
          <div class="col-lg-12 col-md-12 col-sm-12">
            <div class="table-responsive">
              <table class="table-theme table-hover table table-bordered ">
                <thead class="border-less">
                  <tr>
                    <th>Item Details</th>
                    <th>HSN Code</th>
                    <th>Purchase <br />Breach Qty</th>
                    <th>Available Qty</th>
                    <th>Difference <br />(Formula[B])</th>
                    <th>Sale Price/<br />PCS</th>
                    <th>Sale Price/<br />Carton</th>
                    <th>Last Sale Price <br />(Carton)</th>
                    <th>Last Purchase <br />(Purchase By/<br />CTN)</th>
                    <th>Other Supplier Price</th>
                    <th>Order Type</th>
                    <th>Cartons <br />(PO)</th>
                    <th>PCS/Carton <br />(PO)</th>
                    <th>Total Quantity <br />(PO)</th>
                    <th>Purchase <br />Price</th>
                    <th>Total Amount</th>
                    <th>GST %</th>
                    <th>GST AMT</th>
                    <th>Total Amount <br />(With TAX)</th>
                    <th>Note</th>
                    <th>Inquiry (Cus <br />Count) - (Total <br />inq. Count)</th>
                    <th>Item Rating</th>
                    <th>Market Type</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let item of [1,2,3,4,5]">
                    <td class="tbl-user ">
                      <div class="tbl-user-checkbox-srno">
                        <div class="checkbox checkbox-primary checkbox-small">
                          <input type="checkbox" id="tbl-checkbox2" class="material-inputs filled-in" />
                          <label for="tbl-checkbox2"></label>
                        </div>
                        <span>01.</span>
                        <div class="tbl-user-wrapper">
                          <div class="tbl-user-image"><img src="assets/images/dummy-product.png" alt="valamji">
                          </div>
                          <div class="tbl-user-text">
                            <p>Ink Pen</p>
                            <span>VO #1550</span>
                          </div>
                        </div>
                      </div>
                    </td>
                    <td>458555</td>
                    <td>
                      <a class="btn-link text-primary" data-bs-toggle="modal"
                        data-bs-target="#poStockDetailsModal">20</a>
                    </td>
                    <td>25</td>
                    <td>20</td>
                    <td>110</td>
                    <td>100</td>
                    <td>105,106,104</td>
                    <td>
                      <p class="mb-0">Kavya, 50 </p>
                      <p class="mb-0">Nirmal,45</p>
                      <p class="mb-0">Ashopalav,45</p>
                      <div class="dropdown dropdown-with-tables">
                        <button class="btn btn-link text-primary" type="button" data-bs-toggle="dropdown"
                          aria-expanded="false" data-bs-auto-close="outside"
                          data-bs-popper-config='{"strategy":"fixed"}'>
                          View more
                        </button>
                        <div class="dropdown-menu">
                          <div class="table-responsive">
                            <table class="table-theme table-hover table table-bordered table-sticky ">
                              <thead class="border-less">
                                <tr>
                                  <th>Supplier Name</th>
                                  <th>Carton Qty</th>
                                  <th>Qty/Carton</th>
                                  <th>Total Qty</th>
                                  <th>Price</th>
                                  <th>Dimension</th>
                                </tr>
                              </thead>
                              <tbody>
                                <tr *ngFor="let item of [1,2,3,4,5]">
                                  <td class="tbl-user">
                                    <div class="tbl-user-checkbox-srno">
                                      <div class="tbl-user-wrapper">
                                        <div class="tbl-user-image">
                                          <i class="th th-outline-user"></i>
                                        </div>
                                        <div class="tbl-user-text">
                                          <p>Alpeshbhai</p>
                                          <span>alpeshbhai56&#64;gmail.com</span>
                                        </div>
                                      </div>
                                    </div>
                                  </td>
                                  <td>15</td>
                                  <td>20</td>
                                  <td>300</td>
                                  <td>85</td>
                                  <td>20 x 30 x 49</td>
                                </tr>
                              </tbody>
                            </table>
                          </div>
                        </div>
                      </div>
                    </td>
                    <td>
                      <div class="d-flex align-items-center">
                        <div class="dropdown dropdown-with-tables">
                          <button class="btn btn-link text-primary" type="button" data-bs-toggle="dropdown"
                            aria-expanded="false" data-bs-auto-close="outside"
                            data-bs-popper-config='{"strategy":"fixed"}'>
                            Amish, 45
                          </button>
                          <div class="dropdown-menu">
                            <div class="table-responsive">
                              <table class="table-theme table-hover table table-bordered table-sticky ">
                                <thead class="border-less">
                                  <tr>
                                    <th>Supplier Name</th>
                                    <th>Qty/Carton</th>
                                    <th>Last Purchased on</th>
                                    <th>Price</th>
                                  </tr>
                                </thead>
                                <tbody>
                                  <tr *ngFor="let item of [1,2,3,4,5]">
                                    <td class="tbl-user">
                                      <div class="tbl-user-checkbox-srno">
                                        <div class="tbl-user-wrapper">
                                          <div class="tbl-user-image">
                                            <i class="th th-outline-user"></i>
                                          </div>
                                          <div class="tbl-user-text">
                                            <p>Alpeshbhai</p>
                                            <span>alpeshbhai56&#64;gmail.com</span>
                                          </div>
                                        </div>
                                      </div>
                                    </td>
                                    <td>15</td>
                                    <td>12/05/2024</td>
                                    <td>85</td>
                                  </tr>
                                </tbody>
                              </table>
                            </div>
                          </div>
                        </div>

                        <i class="th th-outline-info-circle ms-auto text-danger" ngbTooltip="Other Supplier Price	"
                          placement="bottom" container="body" triggers="hover"></i>
                      </div>
                    </td>
                    <td class="tbl-form-group-borderless">
                      <div class="form-group theme-ngselect">
                        <ng-select placeholder="Order Type" [multiple]="false" [clearable]="false" [items]="demo"
                          bindLabel="name" bindValue="id" [(ngModel)]="selectedDemo1" appendTo="body">
                        </ng-select>
                      </div>
                    </td>
                    <td>5</td>
                    <td>10</td>
                    <td>50</td>
                    <td>100</td>
                    <td>5000</td>
                    <td>18</td>
                    <td>900</td>
                    <td>5900</td>
                    <td>kale mokalshe-gujrat transport ma</td>
                    <td>3 [15]</td>
                    <td>2.1</td>
                    <td class="tbl-form-group-borderless">
                      <div class="form-group theme-ngselect">
                        <ng-select placeholder="Market Type" [multiple]="false" [clearable]="false" [items]="demo"
                          bindLabel="name" bindValue="id" [(ngModel)]="selectedDemo1" appendTo="body">
                        </ng-select>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class='bottombar-wrapper bottom-fixed'>
      <div class='bottombar-container'>
        <div class='bottombar-left'>
          <button type="button" class="btn btn-primary btn-icon-text btn-sm"> <i
              class="th th-outline-tick-circle"></i>Save And Send</button>
          <button type="button" class="btn btn-outline-primary btn-icon-text btn-sm"> <i
              class="th th-outline-document-text"></i>Save as Draft</button>
          <button type="button" class="btn btn-outline-white btn-icon-text btn-sm"><i
              class="th th-outline-close-circle"></i>Cancel</button>
        </div>
        <div class='bottombar-right'>

        </div>
      </div>
    </div>
  </div>
</div>

<!-- ----------------------------------------------------------------------- -->
<!--                      PO Stock Details Modal Start                       -->
<!-- ----------------------------------------------------------------------- -->
<div class="modal modal-theme fade" id="poStockDetailsModal" tabindex="-1" aria-labelledby="poStockDetailsModalLabel"
  aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-xl">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="poStockDetailsModalLabel">PO Stock Details</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
            class='th th-close'></i></button>
      </div>
      <div class="modal-body">
        <div class="table-responsive">
          <table class="table-theme table-hover table table-bordered">
            <thead class="border-less">
              <tr>
                <th>
                  <div class="d-flex align-items-center gap-2">
                    <div class="checkbox checkbox-primary checkbox-small">
                      <input type="checkbox" id="tbl-checkbox" class="material-inputs filled-in" />
                      <label for="tbl-checkbox"></label>
                    </div>
                    Item Details
                  </div>
                </th>
                <th>Pur- Breach</th>
                <th>Stock on Hand</th>
                <th>Deficit</th>
                <th>Location</th>
                <th>Received</th>
                <th>Loaded</th>
                <th>Container No</th>
                <th>Released</th>
                <th>Way to surat</th>
                <th>Rec Surat</th>
                <th>GRN</th>
                <th>Breach Type</th>
              </tr>
            </thead>
            <ng-container *ngFor="let item of [1,2]">
              <tbody>
                <tr>
                  <td rowspan="100" class="tbl-user">
                    <div class="tbl-user-checkbox-srno">
                      <div class="checkbox checkbox-primary checkbox-small">
                        <input type="checkbox" id="tbl-checkbox2" class="material-inputs filled-in" />
                        <label for="tbl-checkbox2"></label>
                      </div>
                      <div class="tbl-user-wrapper">
                        <div class="tbl-user-image">
                          <img src="assets/images/avatar.jpg" alt="valamji">
                        </div>
                        <div class="tbl-user-text-action">
                          <div class="tbl-user-text">
                            <p>Ink Pen</p>
                            <span>SKU #58545854585</span>
                          </div>

                        </div>
                      </div>
                    </div>
                  </td>
                  <td rowspan="100">
                    <span>Level 1 : <b>4500</b></span><br />
                    <span>Level 2: <b>100</b></span><br />
                    <span>Level 3: <b>50</b></span><br />
                  </td>
                  <td rowspan="100">700</td>
                  <td rowspan="100" class="text-danger">-200</td>
                  <td>China</td>
                  <td>3500<br />10/10/2024</td>
                  <td>1000<br />10/10/2024</td>
                  <td>#2024 A</td>
                  <td>1000<br />10/10/2024</td>
                  <td>-</td>
                  <td>-</td>
                  <td>-</td>
                  <td rowspan="100" class="text-primary fw-600">TYPE II</td>
                </tr>
                <tr>
                  <td>China</td>
                  <td>3500<br />10/10/2024</td>
                  <td>1000<br />10/10/2024</td>
                  <td>#2024 A</td>
                  <td>-</td>
                  <td>-</td>
                  <td>-</td>
                  <td>-</td>
                </tr>
              </tbody>
            </ng-container>
          </table>
        </div>
      </div>
      <div class="modal-footer">
        <div class="modal-footer-group full-width-btn justify-content-end">
          <button type="button" class="btn btn-primary btn-icon-text"> <i class="th th-outline-tick-circle"></i>
            Done</button>
          <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                       PO Stock Details Modal End                        -->
<!-- ----------------------------------------------------------------------- -->