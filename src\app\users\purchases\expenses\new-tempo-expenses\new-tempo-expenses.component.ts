import { Component, OnInit, AfterViewInit, ViewChild, inject } from "@angular/core";
import { FormGroup, FormBuilder, Validators, FormArray } from "@angular/forms";
import { ActivatedRoute } from "@angular/router";
import { TempoExpense, TempoExpenseList } from "@modal/TempoExpense";
import { UtilsService } from "@service/utils.service";
import { Serialize, Deserialize } from "cerialize";
import dayjs from "dayjs";
import moment from "moment";
import { DaterangepickerDirective } from "ngx-daterangepicker-material";
declare var window: any;

interface Dropdown {
  expenseTypes: any[];
  tempo: any[];
}

@Component({
  selector: 'app-new-tempo-expenses',
  templateUrl: './new-tempo-expenses.component.html',
  styleUrls: ['./new-tempo-expenses.component.css']
})
export class NewTempoExpensesComponent implements OnInit, AfterViewInit {

  @ViewChild(DaterangepickerDirective, { static: true }) pickerDirectiveModal: DaterangepickerDirective;
  utilsService = inject(UtilsService);

  deleteExpenseTModal: any;

  tempoMaxDate: any;
  dropdown: Dropdown = { expenseTypes: [], tempo: [] }

  expenseGroup: FormGroup;
  tempoExpObj = new TempoExpense();
  expenseId: number;
  selectedExpIndex: number;

  tempoId: number;
  fromTempoRedirect: boolean;
  isOldRecordFound: boolean = false;

  constructor(private fb: FormBuilder, private route: ActivatedRoute) {
    this.expenseId = Number(this.route.snapshot.paramMap.get('id'));

    // From tempo
    this.fromTempoRedirect = false;
    this.tempoId = Number(this.route.snapshot.paramMap.get('tempoId'));
    if(this.tempoId) {
      this.fromTempoRedirect = true;
    }

  }

  ngOnInit() {
    this.initForm();

    // Current date initally
    this.initStartDate();

    this.getRequiredData(null, true);

    this.tempoMaxDate = dayjs().endOf('day').toDate().toISOString();

    this.deleteExpenseTModal = new window.bootstrap.Modal(
      document.getElementById('deleteExpenseTModal')
    );
  }

  ngAfterViewInit(): void {
    //disable containersName
    this.expenseGroup.get('containersName').disable();
    this.expenseGroup.get('containersName').updateValueAndValidity();
  }

  initForm = () => {
    this.expenseGroup = this.fb.group({
      expenseDate: [null, Validators.compose([Validators.required])],
      tempoID: [null, Validators.compose([Validators.required])],
      notes: [null],
      containersName: [null],
      expenseTypeItems: this.fb.array([])
    })
  }

  initStartDate = () => {
    this.tempoExpObj.temp_date = this.tempoExpObj.temp_date ?? {};
    this.tempoExpObj.temp_date.start = moment().toDate();
    this.tempoExpObj.temp_date.end = moment().toDate();
  }

  getRequiredData = (changedTempo: number, firstLoad: boolean) => {

    let API = null;

    if (changedTempo || this.tempoId) {
      const tempoID = changedTempo ? changedTempo : this.tempoId;
      API = `${this.utilsService.serverVariableService.REQ_TEMPO_EXP}?tempoID=${tempoID}`;
    } else {
      API = `${this.utilsService.serverVariableService.REQ_TEMPO_EXP}${this.expenseId ? `?id=${this.expenseId}` : ''}`;
    }

    this.utilsService.getMethodAPI(false, API, null, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.dropdown = response;

        // Redirect from Tempo
        if (this.fromTempoRedirect && firstLoad) {
          this.tempoExpObj.tempoID = this.tempoId;
          this.expenseGroup.get('tempoID').setValue(this.tempoId);
          this.tempoExpObj.containersName = this.dropdown.tempo.find(v => v.id === this.tempoExpObj?.tempoID)?.containersName;
          if (response?.poExpense) {
            this.setGetData(response);
            this.isOldRecordFound = true;
          }
        }

        if (this.expenseId) {
          this.expenseGroup.get('tempoID').disable();
          this.expenseGroup.get('tempoID').updateValueAndValidity();
          this.setGetData(response);
        }

        // On tempo change check if it already has data
        if (changedTempo) {
          if (response.poExpense) {
            this.setGetData(response);
            this.isOldRecordFound = true;
          } else if (this.isOldRecordFound) {
            this.isOldRecordFound = false;
            this.resetFormExpense(changedTempo);
          }
        }

        if (!this.expenseId && !changedTempo) {
          this.addExpenseTypeItem();
        }
      }
    })
  }

  resetFormExpense = (tempoId: number) => {
    this.expenseGroup.reset();
    const fa = this.expenseGroup.get('expenseTypeItems') as FormArray;
    fa.clear();
    this.tempoExpObj = new TempoExpense();
    this.tempoExpObj.tempoID = tempoId;
    this.expenseGroup.get('tempoID').setValue(tempoId);
    this.expenseGroup.get('containersName').setValue(this.dropdown.tempo.find(v => v.id === tempoId)?.containersName);
    this.tempoExpObj.containersName = this.dropdown.tempo.find(v => v.id === tempoId)?.containersName;
    this.addExpenseTypeItem();
    this.initStartDate();
  }

  //on change tempoNo
  onTempoChange = () => {
    this.expenseGroup.get('containersName').setValue(this.dropdown.tempo.find(v => v.id === this.tempoExpObj.tempoID)?.containersName);
    this.getRequiredData(this.expenseGroup.get('tempoID').value, false);
  }

  // documents
  onSelectAttachments = (event) => {

    let selectedFiles: FileList | null = null;
    if (event.type === 'drop') {
      event.preventDefault();
      selectedFiles = event.dataTransfer?.files;
    }

    if (event.type === 'dragover') {
      event.preventDefault();
    }

    if (event.type === 'change') {
      selectedFiles = event.target.files;
    }

    if (event.type === 'paste') {
      const items = (event.clipboardData.items);
      const dataTransfer = new DataTransfer();

      for (const item of items) {
        if (item.type.indexOf('image') === 0) {
          const blob = item.getAsFile();
          const fileName = blob?.name;
          if (blob !== null && fileName) {
            const fileName = `${Date.now()}-image.${item.type.split('/')[1]}`;
            const fileFromBlob = new File([blob], fileName, { type: item.type });
            dataTransfer.items.add(fileFromBlob);
          }
        }
      }
      selectedFiles = dataTransfer.files;
    }

    const max_file_size = 5242880;

    if (selectedFiles) {
      Array.from(selectedFiles).forEach((file: File, i: number) => {
        const ext = file.name.substr(file.name.lastIndexOf('.') + 1).toLowerCase();

        if (['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx', 'jfif', 'webp', 'xls', 'xlsx'].includes(ext)) {
          if (file.size > max_file_size) {
            this.utilsService.toasterService.error(this.utilsService.validationService.ATTACHMENT_MAX_FILE_SIZE + "5 MB");
          } else {
            const fileUrl = URL.createObjectURL(file);
            let fileData = null;
            fileData = {
              id: null,
              file: file,
              originalName: file.name,
              formattedName: fileUrl,
            };

            if (this.tempoExpObj?.docs.length >= 10) {
              this.utilsService.toasterService.error('You have exceeded the file upload limit of 10', '', {
                positionClass: 'toast-top-right',
                closeButton: true,
                timeOut: 10000
              });
              selectedFiles = null;
              return;
            }

            this.tempoExpObj?.docs.push(fileData);
            selectedFiles = null;
          }
        } else {
          this.utilsService.toasterService.error(this.utilsService.validationService.FILE_INVALID_EXTENSION);
        }
      });
    }

  }

  openLink = (link, newUpload: any) => {
    const filePreview = link ? `${this.utilsService.imgPath}${link}` : null

    if (!this.utilsService.isEmptyObjectOrNullUndefined(newUpload)) {
      window.open(newUpload, "_blank");
      return;
    }

    if (!this.utilsService.isEmptyObjectOrNullUndefined(filePreview)) {
      window.open(filePreview, "_blank");
      return;
    }
  }

  removeAttachment(i: number, file) {
    this.tempoExpObj?.docs?.splice(i, 1)
    if (file.id) {
      this.tempoExpObj.deletedDocumentID.push(file.id)
    }
  }

  openDateModal = () => {
    if (!this.pickerDirectiveModal.picker.isShown) {
      this.pickerDirectiveModal.open();
    } else this.pickerDirectiveModal.hide()
  }

  //FormArray

  get expenseTypeItems() {
    return this.expenseGroup.get('expenseTypeItems') as FormArray;
  }

  addExpenseTypeItem = () => {
    this.expenseTypeItems.push(this.fb.group({
      expenseTypeId: [null, Validators.compose([Validators.required])],
      amount: [null, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.ONLY_GREATER_THAN_ZERO)])],
    }));

    const expObj = new TempoExpenseList()
    expObj.expenseDropdown = this.utilsService.transformDropdownItems(this.dropdown.expenseTypes);
    this.tempoExpObj.expenseTypeItems.push(expObj);
  }

  openExpenseDeleteModal = (index: number) => {
    this.selectedExpIndex = index;
    this.deleteExpenseTModal.show();
  }

  onDeleteExpense = () => {
    const selectedExpense = this.tempoExpObj.expenseTypeItems[this.selectedExpIndex];

    if (selectedExpense?.id) {
      this.tempoExpObj.deletedTypeID.push(selectedExpense.id);
    }

    this.expenseTypeItems.removeAt(this.selectedExpIndex);
    this.tempoExpObj.expenseTypeItems.splice(this.selectedExpIndex, 1);

    this.deleteExpenseTModal.hide();
  }

  get totalAmount(): number {
    return this.expenseTypeItems.controls.reduce((acc, group) => {
      const val = parseFloat(group.get('amount')?.value?.toString()) || 0;
      return Number((acc + val).toFixed(2));
    }, 0);
  }

  // Save

  onSave = () => {

    const formData = new FormData();

    if (this.expenseGroup.invalid) {
      this.expenseGroup.markAllAsTouched();
      return;
    }

    if (this.utilsService.isEmptyObjectOrNullUndefined(this.tempoExpObj.temp_date)) {
      return;
    }

    if (!this.utilsService.isEverythingUnique(this.tempoExpObj.expenseTypeItems, 'expenseTypeId')) {
      this.utilsService.toasterService.error('Expense Type should be unique.', '', {
        positionClass: 'toast-top-right',
        closeButton: true,
        timeOut: 10000
      });
      return;
    }

    if (this.tempoExpObj?.docs) {
      this.tempoExpObj.docs.map((v, i) => {
        if (v.file) {
          formData.append('docs', v.file);
        }
      })
    }

    let date = null;
    if (this.tempoExpObj.temp_date) {
      date = dayjs(this.tempoExpObj.temp_date?.start).format('YYYY-MM-DD HH:mm:00');
    }
    this.tempoExpObj.expenseDate = Serialize(date)

    const param = Serialize(this.tempoExpObj) as TempoExpense;
    formData.set('expenseInfo', JSON.stringify(param));

    this.utilsService.postMethodAPI(true, this.utilsService.serverVariableService.TEMPO_EXP_SAVE_DEL, formData, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.onCloseCancel();
      }
    })

  }

  // On Close/Cancel
  onCloseCancel = () => {
    if (this.fromTempoRedirect) {
      this.utilsService.redirectTo(`/users/purchases/po-import/`);
    } else {
      this.utilsService.redirectTo('/users/purchases/expenses/');
    }
  }

  setGetData = (response: any) => {
    this.tempoExpObj = Deserialize(response.poExpense, TempoExpense);
    if (response?.documents) {
      this.tempoExpObj.docs = (response.documents || [])
    }

    this.tempoExpObj.containersName = this.dropdown.tempo.find(v => v.id === this.tempoExpObj?.tempoID)?.containersName;

    if (response?.poExpense?.expenseDate) {
      this.tempoExpObj.temp_date = this.tempoExpObj.temp_date ?? {};
      this.tempoExpObj.temp_date.start = moment(response.poExpense.expenseDate).toDate();
      this.tempoExpObj.temp_date.end = moment(response.poExpense.expenseDate).toDate();
      this.pickerDirectiveModal.picker.setStartDate(this.tempoExpObj.temp_date.start);
      this.pickerDirectiveModal.picker.setEndDate(this.tempoExpObj.temp_date.end);
    }

    if (response.poExpense?.expenseTypeItems) {
      this.tempoExpObj.expenseTypeItems = Deserialize(response.poExpense?.expenseTypeItems, TempoExpenseList);
      const fa = this.expenseGroup.get('expenseTypeItems') as FormArray;
      fa.clear();

      for (let i = 0; i < this.tempoExpObj.expenseTypeItems.length; i++) {
        const item = this.tempoExpObj.expenseTypeItems[i];
        fa.push(this.fb.group({
          expenseTypeId: [item.expenseTypeId, Validators.compose([Validators.required])],
          amount: [item.amount, [Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.ONLY_GREATER_THAN_ZERO)])]],
        }));
        item.expenseDropdown = this.utilsService.filterIsActive(this.dropdown.expenseTypes, item.expenseTypeId);
      }
    }
  }
}
