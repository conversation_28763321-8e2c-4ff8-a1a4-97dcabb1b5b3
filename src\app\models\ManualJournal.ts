import { deserializeAs, serializeAs } from 'cerialize';

export class ManualJournal {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('isActive')
    @deserializeAs('isActive')
    private _isActive: boolean;

    @serializeAs('isSelected')
    @deserializeAs('isSelected')
    private _isSelected: boolean;

    @serializeAs('date')
    @deserializeAs('date')
    private _date: any;

    @deserializeAs('tempDate')
    private _tempDate: any;

    @serializeAs('note')
    @deserializeAs('note')
    private _note: string;

    @serializeAs('journalNumber')
    @deserializeAs('journalNumber')
    private _journalNumber: number;

    @serializeAs('amount')
    @deserializeAs('amount')
    private _amount: number;

    @serializeAs('created')
    @deserializeAs('created')
    private _created: string;

    // @serializeAs('createdDate')
    @deserializeAs('createdDate')
    private _createdDate: string;

    @deserializeAs('lastModifiedDate')
    private _lastModifiedDate: string;

    @serializeAs('accountRequestList')
    @deserializeAs('accountRequestList')
    private _accountRequestList: AccountRequestList[];

    @deserializeAs('saveAmountList')
    private _saveAmountList: AccountRequestList[];

    constructor() {
        this.isActive = false;
        this.isSelected = false;
        this.accountRequestList = [];
    }


    /**
     * Getter lastModifiedDate
     * @return {string}
     */
	public get lastModifiedDate(): string {
		return this._lastModifiedDate;
	}

    /**
     * Setter lastModifiedDate
     * @param {string} value
     */
	public set lastModifiedDate(value: string) {
		this._lastModifiedDate = value;
	}


    /**
     * Getter createdDate
     * @return {string}
     */
	public get createdDate(): string {
		return this._createdDate;
	}

    /**
     * Setter createdDate
     * @param {string} value
     */
	public set createdDate(value: string) {
		this._createdDate = value;
	}


    /**
     * Getter id
     * @return {number}
     */
    public get id(): number {
        return this._id;
    }

    /**
     * Getter isActive
     * @return {boolean}
     */
    public get isActive(): boolean {
        return this._isActive;
    }

    /**
     * Getter isSelected
     * @return {boolean}
     */
    public get isSelected(): boolean {
        return this._isSelected;
    }

    /**
     * Getter date
     * @return {any}
     */
    public get date(): any {
        return this._date;
    }

    /**
     * Getter tempDate
     * @return {any}
     */
    public get tempDate(): any {
        return this._tempDate;
    }

    /**
     * Getter note
     * @return {string}
     */
    public get note(): string {
        return this._note;
    }

    /**
     * Getter journalNumber
     * @return {number}
     */
    public get journalNumber(): number {
        return this._journalNumber;
    }

    /**
     * Getter amount
     * @return {number}
     */
    public get amount(): number {
        return this._amount;
    }

    /**
     * Getter created
     * @return {string}
     */
    public get created(): string {
        return this._created;
    }

    /**
     * Getter accountRequestList
     * @return {AccountRequestList[]}
     */
    public get accountRequestList(): AccountRequestList[] {
        return this._accountRequestList;
    }

    /**
     * Setter id
     * @param {number} value
     */
    public set id(value: number) {
        this._id = value;
    }

    /**
     * Setter isActive
     * @param {boolean} value
     */
    public set isActive(value: boolean) {
        this._isActive = value;
    }

    /**
     * Setter isSelected
     * @param {boolean} value
     */
    public set isSelected(value: boolean) {
        this._isSelected = value;
    }

    /**
     * Setter date
     * @param {any} value
     */
    public set date(value: any) {
        this._date = value;
    }

    /**
     * Setter tempDate
     * @param {any} value
     */
    public set tempDate(value: any) {
        this._tempDate = value;
    }

    /**
     * Setter note
     * @param {string} value
     */
    public set note(value: string) {
        this._note = value;
    }

    /**
     * Setter journalNumber
     * @param {number} value
     */
    public set journalNumber(value: number) {
        this._journalNumber = value;
    }

    /**
     * Setter amount
     * @param {number} value
     */
    public set amount(value: number) {
        this._amount = value;
    }

    /**
     * Setter created
     * @param {string} value
     */
    public set created(value: string) {
        this._created = value;
    }

    /**
     * Setter accountRequestList
     * @param {AccountRequestList[]} value
     */
    public set accountRequestList(value: AccountRequestList[]) {
        this._accountRequestList = value;
    }


    /**
     * Getter saveAmountList
     * @return {AccountRequestList[]}
     */
	public get saveAmountList(): AccountRequestList[] {
		return this._saveAmountList;
	}

    /**
     * Setter saveAmountList
     * @param {AccountRequestList[]} value
     */
	public set saveAmountList(value: AccountRequestList[]) {
		this._saveAmountList = value;
	}



}

export class AccountRequestList {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('credit')
    @deserializeAs('credit')
    private _credit: number;

    @serializeAs('debit')
    @deserializeAs('debit')
    private _debit: number;

    @serializeAs('accountId')
    @deserializeAs('accountId')
    private _accountId: number;

    @serializeAs('updateAmountId')
    @deserializeAs('updateAmountId')
    private _updateAmountId: number;

    @serializeAs('deleteAmountId')
    @deserializeAs('deleteAmountId')
    private _deleteAmountId: number;

    constructor() {

    }


    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}


    /**
     * Getter credit
     * @return {number}
     */
    public get credit(): number {
        return this._credit;
    }

    /**
     * Setter credit
     * @param {number} value
     */
    public set credit(value: number) {
        this._credit = value;
    }

    /**
     * Getter debit
     * @return {number}
     */
    public get debit(): number {
        return this._debit;
    }

    /**
     * Setter debit
     * @param {number} value
     */
    public set debit(value: number) {
        this._debit = value;
    }

    /**
     * Getter accountId
     * @return {number}
     */
    public get accountId(): number {
        return this._accountId;
    }

    /**
     * Setter accountId
     * @param {number} value
     */
    public set accountId(value: number) {
        this._accountId = value;
    }

    /**
     * Getter updateAmountId
     * @return {number}
     */
    public get updateAmountId(): number {
        return this._updateAmountId;
    }

    /**
     * Setter updateAmountId
     * @param {number} value
     */
    public set updateAmountId(value: number) {
        this._updateAmountId = value;
    }

    /**
     * Getter deleteAmountId
     * @return {number}
     */
    public get deleteAmountId(): number {
        return this._deleteAmountId;
    }

    /**
     * Setter deleteAmountId
     * @param {number} value
     */
    public set deleteAmountId(value: number) {
        this._deleteAmountId = value;
    }


}