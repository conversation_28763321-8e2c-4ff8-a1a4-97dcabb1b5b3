<div class="app-menu navbar-menu">
  <!-- LOGO -->
  <div class="navbar-brand-box">
    <a href="index.html" class="logo logo-dark">

      <span class="logo-sm">
        <img src="assets/images/logo-sm.svg" alt="sidebarLogoLg" height="48" />
      </span>
      <span class="logo-lg">
        <img src="assets/images/logo.svg" alt="sidebarLogoLg" height="48" />
      </span>
    </a>

  </div>
  <div id="scrollbar" class="navbar-scrollbar h-100">

    <ngx-simplebar [options]="options" style="height: 100%; max-height: calc(100vh - 160px)">


      <div class="navbar-sidemenu ">
        <ul class="navbar-nav" id="navbar-nav">
          <li class="nav-item">
            <a class="nav-link menu-link " routerLinkActive="active" [routerLink]="['/users/dashboard']">
              <i class="th-outline-home-1"></i> <span data-key="t-widgets">Dashboard</span>
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link menu-link collapsed" href="#InventoryMenu" data-bs-toggle="collapse" role="button"
              aria-expanded="false" aria-controls="InventoryMenu" data-bs-display="static" data-bs-container="body">
              <i class="th-outline-buildings"></i> <span data-key="t-widgets">Inventory</span>
            </a>
            <div class="collapse menu-dropdown" id="InventoryMenu">
              <ul class="nav nav-sm flex-column">
                <li class="nav-item"
                  [pageAccess]="{page: utilsService.enumForPage.ITEM, action: utilsService.enumForPage.VIEW_ITEM}">
                  <a class="nav-link" routerLinkActive="active" [routerLink]="['/users/inventory/items']"> Items </a>
                </li>
                <li class="nav-item"
                  [pageAccess]="{page: utilsService.enumForPage.ITEM_GROUP, action: utilsService.enumForPage.VIEW_ITEM_GRP}">
                  <a class="nav-link" routerLinkActive="active" [routerLink]="['/users/inventory/item-group']">Item
                    Groups</a>
                </li>
                <li class="nav-item">
                  <a class="nav-link" routerLinkActive="active" [routerLink]="['/users/inventory/group-code']">Group Code</a>
                </li>
                <li class="nav-item" *ngIf="true">
                  <a class="nav-link" routerLinkActive="active" [routerLink]="['/users/inventory/catalogs']"> Catalogs
                  </a>
                </li>
                <li class="nav-item" *ngIf="true">
                  <a class="nav-link" routerLinkActive="active"
                    [routerLink]="['/users/inventory/average-price']">Average Price</a>
                </li>
                <li class="nav-item"
                  [pageAccess]="{page: utilsService.enumForPage.CATEGORY, action: utilsService.enumForPage.VIEW_CATEGORY}">
                  <a class="nav-link" routerLinkActive="active"
                    [routerLink]="['/users/inventory/category']">Category</a>
                </li>
                <li class="nav-item" *ngIf="true">
                  <a to="" class="nav-link" href="#InventoryManagementMenu" data-bs-toggle="collapse" role="button"
                    aria-expanded="false" aria-controls="InventoryManagementMenu">Inventory Mgmt.</a>

                  <div class="collapse menu-dropdown" id="InventoryManagementMenu">

                    <ul class="nav nav-sm flex-column">
                      <li class="nav-item">
                        <a class="nav-link" routerLinkActive="active"
                          [routerLink]="['/users/inventory/stock-branch-transfer']">Stock Branch Transfer</a>
                      </li>
                      <li class="nav-item">
                        <a class="nav-link" routerLinkActive="active"
                          [routerLink]="['/users/inventory/stock-warehouse-transfer']">Stock Warehouse Transfer</a>
                      </li>
                      <li class="nav-item">
                        <a class="nav-link" routerLinkActive="active"
                          [routerLink]="['/users/inventory/stock-recalculation']">Stock Recalculation</a>
                      </li>
                      <li class="nav-item">
                        <a class="nav-link" routerLinkActive="active"
                          [routerLink]="['/users/inventory/updated-sale-price']">Updated Sale Price</a>
                      </li>
                      <li class="nav-item">
                        <a class="nav-link" routerLinkActive="active"
                          [routerLink]="['/users/inventory/mismatch']">Mismatch</a>
                      </li>
                    </ul>
                  </div>
                </li>
                <li class="nav-item" *ngIf="true">
                  <a class="nav-link" routerLinkActive="active" [routerLink]="['/users/inventory/lost-found']"> Lost &
                    Found </a>
                </li>
                <li class="nav-item" *ngIf="true">
                  <a class="nav-link" routerLinkActive="active" [routerLink]="['/users/inventory/print-qr-code']"> Print
                    QR Code </a>
                </li>
              </ul>
            </div>
          </li>
          <li class="nav-item"
            [pageAccess]="{page: utilsService.enumForPage.REG, action: utilsService.enumForPage.VIEW_REG}">
            <a class="nav-link menu-link " routerLinkActive="active" [routerLink]="['/users/registration/']">
              <i class="th-outline-profile-2user"></i> <span data-key="t-widgets">Registration</span>
            </a>
          </li>
          <li class="nav-item" *ngIf="true">
            <a class="nav-link menu-link collapsed" href="#SalesMenu" data-bs-toggle="collapse" role="button"
              aria-expanded="false" aria-controls="SalesMenu" data-bs-display="static" data-bs-container="body">
              <i class="th-outline-shopping-cart"></i> <span data-key="t-widgets">Sales</span>
            </a>
            <div class="collapse menu-dropdown" id="SalesMenu">
              <ul class="nav nav-sm flex-column">
                <li class="nav-item"><a class="nav-link" routerLinkActive="active"
                    [routerLink]="['/users/sales/sales-expenses']">Expenses</a></li>
                <li class="nav-item"><a class="nav-link" routerLinkActive="active"
                    [routerLink]="['/users/sales/sales-orders']">Sales Orders</a></li>
                <li class="nav-item"><a class="nav-link" routerLinkActive="active"
                    [routerLink]="['/users/sales/sales-bills']">Sales Bills</a></li>
                <li class="nav-item"><a class="nav-link" routerLinkActive="active"
                    [routerLink]="['/users/sales/payment-received']">Payment Received</a></li>
                <li class="nav-item"><a class="nav-link" routerLinkActive="active"
                    [routerLink]="['/users/sales/debtors']">Debtors</a></li>
                <li class="nav-item"><a class="nav-link" routerLinkActive="active"
                    [routerLink]="['/users/sales/sales-returns']">Sales Returns</a></li>
                <li class="nav-item"><a class="nav-link" routerLinkActive="active"
                    [routerLink]="['/users/sales/debit-note']">Debit Note</a></li>
              </ul>
            </div>
          </li>
          <li class="nav-item" *ngIf="true">
            <a class="nav-link menu-link collapsed" href="#PurchaseMenu" data-bs-toggle="collapse" role="button"
              aria-expanded="false" aria-controls="PurchaseMenu" data-bs-display="static" data-bs-container="body">
              <i class="th-outline-bag-2"></i> <span data-key="t-widgets">Purchases</span>
            </a>
            <div class="collapse menu-dropdown" id="PurchaseMenu">
              <ul class="nav nav-sm flex-column">
                <li class="nav-item">
                  <a class="nav-link" routerLinkActive="active"
                    [routerLink]="['/users/purchases/expenses']">Expenses</a>
                </li>
                <li class="nav-item"
                  [pageAccess]="{page: utilsService.enumForPage.PO, action: utilsService.enumForPage.VIEW_PO}">
                  <a class="nav-link" routerLinkActive="active" [routerLink]="['/users/purchases/po-import']">PO -
                    Import</a>
                </li>
                <li class="nav-item">
                  <a class="nav-link" routerLinkActive="active" [routerLink]="['/users/purchases/po-local']">PO -
                    Local</a>
                </li>
                <li class="nav-item"
                  [pageAccess]="{page: utilsService.enumForPage.SETTINGS, action: utilsService.enumForPage.CARTOM_MAPPING}">
                  <a class="nav-link" routerLinkActive="active" [routerLink]="['/users/purchases/carton-mapping']">Carton
                    Mapping</a>
                </li>
                <li class="nav-item">
                  <a class="nav-link" routerLinkActive="active" [routerLink]="['/users/purchases/payments']">Bill
                    Payment</a>
                </li>
                <li class="nav-item">
                  <a class="nav-link" routerLinkActive="active" [routerLink]="['/users/purchases/gst-invoices']">GST
                    Invoices</a>
                </li>
                <li class="nav-item">
                  <a class="nav-link" routerLinkActive="active" [routerLink]="['/users/purchases/payment-made']">Payment
                    Made</a>
                </li>
                <li class="nav-item">
                  <a class="nav-link" routerLinkActive="active"
                    [routerLink]="['/users/purchases/purchase-returns']">Purchase Returns</a>
                </li>
                <li class="nav-item">
                  <a class="nav-link" routerLinkActive="active" [routerLink]="['/users/purchases/creditors']">Creditor
                    Payment</a>
                </li>
                <li class="nav-item">
                  <a class="nav-link" routerLinkActive="active" [routerLink]="['/users/purchases/credit-note']">Credit
                    Note</a>
                </li>
              </ul>
            </div>
          </li>

          <li class="nav-item" *ngIf="true">
            <a class="nav-link menu-link " routerLinkActive="active" [routerLink]="['/users/inquiry']">
              <i class="th-outline-box-search"></i> <span data-key="t-widgets">Inquiry</span>
            </a>
          </li>
          <li class="nav-item"
            [pageAccess]="{page: utilsService.enumForPage.WAREHOUSE, action: utilsService.enumForPage.VIEW_WAREHOUSE}">
            <a class="nav-link menu-link " routerLinkActive="active" [routerLink]="['/users/warehouse-management/']">
              <i class="th-outline-home-2"></i> <span data-key="t-widgets">Warehouse</span>
            </a>
          </li>
          <li class="nav-item"
            [pageAccess]="{page: utilsService.enumForPage.BRANCH, action: utilsService.enumForPage.VIEW_BRANCH}">
            <a class="nav-link menu-link  " routerLinkActive="active" [routerLink]="['/users/branch-management/']">
              <i class="th-outline-buildings-2"></i> <span data-key="t-widgets">Branch Management </span>
            </a>
          </li>

          <li class="nav-item"
            *ngIf="utilsService.checkPageAccess([utilsService.enumForPage.VIEW_USER, utilsService.enumForPage.VIEW_ROLE])">
            <a class="nav-link menu-link collapsed" href="#UserManagementMenu" data-bs-toggle="collapse" role="button"
              aria-expanded="false" aria-controls="MasterMenu" data-bs-display="static" data-bs-container="body">
              <i class="th-outline-user"></i> <span data-key="t-widgets">User Management </span>
            </a>
          
            <div class="collapse menu-dropdown" id="UserManagementMenu">
              <ul class="nav nav-sm flex-column">
                <li class="nav-item"
                  [pageAccess]="{page: utilsService.enumForPage.USER, action: utilsService.enumForPage.VIEW_USER}">
                  <a class="nav-link" routerLinkActive="active" [routerLink]="['/users/user-management/users']"> User
                  </a>
                </li>
                <li class="nav-item"
                  [pageAccess]="{page: utilsService.enumForPage.ROLES, action: utilsService.enumForPage.VIEW_ROLE}">
                  <a class="nav-link" routerLinkActive="active" [routerLink]="['/users/user-management/roles']"> Roles
                  </a>
                </li>
              </ul>
            </div>
          </li>

          <li class="nav-item" *ngIf="true">
            <a class="nav-link menu-link " to="/page-component">
              <i class="th-outline-chart"></i> <span data-key="t-widgets">Reports </span>
            </a>
          </li>
          <li class="nav-item" *ngIf="true">
            <a class="nav-link menu-link " routerLinkActive="active" to="/page-component"
              [routerLink]="['/users/audit-tickets/list']">
              <i class="th th-outline-ticket-2"></i> <span data-key="t-widgets">Audit Tickets </span>
            </a>
          </li>
          <li class="nav-item"
            [pageAccess]="{page: utilsService.enumForPage.MASTER, action: utilsService.enumForPage.VIEW_MASTER}">
            <a class="nav-link menu-link collapsed" href="#MasterMenu" data-bs-toggle="collapse" role="button"
              aria-expanded="false" aria-controls="MasterMenu" data-bs-display="static" data-bs-container="body">
              <i class="th-outline-data"></i><span data-key="t-widgets">Masters</span>
            </a>
            <div class="collapse menu-dropdown" id="MasterMenu">
              <ul class="nav nav-sm flex-column">
                <li class="nav-item" *ngFor="let item of navItems">
                  <a class="nav-link" routerLinkActive="active" [routerLink]="item.url">
                    {{ item.name }}
                  </a>
                </li>
              </ul>
            </div>
          </li>
          <li class="nav-item"
            [pageAccess]="{page: utilsService.enumForPage.ACCOUNTS, action: utilsService.enumForPage.VIEW_ACCOUNTS}">
            <a class="nav-link menu-link collapsed" href="#AccountMasterMenu" data-bs-toggle="collapse" role="button"
              aria-expanded="false" aria-controls="AccountMasterMenu">
              <i class="th-outline-bank"></i><span data-key="t-widgets">Account Master</span>
            </a>
            <div class="collapse menu-dropdown" id="AccountMasterMenu">
              <ul class="nav nav-sm flex-column">
                <li class="nav-item">
                  <a class="nav-link" routerLinkActive="active"
                    [routerLink]="['/users/accounts/chart-of-account']">Chart of Account</a>
                </li>
                <li class="nav-item">
                  <a class="nav-link" routerLinkActive="active" [routerLink]="['/users/accounts/account-type']">Account
                    Type</a>
                </li>
                <li class="nav-item">
                  <a class="nav-link" routerLinkActive="active" [routerLink]="['/users/accounts/manual-journal']">Manual
                    Journal</a>
                </li>
                <li class="nav-item" *ngIf="true">
                  <a class="nav-link" routerLinkActive="active"
                    [routerLink]="['/users/accounts/general-ledger']">General Ledger</a>
                </li>
                <li class="nav-item" *ngIf="true">
                  <a class="nav-link" routerLinkActive="active"
                    [routerLink]="['/users/accounts/account-reconciliation']">Account Reconciliation</a>
                </li>
              </ul>
            </div>
          </li>
          <li class="nav-item"
            [pageAccess]="{page: utilsService.enumForPage.SETTINGS, action: utilsService.enumForPage.VIEW_SETTING}">
            <a class="nav-link menu-link" routerLinkActive="active" [routerLink]="['/users/settings']">
              <i class="th-outline-setting-2"></i> <span data-key="t-widgets">Settings </span>
            </a>
          </li>

        </ul>
      </div>
    </ngx-simplebar>
    <div class="navbar-bottom navbar-sidemenu ">


      <div class="dropdown dropdown-branch">
        <div (click)="openSideBranchDropdown()" class="side-user" type="button" data-bs-toggle="dropdown"
          aria-expanded="false" data-bs-auto-close="outside" data-bs-popper-config='{"strategy":"fixed"}'>
          <div class="side-user-logo">
            <img src="assets/images/logo-sm.svg" alt="valamji">
          </div>
          <div class="side-user-text">
            <div class="side-user-text-wrapper">
              <h6 [title]="utilsService.defaultBranch?.branchName ? utilsService.defaultBranch?.branchName : ''">
                {{utilsService.defaultBranch?.branchName ? utilsService.defaultBranch?.branchName : '-'}}</h6>
              <p [title]="utilsService.defaultBranch?.branchEmail ? utilsService.defaultBranch?.branchEmail : ''">
                {{utilsService.defaultBranch?.branchEmail ? utilsService.defaultBranch?.branchEmail : '-'}}</p>
            </div>
            <i class="th th-outline-arrow-right-3"></i>
          </div>
        </div>

        <div class="dropdown-menu">
          <div class="card-dropdown-branch">
            <div class="card-header">
              <div class="form-group form-group-sm mb-0 w-100">
                <div class="form-group-icon-start"><i class="th th-outline-search-normal-1 icon-broder"></i>
                  <input (input)="customSearchFn()" type="text" class="form-control" placeholder="Search"
                    [(ngModel)]="searchBranch">
                </div>
              </div>
            </div>
            <div class="card-body">
              <div class="branch-details-group" *ngIf="utilsService.defaultBranch">
                <div class="branch-details-list">
                  <h6 class="branch-title">Active Branch</h6>
                  <div class="branch-item">
                    <div class="branch-info">
                      <div class="branch-icon">
                        <img src="assets/images/logo-sm.svg" alt="valamji">
                      </div>
                      <div class="branch-content">
                        <div class="d-flex align-items-center">
                          <h5
                            [title]="utilsService.defaultBranch?.branchName ? utilsService.defaultBranch?.branchName : ''">
                            {{utilsService.defaultBranch?.branchName}}</h5>
                            <div ngbTooltip="Main Branch" placement="right" container="body" *ngIf="utilsService.defaultBranch?.isMainBranch"><i class="th th-bold-star ms-2 text-warning"></i></div>
                        </div>
                        <p
                          [title]="utilsService.defaultBranch?.branchEmail ? utilsService.defaultBranch?.branchEmail : ''">
                          {{utilsService.defaultBranch?.branchEmail}}</p>
                      </div>
                    </div>
                    <div class="branch-tag">
                      <div class="badge badge-primary-light">Default</div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="branch-details-group" *ngIf="checkIfOtherBranchPresent()">
                <div class="branch-details-list">
                  <h6 class="branch-title">Other Branch</h6>
                  <div class="branch-item cursor-pointer"
                    *ngFor="let item of utilsService.userBranchList | branchFilter: false; index as j"
                    (click)="onSaveUserBranch(item.id)">
                    <div class="branch-info">
                      <div class="branch-icon">
                        <img src="assets/images/logo-sm.svg" alt="valamji">
                      </div>
                      <div class="branch-content">
                        <div class="d-flex align-items-center">
                          <h5 [title]="item.branchName ? item.branchName : null">{{item.branchName}}</h5>
                          <div ngbTooltip="Main Branch" placement="right" container="body" *ngIf="item.isMainBranch"><i class="th th-bold-star ms-2 text-warning"></i></div>
                        </div>
                        <p [title]="item.branchEmail ? item.branchEmail : null">{{item.branchEmail}}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="card-footer">
              <div class="button-group card-button-group">
                <button (click)="utilsService.redirectTo('/users/branch-management/new-branch')"
                  [pageAccess]="{page: utilsService.enumForPage.BRANCH, action: utilsService.enumForPage.ADD_BRANCH}"
                  type="button" class="btn btn-sm btn-light-primary btn-icon-text">
                  <i class="th th-outline-add-circle"></i>
                  New Branch
                </button>
                <button (click)="utilsService.redirectTo('/users/branch-management/')"
                  [pageAccess]="{page: utilsService.enumForPage.BRANCH, action: utilsService.enumForPage.VIEW_BRANCH}"
                  type="button" class="btn btn-sm btn-outline-white btn-icon-text">
                  <i class="th th-outline-setting-2"></i>
                  Manage Branches
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>


      <div class="sidebar-footer">
        <p>{{utilsService.version}}</p>
      </div>
    </div>

  </div>
  <div class="sidebar-background"></div>
</div>