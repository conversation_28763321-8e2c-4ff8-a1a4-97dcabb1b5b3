<div class="product-modal-slider-wrapper">
  <ngx-slick-carousel class="carousel product-modal-slider custom-slick-arrow " #galleryOne="slick-carousel"
    (afterChange)="onChange($event)" [config]="ProductModalImageSlider">
    <div ngxSlickItem class="product-modal-item" *ngFor="let item of itemImagesList; index as i">
      <ng-container *ngIf="item?.originalName">
        <div class="product-image" *ngIf="utilsService.isImage(item.formattedName ? item.formattedName : item.originalName)">
          <img loading="lazy"
            [src]="item.formattedName ? (utilsService.imgPath + item.formattedName) : 'assets/images/avatar-default.svg'"
            alt="valamji">
        </div>
        <div class="product-image" *ngIf="utilsService.isMedia(item.originalName)">
          <video class="" controls [autoplay]="false">
            <source loading="lazy" [src]="utilsService.imgPath + item.formattedName" type="video/mp4">
          </video>
        </div>
      </ng-container>
    </div>
  </ngx-slick-carousel>
  <div class="product-modal-counter">
    <span class="current-slide">{{selectedImageIndex}}</span> / <span
      class="total-slides">{{itemImagesList?.length}}</span>
  </div>
</div>