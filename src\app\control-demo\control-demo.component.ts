import { Component, ElementRef } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import moment from 'moment'

@Component({
  selector: 'app-control-demo',
  templateUrl: './control-demo.component.html',
  styleUrls: ['./control-demo.component.scss'],
})
export class ControlDemoComponent {

  // Month-Day Picker demo properties
  monthPickerValue: string = '';
  dayMonthPickerValue: string = '';
  monthRangeValue: string = '';

  // Date-Time Picker demo properties
  dateOnlyValue: string = '';
  dateTime12Value: string = '';
  dateTime24Value: string = '';
  dateTimeRightValue: string = '';
  timeOnlyValue: string = '';
  timeOnly24Value: string = '';
  fiveMinuteValue: string = '';
  tenMinuteValue: string = '';
  fifteenMinuteValue: string = '';
  isoFormatValue: string = '';
  usFormatValue: string = '';
  dbFormatValue: string = '';
  zeroPaddedValue: string = '';
  momentValue: moment.Moment | null = null;

  // YYYY-MM-DD Display Format examples
  isoDateOnlyValue: string = '';
  isoDateTimeValue: string = '';

  // Min/Max Date Range examples
  minMaxDateValue: string = '';
  minMaxDateTimeValue: string = '';
  pastOnlyValue: string = '';
  futureOnlyValue: string = '';

  // Time-aware validation examples
  timeAwareDateTimeValue: string = '';
  todayTimeConstraintValue: string = '';

  // Reactive Forms demo
  demoForm = new FormGroup({
    monthOnly: new FormControl(''),
    dayMonth: new FormControl(''),
    disabledPicker: new FormControl({ value: '', disabled: true })
  });

  // Date-Time Picker Reactive Forms demo
  dateTimeForm = new FormGroup({
    dateOnly: new FormControl('', [Validators.required]),
    dateTime12: new FormControl(''),
    dateTime24: new FormControl('')
  });

  // Disabled dates example (Christmas, New Year, etc.) - DD-MM format
  // disabledDates: string[] = ['25-12', '01-01', '04-07'];
    disabledDates: string[] = [];

  // Min/Max date examples (for month pickers)
  minDate: string = '03'; // March minimum
  maxDate: string = '10'; // October maximum
  minDayMonth: string = '15-06'; // June 15th minimum (DD-MM)
  maxDayMonth: string = '30-09'; // September 30th maximum (DD-MM)

  // Min/Max date constraints for date pickers (DD-MM-YYYY format)
  dateRangeMinDate: string = moment().subtract(30, 'days').format('DD-MM-YYYY'); // 30 days ago
  dateRangeMaxDate: string = moment().add(30, 'days').format('DD-MM-YYYY'); // 30 days from now
  pastOnlyMaxDate: string = moment().format('DD-MM-YYYY'); // Today as max (past only)
  futureOnlyMinDate: string = moment().format('DD-MM-YYYY'); // Today as min (future only)

  // Time-aware min/max date constraints for date-time pickers (DD-MM-YYYY HH:mm format)
  dateTimeRangeMinDate: string = moment().subtract(2, 'days').format('DD-MM-YYYY') + ' 14:00'; // 2 days ago at 2:00 PM
  dateTimeRangeMaxDate: string = moment().add(2, 'days').format('DD-MM-YYYY') + ' 16:00'; // 2 days from now at 4:00 PM
  todayTimeMinDate: string = moment().format('DD-MM-YYYY') + ' ' + moment().format('HH:mm'); // Today at current time
  todayTimeMaxDate: string = moment().format('DD-MM-YYYY') + ' 18:00'; // Today at 6:00 PM

  cars = [
    { id: 1, name: 'Volvo' },
    { id: 2, name: 'Saab' },
    { id: 3, name: 'Opel' },
    { id: 4, name: 'Audi' },
  ];

  sku = [
    { id: 1, name: 'VO15251' },
    { id: 2, name: 'VO15252' },
    { id: 3, name: 'VO15253' },
    { id: 4, name: 'VO15254' },
  ];
  level = [
    { id: 1, name: 'level 1 : 200' },
    { id: 2, name: 'level 2 : 700' },
    { id: 3, name: 'level 3 : 600' },
    { id: 4, name: 'level 4 : 500' },
  ];
  type = [
    { id: 1, name: 'Online' },
    { id: 2, name: 'Offline' },
  ];

  demo2 = [
    { id: 1, name: 'Export' },
    { id: 2, name: 'Another action' },
    { id: 3, name: 'Something' },
    { id: 4, name: 'Something' },
    { id: 5, name: 'Something' },
    { id: 6, name: 'Something' },
    { id: 7, name: 'Something' },
    { id: 8, name: 'Something' },
    { id: 9, name: 'Something' },
    { id: 10, name: 'Something' },
    { id: 11, name: 'Something' },
    { id: 12, name: 'Something' },
    { id: 13, name: 'Something' },
  ];

  Phone = [
    { id: 1, name: '+91' },
    { id: 2, name: '+96' },
    { id: 3, name: '+93' },
  ];

  selectedMultiple: number[] = [1, 2]

  selectedSku: number = 1;
  selectedCar: number = 1;
  selectedLevel: number = 1;
  selectedType: number = 1;
  selectedDemo2: number = 1;
  selectedPhone: number = 1;

  isExpanded: boolean = true;

  menuItems: any[] = [
    { label: 'Category 1', link: '#' },
    { label: 'Category 2', link: '#' },
    { label: 'Category 3', link: '#' },
    {
      label: 'Category 4',
      subMenu: [
        { label: 'SubCategory 1', link: '#' },
        {
          label: 'Category 4 Nested',
          subMenu: [
            { label: 'SubCategory 1', link: '#' },
            { label: 'SubCategory 2', link: '#' },
            {
              label: 'Category 4 Deep Nested',
              subMenu: [
                { label: 'SubCategory 1', link: '#' },
                {
                  label: 'Category 5',
                  subMenu: [
                    { label: 'SubCategory 1', link: '#' },
                    { label: 'SubCategory 2', link: '#' },
                    { label: 'SubCategory 3', link: '#' },
                  ],
                },
                { label: 'SubCategory 2', link: '#' },
                { label: 'SubCategory 3', link: '#' },
              ],
            },
          ],
        },
        { label: 'SubCategory 3', link: '#' },
        { label: 'SubCategory 4', link: '#' },
      ],
    },
    { label: 'Category 5', link: '#' },
    { label: 'Category 6', link: '#' },
  ];


  toggleExpand(): void {
    this.isExpanded = !this.isExpanded;
  }

  constructor(private elRef: ElementRef) { }
  ngAfterViewInit(): void {
    const dropdownSubmenus: NodeListOf<HTMLElement> = this.elRef.nativeElement.querySelectorAll('.dropdown-submenu');
    const dropdownMenus: NodeListOf<HTMLElement> = this.elRef.nativeElement.querySelectorAll('.dropdown');

    // dropdownMenus.forEach((submenu: HTMLElement) => {
    //   submenu.addEventListener('mouseenter', () => {
    //     const dropdownMenu = submenu.querySelector('.dropdown-menu') as HTMLElement;
    //     if (dropdownMenu) {
    //       dropdownMenu.classList.add('active');
    //     }
    //   });

    //   submenu.addEventListener('mouseleave', () => {
    //     const dropdownMenu = submenu.querySelector('.dropdown-menu') as HTMLElement;
    //     if (dropdownMenu) {
    //       dropdownMenu.classList.remove('show');
    //     }
    //   });
    // });

    // dropdownSubmenus.forEach((submenu: HTMLElement) => {
    //   submenu.addEventListener('mouseenter', () => {
    //     const dropdownMenu = submenu.querySelector('.dropdown-menu') as HTMLElement;
    //     if (dropdownMenu) {
    //       dropdownMenu.classList.add('show');
    //     }
    //   });

    //   submenu.addEventListener('mouseleave', () => {
    //     const dropdownMenu = submenu.querySelector('.dropdown-menu') as HTMLElement;
    //     if (dropdownMenu) {
    //       dropdownMenu.classList.remove('show');
    //     }
    //   });
    // });
  }



  // Month-Day Picker event handlers
  onMonthPickerChange(value: string | moment.Moment): void {
    console.log('Month picker value changed:', value);
    this.monthPickerValue = typeof value === 'string' ? value : value.format('MM');
  }

  onDayMonthPickerChange(value: string | moment.Moment): void {
    console.log('Day-Month picker value changed:', value);
    this.dayMonthPickerValue = typeof value === 'string' ? value : value.format('MM-DD');
  }

  onMonthRangeChange(value: string | moment.Moment): void {
    console.log('Month range picker value changed:', value);
    this.monthRangeValue = typeof value === 'string' ? value : 'Range object';
  }

  // Callback event handlers for demo
  onPickerOpen(): void {
    console.log('📅 Date picker opened');
  }

  onPickerClose(): void {
    console.log('❌ Date picker closed');
  }

  onMonthChange(month: number): void {
    console.log('📆 Month changed to:', month);
  }

  onDateSelect(selection: {month: number, day?: number}): void {
    console.log('✅ Date selected:', selection);
  }

  onValueChange(value: string | moment.Moment): void {
    console.log('🔄 Value changed:', value);
  }

  // Reset form values
  resetForm(): void {
    this.demoForm.reset();
    this.monthPickerValue = '';
    this.dayMonthPickerValue = '';
  }

  // Set sample values
  setSampleValues(): void {
    this.demoForm.patchValue({
      // Use 3-letter month abbreviation in uppercase (JAN, FEB, etc.)
      monthOnly: moment().add(1, 'month').format('MMM').toUpperCase(),
      dayMonth: moment().add(1, 'month').format('DD-MM'),
      disabledPicker: moment().add(2, 'month').format('DD-MM').toUpperCase(),
    });
  }

  // Date-Time Picker event handlers
  onDateOnlyChange(value: string | moment.Moment): void {
    console.log('Date only picker value changed:', value);
    this.dateOnlyValue = typeof value === 'string' ? value : value.format('DD/MM/YYYY');
  }

  onDateTime12Change(value: string | moment.Moment): void {
    console.log('Date-Time 12h picker value changed:', value);
    this.dateTime12Value = typeof value === 'string' ? value : value.format('DD/MM/YYYY hh:mm A');
  }

  onDateTime24Change(value: string | moment.Moment): void {
    console.log('Date-Time 24h picker value changed:', value);
    this.dateTime24Value = typeof value === 'string' ? value : value.format('DD/MM/YYYY HH:mm');
  }

  onDateTimeRightChange(value: string | moment.Moment): void {
    console.log('Date-Time right layout picker value changed:', value);
    this.dateTimeRightValue = typeof value === 'string' ? value : value.format('DD/MM/YYYY HH:mm');
  }

  onTimeOnlyChange(value: string | moment.Moment): void {
    console.log('Time only picker value changed:', value);
    this.timeOnlyValue = typeof value === 'string' ? value : value.format('hh:mm A');
  }

  onTimeOnly24Change(value: string | moment.Moment): void {
    console.log('Time only 24h picker value changed:', value);
    this.timeOnly24Value = typeof value === 'string' ? value : value.format('HH:mm');
  }

  onFiveMinuteChange(value: string | moment.Moment): void {
    console.log('5-minute step picker value changed:', value);
    this.fiveMinuteValue = typeof value === 'string' ? value : value.format('DD/MM/YYYY hh:mm A');
  }

  onTenMinuteChange(value: string | moment.Moment): void {
    console.log('10-minute step picker value changed:', value);
    this.tenMinuteValue = typeof value === 'string' ? value : value.format('DD/MM/YYYY HH:mm');
  }

  onFifteenMinuteChange(value: string | moment.Moment): void {
    console.log('15-minute step picker value changed:', value);
    this.fifteenMinuteValue = typeof value === 'string' ? value : value.format('DD/MM/YYYY hh:mm A');
  }

  onMomentChange(value: string | moment.Moment): void {
    console.log('Moment picker value changed:', value);
    this.momentValue = moment.isMoment(value) ? value : moment(value);
  }

  onIsoFormatChange(value: string | moment.Moment): void {
    console.log('ISO format picker value changed:', value);
    this.isoFormatValue = typeof value === 'string' ? value : value.format('YYYY-MM-DD');
  }

  onUsFormatChange(value: string | moment.Moment): void {
    console.log('US format picker value changed:', value);
    this.usFormatValue = typeof value === 'string' ? value : value.format('MM/DD/YYYY');
  }

  onDbFormatChange(value: string | moment.Moment): void {
    console.log('Database format picker value changed:', value);
    this.dbFormatValue = typeof value === 'string' ? value : value.format('YYYY-MM-DD HH:mm:ss');
  }

  onZeroPaddedChange(value: string | moment.Moment): void {
    console.log('Zero-padded picker value changed:', value);
    this.zeroPaddedValue = typeof value === 'string' ? value : value.format('HH:mm');
  }

  // YYYY-MM-DD Output Format event handlers
  onIsoDateOnlyChange(value: string | moment.Moment): void {
    console.log('ISO date only picker value changed:', value);
    // Value is already in YYYY-MM-DD format due to outputDateFormat
    this.isoDateOnlyValue = typeof value === 'string' ? value : value.format('YYYY-MM-DD');
  }

  onIsoDateTimeChange(value: string | moment.Moment): void {
    console.log('ISO date-time picker value changed:', value);
    // Value is already in YYYY-MM-DD HH:mm format due to outputDateFormat
    this.isoDateTimeValue = typeof value === 'string' ? value : value.format('YYYY-MM-DD HH:mm');
  }

  // Min/Max Date Range event handlers
  onMinMaxDateChange(value: string | moment.Moment): void {
    console.log('Min/Max date picker value changed:', value);
    this.minMaxDateValue = typeof value === 'string' ? value : value.format('DD/MM/YYYY');
  }

  onMinMaxDateTimeChange(value: string | moment.Moment): void {
    console.log('Min/Max date-time picker value changed:', value);
    this.minMaxDateTimeValue = typeof value === 'string' ? value : value.format('DD/MM/YYYY HH:mm');
  }

  onPastOnlyChange(value: string | moment.Moment): void {
    console.log('Past only picker value changed:', value);
    this.pastOnlyValue = typeof value === 'string' ? value : value.format('DD/MM/YYYY');
  }

  onFutureOnlyChange(value: string | moment.Moment): void {
    console.log('Future only picker value changed:', value);
    this.futureOnlyValue = typeof value === 'string' ? value : value.format('DD/MM/YYYY');
  }

  // Time-aware validation event handlers
  onTimeAwareDateTimeChange(value: string | moment.Moment): void {
    console.log('Time-aware date-time picker value changed:', value);
    this.timeAwareDateTimeValue = typeof value === 'string' ? value : value.format('DD/MM/YYYY HH:mm');
  }

  onTodayTimeConstraintChange(value: string | moment.Moment): void {
    console.log('Today time constraint picker value changed:', value);
    this.todayTimeConstraintValue = typeof value === 'string' ? value : value.format('DD/MM/YYYY HH:mm');
  }

  // Date-Time Form methods
  resetDateTimeForm(): void {
    this.dateTimeForm.reset();
    this.dateOnlyValue = '';
    this.dateTime12Value = '';
    this.dateTime24Value = '';
    this.dateTimeRightValue = '';
    this.timeOnlyValue = '';
    this.timeOnly24Value = '';
    this.fiveMinuteValue = '';
    this.tenMinuteValue = '';
    this.fifteenMinuteValue = '';
    this.isoFormatValue = '';
    this.usFormatValue = '';
    this.dbFormatValue = '';
    this.zeroPaddedValue = '';
    this.momentValue = null;
    this.isoDateOnlyValue = '';
    this.isoDateTimeValue = '';
    this.minMaxDateValue = '';
    this.minMaxDateTimeValue = '';
    this.pastOnlyValue = '';
    this.futureOnlyValue = '';
    this.timeAwareDateTimeValue = '';
    this.todayTimeConstraintValue = '';
  }

  setDateTimeSampleValues(): void {
    const now = moment();

    // Set reactive form values with formatted strings
    this.dateTimeForm.patchValue({
      dateOnly: now.format('DD/MM/YYYY'),
      dateTime12: now.format('DD/MM/YYYY hh:mm A'),
      dateTime24: now.format('DD/MM/YYYY HH:mm')
    });

    // Force change detection by marking controls as dirty and touched
    Object.keys(this.dateTimeForm.controls).forEach(key => {
      const control = this.dateTimeForm.get(key);
      if (control) {
        control.markAsDirty();
        control.markAsTouched();
        control.updateValueAndValidity();
      }
    });

    // Update demo values for non-reactive form examples
    this.dateOnlyValue = now.format('DD/MM/YYYY');
    this.dateTime12Value = now.format('DD/MM/YYYY hh:mm A');
    this.dateTime24Value = now.format('DD/MM/YYYY HH:mm');
    this.dateTimeRightValue = now.format('DD/MM/YYYY HH:mm');
    this.timeOnlyValue = now.format('hh:mm A');
    this.timeOnly24Value = now.format('HH:mm');
    this.fiveMinuteValue = now.format('DD/MM/YYYY hh:mm A');
    this.tenMinuteValue = now.format('DD/MM/YYYY HH:mm');
    this.fifteenMinuteValue = now.format('DD/MM/YYYY hh:mm A');
    this.isoFormatValue = now.format('YYYY-MM-DD');
    this.usFormatValue = now.format('MM/DD/YYYY');
    this.dbFormatValue = now.format('YYYY-MM-DD HH:mm:ss');
    this.zeroPaddedValue = now.format('HH:mm');
    this.momentValue = now.clone();
    this.isoDateOnlyValue = now.format('YYYY-MM-DD');
    this.isoDateTimeValue = now.format('YYYY-MM-DD HH:mm');
    this.minMaxDateValue = now.format('DD/MM/YYYY');
    this.minMaxDateTimeValue = now.format('DD/MM/YYYY HH:mm');
    this.pastOnlyValue = now.subtract(5, 'days').format('DD/MM/YYYY'); // 5 days ago
    this.futureOnlyValue = now.add(10, 'days').format('DD/MM/YYYY'); // 5 days from now (net +5)
    this.timeAwareDateTimeValue = now.add(1, 'hour').format('DD/MM/YYYY HH:mm'); // 1 hour from now (net +6 days, +1 hour)
    this.todayTimeConstraintValue = moment().add(30, 'minutes').format('DD/MM/YYYY HH:mm'); // 30 minutes from now
  }

  // stopEvent(event: Event): void {
  //   // Prevent event propagation
  //   event.stopPropagation();
  // }

}
