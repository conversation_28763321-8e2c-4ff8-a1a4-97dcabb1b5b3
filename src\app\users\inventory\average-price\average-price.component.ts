import { Component, OnDestroy, OnInit } from '@angular/core';
import { AveragePriceService } from './average-price.service';
import { map, mergeMap, Observable, Subject, takeUntil, tap } from 'rxjs';
import { AveragePrice } from '@modal/AveragePrice';
declare var window: any;

@Component({
  selector: 'app-average-price',
  templateUrl: './average-price.component.html',
  styleUrls: ['./average-price.component.css']
})
export class AveragePriceComponent extends AveragePriceService implements OnInit, OnDestroy {

  private destroy$ = new Subject<void>();

  ngOnInit() {
    this.getContainerData();

    this.approveAvgPriceModal = new window.bootstrap.Modal(
      document.getElementById('approveAvgPriceModal')
    );

    this.rollBackAvgPriceModal = new window.bootstrap.Modal(
      document.getElementById('rollBackAvgPriceModal')
    );
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  getContainerData = () => {
    this.averagePriceList.set([]);
    this.containerList.set([]);
    this.getContainersList().pipe(
      tap((response) => {
        if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
          this.containerList.set(response.container);
          let containerId = this.paginationRequest().containerId;
          if (containerId) {
            this.paginationRequest.update(prev => ({ ...prev, containerId }));
          } else {
            containerId = response.container?.[0]?.id ?? null;
            this.paginationRequest.update(prev => ({ ...prev, containerId }));
          }
        }
      }),
      map((c) => c.container),
      this.utilsService.skipEmitIfEmpty(),
      mergeMap(() => this.getAPListing())
    ).subscribe();
  }

  getAPListing = (): Observable<AveragePrice[]> => {
    return this.getAveragePriceListing().pipe(
      tap((response) => {
        if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
          this.averagePriceList.set(response);
        }
      }),
      takeUntil(this.destroy$)
    )
  }

  onChangeFilter = (field: 'search' | 'avgPriceFrom' | 'avgPriceTo' | 'containerId', event: any) => {
    switch (field) {
      case 'avgPriceFrom':
        const from = (event.target as HTMLInputElement).value.replace(/^\./, '');
        this.paginationRequest.update(prev => ({ ...prev, avgPriceFrom: +from }));
        break;
      case 'avgPriceTo':
        const to = (event.target as HTMLInputElement).value.replace(/^\./, '');
        this.paginationRequest.update(prev => ({ ...prev, avgPriceTo: +to })); 
        break;
      case 'containerId':
        this.paginationRequest.update(prev => ({ ...prev, containerId: +event.id })); 
        break;
      case 'search':
        this.paginationRequest.update(prev => ({ ...prev, searchByItemName: event.target.value })); 
        break;
    }
    this.getContainerData();
  }

  onRefresh = () => {
    this.getContainerData();
  }

  /// Select Deselect 
  selectAll = () => {
    this.selectedIds = [];

    if (this.paginationRequest().flagForSelectAll) {
      this.averagePriceList.update((list: AveragePrice[]) => list.map(val => {
        val.isSelected = true;
        return val;
      }));
    } else {
      this.averagePriceList.update((list: AveragePrice[]) => list.map(val => {
        val.isSelected = false;
        return val;
      }));
    }
  }

  selectUnselect = (id: number, value: boolean) => {
    const isSelected = this.selectedIds.includes(id);
    if (value && !isSelected) {
      this.selectedIds.push(id);
    } else if (!value && isSelected) {
      const assetIndex = this.selectedIds.indexOf(id);
      this.selectedIds.splice(assetIndex, 1);
    }
    this.paginationRequest.update(prev => ({...prev, flagForSelectAll: this.checkIfAllSelected()}));
  }

  checkIfAllSelected = (): boolean => { 
    return this.averagePriceList().length > 0 && this.averagePriceList().every((val: AveragePrice) => val.isSelected);
  }

  // Approve Modal
  onApproveModalOpen = () => {
    this.approveAvgPriceModal.show();
  }
  
  // RollBack
  onOpenRollBackModal = () => {
    this.rollBackAvgPriceModal.show();
  }

  onSaveSpStaffApprove = (stage: 'save' | 'approve' | 'rollback') => {
    this.saveAveragePriceList(stage).pipe(
      tap((res) => {
        if (!this.utilsService.isEmptyObjectOrNullUndefined(res)) {
          this.getContainerData();
          if (stage === 'approve') {
            this.approveAvgPriceModal.hide();
          }
          if (stage === 'rollback') {
            this.rollBackAvgPriceModal.hide();
          }
        }
      }),
      takeUntil(this.destroy$)).subscribe();
  }
}
