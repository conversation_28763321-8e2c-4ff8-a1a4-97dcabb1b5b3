import { Component, OnInit, signal } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Deserialize, Serialize } from 'cerialize';
import { debounceTime, distinctUntilChanged, Subject } from 'rxjs';
import { SalesOrderType } from 'src/app/models/SalesOrderType';
import { activeInactiveStatus, SALES_ORDER_TYPE_MASTER } from 'src/app/shared/constants/constant';
import { UtilsService } from 'src/app/shared/services/utils.service';
import saveAs from 'file-saver';
declare var window: any;

@Component({
  selector: 'app-sales-order-type',
  templateUrl: './sales-order-type.component.html',
  styleUrls: ['./sales-order-type.component.scss']
})
export class SalesOrderTypeComponent implements OnInit {

  activeInactiveStatus = activeInactiveStatus
  salesOTMasterModal: any;
  deleteSalesOTMasterModal: any;

  salesOtTH: any[] = []
  selectedIds = signal<number[]>([])
  salesOtList = signal<SalesOrderType[]>([]);
  salesOtObj = new SalesOrderType();
  activeFlag: boolean = true;
  salesOtGroup: FormGroup;
  flagForSelectAll: boolean = false;

  pageNo: number = 1;
  pageSize: string = '100';
  sortOrder: string;
  sortColumn: string;
  totalData: number;
  statusForModal: string;

  enumForSortOrder = this.utilsService.enumForSortOrder;
  searchSubject: Subject<string> = new Subject<string>();
  searchText: string;
  pagination: any;

  constructor(public utilsService: UtilsService, private fb: FormBuilder) {
    this.salesOtTH = SALES_ORDER_TYPE_MASTER
  }

  ngOnInit() {

    this.searchSubject.pipe(debounceTime(300), distinctUntilChanged()).subscribe((res: string) => {
      this.pageNo = 1;
      // this.pageSize = '100';
      this.searchText = null;
      this.searchText = res;
      this.getSalesOT();
    });

    this.getSalesOT();
    this.salesFormGroup();

    this.salesOTMasterModal = new window.bootstrap.Modal(
      document.getElementById('salesOTMasterModal')
    );

    this.deleteSalesOTMasterModal = new window.bootstrap.Modal(
      document.getElementById('deleteSalesOTMasterModal')
    );

    document.getElementById('salesOTMasterModal').addEventListener('shown.bs.modal', () => {
      document.getElementById('f').focus();
    });

  }

  getSalesOT() {

    this.flagForSelectAll = false;
    this.totalData = 0;
    this.selectedIds.set([])

    const param = {
      pageNo: this.pageNo,
      pageSize: this.pageSize,
      sortOrder: this.sortOrder,
      sortColumn: this.sortColumn,
      name: this.searchText,
      isActive: this.activeFlag
    }

    this.utilsService.postMethodAPI(false, this.utilsService.serverVariableService.SALES_OT_LISTING, param, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.salesOtList.set(Deserialize(response.content, SalesOrderType));
        this.totalData = response.totalElements;
        this.pagination = response;
      } else {
        this.salesOtList.set([]);
      }
    })

  }

  salesFormGroup() {
    this.salesOtGroup = this.fb.group({
      name: ['', Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.PATTERN_FOR_ALPHANUMERIC_WITH_SPECIAL_CHAR)])],
      status: [true, Validators.compose([Validators.required])]
    })
  }

  openAddEditModal(obj: SalesOrderType, status: string) {
    this.salesOtObj = new SalesOrderType();
    this.salesOtGroup.reset();
    this.statusForModal = status;

    if (this.statusForModal === 'Add') {
      setTimeout(() => {
        this.salesOtObj.isActive = true;
      }, 100);
    }

    if (obj) {
      setTimeout(() => {
        this.salesOtObj = Serialize(obj);
      }, 100);
    }
    this.salesOTMasterModal.show();
  }

  addPageSizeData(event) {
    this.pageNo = 1;
    this.pageSize = event;
    this.getSalesOT();
  }

  pageNumber(event) {
    this.pageNo = event
    this.getSalesOT();
  }

  trackBy(index: number, item: SalesOrderType): number {
    return item.id;
  }

  onSaveSalesOrder() {

    if (this.salesOtGroup.invalid) {
      this.salesOtGroup.markAllAsTouched();
      return;
    }

    let param = this.utilsService.trimObjectValues(Serialize(this.salesOtObj));
    this.utilsService.postMethodAPI(true, this.utilsService.serverVariableService.SALES_OT_SAVE_EDIT_DELETE, param, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.salesOTMasterModal.hide();
        this.getSalesOT();
      }
    })

  }

  openDeleteSalesOrderModal(obj: SalesOrderType) {
    this.salesOtObj = Serialize(obj)
    this.deleteSalesOTMasterModal.show();
  }

  deleteSalesOrder() {

    this.utilsService.deleteMethodAPI(true, this.utilsService.serverVariableService.SALES_OT_SAVE_EDIT_DELETE + `?id=${this.salesOtObj.id}`, {}, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.deleteSalesOTMasterModal.hide();
        if(!this.pagination?.first && this.pagination?.last && this.pagination?.numberOfElements === 1) {
          this.pageNo = this.pageNo - 1
        }
        this.getSalesOT();
      }
    })
  }

  // Select Deselect 

  selectAll() {
    if (this.flagForSelectAll) {
      this.selectedIds.set([])
    }

    this.salesOtList.update((obj: SalesOrderType[]) => {
      for (const item of obj) {
        item.isSelected = this.flagForSelectAll;
        if (this.flagForSelectAll) {
          this.selectedIds.update((ids: number[]) => [...ids, item.id]);
        } else {
          this.selectedIds.update((ids: number[]) => ids.filter(id => id !== item.id));
        }
      }
      return [...obj];
    });

    if (!this.flagForSelectAll) {
      this.selectedIds.set([]);
    }
  }

  selectUnselect(id: number, index, value) {

    const isSelected = this.selectedIds().includes(id);
    if (value && !isSelected) {
      this.selectedIds.update((ids: number[]) => [...ids, id]);
    } else if (!value && isSelected) {
      this.selectedIds.update((ids: number[]) => ids.filter(id => id !== id));
    }
    this.flagForSelectAll = this.checkIfAllSelected();
  }

  checkIfAllSelected(): boolean {
    return this.salesOtList().every(item => item.isSelected === true);
  }

  // sorting 

  onSortTH(key) {

    if (this.utilsService.isEmptyObjectOrNullUndefined(this.salesOtList)) {
      return;
    }

    if (key === this.sortColumn) {
      if (this.sortOrder === this.enumForSortOrder.A) {
        this.sortOrder = this.enumForSortOrder.D;
      } else if (this.sortOrder === this.enumForSortOrder.D) {
        this.sortOrder = this.enumForSortOrder.A;
      }
    } else {
      this.sortOrder = this.enumForSortOrder.D;
    }

    this.sortColumn = key;
    this.getSalesOT();
  }

  // status 

  onChangeStatus(item: SalesOrderType, value, index) {

    this.utilsService.putMethodAPI(true, this.utilsService.serverVariableService.SALES_OT_STATUS_CHANGE + `${item.id}`, {}, '', (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response.data)) {
        const newValue = this.utilsService.isEmptyObjectOrNullUndefined(response.data) ? !value : value;
        this.salesOtList.update(list => { list[index].isActive = newValue; return [...list] });
      }
      if(!this.pagination?.first && this.pagination?.last && this.pagination?.numberOfElements === 1 && this.activeFlag) {
        this.pageNo = this.pageNo - 1
      }
      this.getSalesOT()
    }, true);
  }

  //Search
  onSearch(event: any) {
    this.searchSubject.next(event.target.value);
  }

  // active/inactive

  onChangeActive() {
    this.pageNo = 1;
    // this.pageSize = '100';
    this.getSalesOT();
  }

  // export 

  exportReport() {

    const param = {
      ids: this.selectedIds() ? this.selectedIds() : [],
      searchText: this.searchText,
      isActive: this.activeFlag,
      sortOrder: this.sortOrder,
      sortColumn: this.sortColumn,
    }

    this.utilsService.exportReport(param, this.utilsService.serverVariableService.SALES_OT_EXPORT).subscribe((data: any) => {
      saveAs(new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' }), 'Sales Order Type Sheet');
    });
  }

}
