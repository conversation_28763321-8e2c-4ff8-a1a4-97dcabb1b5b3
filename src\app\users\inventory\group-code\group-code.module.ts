import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { GroupCodeComponent } from './group-code.component';
import { RouterModule, Routes } from '@angular/router';
import { NewGroupCodeComponent } from './new-group-code/new-group-code.component';
import { SharedModule } from 'src/app/shared/shared.module';

const routes: Routes = [
  { path: '', component: GroupCodeComponent },
  { path: 'new-group-code', component: NewGroupCodeComponent },
  { path: 'edit-group-code/:id', component: NewGroupCodeComponent },
];

@NgModule({
  imports: [
    CommonModule,
    SharedModule.forRoot(),
    RouterModule.forChild(routes)
  ],
  declarations: [GroupCodeComponent, NewGroupCodeComponent]
})

export class GroupCodeModule { }
