import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AccountMasterComponent } from './account-master.component';
import { RouterModule, Routes } from '@angular/router';
import { SharedModule } from 'src/app/shared/shared.module';
import { ChartOfAccountComponent } from './chart-of-account/chart-of-account.component';
import { AccountTypeComponent } from './account-type/account-type.component';
import { GeneralLedgerComponent } from './general-ledger/general-ledger.component';
import { AccountReconciliationComponent } from './account-reconciliation/account-reconciliation.component';

const routes: Routes = [
  { path: '', redirectTo: 'accounts', pathMatch: 'full' },
  { path: 'chart-of-account', component: ChartOfAccountComponent, title: 'Chart Of Account' },
  { path: 'account-type', component: AccountTypeComponent, title: 'Account Type' },
  { path: 'manual-journal', canActivate: [], loadChildren: () => import('./manual-journal/manual-journal.module').then(m => m.ManualJournalModule), title: 'Manual Journal' },
  { path: 'general-ledger', component: GeneralLedgerComponent },
  { path: 'account-reconciliation', component: AccountReconciliationComponent },
];

@NgModule({
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    SharedModule.forRoot()
  ],
  declarations: [AccountMasterComponent, ChartOfAccountComponent, AccountTypeComponent, GeneralLedgerComponent, AccountReconciliationComponent]
})

export class AccountMasterModule { }
