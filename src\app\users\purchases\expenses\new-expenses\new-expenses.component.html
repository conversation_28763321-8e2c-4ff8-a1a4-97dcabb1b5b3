<div class="page-content">
  <div class="page-title-wrapper">
    <div class="page-title-left">
      <h4>{{expenseId ? 'Edit' : 'Add New'}} Container Expenses</h4>
    </div>
    <div class="page-title-right">
      <button (click)="onCloseCancel()" class="btn btn-sm btn-icon btn-outline-white" ngbTooltip="Close"
        placement="left" container="body" triggers="hover">
        <i class="th th-close"></i>
      </button>
    </div>
  </div>
  <div class="content-area">
    <div class="card card-theme card-forms">
      <div class="card-body" [formGroup]="expenseGroup">
        <div class="row">
          <div class="col-lg-4 col-md-6 col-sm-12">
            <div class="form-group form-group-inline-control single-date required">
              <label class="form-label">Date</label>
              <div class="form-control-wrapper">
                <div class="form-group-icon-end date-tempo-clear">
                  <i (click)="openDateModal()" class="th th-outline-calendar ngx-daterangepicker-action"></i>
                  <input #pickerDirectiveModal class="form-control" type="text" ngxDaterangepickerMd readonly
                    placeholder="Select Date" [autoApply]="true" [singleDatePicker]="true" [timePicker24Hour]="false"
                    [timePicker]="false" startKey="start" [closeOnAutoApply]="false" [maxDate]="containerMaxDate"
                    [locale]="{format: 'DD/MM/YYYY h:mm A', displayFormat: 'DD/MM/YYYY'}"
                    [(ngModel)]="containerExpenseObj.temp_date" formControlName="expenseDate">
                </div>
                <div class="message error-message"
                  *ngIf="expenseGroup.controls['expenseDate'].touched && !containerExpenseObj.temp_date">
                  {{utilsService.validationService.DATE_TIME_REQ}}
                </div>
              </div>
            </div>
            <div class="form-group theme-ngselect form-group-inline-control required">
              <label class="form-label">Container</label>
              <div class="form-control-wrapper">
                <ng-select (change)="onContainerChange(containerExpenseObj.containerID)" placeholder="Select Container"
                  [multiple]="false" [clearable]="false" [items]="dropdown.containers" bindLabel="containerName"
                  bindValue="id" [(ngModel)]="containerExpenseObj.containerID" formControlName="containerID">
                </ng-select>
                <div class="message error-message"
                  *ngIf="expenseGroup.controls['containerID'].hasError('required') &&  expenseGroup.controls['containerID'].touched">
                  {{utilsService.validationService.CONTAINER_REQ}}
                </div>
              </div>
            </div>
            <div class="form-group theme-ngselect form-group-inline-control required">
              <label class="form-label">Our Carrying Type</label>
              <div class="form-control-wrapper">
                <ng-select placeholder="Our Carrying Type" [multiple]="false" [clearable]="false"
                  [items]="dropdown.OurShippingTypes" bindLabel="label" bindValue="value"
                  [(ngModel)]="containerExpenseObj.ourShippingTypes" formControlName="ourShippingTypes">
                </ng-select>
                <div class="message error-message"
                  *ngIf="expenseGroup.controls['ourShippingTypes'].hasError('required') &&  expenseGroup.controls['ourShippingTypes'].touched">
                  {{utilsService.validationService.SHIPPING_TYPE_REQ}}
                </div>
              </div>
            </div>
            @if (ourShippingTypes) {
              <div class="form-group form-group-inline-control" [ngClass]="{'form-error': expenseGroup.get('ourPrice').invalid && expenseGroup.get('ourPrice').touched}">
                @switch (ourShippingTypes) {
                  @case (enumForShippingType.CBM) {
                    <label class="form-label">CBM/Carton</label>
                  }
                  @case (enumForShippingType.WEIGHT) {
                    <label class="form-label">Cost / kg (RS)</label>
                  }
                  @case (enumForShippingType.PERCENTAGE) {
                    <label class="form-label">Total Expense / Piece (₹) [%]</label>
                  }
                  @case (enumForShippingType.PIECE) {
                    <label class="form-label">Expense/PCS</label>
                  }
                  @case (enumForShippingType.DONE) {
                    <label class="form-label">Amount (Fixed)</label>
                  }
                }
                <div class="form-control-wrapper">
                  <input type="text" class="form-control" placeholder="Enter" [maxLength]="utilsService.validationService.MAX_10"
                    [(ngModel)]="containerExpenseObj.ourPrice" mask="separator.2" thousandSeparator="" formControlName="ourPrice">
                </div>
              </div>
            }
            <div class="form-group form-group-inline-control">
              <label class="form-label">Our Amount</label>
              <div class="form-control-wrapper">
                <div class="input-group">
                  <span class="input-group-text">Rs.</span>
                  <input [maxLength]="utilsService.validationService.MAX_15"  [(ngModel)]="containerExpenseObj.ourAmount"
                    formControlName="ourAmount" mask="separator.2" thousandSeparator="" type="text" class="form-control"
                    placeholder="Our Amount">
                </div>
                <div class="message error-message"
                  *ngIf="expenseGroup.controls['ourAmount'].hasError('required') &&  expenseGroup.controls['ourAmount'].touched">
                  {{utilsService.validationService.AMOUNT_REQ}}
                </div>
              </div>
            </div>
            <div class="form-group form-group-inline-control">
              <label class="form-label">Notes</label>
              <div class="form-control-wrapper">
                <textarea rows="3" formControlName="notes" [(ngModel)]="containerExpenseObj.notes"
                  [maxlength]="utilsService.validationService.MAX_500" class="form-control"
                  placeholder="Enter Notes"></textarea>
              </div>
            </div>
          </div>
          <div class="col-lg-4 col-md-6 col-sm-12">
            <div class="form-group form-group-inline-control required" *ngIf="expenseId">
              <label class="form-label">Expense ID</label>
              <div class="form-control-wrapper">
                <input type="text" class="form-control" placeholder="Expense ID" [disabled]="true"
                  [(ngModel)]="containerExpenseObj.expenseID" [ngModelOptions]="{standalone: true}">
              </div>
            </div>
            <div class="form-group theme-ngselect form-group-inline-control required">
              <label class="form-label">CHA Agent</label>
              <div class="form-control-wrapper">
                <ng-select placeholder="Select CHA Agent" [multiple]="false" [clearable]="false"
                  [items]="dropdown.importer" bindLabel="label" bindValue="value"
                  [(ngModel)]="containerExpenseObj.importerID" formControlName="importerID">
                </ng-select>
                <div class="message error-message"
                  *ngIf="expenseGroup.controls['importerID'].hasError('required') &&  expenseGroup.controls['importerID'].touched">
                  {{utilsService.validationService.CHA_AGENT_REQ}}
                </div>
              </div>
            </div>
            <div class="form-group theme-ngselect form-group-inline-control required">
              <label class="form-label">CHA Carrying Type</label>
              <div class="form-control-wrapper">
                <ng-select placeholder="CHA Carrying Type" [multiple]="false" [clearable]="false"
                  [items]="dropdown.CHAShippingTypes" bindLabel="label" bindValue="value"
                  [(ngModel)]="containerExpenseObj.chaShippingTypes" formControlName="chaShippingTypes">
                </ng-select>
                <div class="message error-message"
                  *ngIf="expenseGroup.controls['chaShippingTypes'].hasError('required') &&  expenseGroup.controls['chaShippingTypes'].touched">
                  {{utilsService.validationService.CHA_CARRYING_TYPE_REQ}}
                </div>
              </div>
            </div>
            @if (chaShippingTypes) {
              <div class="form-group form-group-inline-control" [ngClass]="{'form-error': expenseGroup.get('chaPrice').invalid && expenseGroup.get('chaPrice').touched}">
                @switch (chaShippingTypes) {
                  @case (enumForShippingType.CBM) {
                    <label class="form-label">CBM/Carton</label>
                  }
                  @case (enumForShippingType.WEIGHT) {
                    <label class="form-label">Cost / kg (RS)</label>
                  }
                  @case (enumForShippingType.PERCENTAGE) {
                    <label class="form-label">Total Expense / Piece (₹) [%]</label>
                  }
                  @case (enumForShippingType.PIECE) {
                    <label class="form-label">Expense/PCS</label>
                  }
                  @case (enumForShippingType.DONE) {
                    <label class="form-label">Amount (Fixed)</label>
                  }
                }
                <div class="form-control-wrapper">
                  <input type="text" class="form-control" placeholder="Enter" formControlName="chaPrice" 
                    [(ngModel)]="containerExpenseObj.chaPrice" [maxLength]="utilsService.validationService.MAX_10"
                    mask="separator.2" thousandSeparator="">
                </div>
              </div>
            }
            <div class="form-group form-group-inline-control">
              <label class="form-label">Agent Amount</label>
              <div class="form-control-wrapper">
                <div class="input-group">
                  <span class="input-group-text">Rs.</span>
                  <input mask="separator.2" thousandSeparator="" [maxLength]="utilsService.validationService.MAX_15"
                    [(ngModel)]="containerExpenseObj.chaRate" formControlName="chaRate" type="text"
                    class="form-control" placeholder="Agent Amount">
                </div>
                <div class="message error-message"
                  *ngIf="expenseGroup.controls['chaRate'].hasError('required') &&  expenseGroup.controls['chaRate'].touched">
                  {{utilsService.validationService.AMOUNT_REQ}}
                </div>
              </div>
            </div>
            <div class="form-group form-group-inline-control required">
              <label class="form-label">Final</label>
              <div class="form-control-wrapper">
                <div
                  *ngFor="let item of dropdown.expenseCarringTypes; index as i"
                  class="radio radio-primary form-check-inline">
                  <input type="radio" [id]="item.value" [value]="item.value"
                    [(ngModel)]="containerExpenseObj.expenseCarringType" formControlName="expenseCarringType">
                  <label [for]="item.value">{{item.label}}</label>
                </div>
              </div>
            </div>
            
            <!-- rate -->
            @if (ourShippingTypes && expenseCarringType === enumForExpenseFinal.OTHER) {
              <div class="form-group form-group-inline-control" [ngClass]="{'form-error': expenseGroup.get('otherPrice').invalid && expenseGroup.get('otherPrice').touched}">
                @switch (ourShippingTypes) {
                  @case (enumForShippingType.CBM) {
                    <label class="form-label">CBM/Carton</label>
                  }
                  @case (enumForShippingType.WEIGHT) {
                    <label class="form-label">Cost / kg (RS)</label>
                  }
                  @case (enumForShippingType.PERCENTAGE) {
                    <label class="form-label">Total Expense / Piece (₹) [%]</label>
                  }
                  @case (enumForShippingType.PIECE) {
                    <label class="form-label">Expense/PCS</label>
                  }
                  @case (enumForShippingType.DONE) {
                    <label class="form-label">Amount (Fixed)</label>
                  }
                }
                <div class="form-control-wrapper">
                  <input type="text" class="form-control" placeholder="Enter" [maxLength]="utilsService.validationService.MAX_10"
                    [(ngModel)]="containerExpenseObj.otherPrice" mask="separator.2" thousandSeparator="" formControlName="otherPrice">
                </div>
              </div>
              <div class="form-group form-group-inline-control required">
                <label class="form-label">Other Amount</label>
                <div class="form-control-wrapper">
                  <div class="input-group">
                    <span class="input-group-text">Rs.</span>
                    <input mask="separator.2" thousandSeparator="" [maxLength]="utilsService.validationService.MAX_15"
                      type="text" class="form-control" placeholder="Other Amount"
                      [(ngModel)]="containerExpenseObj.otherRate" formControlName="otherRate">
                  </div>
                </div>
              </div>
            }
          </div>
          <div class="col-lg-4 col-md-6 col-sm-12">
            <div class="table-responsive">
              <table class="table-theme table-hover table table-bordered " formArrayName="expenseTypeItems">
                <thead class="border-less">
                  <tr>
                    <th class="text-center">Expense Type</th>
                    <th class="text-center">Enter Amount (₹)</th>
                    <th class="text-center">Add in <br />avg. price</th>
                    <th class="text-center">Add In <br />Ledger</th>
                    <th class="text-center">Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr class="tbl-add-row tbl-bg-white" *ngFor="let contact of expenseTypeItems.controls; index as i"
                    [formGroupName]="i">
                    <td>
                      <div class="form-group theme-ngselect">
                        <ng-select *ngIf="!containerExpenseObj.expenseTypeItems[i].isDefault;else notDefault" [(ngModel)]="containerExpenseObj.expenseTypeItems[i].expenseTypeId"
                          formControlName="expenseTypeId"
                          [ngClass]="{'required': contact.get('expenseTypeId').invalid && contact.get('expenseTypeId').touched}"
                          placeholder="Expense Type" [multiple]="false" [clearable]="false"
                          [items]="containerExpenseObj.expenseTypeItems[i].expenseDropdown" bindValue="id"
                          [appendTo]="'.theme-ngselect'">
                          <ng-template ng-label-tmp let-item="item">
                            <span class="ng-option-label tbl-description">{{item.expenseTypeName}}</span>
                          </ng-template>
                          <ng-template ng-option-tmp let-item="item">
                            <span class="ng-option-label">{{item.expenseTypeName}}</span>
                          </ng-template>
                        </ng-select>
                        <ng-template #notDefault>
                          <p style="text-align: center;">{{containerExpenseObj.expenseTypeItems[i].expenseTypeName}}</p>
                        </ng-template>
                      </div>
                    </td>
                    <td>
                      <div class="form-group form-group-100" 
                        [ngClass]="{'form-error': contact.get('amount').invalid && contact.get('amount').touched}">
                        <input mask="separator.2" thousandSeparator=""
                          [(ngModel)]="containerExpenseObj.expenseTypeItems[i].amount" formControlName="amount"
                          type="text" class="form-control" placeholder="Enter"
                          [maxLength]="utilsService.validationService.MAX_15">
                      </div>
                    </td>
                    <td>
                      <div class="d-flex justify-content-center">
                        <div class="checkbox checkbox-primary checkbox-small">
                          <input [(ngModel)]="containerExpenseObj.expenseTypeItems[i].isAddInAvgAmount"
                            formControlName="isAddInAvgAmount" type="checkbox" [id]="'avg-price' + i"
                            class="material-inputs filled-in" (change)="onChangeAddIn(i, 'avg')" />
                          <label [for]="'avg-price' + i"></label>
                        </div>
                      </div>
                    </td>
                    <td>
                      <div class="d-flex justify-content-center">
                        <div class="checkbox checkbox-primary checkbox-small">
                          <input [(ngModel)]="containerExpenseObj.expenseTypeItems[i].isAddInLedger"
                            formControlName="isAddInLedger" type="checkbox" [id]="'ledger' + i"
                            class="material-inputs filled-in" (change)="onChangeAddIn(i, 'ledger')" />
                          <label [for]="'ledger' + i"></label>
                        </div>
                      </div>
                    </td>
                    <td class="tbl-action">
                      <div class="tbl-action-group">
                        <button
                          *ngIf="expenseTypeItems.length > 1 && !containerExpenseObj.expenseTypeItems[i].isDefault && !containerExpenseObj.expenseTypeItems[i].isGST"
                          (click)="openExpenseDeleteModal(i)" class="btn btn-xs btn-light-danger btn-icon" ngbTooltip="Delete" placement="left"
                          container="body" triggers="hover">
                          <i class="th th-outline-trash"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                </tbody>
                <tfoot>
                  <tr class="tbl-total-row" *ngIf="containerExpenseObj?.expenseTypeItems?.length > 0">
                    <th>Total</th>
                    <th colspan="4">{{totalAmount ? (totalAmount | indianCurrency) : 0}}</th>
                  </tr>
                  <tr class="tbl-add-new">
                    <td colspan="100">
                      <button (click)="addExpenseTypeItem()" class="btn btn-sm btn-link btn-icon-text text-primary"> <i
                          class="th-bold-add-circle"></i>
                        Add New Row
                      </button>
                    </td>
                  </tr>
                </tfoot>
              </table>
            </div>
          </div>
          <div class="col-lg-6 col-md-4 col-sm-12">
            <div class="form-group form-group-inline-control" (paste)="onSelectAttachments($event);doc.value"
              (drop)="onSelectAttachments($event);doc.value = ''" (dragover)="onSelectAttachments($event);doc.value = ''">
              <label class="form-label">Upload<i class="th th-outline-info-circle ms-1"
                  [ngbTooltip]="utilsService.validationService.DOC_INFO" placement="bottom" container="body"
                  triggers="hover"></i></label>
              <div class="form-control-wrapper">
                <div class='attachments-container h-100'>
                  <div class='attachments-content'>
                    <button class='btn btn-attachments'><i class="bi bi-upload"></i></button>
                    <p>Upload Photos & Document</p>
                  </div>
                  <input #doc type="file" ref={imageRef} multiple accept=".xls,.xlsx,.xlss,.csv,image/*,.pdf"
                    (change)="onSelectAttachments($event);doc.value = ''" />
                </div>
                <!-- <button class='btn btn-fileupload btn-fileupload-white'> <i class="bi bi-upload"></i>
                                  Upload Photos & Document
                                  <input #doc (paste)="onSelectAttachments($event);doc.value = ''" multiple
                                    (change)="onSelectAttachments($event);doc.value = ''" type="file" ref={imageRef}
                                    accept=".jpg,.jpeg,.png,.pdf,.doc,.docx,.jfif,.webp,.xls,.xlsx">
                                </button> -->
              </div>
            </div>
            <div class="form-group d-flex flex-column" (paste)="onSelectAttachments($event);doc.value"
              (drop)="onSelectAttachments($event);doc.value = ''" (dragover)="onSelectAttachments($event);doc.value = ''">
              <div class="attachments-wrapper">
                <div class='attachments-upload-grid-container attachments-upload-grid-container2'>
                  <div class='attachments-upload-row attachment-list'>
                    <div class='attachments-upload-col' *ngFor="let item of containerExpenseObj.docs; index as i">
                      <div class='card-attachments-upload'>
                        <div class='attachments-image'>
          
                          <ng-container *ngIf="utilsService.isImage(item.file ? item.originalName : item.formattedName)">
                            <img (click)="openLink(item.file ? null : item.formattedName, item.file ? item.formattedName : null)"
                              loading="lazy" [src]="item.file ? item.formattedName : (utilsService.imgPath + item.formattedName)"
                              alt="valamji" />
                          </ng-container>
          
                          <ng-container *ngIf="utilsService.isDocument(item.file ? item.originalName : item.formattedName)">
                            <img (click)="openLink(item.file ? null : item.formattedName, item.file ? item.formattedName : null)"
                              src="assets/images/files/file-pdf.svg" alt="valamji" />
                          </ng-container>
          
                          <ng-container *ngIf="utilsService.isExcel(item.file ? item.originalName : item.formattedName)">
                            <img (click)="openLink(item.file ? null : item.formattedName, item.file ? item.formattedName : null)"
                              src="assets/images/files/file-excel.svg" alt="valamji" />
                          </ng-container>
          
                        </div>
                        <div class="attachments-text" [ngbTooltip]="item.fileName ? item.fileName : item.originalName"
                          placement="bottom" container="body" triggers="hover">
                          <h6 class="file-name">{{item.fileName ? item.fileName : item.originalName}}</h6>
                        </div>
                        <button (click)="removeAttachment(i, item)" class="btn-close" variant="close"><i class='th th-close'></i>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-lg-12 col-md-12 col-sm-12" *ngIf="false">
            <div class="table-responsive">
              <table class="table-theme table-hover table table-bordered ">
                <thead class="border-less">
                  <tr>
                    <th>Item Details</th>
                    <th>Receive Date <br />China</th>
                    <th>Load Date</th>
                    <th>Carton Code</th>
                    <th>Container No</th>
                    <th>PO Carton</th>
                    <th>Carton <br />Received <br />(China )</th>
                    <th>Carton <br />Received <br />(Surat )</th>
                    <th>PCS/Carton</th>
                    <th>Total Received <br />Qty <br />(Surat )</th>
                    <th>Total PO PCS</th>
                    <th>Carton Size <br />(OLD)</th>
                    <th>Carton Size <br />(Supplier)</th>
                    <th>Payment <br />Status</th>
                    <th>PO Date</th>
                    <th>Delivery Date</th>
                    <th>Old Carton <br />Price</th>
                    <th>Carton/CBM <br />(Supplier)</th>
                    <th>Total CBM <br />(Supplier)</th>
                    <th>Carton Size <br />(Agent)</th>
                    <th>Carton CBM <br />(Agent)</th>
                    <th>Total CBM <br />(Agent)</th>
                    <th>Carton Size <br />(Owner)</th>
                    <th>Carton/CBM <br />(Owner)</th>
                    <th>Total CBM <br />(Owner)</th>
                    <th>Final CBM <br />Status</th>
                    <th class="tbl-bg-secondary">Final CBM <br />(Supp. + <br />Agent + <br />Owner)</th>
                    <th class="tbl-bg-secondary">CBM Rate</th>
                    <th class="tbl-bg-secondary">Total Amount</th>
                    <th class="tbl-bg-secondary">Insurance</th>
                    <th class="tbl-bg-secondary">Final Amount <br />(CBM+INS)</th>
                    <th>CHA Agent <br />Name</th>
                    <th>Note</th>
                    <th>GST Bill No</th>
                    <th>GST Bill <br />Amount</th>
                    <th>I GST</th>
                    <th>C GST</th>
                    <th>S GST</th>
                    <th>Total Tax</th>
                    <th>Price/PCS <br />(RMB)</th>
                    <th>Conversion <br />Rate</th>
                    <th>Price/PCS <br />(Rs)</th>
                    <th>Total Expense <br />(CHA To SRT)</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let item of [1]">
                    <td class="tbl-user ">
                      <div class="tbl-user-checkbox-srno">
                        <span>01.</span>
                        <div class="tbl-user-wrapper">
                          <div class="tbl-user-image"><img src="assets/images/dummy-product.png" alt="valamji">
                          </div>
                          <div class="tbl-user-text">
                            <p>VO-58554</p>
                            <span>Water Bottle</span>
                          </div>
                        </div>
                      </div>
                    </td>
                    <td>12-12-2024</td>
                    <td>10-12-2024, <br />15-12-2024</td>
                    <td>A042 <br />ZO-2354-24-10A <br />100 Pcs. <br />10 x 12 x 15 cm. <br />Blue <br />[CMT-China]
                    </td>
                    <td>#58565, <br />#584585</td>
                    <td>100</td>
                    <td>90</td>
                    <td>80</td>
                    <td>100</td>
                    <td>8000</td>
                    <td>10000</td>
                    <td>12x12x12 cm</td>
                    <td>12 x 12x 12 cm</td>
                    <td class="text-warning">Pending</td>
                    <td>10/8/2024</td>
                    <td>10/15/2024</td>
                    <td>8</td>
                    <td>8000</td>
                    <td>100</td>
                    <td>12 x 12x 12 cm</td>
                    <td>12.05</td>
                    <td>101250</td>
                    <td>12 x12 x 12 cm</td>
                    <td>12</td>
                    <td>12</td>
                    <td class="tbl-form-group-borderless">
                      <div class="form-group theme-ngselect">
                        <ng-select placeholder="Frequency" [multiple]="false" [clearable]="false" [items]="demo"
                          bindLabel="name" bindValue="id" [(ngModel)]="selectedDemo1" appendTo="body">
                        </ng-select>
                      </div>
                    </td>
                    <td class="tbl-bg-secondary">152</td>
                    <td class="tbl-bg-secondary">0.7</td>
                    <td class="tbl-bg-secondary">12000</td>
                    <td class="tbl-bg-secondary">8400</td>
                    <td class="tbl-bg-secondary">8.4</td>
                    <td class="tbl-form-group-borderless">
                      <div class="form-group theme-ngselect">
                        <ng-select placeholder="Frequency" [multiple]="false" [clearable]="false" [items]="demo"
                          bindLabel="name" bindValue="id" [(ngModel)]="selectedDemo1" appendTo="body">
                        </ng-select>
                      </div>
                    </td>
                    <td>sdfsf dd</td>
                    <td>0.7</td>
                    <td>1012.5</td>
                    <td>1012.5</td>
                    <td>5.5</td>
                    <td>2000.0</td>
                    <td>2.0</td>
                    <td>9.2</td>
                    <td>118.8</td>
                    <td>160.4</td>
                    <td>1520,50, 10C <br />1850,40, 50C</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class='bottombar-wrapper bottom-fixed'>
      <div class='bottombar-container'>
        <div class='bottombar-left'>
          <button (click)="onSave(false)" type="button" class="btn btn-primary btn-icon-text btn-sm"> <i
              class="th th-outline-tick-circle"></i>{{expenseId ? 'Update' : 'Save'}}</button>
          <button *ngIf="containerExpenseObj.expenseStatus === enumForExpenseStatus.WAITING_FOR_APPROVAL" (click)="onSave(true)" type="button" class="btn btn-outline-primary btn-icon-text btn-sm"> <i
              class="th th-outline-tick-circle"></i>Approve
          </button>
          <button (click)="onCloseCancel()" type="button" class="btn btn-outline-white btn-icon-text btn-sm"><i
              class="th th-outline-close-circle"></i>Cancel</button>
        </div>
        <div class='bottombar-right'>

        </div>
      </div>
    </div>
  </div>
</div>

<!-- ----------------------------------------------------------------------- -->
<!--                           Delete Modal Start                            -->
<!-- ----------------------------------------------------------------------- -->
<div class="modal modal-theme modal-confirmation modal-reject fade" id="deleteContainerExpense" tabindex="-1"
  aria-labelledby="deleteContainerExpenseLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
            class='th th-close'></i></button>
      </div>
      <div class="modal-body">
        <div class="modal-confirmation-container">
          <div class="modal-confirmation-icon">
            <i class="th th-bold-trash"></i>
          </div>
          <div class="modal-confirmation-content">
            <h5>Are You Sure?</h5>
            <p>You want to Delete Expense Type.</p>
          </div>
        </div>
        <div class="modal-button-group modal-full-width-btn">
          <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
          <button (click)="onDeleteExpense()" type="button" class="btn btn-primary btn-icon-text"> <i
              class="th th-outline-tick-circle"></i>
            Delete</button>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                           Delete Modal End                            -->
<!-- ----------------------------------------------------------------------- -->