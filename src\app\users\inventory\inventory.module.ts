import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { InventoryComponent } from './inventory.component';
import { RouterModule, Routes } from '@angular/router';
import { SharedModule } from 'src/app/shared/shared.module';
import { CategoryComponent } from './category/category.component';
import { PrintQrCodeComponent } from './print-qr-code/print-qr-code.component';
import { CatalogsComponent } from './catalogs/catalogs.component';
import { CatalogListComponent } from './catalogs/catalog-list/catalog-list.component';
import { CatalogSettingsComponent } from './catalogs/catalog-settings/catalog-settings.component';
import { AddNewCatalogComponent } from './catalogs/add-new-catalog/add-new-catalog.component';
import { AveragePriceComponent } from './average-price/average-price.component';
import { LostFoundComponent } from './lost-found/lost-found.component';
import { DamageLostComponent } from './lost-found/damage-lost/damage-lost.component';
import { FoundComponent } from './lost-found/found/found.component';
import { AddNewDamageLostItemComponent } from './lost-found/add-new-damage-lost-item/add-new-damage-lost-item.component';
import { AddNewFoundedItemComponent } from './lost-found/add-new-founded-item/add-new-founded-item.component';
import { MissingItemByUsReportComponent } from './stock-branch-transfer/missing-item-by-us-report/missing-item-by-us-report.component';
import { SwtNewRequestedBySelfComponent } from './stock-warehouse-transfer/swt-new-requested-by-self/swt-new-requested-by-self.component';
import { SwtReceivedStockTransferComponent } from './stock-warehouse-transfer/swt-received-stock-transfer/swt-received-stock-transfer.component';
import { SwtNewApprovalStockTransferComponent } from './stock-warehouse-transfer/swt-new-approval-stock-transfer/swt-new-approval-stock-transfer.component';
import { AveragePriceService } from './average-price/average-price.service';

const routes: Routes = [
  { path: '', redirectTo: 'items', pathMatch: 'full' },
  { path: 'items', canActivate: [], loadChildren: () => import('./items/items.module').then(m => m.ItemModule), title: 'Item'  },
  { path: 'item-group', canActivate: [], loadChildren: () => import('./item-groups/item-groups.module').then(m => m.ItemGroupsModule), title: 'Item Group' },
  { path: 'group-code', canActivate: [], loadChildren: () => import('./group-code/group-code.module').then(m => m.GroupCodeModule), title: 'Group Code' },
  { path: 'category', component: CategoryComponent, title: 'Category' },
  { path: 'print-qr-code', component: PrintQrCodeComponent },
  { path: 'catalogs', component: CatalogsComponent },
  { path: 'add-new-catalogs', component: AddNewCatalogComponent },
  { path: 'average-price', component: AveragePriceComponent, title: 'Average Price' },
  { path: 'lost-found', component: LostFoundComponent },
  { path: 'add-new-damage-lost', component: AddNewDamageLostItemComponent },
  { path: 'add-new-found', component: AddNewFoundedItemComponent },
  { path: 'stock-recalculation', canActivate: [], loadChildren: () => import('./stock-recalculation/stock-recalculation.module').then(m => m.StockRecalculationModule), title: 'Stock Recalculation' },
  { path: 'stock-branch-transfer', canActivate: [], loadChildren: () => import('./stock-branch-transfer/stock-branch-transfer.module').then(m => m.StockBranchTransferModule), title: 'Stock Branch Transfer' },
  { path: 'mismatch', canActivate: [], loadChildren: () => import('./mismatch/mismatch.module').then(m => m.MismatchModule), title: 'Mismatch' },
  { path: 'stock-warehouse-transfer', canActivate: [], loadChildren: () => import('./stock-warehouse-transfer/stock-warehouse-transfer.module').then(m => m.StockWarehouseTransferModule), title: 'Stock Warehouse Transfer' },
  { path: 'swt-new-requested-by-self', component: SwtNewRequestedBySelfComponent },
  { path: 'swt-received-stock-transfer', component: SwtReceivedStockTransferComponent },
  { path: 'swt-new-approval-stock-transfer', component: SwtNewApprovalStockTransferComponent },
]

@NgModule({
  declarations: [
    InventoryComponent,
    CategoryComponent,
    PrintQrCodeComponent,
    CatalogsComponent,
    CatalogListComponent,
    CatalogSettingsComponent,
    AddNewCatalogComponent,
    AveragePriceComponent,
    LostFoundComponent,
    DamageLostComponent,
    FoundComponent,
    AddNewDamageLostItemComponent,
    AddNewFoundedItemComponent,
    MissingItemByUsReportComponent,
    SwtNewRequestedBySelfComponent,
    SwtReceivedStockTransferComponent,
    SwtNewApprovalStockTransferComponent,
  ],
  imports: [
    CommonModule,
    SharedModule.forRoot(),
    RouterModule.forChild(routes)
  ],
  providers: [AveragePriceService]
})
export class InventoryModule { }
