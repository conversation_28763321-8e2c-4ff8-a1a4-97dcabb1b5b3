import { deserializeAs, serializeAs } from 'cerialize';

export class CourierCompany {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('createdDate')
    @deserializeAs('createdDate')
    private _createdDate: string;

    @serializeAs('courierName')
    @deserializeAs('courierName')
    private _courierName: string;

    @serializeAs('contactName')
    @deserializeAs('contactName')
    private _contactName: string;

    @serializeAs('mobileNo')
    @deserializeAs('mobileNo')
    private _mobileNo: string;

    @serializeAs('email')
    @deserializeAs('email')
    private _email: string;

    @serializeAs('logoImg')
    @deserializeAs('logoImg')
    private _logoImg: string;

    @serializeAs('priceKg')
    @deserializeAs('priceKg')
    private _priceKg: any;

    @serializeAs('isActive')
    @deserializeAs('isActive')
    private _isActive: boolean;

    // @serializeAs('isSelected')
    @deserializeAs('isSelected')
    private _isSelected: boolean;

    // @serializeAs('isDeleted')
    @deserializeAs('isDeleted')
    private _isDeleted: boolean;

    @serializeAs('countryMaster')
    @deserializeAs('countryMaster')
    private _countryMaster: any;

    @serializeAs('stateMaster')
    @deserializeAs('stateMaster')
    private _stateMaster: any;

    @serializeAs('cityMaster')
    @deserializeAs('cityMaster')
    private _cityMaster: any;

    @serializeAs('zipCode')
    @deserializeAs('zipCode')
    private _zipCode: string;

    @serializeAs('address')
    @deserializeAs('address')
    private _address: string;

    @serializeAs('countryId')
    @deserializeAs('countryId')
    private _countryId: number;

    @serializeAs('stateId')
    @deserializeAs('stateId')
    private _stateId: number;

    @serializeAs('cityId')
    @deserializeAs('cityId')
    private _cityId: number;

    @serializeAs('lastModifiedDate')
    @deserializeAs('lastModifiedDate')
    private _lastModifiedDate: string;
    
    private _originalName: string;

    @serializeAs('contactCountryExtensionId')
    @deserializeAs('contactCountryExtensionId')
    private _contactCountryExtensionId: number;

    @deserializeAs('contactExtension')
    private _contactExtension: any;

    @deserializeAs('isDefault')
    private _isDefault: boolean;

    constructor() {
        this.isActive = false;
        this.isSelected = false;
        this.isDeleted = false;
        this.isDefault = false;
    }

    /**
     * Getter isDefault
     * @return {boolean}
     */
	public get isDefault(): boolean {
		return this._isDefault;
	}

    /**
     * Setter isDefault
     * @param {boolean} value
     */
	public set isDefault(value: boolean) {
		this._isDefault = value;
	}


    /**
     * Getter contactExtension
     * @return {any}
     */
	public get contactExtension(): any {
		return this._contactExtension;
	}

    /**
     * Setter contactExtension
     * @param {any} value
     */
	public set contactExtension(value: any) {
		this._contactExtension = value;
	}


    /**
     * Getter contactCountryExtensionId
     * @return {number}
     */
	public get contactCountryExtensionId(): number {
		return this._contactCountryExtensionId;
	}

    /**
     * Setter contactCountryExtensionId
     * @param {number} value
     */
	public set contactCountryExtensionId(value: number) {
		this._contactCountryExtensionId = value;
	}
    

    /**
     * Getter originalName
     * @return {string}
     */
	public get originalName(): string {
		return this._originalName;
	}

    /**
     * Setter originalName
     * @param {string} value
     */
	public set originalName(value: string) {
		this._originalName = value;
	}


    /**
     * Getter lastModifiedDate
     * @return {string}
     */
	public get lastModifiedDate(): string {
		return this._lastModifiedDate;
	}

    /**
     * Setter lastModifiedDate
     * @param {string} value
     */
	public set lastModifiedDate(value: string) {
		this._lastModifiedDate = value;
	}


    /**
     * Getter isDeleted
     * @return {boolean}
     */
	public get isDeleted(): boolean {
		return this._isDeleted;
	}

    /**
     * Setter isDeleted
     * @param {boolean} value
     */
	public set isDeleted(value: boolean) {
		this._isDeleted = value;
	}


    /**
     * Getter countryId
     * @return {number}
     */
	public get countryId(): number {
		return this._countryId;
	}

    /**
     * Getter stateId
     * @return {number}
     */
	public get stateId(): number {
		return this._stateId;
	}

    /**
     * Getter cityId
     * @return {number}
     */
	public get cityId(): number {
		return this._cityId;
	}

    /**
     * Setter countryId
     * @param {number} value
     */
	public set countryId(value: number) {
		this._countryId = value;
	}

    /**
     * Setter stateId
     * @param {number} value
     */
	public set stateId(value: number) {
		this._stateId = value;
	}

    /**
     * Setter cityId
     * @param {number} value
     */
	public set cityId(value: number) {
		this._cityId = value;
	}


    /**
     * Getter zipCode
     * @return {string}
     */
	public get zipCode(): string {
		return this._zipCode;
	}

    /**
     * Getter address
     * @return {string}
     */
	public get address(): string {
		return this._address;
	}

    /**
     * Setter zipCode
     * @param {string} value
     */
	public set zipCode(value: string) {
		this._zipCode = value;
	}

    /**
     * Setter address
     * @param {string} value
     */
	public set address(value: string) {
		this._address = value;
	}


    /**
     * Getter countryMaster
     * @return {any}
     */
	public get countryMaster(): any {
		return this._countryMaster;
	}

    /**
     * Getter stateMaster
     * @return {any}
     */
	public get stateMaster(): any {
		return this._stateMaster;
	}

    /**
     * Getter cityMaster
     * @return {any}
     */
	public get cityMaster(): any {
		return this._cityMaster;
	}

    /**
     * Setter countryMaster
     * @param {any} value
     */
	public set countryMaster(value: any) {
		this._countryMaster = value;
	}

    /**
     * Setter stateMaster
     * @param {any} value
     */
	public set stateMaster(value: any) {
		this._stateMaster = value;
	}

    /**
     * Setter cityMaster
     * @param {any} value
     */
	public set cityMaster(value: any) {
		this._cityMaster = value;
	}


    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Getter createdDate
     * @return {string}
     */
	public get createdDate(): string {
		return this._createdDate;
	}

    /**
     * Getter courierName
     * @return {string}
     */
	public get courierName(): string {
		return this._courierName;
	}

    /**
     * Getter contactName
     * @return {string}
     */
	public get contactName(): string {
		return this._contactName;
	}

    /**
     * Getter mobileNo
     * @return {string}
     */
	public get mobileNo(): string {
		return this._mobileNo;
	}

    /**
     * Getter email
     * @return {string}
     */
	public get email(): string {
		return this._email;
	}

    /**
     * Getter logoImg
     * @return {string}
     */
	public get logoImg(): string {
		return this._logoImg;
	}

    /**
     * Getter priceKg
     * @return {any}
     */
	public get priceKg(): any {
		return this._priceKg;
	}

    /**
     * Getter isActive
     * @return {boolean}
     */
	public get isActive(): boolean {
		return this._isActive;
	}

    /**
     * Getter isSelected
     * @return {boolean}
     */
	public get isSelected(): boolean {
		return this._isSelected;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Setter createdDate
     * @param {string} value
     */
	public set createdDate(value: string) {
		this._createdDate = value;
	}

    /**
     * Setter courierName
     * @param {string} value
     */
	public set courierName(value: string) {
		this._courierName = value;
	}

    /**
     * Setter contactName
     * @param {string} value
     */
	public set contactName(value: string) {
		this._contactName = value;
	}

    /**
     * Setter mobileNo
     * @param {string} value
     */
	public set mobileNo(value: string) {
		this._mobileNo = value;
	}

    /**
     * Setter email
     * @param {string} value
     */
	public set email(value: string) {
		this._email = value;
	}

    /**
     * Setter logoImg
     * @param {string} value
     */
	public set logoImg(value: string) {
		this._logoImg = value;
	}

    /**
     * Setter priceKg
     * @param {any} value
     */
	public set priceKg(value: any) {
		this._priceKg = value;
	}

    /**
     * Setter isActive
     * @param {boolean} value
     */
	public set isActive(value: boolean) {
		this._isActive = value;
	}

    /**
     * Setter isSelected
     * @param {boolean} value
     */
	public set isSelected(value: boolean) {
		this._isSelected = value;
	}


}