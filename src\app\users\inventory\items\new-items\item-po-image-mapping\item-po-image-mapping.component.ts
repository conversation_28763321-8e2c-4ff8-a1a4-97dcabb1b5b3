import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormArray, FormGroup } from '@angular/forms';
import { POImageMapping } from '@modal/POImageMapping';
import { UtilsService } from '@service/utils.service';
import { ItemDropdown } from 'src/app/shared/constants/interface';

@Component({
  selector: 'app-item-po-image-mapping',
  templateUrl: './item-po-image-mapping.component.html',
  styleUrls: ['./item-po-image-mapping.component.scss']
})
export class ItemPoImageMappingComponent implements OnInit {

  @Input({ alias: 'dropdown', required: true }) dropdown: ItemDropdown;
  @Input({ alias: 'pOImageMapping', required: true }) pOImageMapping: POImageMapping[];
  @Input({ alias: 'poImageFGP', required: true }) poImageFGP: FormGroup;

  @Output() openRemovePOModal: EventEmitter<any> = new EventEmitter<any>();
  @Output() addPOMapping: EventEmitter<any> = new EventEmitter<any>();
  @Output() onColorImageChange: EventEmitter<any> = new EventEmitter<any>();
  @Output() removePOColorAttachment: EventEmitter<any> = new EventEmitter<any>();

  constructor(public utilsService: UtilsService) { }

  ngOnInit(): void {
  }

  openRemoveModal(index: number) {
    this.openRemovePOModal.emit(index)
  }

  get imageMapping() {
    return (this.poImageFGP.get('imageMapping') as FormArray);
  }
  
  onSelectImage(event: any, index: number) {
    this.onColorImageChange.emit({event: event, index: index})
  }

  onRemoveColorImg(index: number, file: any, childI: number) {
    this.removePOColorAttachment.emit({index: index, file: file, childI: childI})
  }
}
