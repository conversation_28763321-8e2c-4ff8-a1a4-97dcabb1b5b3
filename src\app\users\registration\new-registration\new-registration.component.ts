import { Component, OnIni<PERSON>, <PERSON>Child, ElementRef } from "@angular/core";
import { FormGroup, FormBuilder, Validators, FormArray, ValidatorFn, AbstractControl, ValidationErrors } from "@angular/forms";
import { ActivatedRoute } from "@angular/router";
import { EnumForRegTabs } from "@enums/EnumForRegTab";
import { EnumForRegUserType } from "@enums/EnumForRegUserType";
import { Registration } from "@modal/Registration";
import { RegistrationAI } from "@modal/RegistrationAssociateItem";
import { RegistrationBankDetails } from "@modal/RegistrationBankDetails";
import { RegistrationCP } from "@modal/RegistrationContactPerson";
import { RegistrationCustomerDetails } from "@modal/RegistrationCustomerDetails";
import { RegistrationGST, RegistrationGSTDetails } from "@modal/RegistrationGST";
import { RegistrationOtherDetails } from "@modal/RegistrationOtherDetails";
import { RegistrationSA } from "@modal/RegistrationShippingAdd";
import { RegistrationSupplierDetails } from "@modal/RegistrationSupplierDetails";
import { UtilsService } from "@service/utils.service";
import { Serialize, Deserialize } from "cerialize";
import { RegDropdown } from "src/app/shared/constants/interface";
declare var window: any;

@Component({
  selector: 'app-new-registration',
  templateUrl: './new-registration.component.html',
  styleUrls: ['./new-registration.component.css']
})
export class NewRegistrationComponent implements OnInit {

  @ViewChild('flag') flag: ElementRef;
  selectedFlag: File;
  fileLocal: any;

  @ViewChild('cus_aadhar') cus_aadhar: ElementRef;
  selectedCustomerFile: File;
  fileLocalCustomer: any;

  @ViewChild('sup_aadhar') sup_aadhar: ElementRef;
  selectedSupplierFile: File;
  fileLocalSupplier: any;


  regId: number
  regForm: FormGroup;
  regObj = new Registration();
  dropdown: RegDropdown = null;

  //customer details
  customerDetailsObj = new RegistrationCustomerDetails();
  showCreditLimitReason: boolean = false;
  showDaysReason: boolean = false;
  //supplier details
  supplierDetailsObj = new RegistrationSupplierDetails();
  //other details
  otherDetailsObj = new RegistrationOtherDetails();
  bankDetailsObj = new RegistrationBankDetails();

  enumForRegType = EnumForRegUserType;
  enumForTab = EnumForRegTabs;
  selectedTab: string = this.enumForTab.CUSTOMER_DETAILS;

  //contactPerson
  cpDeleteModal: any;
  selectedCPIndex: number;
  contactPersonList: RegistrationCP[] = []

  //shippingPerson
  spDeleteModal: any;
  seletedSPIndex: number;
  shippingAddressRequestList: RegistrationSA[] = [];

  //GST
  gstDeleteModal: any;
  seletedGSTIndex: number;
  gstList: RegistrationGST[] = []

  //associate Item
  associateItemDeleteModal: any;
  selectedItemIndex: number;
  associatedItems: RegistrationAI[] = [];
  itemDropdown: any[] = [];
  selectedItems: any[] = [];
  flagForSelectAllItems: boolean = false;

  gstDetailsDeleteModal: any;
  selectedGSTDetailIndex: number;

  itemImgDeleteModal: any;
  selecteditemImgIndex: number;

  markAsPrimaryRegModal: any;
  isItemPresent: boolean = false;
  oldReg: any;

  constructor(public utilsService: UtilsService, private fb: FormBuilder, private route: ActivatedRoute) {
    this.customerDetailsObj.isSendNotification = 1;
    this.customerDetailsObj.isMarkAsDebtor = 1;
  }

  ngOnInit() {
    this.regFormGroup();

    this.regObj.isActive = true;
    this.regId = Number(this.route.snapshot.paramMap.get('id'));

    this.getRequiredData();

    if (!this.regId) {
      this.getAllItems();
      this.regObj.isSaveAsDraft = true;
    }

    this.cpDeleteModal = new window.bootstrap.Modal(
      document.getElementById('cpDeleteModal')
    );

    this.spDeleteModal = new window.bootstrap.Modal(
      document.getElementById('spDeleteModal')
    );

    this.gstDeleteModal = new window.bootstrap.Modal(
      document.getElementById('gstDeleteModal')
    );

    this.gstDetailsDeleteModal = new window.bootstrap.Modal(
      document.getElementById('gstDetailsDeleteModal')
    );

    this.associateItemDeleteModal = new window.bootstrap.Modal(
      document.getElementById('associateItemDeleteModal')
    );

    this.itemImgDeleteModal = new window.bootstrap.Modal(
      document.getElementById('itemImgDeleteModal')
    );

    this.markAsPrimaryRegModal = new window.bootstrap.Modal(
      document.getElementById('markAsPrimaryRegModal')
    );

    document.getElementById('markAsPrimaryRegModal').addEventListener('hidden.bs.modal', () => {
      this.regObj.registrationType = Serialize(this.oldReg)
    });
  }

  regFormGroup() {
    this.regForm = this.fb.group({
      registrationType: [null, Validators.compose([Validators.required])],
      firstName: [null, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.PATTERN_FOR_ALPHABATES_AND_SPACE)])],
      middleName: [null, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.PATTERN_FOR_ALPHABATES_AND_SPACE)])],
      lastName: [null, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.PATTERN_FOR_ALPHABATES_AND_SPACE)])],
      companyName: [null, Validators.compose([Validators.pattern(this.utilsService.validationService.PATTERN_FOR_ALPHANUMERIC_WITH_SPECIAL_CHAR)])],
      displayName: [null, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.PATTERN_FOR_ALPHANUMERIC_WITH_SPECIAL_CHAR)])],
      email: [null, Validators.compose([Validators.pattern(this.utilsService.validationService.PATTERN_FOR_EMAIL)])],
      countryId: [null],
      phone: [null, Validators.compose([Validators.pattern(this.utilsService.validationService.PATTERN_FOR_PHONE_NO)])],
      isActive: [null],
      isWhatsAppNo: [null],
      countryIdForWhatsApp: [null],
      whatsAppNo: [null, Validators.compose([Validators.pattern(this.utilsService.validationService.PATTERN_FOR_PHONE_NO)])],
      isTelegramNo: [null],
      countryIdForTelegram: [null],
      telegramNo: [null, Validators.compose([Validators.pattern(this.utilsService.validationService.PATTERN_FOR_PHONE_NO)])],
      userId: [null, Validators.required],
      creditLimit: [null],
      creditReason: [null, Validators.compose([Validators.pattern(this.utilsService.validationService.ONLY_SPACE_NOT_ALLOW)])],
      days: [null],
      daysReason: [null, Validators.compose([Validators.pattern(this.utilsService.validationService.ONLY_SPACE_NOT_ALLOW)])],
      isBlacklist: [null],
      blacklistReason: [null, Validators.compose([Validators.pattern(this.utilsService.validationService.ONLY_SPACE_NOT_ALLOW)])],
      isBlocklist: [null],
      blocklistReason: [null, Validators.compose([Validators.pattern(this.utilsService.validationService.ONLY_SPACE_NOT_ALLOW)])],
      referencePerson: [null, Validators.compose([Validators.pattern(this.utilsService.validationService.PATTERN_FOR_ALPHABATES_AND_SPACE)])],
      countryForPerson: [null],
      personNo: [null, Validators.compose([Validators.pattern(this.utilsService.validationService.PATTERN_FOR_PHONE_NO)])],
      countryForLedger: [null],
      sendLedgerToMobile: [null, Validators.compose([Validators.pattern(this.utilsService.validationService.PATTERN_FOR_PHONE_NO)])],
      isSendNotification: [null],
      isMarkAsDebtor: [null],
      isNewCustomer: [null],
      shortCode: [null, Validators.compose([Validators.required])],
      supplierType: [null, Validators.compose([Validators.required])],
      paymentPerson: [null],
      paymentBy: [null],
      panNo: [null, Validators.compose([Validators.pattern(this.utilsService.validationService.PATTERN_FOR_PANCARD_NO)])],
      businessRegNo: [null],
      paymentTermId: [null],
      websiteUrl: [null],
      facebook: [null],
      instagram: [null],
      twitter: [null],
      holderName: [null, Validators.compose([Validators.pattern(this.utilsService.validationService.PATTERN_FOR_ALPHABATES_AND_SPACE)])],
      bankName: [null, Validators.compose([Validators.pattern(this.utilsService.validationService.PATTERN_FOR_ALPHANUMERIC_WITH_SPECIAL_CHAR)])],
      accountNo: [null],
      reEnterAccountNo: [null],
      IFSC: [null],
      currencyId: [null],
      remarks: [null],
      contactPerson: this.fb.array([]),
      shippingAddress: this.fb.array([]),
      gst: this.fb.array([]),
      items: this.fb.array([])
    }, { validators: [this.checkAccountNo] })
  }

  //dropdowns and getdata
  getRequiredData() {

    let API = this.regId ? this.utilsService.serverVariableService.REG_REQUIRED_DATA + `?id=${this.regId}` : this.utilsService.serverVariableService.REG_REQUIRED_DATA

    this.utilsService.getMethodAPI(false, API, null, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.dropdown = response;
        this.dropdown.phone = Serialize(this.dropdown.Country);
        this.dropdown.phoneWhatsApp = Serialize(this.dropdown.Country);
        this.dropdown.phoneTelegram = Serialize(this.dropdown.Country);
        this.dropdown.phoneRef = Serialize(this.dropdown.Country);
        this.dropdown.phoneLedger = Serialize(this.dropdown.Country);
        Object.keys(this.dropdown).filter(v => v !== 'SupplierType' && v !== 'RegistrationType').forEach(key => {
          if (this.dropdown[key] && Array.isArray(this.dropdown[key])) {
            this.dropdown[key] = this.utilsService.transformDropdownItems(this.dropdown[key]);
          }
        });

        this.regForm.get('creditLimit').patchValue(response.creditLimit ? response.creditLimit : null)

        if(!this.regId) {
          this.regObj.registrationType = this.enumForRegType.CUSTOMER;
          this.onChangeRegType();
        }

        if (response.Registration) {

          this.regObj = Deserialize(response.Registration, Registration);
          this.regObj.registrationType = Serialize(response.Registration.registrationType.value);
          this.regObj.registrationId = Serialize(response.Registration.id);
          if (response.Registration?.formattedName) {
            this.regObj.profileUrl = {
              formattedName: response.Registration?.formattedName,
              originalName: response.Registration?.originalName,
            }
          }
          // this.regObj.profileUrl.fileName = response.Registration?.formattedName ? response.Registration?.formattedName : null;
          // this.regObj.profileUrl.originalName = response.Registration?.formattedName ? response.Registration?.originalName : null;

          if (response.Registration.otherDetail) {
            this.otherDetailsObj.id = Serialize(response.Registration.otherDetail?.id);
            this.otherDetailsObj.businessRegNo = (response.Registration.otherDetail?.businessRegNo);
            this.otherDetailsObj.facebook = (response.Registration.otherDetail?.facebook);
            this.otherDetailsObj.instagram = (response.Registration.otherDetail?.instagram);
            this.otherDetailsObj.panNo = (response.Registration.otherDetail?.panNo);
            this.otherDetailsObj.twitter = (response.Registration.otherDetail?.twitter);
            this.otherDetailsObj.websiteUrl = (response.Registration.otherDetail?.websiteUrl);
            this.otherDetailsObj.paymentTermId = (response.Registration.otherDetail?.paymentTerms?.id);

          }

          if (response.Registration.supplierDetail) {
            this.supplierDetailsObj.id = Serialize(response.Registration.supplierDetail.id)
          }

          if (response.Registration.customerDetail) {
            this.customerDetailsObj.id = Serialize(response.Registration.customerDetail.id)
          }

          if (response.Registration.bankDetail) {
            this.bankDetailsObj.id = Serialize(response.Registration.bankDetail.id)
          }

          if (response.Registration?.countryMaster) {
            this.regObj.countryId = response.Registration?.countryMaster.id
          }

          if (response.Registration?.countyForTelegram) {
            this.regObj.countyForTelegramId = response.Registration?.countyForTelegram.id
          }

          if (response.Registration?.countyForWhatsApp) {
            this.regObj.countyForWhatsAppId = response.Registration?.countyForWhatsApp.id
          }

          if (response.Registration.registrationDocsList) {
            this.customerDetailsObj.customerDoc = response.Registration.registrationDocsList.filter(a => a.customerDetailId && a.isAadharFile)[0];
            this.customerDetailsObj.userDocs = response.Registration.registrationDocsList.filter(a => a.customerDetailId && !a.isAadharFile);
            this.supplierDetailsObj.supplierDoc = response.Registration.registrationDocsList.filter(a => a.supplierDetailId);
          }

          if (response.Registration.bankDetail) {
            this.bankDetailsObj.accountNo = response.Registration.bankDetail?.accountNo
            this.bankDetailsObj.bankName = response.Registration.bankDetail?.bankName
            this.bankDetailsObj.holderName = response.Registration.bankDetail?.holderName
            this.bankDetailsObj.IFSC = response.Registration.bankDetail?.ifsc
            this.bankDetailsObj.reEnterAccountNo = response.Registration.bankDetail?.reEnterAccountNo
            this.bankDetailsObj.currencyId = response.Registration.bankDetail?.currency?.id
          }

          if (response.Registration.customerDetail) {
            this.customerDetailsObj.blacklistReason = response.Registration.customerDetail?.blacklistReason
            this.customerDetailsObj.blocklistReason = response.Registration.customerDetail?.blocklistReason
            this.customerDetailsObj.creditLimit = response.Registration.customerDetail?.creditLimit
            this.customerDetailsObj.creditReason = response.Registration.customerDetail?.creditReason
            this.customerDetailsObj.days = response.Registration.customerDetail?.days
            this.customerDetailsObj.daysReason = response.Registration.customerDetail?.daysReason
            this.customerDetailsObj.isBlacklist = response.Registration.customerDetail?.isBlacklist
            this.customerDetailsObj.isBlocklist = response.Registration.customerDetail?.isBlocklist
            this.customerDetailsObj.isMarkAsDebtor = response.Registration.customerDetail?.isMarkAsDebtor
            this.customerDetailsObj.isNewCustomer = response.Registration.customerDetail?.isNewCustomer
            this.customerDetailsObj.personNo = response.Registration.customerDetail?.personNo
            this.customerDetailsObj.sendLedgerToMobile = response.Registration.customerDetail?.sendLedgerToMobile
            this.customerDetailsObj.referencePerson = response.Registration.customerDetail?.referencePerson

            this.customerDetailsObj.daysT = response.Registration.customerDetail?.days
            this.customerDetailsObj.creditLimitT = response.Registration.customerDetail?.creditLimit
            this.customerDetailsObj.creditReasonT = response.Registration.customerDetail?.creditReason
            this.customerDetailsObj.daysReasonT = response.Registration.customerDetail?.daysReason

            if (this.customerDetailsObj.isBlocklist) {
              this.regForm.get('isActive').setValue(false)
              this.regForm.get('isActive').disable();
            } else {
              this.regForm.get('isActive').enable();
            }

            if (this.customerDetailsObj.creditReason) {
              this.showCreditLimitReason = true;
            }
            if (this.customerDetailsObj.daysReason) {
              this.showDaysReason = true;
            }

            if (response.Registration.customerDetail?.countryForLedger) {
              this.customerDetailsObj.countryForLedgerId = response.Registration.customerDetail?.countryForLedger.id
            }

            if (response.Registration.customerDetail?.countryForPerson) {
              this.customerDetailsObj.countryForPersonId = response.Registration.customerDetail?.countryForPerson.id
            }

            if (response.Registration.customerDetail?.assignSalesPerson) {
              this.customerDetailsObj.userId = response.Registration.customerDetail?.assignSalesPerson.id
            }
          }

          if (response.Registration.supplierDetail) {
            this.supplierDetailsObj.paymentBy = response.Registration.supplierDetail?.paymentBy
            this.supplierDetailsObj.paymentPerson = response.Registration.supplierDetail?.paymentPerson
            this.supplierDetailsObj.shortCode = response.Registration.supplierDetail?.shortCode
            this.supplierDetailsObj.supplierType = response.Registration.supplierDetail?.supplierType?.value
          }

          if (response.Registration.contactPersonList) {
            this.contactPersonList = Deserialize(response.Registration.contactPersonList, RegistrationCP);
            this.contactPersonList.forEach(v => {
              v.countryId = (v.country?.id)
              v.firstName = v.firstName
              v.lastName = v.lastName
              v.email = v.email
            })
            const fa = (this.regForm.get('contactPerson') as FormArray);
            this.contactPersonList.map(v => {
              fa.push(this.fb.group({
                firstNameOfPerson: [v.firstName, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.PATTERN_FOR_ALPHABATES_AND_SPACE)])],
                lastNameOfPerson: [v.lastName, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.PATTERN_FOR_ALPHABATES_AND_SPACE)])],
                designation: [v.designation, Validators.compose([Validators.required])],
                emailOfPerson: [v.email, Validators.compose([Validators.required])],
                countryIdOfPerson: [v.countryId, Validators.compose([Validators.required])],
                phoneNo: [v.phoneNo, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.PATTERN_FOR_PHONE_NO)])]
              }));
            })
            // country dropdown
            this.contactPersonList.forEach(v => {
              v.Country = Serialize(this.utilsService.filterIsActive((this.dropdown?.Country || []), v.countryId ? v.countryId : null))
            });
          }

          if (response.Registration.shippingAddressList) {
            this.shippingAddressRequestList = Deserialize(response.Registration.shippingAddressList, RegistrationSA);
            this.shippingAddressRequestList.forEach(v => {
              v.countryId = v.country?.id
              v.cityId = v.city?.id
              v.stateId = v.state?.id
              v.addressLineName = v.addressLine?.addressName
              v.countryList = Serialize(this.utilsService.filterIsActive((this.dropdown?.Country || []), v.countryId ? v.countryId : null))
            })
            const fa = (this.regForm.get('shippingAddress') as FormArray);
            this.shippingAddressRequestList.map((v, i) => {
              fa.push(this.fb.group({
                addressLineName: [v.addressLineName, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.ONLY_SPACE_NOT_ALLOW)])],
                isDefault: [v.isDefault, Validators.compose([Validators.required])],
                countryId: [v.countryId, Validators.compose([Validators.required])],
                stateId: [v.stateId, Validators.compose([Validators.required])],
                cityId: [v.cityId, Validators.compose([Validators.required])],
                isActive: [v.isActive, Validators.compose([Validators.required])],
                zipCode: [v.zipCode, Validators.compose([Validators.required])]
              }));
              const availableStates = this.dropdown.State.filter(state => state.country.id === v.countryId);
              this.shippingAddressRequestList[i].stateList = availableStates;
              this.shippingAddressRequestList[i].stateList = this.utilsService.filterIsActive(this.shippingAddressRequestList[i].stateList, v?.stateId ? v?.stateId : null);
              const availableCities = this.dropdown.City.filter(state => state.state.id === v.stateId);
              this.shippingAddressRequestList[i].cityList = availableCities;
              this.shippingAddressRequestList[i].cityList = this.utilsService.filterIsActive(this.shippingAddressRequestList[i].cityList, v?.cityId ? v?.cityId : null);
            })
          }

          if (response.Registration.gstList) {
            this.gstList = Deserialize(response.Registration.gstList, RegistrationGST);
            this.gstList.forEach(v => {
              v.gstDetails = v.gstDetails.map(a => Deserialize(a, RegistrationGSTDetails))
              return v;
            })
            const fa = (this.regForm.get('gst') as FormArray);
            this.gstList.map((v, i) => {
              fa.push(this.fb.group({
                gstNo: [v.gstNo, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.PATTERN_FOR_GST_NO)])],
                billingAddress: [null],
                reason: [v.reason],
                isActive: [v.isActive],
                gstDetails: this.fb.array([])
              }));
              const gstGroup = this.gst.at(i) as FormGroup;
              const gstDetails = gstGroup.get('gstDetails') as FormArray;
              if (v.gstDetails) {
                v.gstDetails.map(a => {
                  a.countryId = a.country?.id
                  a.cityId = a.city?.id
                  a.stateId = a.state?.id
                  a.addressLineName = a.addressLine?.addressName
                  a.countryDropdown = Serialize(this.utilsService.filterIsActive((this.dropdown?.Country || []), a.countryId ? a.countryId : null))
                  gstDetails.push(this.fb.group({
                    addressLineName: [a.addressLineName, Validators.compose([Validators.required])],
                    zipCode: [a.zipCode, Validators.compose([Validators.required])],
                    isDefault: [a.isDefault],
                    countryId: [a.countryId, Validators.compose([Validators.required])],
                    stateId: [a.stateId, Validators.compose([Validators.required])],
                    cityId: [a.cityId, Validators.compose([Validators.required])],
                    isActive: [a.isActive],
                  }))
                  const availableStates = this.dropdown.State.filter(state => state.country.id === a.countryId);
                  a.stateDropdown = availableStates;
                  a.stateDropdown = this.utilsService.filterIsActive(a.stateDropdown, a?.stateId ? a?.stateId : null);
                  const availableCities = this.dropdown.City.filter(state => state.state.id === a.stateId);
                  a.cityDropdown = availableCities;
                  a.cityDropdown = this.utilsService.filterIsActive(a.cityDropdown, a?.cityId ? a?.cityId : null);
                })
              }
            })
          }

          switch (this.regObj.registrationType) {
            case this.enumForRegType.SUPPLIER:
              this.selectedTab = this.enumForTab.SUPPLIER_DETAILS;
              break;
            case this.enumForRegType.CUSTOMER:
              this.selectedTab = this.enumForTab.CUSTOMER_DETAILS;
              break;
            case this.enumForRegType.BOTH:
              this.selectedTab = this.enumForTab.CUSTOMER_DETAILS;
              break;
            default:
              break;
          }

          this.onChangeRegType();
          this.setAssociateItems(response)
        }

        // phone extensions
        this.dropdown.phone = this.utilsService.filterIsActive(this.dropdown?.phone, this.regObj.countryId ? this.regObj.countryId : null);
        this.dropdown.phoneWhatsApp = this.utilsService.filterIsActive(this.dropdown?.phoneWhatsApp, this.regObj.countyForWhatsAppId ? this.regObj.countyForWhatsAppId : null);
        this.dropdown.phoneTelegram = this.utilsService.filterIsActive(this.dropdown?.phoneTelegram, this.regObj.countyForTelegramId ? this.regObj.countyForTelegramId : null);
        this.dropdown.phoneRef = this.utilsService.filterIsActive(this.dropdown?.phoneRef, this.customerDetailsObj.countryForPersonId ? this.customerDetailsObj.countryForPersonId : null);
        this.dropdown.phoneLedger = this.utilsService.filterIsActive(this.dropdown?.phoneLedger, this.customerDetailsObj.countryForLedgerId ? this.customerDetailsObj.countryForLedgerId : null);
        this.dropdown.AssignSalesPerson = this.utilsService.filterIsActiveLV(this.dropdown?.AssignSalesPerson, this.customerDetailsObj.userId ? this.customerDetailsObj.userId : null);
        this.dropdown.PaymentTerms = this.utilsService.filterIsActiveLV(this.dropdown?.PaymentTerms, this.otherDetailsObj.paymentTermId ? this.otherDetailsObj.paymentTermId : null);
      }
    })
  }

  //item dropdown
  async getAllItems() {
    return new Promise<void>((resolve, reject) => {
      try {
        this.itemDropdown = [];
        this.utilsService.getMethodAPI(false, this.utilsService.serverVariableService.GET_ALL_ITEMS_DROPDOWN, null, (response) => {
          if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
            this.itemDropdown = response;
            this.itemDropdown = this.utilsService.transformDropdownItems(this.itemDropdown);
          }
          resolve();
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  async setAssociateItems(response) {
    try {
      await this.getAllItems();
      if (response.Registration.associatedItems) {
        this.associatedItems = Deserialize(response.Registration.associatedItems, RegistrationAI);
        (this.associatedItems || []).forEach(v => {
          v.colorIds = (v.itemColors || []).map(a => a.poColorId)
          v.images = v.itemDocList
          v['cartonWeightDim'] = v['cartonWeightDimId']
        });
        //dropdown
        (this.associatedItems || []).forEach(v => {
          v.colorDropdown = (this.itemDropdown.find(a => a.id == v.itemId).color) || []
          // if (v.isDeleteFlag) {
          //   v.colorDropdown = Serialize(this.utilsService.filterIsActiveMultipleLV((this.dropdown?.Color || []), v.colorIds ? v.colorIds : null))
          //   v.colorDropdown = (this.dropdown.Color || []).map(color => {
          //     color.disabled = v.colorIds.includes(color.value);
          //     return color;
          //   });
          // } else {
          //   v.colorDropdown = Serialize(this.utilsService.filterIsActiveMultipleLV((this.dropdown?.Color || []), v.colorIds ? v.colorIds : null))
          // }
          v.unitDropdown = Serialize(this.utilsService.filterIsActive((this.dropdown?.Unit || []), v.unitId ? v.unitId : null))
          v.itemDropdown = Serialize(this.utilsService.filterIsActive((this.itemDropdown || []), v.itemId ? v.itemId : null))
          v.cartownWdropdown = Serialize(this.utilsService.filterIsActive((this.dropdown?.Unit || []), v.cartonWeightDim ? v.cartonWeightDim : null))
          v.hsnCode = v.itemDropdown.find(a => a.id === v.itemId)?.hsnCode

          if (v.unitDropdown) {
            v.unitDropdown = (v.unitDropdown.filter(d => d?.unitMasterCategory?.value == 'LENGTH'))
          }
          if (v.cartownWdropdown) {
            v.cartownWdropdown = v.cartownWdropdown.filter(d => d?.unitMasterCategory?.value == 'WEIGHT')
          }
          
        });
        (this.associatedItems || []).forEach(v => {
          v.formattedName = v.itemDropdown.find(a => a.id == v.itemId)?.formattedName
          v.displayName = v.itemDropdown.find(a => a.id == v.itemId)?.displayName
          v.hsnCode = v.itemDropdown.find(a => a.id == v.itemId)?.hsnCode
          v.skuId = v.itemDropdown.find(a => a.id == v.itemId)?.skuId
        })
        const fa = (this.regForm.get('items') as FormArray);
        (this.associatedItems || []).map((v,i) => {
          fa.push(this.fb.group({
            supplierSku: [v.supplierSku, Validators.compose([Validators.required])],
            cartonLength: [v.cartonLength, Validators.compose([Validators.pattern(this.utilsService.validationService.ONLY_GREATER_THAN_ZERO)])],
            cartonWidth: [v.cartonWidth, Validators.compose([Validators.pattern(this.utilsService.validationService.ONLY_GREATER_THAN_ZERO)])],
            cartonHeight: [v.cartonHeight, Validators.compose([Validators.pattern(this.utilsService.validationService.ONLY_GREATER_THAN_ZERO)])],
            cartonWeight: [v.cartonWeight, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.ONLY_GREATER_THAN_ZERO)])],
            cbm: [{value: v.cbm, disabled: true}],
            cartonQuantity: [v.cartonQuantity, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.ONLY_GREATER_THAN_ZERO)])],
            pricePerItem: [v.pricePerItem, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.ONLY_GREATER_THAN_ZERO)])],
            pricePerCarton: [v.pricePerCarton, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.ONLY_GREATER_THAN_ZERO)])],
            tag: [v.tag, Validators.compose([Validators.required])],
            colorIds: [v.colorIds, Validators.compose([Validators.required])],
            englishComment: [v.englishComment, Validators.compose([Validators.required])],
            chinaComment: [v.chinaComment, Validators.compose([Validators.required])],
            measurementCode: [v.measurementCode, Validators.compose([Validators.required])],
            itemId: [v.itemId, Validators.compose([Validators.required])],
            unitId: [v.unitId],
            cartonWeightDim: [v.cartonWeightDimId, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.ONLY_GREATER_THAN_ZERO)])],
            isActive: [v.isActive]
          }));
          fa.at(i).get('isActive').enable()
          if (!v.itemIsActive && !v.isActive) {
            fa.at(i).get('isActive').disable()
          }
          fa.at(i).get('isActive').updateValueAndValidity()
        })
      }
    }
    catch (error) {
      throw error
    }
  }

  captureOldValue() {
    this.oldReg = Serialize(this.regObj.registrationType)
  }

  onChangeRegType() {

    let firstStepFields = [];
    let supplierField = [];

    switch (this.regObj.registrationType) {
      case this.enumForRegType.SUPPLIER:
        this.selectedTab = this.enumForTab.SUPPLIER_DETAILS;
        firstStepFields = ['userId', 'creditLimit', 'creditReason', 'days', 'daysReason', 'isBlacklist', 'blacklistReason', 'isBlocklist',
          'blocklistReason', 'referencePerson', 'countryForPerson', 'personNo', 'countryForLedger', 'sendLedgerToMobile',
          'isSendNotification', 'isNewCustomer', 'isMarkAsDebtor'];
        firstStepFields.map(field => this.regForm.get(field).clearValidators());
        firstStepFields.map(field => this.regForm.get(field).updateValueAndValidity());
        firstStepFields.map(field => this.regForm.get(field).reset());

        //supp
        supplierField = ['shortCode', 'supplierType'];
        supplierField.map(field => this.regForm.get(field).addValidators(Validators.compose([Validators.required])));
        supplierField.map(field => this.regForm.get(field).updateValueAndValidity());
        this.setInitSupplier('CHINA')
        break;
      case this.enumForRegType.CUSTOMER:
        if (this.items.length >= 1) {
          this.openRegTypeModal();
        } else {
          this.selectedTab = this.enumForTab.CUSTOMER_DETAILS;
          let firstStepFields = ['shortCode', 'supplierType', 'paymentPerson', 'paymentBy', 'items'];
          firstStepFields.map(field => this.regForm.get(field).clearValidators());
          firstStepFields.map(field => this.regForm.get(field).updateValueAndValidity());
          firstStepFields.map(field => this.regForm.get(field).reset());
        }
        break;
      case this.enumForRegType.BOTH:
        this.selectedTab = this.enumForTab.CUSTOMER_DETAILS;
        //supp
        supplierField = ['shortCode', 'supplierType'];
        supplierField.map(field => this.regForm.get(field).addValidators(Validators.compose([Validators.required])));
        supplierField.map(field => this.regForm.get(field).updateValueAndValidity());
        this.setInitSupplier('CHINA')
        break;
      default:
        break;
    }
  }

  setInitSupplier = (type: string) => {
    let control = this.regForm.get('supplierType');
    control.setValue(type);
  }

  openRegTypeModal() {
    this.markAsPrimaryRegModal.show();
  }

  onConfirmRegTypeModal() {
    if (this.associatedItems) {
      this.associatedItems.map(a => {
        this.regObj.deleteAssociateIds.push(a.id)
      })
      this.items.clear();
      this.selectedTab = this.enumForTab.CUSTOMER_DETAILS;
      let firstStepFields = ['shortCode', 'supplierType', 'paymentPerson', 'paymentBy', 'items'];
      firstStepFields.map(field => this.regForm.get(field).clearValidators());
      firstStepFields.map(field => this.regForm.get(field).updateValueAndValidity());
      firstStepFields.map(field => this.regForm.get(field).reset());
      this.oldReg = Serialize(this.regObj.registrationType)
      this.markAsPrimaryRegModal.hide();
    }
  }

  // tab change
  onChangeTab(event: any) {
    this.selectedTab = event;
  }

  //profile
  onSelectProfile(event): void {

    let selectedFile: File = null;
    if (event.type === 'drop') {
      event.preventDefault();
      selectedFile = event.dataTransfer?.files[0];
    }

    if (event.type === 'dragover') {
      event.preventDefault();
    }

    if (event.type === 'change') {
      selectedFile = event.target.files[0];
    }

    if (selectedFile) {

      if (this.regObj.profileUrl?.id) {
        this.regObj.deleteRegistrationDocIds.push(this.regObj.profileUrl.id)
      }

      this.regObj.profileUrl = null;
      let fileData = null;
      const reader = new FileReader();
      const max_file_size = 5242880;
      if (selectedFile) {
        const ext = selectedFile.name.substr(selectedFile.name.lastIndexOf('.') + 1);
        const ext1 = (ext).toLowerCase();

        if (ext1 === 'jpeg' || ext1 === 'png' || ext1 === 'jpg' || ext1 === 'jfif') {
          if (max_file_size < selectedFile.size) {
            this.utilsService.toasterService.error(this.utilsService.validationService.IMAGE_MAX_FILE_SIZE)
          } else {
            const fileUrl = URL.createObjectURL(selectedFile);
            fileData = {
              fileName: fileUrl,
              selectedFile,
              name: selectedFile.name
            };
            this.regObj.profileUrl = fileData;
            if (this.flag?.nativeElement) {
              this.flag.nativeElement.value = "";
            }
          }
        } else {
          this.utilsService.toasterService.error(this.utilsService.validationService.IMAGE_INVALID_EXTENSION)
        }
      }

    }
  }

  //customer Aadhar
  onSelectAadharC(event): void {

    if (event.target.files && event.target.files[0]) {
      this.customerDetailsObj.customerDoc = null;
      let fileData = null;
      const reader = new FileReader();
      const max_file_size = 5242880;
      reader.readAsDataURL(event.target.files[0]); // read file as data url
      const selectedFile = event.target.files[0];
      if (selectedFile) {
        const ext = selectedFile.name.substr(selectedFile.name.lastIndexOf('.') + 1);
        const ext1 = (ext).toLowerCase();

        if (ext1 === 'pdf' || ext1 === 'jpeg' || ext1 === 'png' || ext1 === 'jpg' || ext1 === 'jfif') {
          if (max_file_size < selectedFile.size) {
            this.utilsService.toasterService.error(this.utilsService.validationService.FILE_MAX_SIZE)
          } else {
            // this.userObj.profileUrl = event.target.files[0].name
            const fileUrl = URL.createObjectURL(event.target.files[0]);
            fileData = {
              id: null,
              fileName: selectedFile.name,
              file: selectedFile,
              originalname: fileUrl,
            };
            this.customerDetailsObj.customerDoc = fileData;
            if (this.cus_aadhar?.nativeElement) {
              this.cus_aadhar.nativeElement.value = "";
            }
          }
        } else {
          this.utilsService.toasterService.error(this.utilsService.validationService.FILE_INVALID_EXTENSION)
        }
      }

    }
  }

  onSelectAttachmentsCus(event): void {
    if (event.target.files && event.target.files[0]) {
      let fileData = null;
      const max_file_size = 5242880;
      let selectedFile = Array.from(event.target.files || []);;
      if (selectedFile.length) {
        const obj = selectedFile.map((file: File) => {
          const ext = file.name.substr(file.name.lastIndexOf('.') + 1);
          const ext1 = (ext).toLowerCase();
          if (ext1 === 'pdf' || ext1 === 'jpeg' || ext1 === 'png' || ext1 === 'jpg' || ext1 === 'jfif') {
            if (file.size > max_file_size) {
              this.utilsService.toasterService.error(this.utilsService.validationService.DOCUMENT_MAX_FILE_SIZE)
            } else {
              const fileUrl = URL.createObjectURL(file);
              fileData = {
                id: null,
                originalname: fileUrl,
                fileName: file.name,
                file,
                isFlag: false,
              };
              this.customerDetailsObj?.userDocs.push(fileData);
            }
          }
          else {
            this.utilsService.toasterService.error(this.utilsService.validationService.DOCUMENT_INVALID_EXTENSION)
          }
        });
      }
    }
  }

  onRemoveProfile() {
    if (this.regObj.profileUrl?.id) {
      this.regObj.deleteRegistrationDocIds.push(this.regObj.profileUrl.id)
    }
    this.regObj.profileUrl = null;
  }

  onRemoveAadharC() {
    if (this.cus_aadhar?.nativeElement) {
      this.cus_aadhar.nativeElement.value = "";
    }
    if (this.customerDetailsObj.customerDoc?.id) {
      this.regObj.deleteRegistrationDocIds.push(this.customerDetailsObj.customerDoc?.id)
    }
    this.customerDetailsObj.customerDoc = null;
  }

  removeAttachmentCus(i: number, file) {
    this.customerDetailsObj.userDocs.splice(i, 1)
    if (file.id) {
      this.regObj.deleteRegistrationDocIds.push(file.id)
    }
  }

  //supplier Aadhar
  onSelectAadharS(event): void {

    if (event.target.files && event.target.files[0]) {
      let fileData = null;
      const max_file_size = 5242880;
      let selectedFile = Array.from(event.target.files || []);;
      if (selectedFile.length) {
        const obj = selectedFile.map((file: File) => {
          const ext = file.name.substr(file.name.lastIndexOf('.') + 1);
          const ext1 = (ext).toLowerCase();
          if (ext1 === 'pdf' || ext1 === 'jpeg' || ext1 === 'png' || ext1 === 'jpg' || ext1 === 'jfif') {
            if (file.size > max_file_size) {
              this.utilsService.toasterService.error(this.utilsService.validationService.DOCUMENT_MAX_FILE_SIZE)
            } else {
              const fileUrl = URL.createObjectURL(file);
              fileData = {
                id: null,
                originalname: fileUrl,
                fileName: file.name,
                file,
                isFlag: false,
              };
              this.supplierDetailsObj?.supplierDoc?.push(fileData);
            }
          }
          else {
            this.utilsService.toasterService.error(this.utilsService.validationService.DOCUMENT_INVALID_EXTENSION)
          }
        });
      }
    }
  }

  onRemoveAadharS(i: number, file) {
    this.supplierDetailsObj?.supplierDoc.splice(i, 1)
    if (file.id) {
      this.regObj.deleteRegistrationDocIds.push(file.id)
    }
  }

  // account no same validation
  checkAccountNo: ValidatorFn = (group: AbstractControl): ValidationErrors | null => {
    let accountNo = group.get('accountNo').value;
    let reEnterAccountNo = group.get('reEnterAccountNo').value
    return accountNo === reEnterAccountNo ? null : { confirmed_check: true }
  }

  onSaveRegistration(isDraft: boolean) {

    const formData = new FormData();

    if (isDraft) {
      let firstStepFields = [];
      firstStepFields = ['displayName', 'registrationType', 'lastName', 'middleName', 'firstName', 'items', 'gst', 'contactPerson', 'shippingAddress', 'companyName'];
      firstStepFields.map(field => this.regForm.get(field).markAsTouched());
      const firstStepValid = firstStepFields.every(field => this.regForm.get(field).valid)

      if (!firstStepValid) {
        return;
      }
    }

    if (!isDraft) {
      if (this.regForm.invalid) {

        this.regForm.markAllAsTouched();

        let customerFields = ['userId', 'creditLimit', 'creditReason', 'days', 'daysReason', 'isBlacklist', 'blacklistReason',
          'isBlocklist', 'blocklistReason', 'referencePerson', 'countryForPerson', 'personNo',
          'countryForLedger', 'sendLedgerToMobile', 'isSendNotification', 'isMarkAsDebtor', 'isNewCustomer']

        let supplierFields = ['shortCode', 'supplierType', 'paymentPerson', 'paymentBy']

        let otherDetailsFields = ['panNo', 'businessRegNo', 'paymentTermId', 'websiteUrl', 'facebook', 'instagram', 'twitter']

        let bankDetailsFields = ['holderName', 'bankName', 'accountNo', 'reEnterAccountNo', 'IFSC', 'currencyId']

        let associateItems = ['items']
        let gst = ['gst']
        let contactPerson = ['contactPerson']
        let supplierAdd = ['shippingAddress']

        const hasCustomerErrors = customerFields.some(field =>
          this.regForm.get(field)?.invalid && this.regForm.get(field)?.touched && (this.regObj.registrationType == this.enumForRegType.CUSTOMER || this.regObj.registrationType == this.enumForRegType.BOTH)
        );

        const hasSupplierErrors = supplierFields.some(field =>
          this.regForm.get(field)?.invalid && this.regForm.get(field)?.touched && (this.regObj.registrationType == this.enumForRegType.SUPPLIER || this.regObj.registrationType == this.enumForRegType.BOTH)
        );

        const hasOtherDetailsErrors = otherDetailsFields.some(field =>
          this.regForm.get(field)?.invalid && this.regForm.get(field)?.touched
        );

        const hasBankDetailsErrors = bankDetailsFields.some(field =>
          this.regForm.get(field)?.invalid && this.regForm.get(field)?.touched
        );

        const hasAssociateItemsErrors = associateItems.some(field =>
          this.regForm.get(field)?.invalid && this.regForm.get(field)?.touched && (this.regObj.registrationType == this.enumForRegType.SUPPLIER || this.regObj.registrationType == this.enumForRegType.BOTH)
        );

        const hasGstErrors = gst.some(field =>
          this.regForm.get(field)?.invalid && this.regForm.get(field)?.touched
        );

        const hasContactPersonErrors = contactPerson.some(field =>
          this.regForm.get(field)?.invalid && this.regForm.get(field)?.touched
        );

        const hasSupplierAddErrors = supplierAdd.some(field =>
          this.regForm.get(field)?.invalid && this.regForm.get(field)?.touched
        );

        if (hasCustomerErrors) {
          this.selectedTab = this.enumForTab.CUSTOMER_DETAILS
          return;
        } else if (hasSupplierErrors) {
          this.selectedTab = this.enumForTab.SUPPLIER_DETAILS
          return;
        } else if (hasAssociateItemsErrors) {
          this.selectedTab = this.enumForTab.ASSOCIATE_ITEMS
          return;
        } else if (hasOtherDetailsErrors) {
          this.selectedTab = this.enumForTab.OTHER_DETAILS
          return;
        } else if (hasGstErrors) {
          this.selectedTab = this.enumForTab.GST
          return;
        } else if (hasSupplierAddErrors) {
          this.selectedTab = this.enumForTab.SHIPPING_ADD
          return;
        } else if (hasContactPersonErrors) {
          this.selectedTab = this.enumForTab.CONTACT_PERSON
          return;
        } else if (hasBankDetailsErrors) {
          this.selectedTab = this.enumForTab.BANK_DETAILS
          return;
        }
        return;
      }
    }

    if (!this.regObj.profileUrl) {
      this.regObj.isDeleteFile = true;
    } else {
      this.regObj.isDeleteFile = false;
    }

    if (this.regObj.profileUrl?.selectedFile) {
      formData.append('registrationFiles', this.regObj.profileUrl?.selectedFile);
      this.regObj.isDeleteFile = true;
    }

    //customer file (aadhar)
    if (this.customerDetailsObj.customerDoc?.file) {
      formData.append('custmerDAdharFiles', this.customerDetailsObj.customerDoc?.file);
    }

    // other docs (customer)
    this.customerDetailsObj.userDocs.map(v => {
      if (v.file) {
        formData.append('custmerDFiles', v.file)
      }
    })

    // supplier file
    this.supplierDetailsObj.supplierDoc.map(v => {
      if (v.file) {
        formData.append('supplerDFiles', v.file)
      }
    })

    //CP
    this.regObj.contactPersonList = [];
    if (this.contactPersonList) {
      const param = Serialize(this.contactPersonList) as RegistrationCP[]
      this.regObj.contactPersonList = Serialize(param)
    }

    //GST
    this.regObj.gstList = [];
    if (!this.utilsService.isEmptyObjectOrNullUndefined(this.gstList)) {
      for (let i = 0; i < this.gstList.length; i++) {
        const a = this.gstList[i];
        const noMarkAsPrimaryGST1 = a.gstDetails.every(v => !v.isDefault);
        if (noMarkAsPrimaryGST1) {
          this.utilsService.toasterService.error('Atleast one billing address is required should be marked as default in all GSTs', '', {
            positionClass: 'toast-top-right',
            closeButton: true,
            timeOut: 10000
          });
          return;
        }
      }
      this.gstList.forEach(a => Serialize(a.gstDetails))
      const param = Serialize(this.gstList)
      param.forEach(a => {
        a.isActive = (a.isActive ? 1 : 0 as any)
        a.gstDetails.forEach(v => {
          v.isActive = (v.isActive ? 1 : 0 as any)
          v.isDefault = (v.isDefault ? 1 : 0 as any)
        })
      })
      this.regObj.gstList = Serialize(param)
    }

    //SA
    this.regObj.shippingAddressList = [];
    if (!this.utilsService.isEmptyObjectOrNullUndefined(this.shippingAddressRequestList)) {
      const noMarkAsPrimarySA = this.shippingAddressRequestList.every(v => !v.isDefault)
      if (noMarkAsPrimarySA) {
        this.utilsService.toasterService.error('Atleast one shipping address should be marked as default', '', {
          positionClass: 'toast-top-right',
          closeButton: true,
          timeOut: 10000
        });
        return;
      }

      const param = Serialize(this.shippingAddressRequestList) as RegistrationSA[]
      param.forEach(a => {
        a.isActive = (a.isActive ? 1 : 0 as any)
        a.isDefault = (a.isDefault ? 1 : 0 as any)
      })
      this.regObj.shippingAddressList = Serialize(param)
    }

    //Associate Item
    if (this.associatedItems) {
      this.associatedItems.forEach(a => {
        if (a.images) {
          a.images.map(a => {
            if (a.file) {
              formData.append('associatedItemFiles', a.file);
            }
          })
        }
      })
    }

    this.regObj.associatedItems = [];
    if (!this.utilsService.isEmptyObjectOrNullUndefined(this.associatedItems)) {

      let imageIndex = 0;
      this.associatedItems.forEach(a => {
        const transformedMapping = {
          index: a.images?.filter(a => !a.id).map((image) => {
            const currentImageIndex = imageIndex++;
            return image.file ? currentImageIndex : null
          })
        }
        a.fileIndexes = Serialize(transformedMapping.index)
      })
      const param = Serialize(this.associatedItems) as RegistrationAI[];
      this.regObj.associatedItems = Serialize(param)
    }

    if (isDraft) {
      this.regObj.isSaveAsDraft = true
    }
    else {
      this.regObj.isSaveAsDraft = false;
    };

    this.regObj.customerDetail = this.customerDetailsObj ? Serialize(this.customerDetailsObj) : null;
    this.regObj.supplierDetail = this.supplierDetailsObj ? Serialize(this.supplierDetailsObj) : null;
    this.regObj.otherDetail = this.otherDetailsObj ? Serialize(this.otherDetailsObj) : null;
    this.regObj.bankDetail = Serialize(this.bankDetailsObj);
    this.regObj.deleteAssociateIds = Serialize(this.regObj.deleteAssociateIds.filter(a => a !== null));
    this.regObj.deleteShippingIds = Serialize(this.regObj.deleteShippingIds.filter(a => a !== null));
    this.regObj.deleteRegistrationDocIds = Serialize(this.regObj.deleteRegistrationDocIds.filter(a => a !== null));
    this.regObj.deleteContactPersonIds = Serialize(this.regObj.deleteContactPersonIds.filter(a => a !== null));
    this.regObj.deleteGstDetailIds = Serialize(this.regObj.deleteGstDetailIds.filter(a => a !== null));
    this.regObj.deleteGstIds = Serialize(this.regObj.deleteGstIds.filter(a => a !== null));
    const param = Serialize(this.regObj) as Registration;

    param.isActive = Serialize(param.isActive ? 1 : 0 as any)
    delete param.registrationDocsList

    formData.set('registrationJson', JSON.stringify(param));

    this.utilsService.postMethodAPI(true, this.utilsService.serverVariableService.REG_SAVE_EDIT_DELETE, formData, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.utilsService.redirectTo('/users/registration/')
      }
    })
  }

  ////Contact Person
  get contactPerson() {
    return (this.regForm.get('contactPerson') as FormArray);
  }

  addContactPerson() {
    const itemGroup = this.fb.group({
      firstNameOfPerson: [null, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.PATTERN_FOR_ALPHABATES_AND_SPACE)])],
      lastNameOfPerson: [null, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.PATTERN_FOR_ALPHABATES_AND_SPACE)])],
      designation: [null, Validators.compose([Validators.required])],
      emailOfPerson: [null, Validators.compose([Validators.required])],
      countryIdOfPerson: [null, Validators.compose([Validators.required])],
      phoneNo: [null, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.PATTERN_FOR_PHONE_NO)])]
    });

    this.contactPerson.push(itemGroup);
    const obj = new RegistrationCP()
    obj.Country = this.utilsService.filterIsActive(this.dropdown?.Country, null);
    this.contactPersonList.push(obj)
  }

  openRemoveCPModal(index: number) {
    this.selectedCPIndex = index;
    this.cpDeleteModal.show();
  }

  removeCP() {
    if(this.contactPersonList.at(this.selectedCPIndex)?.id) {
      this.regObj.deleteContactPersonIds.push(this.contactPersonList.at(this.selectedCPIndex).id)
    }
    this.contactPerson.removeAt(this.selectedCPIndex);
    this.contactPersonList.splice(this.selectedCPIndex, 1);
    this.cpDeleteModal.hide();
  }

  ///Shipping Address
  get shippingAddress() {
    return (this.regForm.get('shippingAddress') as FormArray);
  }

  addShippingAddress() {
    const group = this.fb.group({
      addressLineName: [null, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.ONLY_SPACE_NOT_ALLOW)])],
      isDefault: [false, Validators.compose([Validators.required])],
      countryId: [null, Validators.compose([Validators.required])],
      stateId: [null, Validators.compose([Validators.required])],
      cityId: [null, Validators.compose([Validators.required])],
      isActive: [true, Validators.compose([Validators.required])],
      zipCode: [false, Validators.compose([Validators.required])]
    });
    this.shippingAddress.push(group);
    const obj = new RegistrationSA()
    obj.countryList = this.utilsService.filterIsActive(this.dropdown?.Country, null);
    obj.isActive = true;
    this.shippingAddressRequestList.push(obj)
  }

  openRemoveSAModal(index: number) {
    this.seletedSPIndex = index;
    this.spDeleteModal.show();
  }

  removeSA() {
    let id = this.shippingAddressRequestList.at(this.seletedSPIndex).id
    if (id) {
      this.regObj.deleteShippingIds.push(id)
    }
    this.shippingAddress.removeAt(this.seletedSPIndex);
    this.shippingAddressRequestList.splice(this.seletedSPIndex, 1);
    this.spDeleteModal.hide();
  }

  //GST
  get gst() {
    return (this.regForm.get('gst') as FormArray);
  }

  addGST() {
    const group = this.fb.group({
      gstNo: [null, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.PATTERN_FOR_GST_NO)])],
      billingAddress: [null],
      reason: [null],
      isActive: [true],
      gstDetails: this.fb.array([])
    });
    this.gst.push(group);
    const obj = new RegistrationGST()
    this.gstList.push(obj)
    //
    const currentIndex = this.gst.length - 1;
    this.addGstDetail(currentIndex)
  }

  openRemoveGSTModal(index: number) {
    this.seletedGSTIndex = index;
    this.gstDeleteModal.show();
  }

  removeGST() {
    let id = this.gstList.at(this.seletedGSTIndex).id;
    if (id) {
      this.regObj.deleteGstDetailIds.push(id)
    }
    this.gst.removeAt(this.seletedGSTIndex);
    this.gstList.splice(this.seletedGSTIndex, 1);
    this.gstDeleteModal.hide();
  }

  ////// gst details

  addGstDetail(gstIndex: number): void {
    const gstGroup = this.gst.at(gstIndex) as FormGroup;
    const gstDetails = gstGroup.get('gstDetails') as FormArray;
    const group = this.fb.group({
      addressLineName: [null, Validators.compose([Validators.required])],
      zipCode: [null, Validators.compose([Validators.required])],
      isDefault: [null],
      countryId: [null, Validators.compose([Validators.required])],
      stateId: [null, Validators.compose([Validators.required])],
      cityId: [null, Validators.compose([Validators.required])],
      isActive: [null],
    });
    gstDetails.push(group);
    const obj = new RegistrationGSTDetails()
    obj.countryDropdown = this.utilsService.filterIsActive(this.dropdown?.Country, null);
    this.gstList[gstIndex].gstDetails.push(obj)
  }

  openRemoveGSTDetailModal(gstIndex: number, index: number) {
    this.seletedGSTIndex = gstIndex;
    this.selectedGSTDetailIndex = index;
    this.gstDetailsDeleteModal.show();
  }

  removeGstDetail(): void {
    if (this.gstList.at(this.seletedGSTIndex)?.gstDetails[this.selectedGSTDetailIndex]?.id) {
      this.regObj.deleteGstDetailIds.push(this.gstList.at(this.seletedGSTIndex).gstDetails[this.selectedGSTDetailIndex].id)
    }
    const gstGroup = this.gst.at(this.seletedGSTIndex) as FormGroup;
    const gstDetails = gstGroup.get('gstDetails') as FormArray;
    gstDetails.removeAt(this.selectedGSTDetailIndex);
    this.gstList[this.seletedGSTIndex].gstDetails.splice(this.selectedGSTDetailIndex, 1);
    this.gstDetailsDeleteModal.hide();
  }

  // Associate items
  get items() {
    return (this.regForm.get('items') as FormArray);
  }

  addAssociateItems() {
    const group = this.fb.group({
      supplierSku: [null, Validators.compose([Validators.required])],
      cartonLength: [null, Validators.compose([Validators.pattern(this.utilsService.validationService.ONLY_GREATER_THAN_ZERO)])],
      cartonWidth: [null, Validators.compose([Validators.pattern(this.utilsService.validationService.ONLY_GREATER_THAN_ZERO)])],
      cartonHeight: [null, Validators.compose([Validators.pattern(this.utilsService.validationService.ONLY_GREATER_THAN_ZERO)])],
      cartonWeight: [null, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.ONLY_GREATER_THAN_ZERO)])],
      cbm: [{value: null, disabled: true}],
      cartonQuantity: [null, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.ONLY_GREATER_THAN_ZERO)])],
      pricePerItem: [null, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.ONLY_GREATER_THAN_ZERO)])],
      pricePerCarton: [null, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.ONLY_GREATER_THAN_ZERO)])],
      tag: [null, Validators.compose([Validators.required])],
      colorIds: [null, Validators.compose([Validators.required])],
      englishComment: [null, Validators.compose([Validators.required])],
      chinaComment: [null, Validators.compose([Validators.required])],
      measurementCode: [null, Validators.compose([Validators.required])],
      itemId: [null, Validators.compose([Validators.required])],
      unitId: [null],
      cartonWeightDim: [null, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.ONLY_GREATER_THAN_ZERO)])],
      isActive: [null]
    });
    this.items.push(group);
    const obj = new RegistrationAI()
    obj.isActive = true;
    obj.itemDropdown = this.utilsService.filterIsActive(this.itemDropdown, null);
    // obj.colorDropdown = this.utilsService.filterIsActive(this.dropdown?.Color, null);
    obj.unitDropdown = this.utilsService.filterIsActive((this.dropdown?.Unit || []), null);
    obj.cartownWdropdown = this.utilsService.filterIsActive(this.dropdown?.Unit, null);

    // filter unit dropdown acc to unit type
    if (obj.unitDropdown) {
      obj.unitDropdown = (obj.unitDropdown.filter(d => d?.unitMasterCategory?.value == 'LENGTH'))
      obj.unitId = (obj.unitDropdown || []).find(unit => (unit.shortCode)?.toLowerCase() == ('CM')?.toLowerCase())?.id;
    }
    if (obj.cartownWdropdown) {
      obj.cartownWdropdown = obj.cartownWdropdown.filter(d => d?.unitMasterCategory?.value == 'WEIGHT')
      obj.cartonWeightDim = (obj.cartownWdropdown || []).find(unit => (unit.shortCode)?.toLowerCase() == ('KG')?.toLowerCase())?.id;
    }
    this.associatedItems.push(obj)
  }

  openRemoveAIModal(index: number) {
    this.selectedItemIndex = index;
    this.associateItemDeleteModal.show();
  }

  removeAI() {
    if (this.associatedItems.at(this.selectedItemIndex)?.id) {
      this.regObj.deleteAssociateIds.push(this.associatedItems.at(this.selectedItemIndex).id)
    }
    this.items.removeAt(this.selectedItemIndex);
    this.associatedItems.splice(this.selectedItemIndex, 1);
    this.associateItemDeleteModal.hide();
  }

  openRemoveImageItemModal(index: number, childIndex: number) {
    this.selectedItemIndex = index;
    this.selecteditemImgIndex = childIndex;
    this.itemImgDeleteModal.show();
  }

  removeImageItem() {
    if (this.associatedItems[this.selectedItemIndex].images.at(this.selecteditemImgIndex)?.id) {
      this.regObj.deleteRegistrationDocIds.push(this.associatedItems[this.selectedItemIndex].images.at(this.selecteditemImgIndex)?.id)
    }
    this.associatedItems[this.selectedItemIndex].images.splice(this.selecteditemImgIndex, 1);
    this.itemImgDeleteModal.hide();
  }

  onAddImageToItem(event: any, index: number): void {
    if (event.target.files && event.target.files[0]) {
      let fileData = null;
      const max_file_size = 5242880;
      let selectedFile = Array.from(event.target.files || []);;
      if (selectedFile.length) {
        const obj = selectedFile.map((file: File) => {
          const ext = file.name.substr(file.name.lastIndexOf('.') + 1);
          const ext1 = (ext).toLowerCase();
          if (ext1 === 'jpeg' || ext1 === 'png' || ext1 === 'jpg' || ext1 === 'jfif' || ext1 === 'mp4' || ext1 === 'webp' || ext1 === 'm4v' || ext1 === 'avif') {
            if (file.size > max_file_size) {
              this.utilsService.toasterService.error(this.utilsService.validationService.IMAGE_MAX_FILE_SIZE)
            } else {
              const fileUrl = URL.createObjectURL(file);
              fileData = {
                id: null,
                originalname: fileUrl,
                fileName: file.name,
                file: file,
              };
              this.associatedItems[index]?.images.push(fileData);
            }
          }
          else {
            this.utilsService.toasterService.error(this.utilsService.validationService.IMAGE_INVALID_EXTENSION)
          }
        });
      }
    }
  }

  // on change fields checkboxes
  onChangeWhatspp(type: boolean | number) {
    this.regObj.countyForWhatsAppId = null;
    this.regObj.whatsAppNo = null;
    this.regForm.controls['countryIdForWhatsApp'].reset();
    this.regForm.controls['countryIdForWhatsApp'].clearValidators();
    this.regForm.controls['whatsAppNo'].reset();
    this.regForm.controls['whatsAppNo'].clearValidators();
    if (type) {
      this.regForm.controls['countryIdForWhatsApp'].setValidators([Validators.compose([Validators.required])]);
      this.regForm.controls['whatsAppNo'].setValidators([Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.PATTERN_FOR_PHONE_NO)])]);
    }
    this.regForm.controls['whatsAppNo'].updateValueAndValidity();
    this.regForm.controls['countryIdForWhatsApp'].updateValueAndValidity();
  }

  onChangeTelegram(type: boolean | number) {
    this.regObj.countyForTelegramId = null;
    this.regObj.telegramNo = null;
    this.regForm.controls['countryIdForTelegram'].reset();
    this.regForm.controls['countryIdForTelegram'].clearValidators();
    this.regForm.controls['telegramNo'].reset();
    this.regForm.controls['telegramNo'].clearValidators();
    if (type) {
      this.regForm.controls['countryIdForTelegram'].setValidators([Validators.compose([Validators.required])]);
      this.regForm.controls['telegramNo'].setValidators([Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.PATTERN_FOR_PHONE_NO)])]);
    }
    this.regForm.controls['telegramNo'].updateValueAndValidity();
    this.regForm.controls['countryIdForTelegram'].updateValueAndValidity();
  }

  //search
  onSearch(event: any, type: string) {
    const searchTerm = event.term;
    let exactMatch = null;
    switch (type) {
      case 'p':
        exactMatch = this.dropdown.phone.find(
          item => item.countryExtension === searchTerm
        );
        if (exactMatch) {
          this.regObj.countryId = exactMatch.id;
        }
        break;
      case 'w':
        exactMatch = this.dropdown.phoneWhatsApp.find(
          item => item.countryExtension === searchTerm
        );
        if (exactMatch) {
          this.regObj.countyForWhatsAppId = exactMatch.id;
        }
        break;
      case 't':
        exactMatch = this.dropdown.phoneTelegram.find(
          item => item.countryExtension === searchTerm
        );
        if (exactMatch) {
          this.regObj.countyForTelegramId = exactMatch.id;
        }
        break;

      default:
        break;
    }
  }

  // Measurement code will be generated during adding associate item as follows [Item SKU_Qty_Supp. code_Tag] not auto change when other fields change
  generateMeasurementCode(item: RegistrationAI, index: number) {
    const skuId = (item.itemDropdown || []).find(item => item.id === this.associatedItems[index].itemId).skuId || '';
    const tag = this.associatedItems[index].tag || '';
    const cartonQuantity = this.associatedItems[index].cartonQuantity || '';
    const supplierCode = this.regForm.controls['shortCode'].value || '';

    let measurementCode = `${skuId}`;
    if (cartonQuantity) measurementCode += `_${cartonQuantity}`;
    // if (supplierCode) measurementCode += `_${supplierCode}`;
    if (tag) measurementCode += `_${tag}`;
    this.items.at(index).get('measurementCode').setValue(measurementCode);
  }

  // onSupplierChange() {
  //   for(const item of this.associatedItems) {
  //     this.generateMeasurementCode(item, this.associatedItems.indexOf(item));
  //   }
  // }
}
