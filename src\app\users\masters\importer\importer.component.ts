import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Deserialize, Serialize } from 'cerialize';
import { debounceTime, distinctUntilChanged, Subject } from 'rxjs';
import { Importer } from 'src/app/models/Importer';
import { activeInactiveStatus, IMPORTER_MASTER } from 'src/app/shared/constants/constant';
import { UtilsService } from 'src/app/shared/services/utils.service';
import { saveAs } from 'file-saver';
declare var window: any;

@Component({
  selector: 'app-importer',
  templateUrl: './importer.component.html',
  styleUrls: ['./importer.component.scss']
})
export class ImporterComponent implements OnInit {

  activeInactiveStatus = activeInactiveStatus;
  importerMasterModal: any;
  deleteImporterMasterModal: any;

  dropdown: any;

  importerTH: any[] = []
  selectedIds: any[] = []
  importerList: Importer[] = [];
  importerObj = new Importer();
  activeFlag: boolean = true;
  importerGroup: FormGroup;
  flagForSelectAll: boolean = false;

  pageNo: number = 1;
  pageSize: string = '100';
  sortOrder: string;
  sortColumn: string;
  totalData: number;
  statusForModal: string;

  enumForSortOrder = this.utilsService.enumForSortOrder;
  searchSubject: Subject<string> = new Subject<string>();
  searchText: string;
  searchPriceSubject: Subject<any> = new Subject<any>();
  searchPrice: any;
  pagination: any;

  constructor(public utilsService: UtilsService, private fb: FormBuilder) {
    this.importerTH = IMPORTER_MASTER
  }

  ngOnInit() {

    this.searchSubject.pipe(debounceTime(300), distinctUntilChanged()).subscribe((res: string) => {
      this.pageNo = 1;
      // this.pageSize = '100';
      this.searchText = null;
      this.searchText = res;
      this.getImporterList();
    });


    this.getImporterList();
    this.importerFormGroup();

    this.importerMasterModal = new window.bootstrap.Modal(
      document.getElementById('importerMasterModal')
    );

    this.deleteImporterMasterModal = new window.bootstrap.Modal(
      document.getElementById('deleteImporterMasterModal')
    );

    document.getElementById('importerMasterModal').addEventListener('shown.bs.modal', () => {
      document.getElementById('f').focus();
    });
  }

  getImporterList() {

    this.flagForSelectAll = false;
    this.totalData = 0;
    this.selectedIds = []

    const param = {
      pageNo: this.pageNo,
      pageSize: this.pageSize,
      sortOrder: this.sortOrder,
      sortColumn: this.sortColumn,
      searchText: this.searchText,
      priceKg: this.searchPrice,
      isActive: this.activeFlag
    }

    this.utilsService.postMethodAPI(false, this.utilsService.serverVariableService.IMPORTER_LISTING, param, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.importerList = Deserialize(response.content, Importer);
        this.totalData = response.totalElements;
        this.pagination = response;
      } else {
        this.importerList = [];
      }
    })

  }

  importerFormGroup() {
    this.importerGroup = this.fb.group({
      name: ['', Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.PATTERN_FOR_ALPHABATES_AND_SPACE)])],
      l_name: ['', Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.PATTERN_FOR_ALPHABATES_AND_SPACE)])],
      mobile: ['', Validators.compose([Validators.pattern(this.utilsService.validationService.PATTERN_FOR_PHONE_NO)])],
      email: ['', Validators.compose([Validators.pattern(this.utilsService.validationService.PATTERN_FOR_EMAIL)])],
      short_code: [''],
      address: [''],
      country: [''],
      state: [''],
      city: [''],
      zip: [''],
      status: [true, Validators.compose([Validators.required])],
      countryExtension: [null]
    })
  }

  addPageSizeData(event) {
    this.pageNo = 1;
    this.pageSize = event;
    this.getImporterList();
  }

  pageNumber(event) {
    this.pageNo = event
    this.getImporterList();
  }

  trackBy(index: number, item: Importer): number {
    return item.id;
  }

  openAddEditModal(obj: Importer, status: string) {
    this.importerObj = new Importer();
    this.importerGroup.reset();
    this.statusForModal = status;

    if (this.statusForModal === 'Add') {
      setTimeout(() => {
        this.importerObj.isActive = true;
        this.getRequiredData();
      }, 100);
    }

    if (obj) {
      setTimeout(() => {
        this.importerObj = Serialize(obj);
        if (obj.countryMaster) {
          this.importerObj.countryId = Serialize(obj.countryMaster.id)
        }
        if (obj.stateMaster) {
          this.importerObj.stateId = Serialize(obj.stateMaster.id)
        }
        if (obj.cityMaster) {
          this.importerObj.cityId = Serialize(obj.cityMaster.id)
        }
        if (obj.contactExtension) {
          this.importerObj.contactCountryExtensionId = Serialize(obj.contactExtension.id)
        }
        this.getRequiredData();
      }, 100);
    }
    this.importerMasterModal.show();
  }

  onSaveImporter() {

    if (this.importerGroup.invalid) {
      this.importerGroup.markAllAsTouched();
      return;
    }

    let param = this.utilsService.trimObjectValues(Serialize(this.importerObj));

    this.utilsService.postMethodAPI(true, this.utilsService.serverVariableService.IMPORTER_SAVE_EDIT_DELETE, param, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.importerMasterModal.hide();
        this.getImporterList();
      }
    })

  }


  openDeleteImporterModal(obj: Importer) {
    this.importerObj = Serialize(obj)
    this.deleteImporterMasterModal.show();
  }

  deleteImporterCompany() {

    this.utilsService.deleteMethodAPI(true, this.utilsService.serverVariableService.IMPORTER_SAVE_EDIT_DELETE + `?id=${this.importerObj.id}`, {}, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.deleteImporterMasterModal.hide();
        if(!this.pagination?.first && this.pagination?.last && this.pagination?.numberOfElements === 1) {
          this.pageNo = this.pageNo - 1
        }
        this.getImporterList();
      }
    })
  }

  // Select Deselect 

  selectAll() {
    if (this.flagForSelectAll === true) {
      this.selectedIds = new Array<string>();
    }
    const obj = this.importerList.filter((val, index) => {
      if (this.flagForSelectAll === true) {
        val['isSelected'] = true;
        this.selectedIds.push(val.id);
      } else {
        val['isSelected'] = false;
        this.selectedIds.splice(index, 1);
      }
    });
    if (this.flagForSelectAll === false) {
      this.selectedIds = new Array<string>();
    }
  }

  selectUnselect(id: number, index, value) {

    const isSelected = this.selectedIds.includes(id);

    if (value && !isSelected) {

      this.selectedIds.push(id);

    } else if (!value && isSelected) {

      const assetIndex = this.selectedIds.indexOf(id);
      this.selectedIds.splice(assetIndex, 1);
    }
    this.flagForSelectAll = this.checkIfAllSelected();
  }

  checkIfAllSelected() {
    let flag = true;
    this.importerList.filter((val, index) => {
      if (val['isSelected'] === false) {
        flag = false;
        return;
      }
    });
    return flag;
  }

  // sorting 

  onSortTH(key) {

    if (this.utilsService.isEmptyObjectOrNullUndefined(this.importerList)) {
      return;
    }

    if (key === this.sortColumn) {
      if (this.sortOrder === this.enumForSortOrder.A) {
        this.sortOrder = this.enumForSortOrder.D;
      } else if (this.sortOrder === this.enumForSortOrder.D) {
        this.sortOrder = this.enumForSortOrder.A;
      }
    } else {
      this.sortOrder = this.enumForSortOrder.D;
    }

    this.sortColumn = key;
    this.getImporterList();
  }

  //Search
  onSearch(event: any) {
    this.searchSubject.next(event.target.value);
  }

  // status 

  onChangeStatus(item: Importer, value, index) {

    this.utilsService.putMethodAPI(true, this.utilsService.serverVariableService.IMPORTER_STATUS_CHANGE + `${item.id}/${value}`, {}, '', (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response.data)) {
        this.importerList[index].isActive = value
      } else {
        this.importerList[index].isActive = !value
      }
      if(!this.pagination?.first && this.pagination?.last && this.pagination?.numberOfElements === 1 && this.activeFlag) {
        this.pageNo = this.pageNo - 1
      }
      this.getImporterList()
    }, true);
  }

  // active/inactive

  onChangeActive() {
    this.pageNo = 1;
    // this.pageSize = '100';
    this.getImporterList();
  }

  // export 

  exportReport() {

    const param = {
      ids: this.selectedIds ? this.selectedIds : [],
      searchText: this.searchText,
      isActive: this.activeFlag,
      sortOrder: this.sortOrder,
      sortColumn: this.sortColumn,
    }

    this.utilsService.exportReport(param, this.utilsService.serverVariableService.IMPORTER_EXPORT).subscribe((data: any) => {
      saveAs(new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' }), 'Importer Sheet');
    });
  }

  // dropdown

  onChangeCountryState(flag: boolean) {
    if (flag) {
      this.importerObj.stateId = null;
      this.importerObj.cityId = null;
      this.getRequiredData()
    }
    else {
      this.importerObj.cityId = null;
      this.getRequiredData()
    }
  }

  getRequiredData() {

    this.dropdown = null;
    const param = {
      countryIds: this.importerObj.countryId ? [this.importerObj.countryId] : [],
      stateIds: this.importerObj.stateId ? [this.importerObj.stateId] : [],
    }

    this.utilsService.postMethodAPI(false, this.utilsService.serverVariableService.COUNTRY_STATE_CITY_DROPDOWN, param, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.dropdown = response;
        this.dropdown.countryPh = Serialize(this.dropdown.country)
        this.dropdown.country = this.dropdown?.country ? this.utilsService.transformDropdownItems(this.dropdown?.country) : [];
        this.dropdown.countryPh = this.dropdown?.countryPh ? this.utilsService.transformDropdownItems(this.dropdown?.countryPh) : [];
        this.dropdown.state = this.dropdown?.state ? this.utilsService.transformDropdownItems(this.dropdown?.state) : [];
        this.dropdown.city = this.dropdown?.city ? this.utilsService.transformDropdownItems(this.dropdown?.city) : [];
        //
        this.dropdown.country = this.utilsService.filterIsActive(this.dropdown?.country, this.importerObj.countryId ? this.importerObj.countryId : null);
        this.dropdown.state = this.utilsService.filterIsActiveLV((this.dropdown?.state || []), this.importerObj.stateId ? this.importerObj.stateId : null);
        this.dropdown.city = this.utilsService.filterIsActiveLV((this.dropdown?.city || []), this.importerObj.cityId ? this.importerObj.cityId : null);
        this.dropdown.countryPh = this.utilsService.filterIsActive((this.dropdown?.countryPh || []), this.importerObj.contactCountryExtensionId ? this.importerObj.contactCountryExtensionId : null);
        //
      }
    })
  }

  //
  onSearchCountryCode(event: any) {
    const searchTerm = event.term;
    let exactMatch = null;
    exactMatch = this.dropdown?.countryPh.find( item => item.countryExtension === searchTerm);
    if (exactMatch) {
      this.importerObj.contactCountryExtensionId = exactMatch.id;
    }
  }
}
