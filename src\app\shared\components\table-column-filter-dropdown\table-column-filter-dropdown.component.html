<div class="table-filter">
  <div class="dropdown">
    <button id="tbl-filter-dropdown" class="btn btn-icon btn-outline-white btn-sm" data-bs-toggle="dropdown"
      aria-expanded="false" data-bs-auto-close="outside" data-bs-popper-config='{"strategy":"fixed"}'
      ngbTooltip="Columns Filter" placement="left" container="body" triggers="hover">
      <i class="bi bi-sliders"></i>
    </button>

    <div class="table-filter-dialog dropdown-menu" aria-labelledby="tbl-filter-dropdown">
      <div class="table-filter-content">
        <div class="table-filter-header">
          <h5 class="filter-title">Hide & Show Columns</h5>
          <button type="button" class="btn-close column-filter-dropdown-close" aria-label="Close"
            data-bs-toggle="dropdown">
            <i class='th th-close'></i></button>
        </div>
        <div class="table-filter-body">
          <ul class="filter-items-list drag-control-wrapper2">
            <li class="drag-control-item" *ngFor="let item of [1,2,3,4]">
              <div class="drag-control-icon">
                <img src="assets/images/drag.svg" alt="valamji">
              </div>
              <div class="drag-control-button">
                <i class="th th-outline-lock"></i>
              </div>
              <div class="drag-control-label">
                <p>Name & SKU</p>
              </div>
            </li>
            <li class="drag-control-item selected" *ngFor="let item of [1,2,3,4]">
              <div class="drag-control-icon">
                <img src="assets/images/drag.svg" alt="valamji">
              </div>
              <div class="drag-control-button">
                <div class="checkbox checkbox-primary checkbox-small form-check-inline">
                  <input type="checkbox" id="checkbox1" class="material-inputs filled-in" checked />
                  <label for="checkbox1"> </label>
                </div>
              </div>
              <div class="drag-control-label">
                <p>Suppliers</p>
              </div>
            </li>
            <li class="drag-control-item" *ngFor="let item of [1,2,3,4]">
              <div class="drag-control-icon">
                <img src="assets/images/drag.svg" alt="valamji">
              </div>
              <div class="drag-control-button">
                <div class="checkbox checkbox-primary checkbox-small form-check-inline">
                  <input type="checkbox" id="checkbox1" class="material-inputs filled-in" />
                  <label for="checkbox1"> </label>
                </div>
              </div>
              <div class="drag-control-label">
                <p>Description</p>
              </div>
            </li>
          </ul>
        </div>
        <div class="table-filter-footer">
          <div class="table-filter-footer-group full-width-btn">
            <button type="button" class="btn btn-sm btn-primary btn-icon-text"> <i
                class="th th-outline-tick-circle"></i>
              Save</button>
            <button type="button" class="btn btn-sm btn-outline-white column-filter-dropdown-close">Cancel</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>