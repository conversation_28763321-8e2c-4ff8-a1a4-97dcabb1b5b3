<div class="row" [formGroup]="regForm">
  <div class="col-md-4">

    <div class="form-group theme-ngselect  form-group-inline-control required">
      <label class="form-label">Assign Sales Person</label>
      <div class="form-control-wrapper ">
        <ng-select (clear)="clearAssignUser()" placeholder="Select Assign Sales Person" [multiple]="false"
          [clearable]="true" formControlName="userId" [items]="dropdown?.AssignSalesPerson" bindLabel="label"
          bindValue="value" [(ngModel)]="obj.userId">
        </ng-select>
        <div class="message error-message"
          *ngIf="regForm.controls['userId'].hasError('required') &&  regForm.controls['userId'].touched">
          {{utilsService.validationService.ASSIGN_SALES_PERSON_REQUIRED}}
        </div>
      </div>
    </div>
    <div class="form-group  form-group-inline-control ">
      <label class="form-label">Credit Limit</label>
      <div class="form-control-wrapper ">
        <div class="input-group">
          <span class="input-group-text">Rs</span>
          <input [maxlength]="utilsService.validationService.MAX_10" mask="separator.3" thousandSeparator=""
            formControlName="creditLimit" [(ngModel)]="obj.creditLimit" type="text" placeholder="Enter Amount"
            class="form-control" (change)="onCreditLimitChange()">
        </div>
      </div>
    </div>
    <div class="form-group  form-group-inline-control " *ngIf="regId && obj.creditLimit && obj.creditLimit != this.obj.creditLimitT">
      <label class="form-label">Reason to change <br/>(Credit Limit)</label>
      <div class="form-control-wrapper ">
        <input [maxlength]="utilsService.validationService.MAX_100" type="text" placeholder="" class="form-control"
          [(ngModel)]="obj.creditReason" formControlName="creditReason" placeholder="Enter Reason to change">
        <div class="message error-message"
          *ngIf="regForm.controls['creditReason'].hasError('required') &&  regForm.controls['creditReason'].touched">
          {{utilsService.validationService.REASON_TO_CHANGE_CL_REQ}}
        </div>
        <div class="message error-message"
          *ngIf="!regForm.controls['creditReason'].hasError('required') && !regForm.controls['creditReason'].valid && regForm.controls['creditReason'].touched">
          {{utilsService.validationService.REASON_TO_CHANGE_CL_INVALID}}
        </div>
      </div>
    </div>
    <div class="form-group  form-group-inline-control ">
      <label class="form-label">Credit Days</label>
      <div class="form-control-wrapper ">
        <div class="input-group">
          <span class="input-group-text">Days</span>
          <input [maxlength]="utilsService.validationService.MAX_8" mask="separator.0" thousandSeparator=""
            (change)="onDaysChange()" formControlName="days" [(ngModel)]="obj.days" type="text" class="form-control">
        </div>
      </div>
    </div>
    <div class="form-group  form-group-inline-control " *ngIf="regId && obj.days && obj.days != obj.daysT">
      <label class="form-label">Reason to change <br/>(Credit Days)</label>
      <div class="form-control-wrapper ">
        <input [maxlength]="utilsService.validationService.MAX_100" [(ngModel)]="obj.daysReason"
          formControlName="daysReason" type="text" placeholder="" class="form-control" placeholder="Enter Days Reason">
        <div class="message error-message"
          *ngIf="regForm.controls['daysReason'].hasError('required') &&  regForm.controls['daysReason'].touched">
          {{utilsService.validationService.REASON_TO_CHANGE_CD_REQ}}
        </div>
        <div class="message error-message"
          *ngIf="!regForm.controls['daysReason'].hasError('required') && !regForm.controls['daysReason'].valid && regForm.controls['daysReason'].touched">
          {{utilsService.validationService.REASON_TO_CHANGE_CD_INVALID}}
        </div>
      </div>
    </div>
    <div class="form-group  form-group-inline-control ">
      <label class="form-label">Add to Blacklist</label>
      <div class="form-control-wrapper ">
        <div class="form-group-button">
          <div class="checkbox checkbox-primary checkbox-small w-auto">
            <input (change)="onChangeBlockBlackList(obj.isBlacklist, 'blacklistReason')" [(ngModel)]="obj.isBlacklist"
              formControlName="isBlacklist" type="checkbox" id="blacklist" class="material-inputs filled-in">
            <label for="blacklist" class="w-auto"></label>
          </div>
          <input *ngIf="obj.isBlacklist" formControlName="blacklistReason" [(ngModel)]="obj.blacklistReason" type="text"
            placeholder="Enter Blacklist Reason*" class="form-control"
            [maxlength]="utilsService.validationService.MAX_100">
        </div>
        <div class="message error-message ms-4"
          *ngIf="regForm.controls['blacklistReason'].hasError('required') &&  regForm.controls['blacklistReason'].touched">
          {{utilsService.validationService.REASON_TO_BLACHANGE_REQ}}
        </div>
        <div class="message error-message ms-4"
          *ngIf="!regForm.controls['blacklistReason'].hasError('required') && !regForm.controls['blacklistReason'].valid && regForm.controls['blacklistReason'].touched">
          {{utilsService.validationService.REASON_TO_CHANGE_INVALID}}
        </div>
      </div>
    </div>
    <div class="form-group  form-group-inline-control d-flex align-items-center">
      <label class="form-label">Add to Blocklist</label>
      <div class="form-control-wrapper ">
        <div class="form-group-button">
          <div class="checkbox checkbox-primary checkbox-small w-auto">
            <input (change)="onChangeBlockBlackList(obj.isBlocklist, 'blocklistReason')" [(ngModel)]="obj.isBlocklist"
              formControlName="isBlocklist" type="checkbox" placeholder="" id="block" class="material-inputs filled-in">
            <label for="block" class="w-auto"></label>
          </div>
          <input *ngIf="obj.isBlocklist" formControlName="blocklistReason" [(ngModel)]="obj.blocklistReason" type="text"
            placeholder="Enter Blocklist Reason*  " class="form-control"
            [maxlength]="utilsService.validationService.MAX_100">
        </div>
        <div class="message error-message ms-4"
          *ngIf="regForm.controls['blocklistReason'].hasError('required') &&  regForm.controls['blocklistReason'].touched">
          {{utilsService.validationService.REASON_TO_BLOCHANGE_REQ}}
        </div>
        <div class="message error-message ms-4"
          *ngIf="!regForm.controls['blocklistReason'].hasError('required') && !regForm.controls['blocklistReason'].valid && regForm.controls['blocklistReason'].touched">
          {{utilsService.validationService.REASON_TO_CHANGE_INVALID}}
        </div>
      </div>
    </div>

  </div>
  <div class="col-md-4">
    <div class="form-group  form-group-inline-control ">
      <label class="form-label">Reference Person </label>
      <div class="form-control-wrapper ">
        <input [maxlength]="utilsService.validationService.MAX_50" [(ngModel)]="obj.referencePerson"
          formControlName="referencePerson" type="text" placeholder="Enter Reference Person" class="form-control">
        <div class="message error-message"
          *ngIf="!regForm.controls['referencePerson'].hasError('required') && !regForm.controls['referencePerson'].valid && regForm.controls['referencePerson'].touched">
          {{utilsService.validationService.REF_PERSON_INVALID}}
        </div>
      </div>
    </div>

    <div class="form-group form-group-inline-control theme-ngselect">
      <div class="form-label">Ref. Person No</div>
      <div class="form-control-wrapper">
        <div class="input-group input-group-select">
          <ng-select class="" placeholder="Ph." [multiple]="false" [clearable]="false" [items]="dropdown?.phoneRef"
            bindLabel="countryExtension" bindValue="id" [(ngModel)]="obj.countryForPersonId"
            formControlName="countryForPerson" (search)="onSearch($event, 'r')">
          </ng-select>
          <input formControlName="personNo" [(ngModel)]="obj.personNo" type="text" placeholder="Enter Phone No."
            class="form-control">
        </div>
        <div class="message error-message"
          *ngIf="!regForm.controls['personNo'].hasError('required') && !regForm.controls['personNo'].valid && regForm.controls['personNo'].touched">
          {{utilsService.validationService.PHONE_NUMBER_INVALID}}
        </div>
      </div>
    </div>

    <div class="form-group form-group-inline-control theme-ngselect">
      <div class="form-label">Send Ledger to mobile</div>
      <div class="form-control-wrapper ">
        <div class="input-group input-group-select">
          <ng-select class="" placeholder="Ph." [multiple]="false" [clearable]="false" [items]="dropdown?.phoneLedger"
            bindLabel="countryExtension" bindValue="id" [(ngModel)]="obj.countryForLedgerId"
            formControlName="countryForLedger" (search)="onSearch($event, 'l')">
          </ng-select>
          <input formControlName="sendLedgerToMobile" [(ngModel)]="obj.sendLedgerToMobile" type="text"
            placeholder="Enter Phone No." class="form-control">
        </div>
        <div class="message error-message"
          *ngIf="!regForm.controls['sendLedgerToMobile'].hasError('required') && !regForm.controls['sendLedgerToMobile'].valid && regForm.controls['sendLedgerToMobile'].touched">
          {{utilsService.validationService.PHONE_NUMBER_INVALID}}
        </div>
      </div>
    </div>

    <div class="form-group  form-group-inline-control ">
      <label class="form-label">Aadhar Front & Back<i class="th th-outline-info-circle ms-1"
          [ngbTooltip]="utilsService.validationService.FILE_INFO" placement="bottom" container="body"
          triggers="hover"></i>
      </label>
      <div class="form-control-wrapper ">
        <button class="btn btn-fileupload btn-fileupload-white w-100 mb-3"><i class="bi bi-upload"></i> Choose Document
          <input #cus_aadhar (change)="onSelectFile($event);cus_aadhar.value=''" type="file" ref="{imageRef}"
            accept="image/x-png,image/jpeg,image/jpg,.pdf">
        </button>
        <div class='attachments-upload-grid-container attachments-upload-grid-container2'>
          <div class='attachments-upload-row'>
            <div class='attachments-upload-col'>
              <div class='card-attachments-upload' *ngIf="obj.customerDoc">
                <div class='attachments-image'
                  *ngIf="utilsService.isImage(obj.customerDoc.fileName ? obj.customerDoc.fileName : obj.customerDoc.originalName)">
                  <img (click)="openLink(obj.customerDoc.formattedName, obj.customerDoc.originalname)" *ngIf="!obj.customerDoc.file"
                    [src]="obj.customerDoc.formattedName ? (utilsService.imgPath + obj.customerDoc.formattedName) : null"
                    alt="valamji" />
                  <img (click)="openLink(null, obj.customerDoc.originalname)" loading="lazy" *ngIf="obj.customerDoc.file"
                    [src]="obj.customerDoc.originalname ? (obj.customerDoc.originalname) : null" alt="valamji" />
                </div>
                <div class='attachments-image'
                  *ngIf="utilsService.isDocument(obj.customerDoc.fileName ? obj.customerDoc.fileName : obj.customerDoc.originalName)">
                  <img (click)="openLink(obj.customerDoc.formattedName, obj.customerDoc.originalname)" *ngIf="!obj.customerDoc.file"
                    src="assets/images/files/file-pdf.svg" alt="valamji" />
                  <img (click)="openLink(null, obj.customerDoc.originalname)" *ngIf="obj.customerDoc.file"
                    src="assets/images/files/file-pdf.svg" alt="valamji" />
                </div>
                <div class="attachments-text"
                  [ngbTooltip]="obj.customerDoc.fileName ? obj.customerDoc.fileName : obj.customerDoc.originalName" placement="bottom"
                  container="body" triggers="hover">
                  <h6 class="file-name">{{obj.customerDoc.fileName ? obj.customerDoc.fileName :
                    obj.customerDoc.originalName}}</h6>
                </div>
                <button (click)="removeFile()" class="btn-close" variant="close"><i class='th th-close'></i></button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="form-group  form-group-inline-control ">
      <label class="form-label">Document Upload<i class="th th-outline-info-circle ms-1"
          [ngbTooltip]="utilsService.validationService.FILE_INFO" placement="bottom" container="body"
          triggers="hover"></i></label>
      <div class="form-control-wrapper ">
        <button class="btn btn-fileupload btn-fileupload-white w-100 mb-3"><i class="bi bi-upload"></i> Choose Document
          <input #docUpload (change)="onAddDocs($event);docUpload.value=''" type="file" ref="{imageRef}"
            accept="image/x-png,image/jpeg,image/jpg,.pdf" [multiple]="true">
        </button>
        <div class='attachments-upload-grid-container attachments-upload-grid-container2'>
          <div class='attachments-upload-row'>
            <div class='attachments-upload-col' *ngFor="let item of obj.userDocs; index as i">
              <div class='card-attachments-upload'>
                <div class='attachments-image'
                  *ngIf="utilsService.isImage(item.fileName ? item.fileName : item.originalName)">
                  <img (click)="openLink(item.formattedName, item.originalname)" *ngIf="!item.file"
                    [src]="item.formattedName ? (utilsService.imgPath + item.formattedName) : null" alt="valamji" />
                  <img (click)="openLink(null, item.originalname)" loading="lazy" *ngIf="item.file"
                    [src]="item.originalname ? (item.originalname) : null" alt="valamji" />
                </div>
                <div (click)="openLink(item.formattedName, item.originalname)" class='attachments-image'
                  *ngIf="utilsService.isDocument(item.fileName ? item.fileName : item.originalName)">
                  <img src="assets/images/files/file-pdf.svg" alt="valamji" />
                </div>
                <div (click)="openLink(item.formattedName, item.originalname)" class='attachments-image'
                  *ngIf="utilsService.isExcel(item.fileName ? item.fileName : item.originalName)">
                  <img src="assets/images/files/file-excel.svg" alt="valamji" />
                </div>
                <div class="attachments-text" [ngbTooltip]="item.fileName ? item.fileName : item.originalName"
                  placement="bottom" container="body" triggers="hover">
                  <h6 class="file-name">{{item.fileName ? item.fileName : item.originalName}}</h6>
                </div>
                <button (click)="removeAttachment(i, item)" class="btn-close" variant="close"><i
                    class='th th-close'></i></button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="form-group  form-group-inline-control ">
      <label class="form-label">Don’t Send Notification</label>
      <div class="form-control-wrapper ">
        <div class="radio radio-primary radio-small form-check-inline" *ngFor="let item of statusYesNo; index as i">
          <input formControlName="isSendNotification" type="radio" id="check-{{i}}" class="material-inputs filled-in"
            [(ngModel)]="obj.isSendNotification" [value]="item.value">
          <label for="check-{{i}}">{{item.label}} </label>
        </div>
      </div>
    </div>

    <div class="form-group  form-group-inline-control  d-flex align-items-center">
      <label class="form-label">Mark As Debtor </label>
      <div class="form-control-wrapper ">
        <div class="checkbox checkbox-primary checkbox-small form-check-inline">
          <input [(ngModel)]="obj.isMarkAsDebtor" formControlName="isMarkAsDebtor" type="checkbox" id="debtor"
            class="material-inputs filled-in">
          <label for="debtor"> </label>
        </div>
      </div>
    </div>

    <div class="form-group  form-group-inline-control d-flex align-items-center ">
      <label class="form-label">New Customer </label>
      <div class="form-control-wrapper ">
        <div class="checkbox checkbox-primary checkbox-small form-check-inline">
          <input [(ngModel)]="obj.isNewCustomer" formControlName="isNewCustomer" type="checkbox" id="customerNew"
            class="material-inputs filled-in">
          <label for="customerNew"></label>
        </div>
      </div>
    </div>

  </div>
</div>