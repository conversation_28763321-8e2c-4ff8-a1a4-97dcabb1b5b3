import { AfterViewInit, Component, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { UtilsService } from '../../services/utils.service';
import { Registration } from 'src/app/models/Registration';
import { NgbDropdown } from '@ng-bootstrap/ng-bootstrap';
import { saveAs } from 'file-saver';
import { TempoExpense } from 'src/app/models/TempoExpense';
import { EnumForPages } from '../../enums/EnumForPages';
import { ContainerExpense } from 'src/app/models/ContainerExpense';

@Component({
  selector: 'app-attachment-download-dropdown',
  templateUrl: './attachment-download-dropdown.component.html',
  styleUrls: ['./attachment-download-dropdown.component.css']
})
export class AttachmentDownloadDropdownComponent implements OnInit {

  enumForPage = EnumForPages;

  @ViewChild('dropdownAtt') dropdown: NgbDropdown;

  @Input('fileList') fileList: any[];
  @Input('obj') obj: Registration;
  @Input('tempoExpense') tempoExpense: TempoExpense | ContainerExpense;
  @Input('local') local: boolean = false;
  @Input('isDelete') isDelete: boolean = true;
  @Input('page') page: string;

  @Input('selectedIndex') selectedIndex: number;

  @Output() openDeleteDocsModal: EventEmitter<any> = new EventEmitter<any>();
  @Output() openRemoveImageItemModal: EventEmitter<any> = new EventEmitter<any>();

  isOpen: boolean = false;

  constructor(public utilsService: UtilsService) { }

  ngOnInit() {
  }

  onDropdownOpenChange(isOpen: boolean): void {
    if (isOpen) {
      this.isOpen = true;
    } else {
      this.isOpen = false;
    }
  }

  closeDropdown(): void {
    if (this.dropdown) {
      this.dropdown.close();
    }
  }

  downloadAttachment(link: any) {
    const filePreview = `${this.utilsService.imgPath}${link.formattedName}`
    saveAs(filePreview, link.originalName);
  }

  openDocModal(index: number) {
    if (this.page === this.enumForPage.EXPENSES) {
      this.openDeleteDocsModal.emit({ index: index, obj: this.tempoExpense })
    }
    else {
      this.openDeleteDocsModal.emit({ index: index, obj: this.obj })
    }
  }

  openLinkLocal(link, newUpload: any, oldFile: any) {
    const filePreview = link ? `${this.utilsService.imgPath}${link}` : null

    if (!this.utilsService.isEmptyObjectOrNullUndefined(newUpload)) {
      window.open(newUpload, "_blank");
      return;
    }

    if (!this.utilsService.isEmptyObjectOrNullUndefined(filePreview)) {
      saveAs(filePreview, oldFile);
      return;
    }
  }

  //local
  openImgItem(childIndex: number) {
    this.openRemoveImageItemModal.emit({index: this.selectedIndex, childIndex: childIndex})
  }
}
