
import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { Injectable, ElementRef, Renderer2 } from '@angular/core';
import { FormGroup, FormControl, FormBuilder, Validators } from '@angular/forms';
import { SafeResourceUrl, DomSanitizer } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { EnumForWarehouseDetailsTab } from '@enums/EnumForWarehouseDetailsTab';
import { Aisle } from '@modal/Aisle';
import { DropOffLocation } from '@modal/DropoffLocation';
import { Racks } from '@modal/Racks';
import { User } from '@modal/User';
import { Warehouse } from '@modal/Warehouse';
import { UtilsService } from '@service/utils.service';
import { Deserialize, Serialize } from 'cerialize';
import { saveAs } from 'file-saver';
import { NgxPrintService } from 'ngx-print';
import { Subscription, Subject } from 'rxjs';
import { activeInactiveStatus } from 'src/app/shared/constants/constant';
@Injectable({
  providedIn: 'root'
})
export class WarehouseService {

  base64SourceSingle: SafeResourceUrl[];

  activeInactiveStatus = activeInactiveStatus

  warehouseList: Warehouse[] = [];
  warehouseForm: FormGroup;
  warehouseObj = new Warehouse();
  flagForPasswordHideShow: boolean;

  //MODALS
  addNewWareHouse: any;
  deleteWarehouseModal: any;
  statusForModal: string;
  showAddEditModal: boolean = false;

  addNewWareHouseDetails: any;
  addEditDropOff: any;
  showAddEditDropOff: boolean = false;
  deleteDropOff: any;

  deleteWarehouseDetailModal: any;

  //DROPDOWNS
  dropdownsList = {
    dropdown: null,
    city: [],
    state: [],
  }

  //Details
  warehouseRemoveModal: any;
  warehouseEmpPage = {
    pageSize: '100',
    pageNo: 1,
    selectedIds: [],
    totalData: 0,
    pagination: null,
  }
  wareHouseEmpObj = new User();
  wareHouseStatus: boolean = null;
  warehouseId: number = null;
  warehouseDetailsList: Warehouse[] = [];
  selectedWarehouseIndex: number = 0;
  warehouseDetails = new Warehouse();
  warehouseForEdit = new Warehouse();
  managerDetails = new User();
  activeFlag: boolean = null;
  enumForTab = EnumForWarehouseDetailsTab
  selectedTab: string = this.enumForTab.OVERVIEW;
  routeSub: Subscription;
  passwordControl: FormControl;
  isExpandedIDs: any[] = [];
  warehouseEmp: User[] = []
  markAsPrimaryObj = {
    item: null,
    value: null,
    index: null
  }
  markAsPrimaryModal: any;
  markAsPrimaryDetailModal: any;

  //dropOff location
  dropOffTH: any[] = [];
  dropOffFormGroup: FormGroup;
  dropOffLocationList: DropOffLocation[] = [];
  dropOffLocObj = new DropOffLocation();
  dropOffParam = {
    searchText: null
  }
  searchDOSubject: Subject<string> = new Subject<string>();

  //AISLE & RACK
  rackTH: any[] = [];
  aiscleList: Aisle[] = [];
  aisleStatus: boolean = null;
  aisleRackName: string;
  searchARSubject: Subject<string> = new Subject<string>();
  aisleObj = new Aisle()
  rackObj = new Racks()
  deleteAisleModal: any;
  deleteRackModal: any;
  flagForExpandAllRacks: boolean = false;
  itemDropdown: any[];
  selectedItem: number[];

  constructor(public utilsService: UtilsService, public fb: FormBuilder, public route: ActivatedRoute, private router: Router, public elementRef: ElementRef, private sanitizer: DomSanitizer, 
              private renderer: Renderer2, private printService: NgxPrintService) { }

  getAllWarehouse() {
    this.utilsService.getMethodAPI(false, this.utilsService.serverVariableService.WAREHOUSE_SAVE_EDIT_DELETE, null, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.warehouseList = Deserialize(response, Warehouse)
      }
      else {
        this.warehouseList = [];
      }
    })
  }

  openMarkAsPrimary(item: Warehouse, value, index) {
    this.markAsPrimaryObj.item = Serialize(item)
    this.markAsPrimaryObj.value = (value)
    this.markAsPrimaryObj.index = (index)
    this.markAsPrimaryModal.show();
  }

  onMarkAsPrimary() {
    this.utilsService.putMethodAPI(true, this.utilsService.serverVariableService.WAREHOUSE_MARK_AS_PRIMARY + `${this.markAsPrimaryObj.item.id}`, {}, '', (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response.data)) {
        this.warehouseList[this.markAsPrimaryObj.index].isMainWarehouse = this.markAsPrimaryObj.value
      } else {
        this.warehouseList[this.markAsPrimaryObj.index].isMainWarehouse = !this.markAsPrimaryObj.value
      }
      this.markAsPrimaryModal.hide();
      this.getAllWarehouse()
    }, true);
  }

  onChangeStatus(item: Warehouse, value, index) {
    this.utilsService.putMethodAPI(true, this.utilsService.serverVariableService.WAREHOUSE_STATUS + `${item.id}`, {}, '', (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response.data)) {
        this.warehouseList[index].isActive = value
      } else {
        this.warehouseList[index].isActive = !value
      }
      this.getAllWarehouse()
    }, true);
  }

  // Delete
  openDeleteWarehouseModal(obj: Warehouse) {
    this.passwordControl.reset();
    this.warehouseObj = Serialize(obj)
    this.deleteWarehouseModal.show();
  }

  deleteWarehouse() {

    if (this.passwordControl.invalid) {
      this.passwordControl.markAllAsTouched();
      return;
    }

    const param = {
      id: this.warehouseObj.id,
      password: this.passwordControl.value
    }

    this.utilsService.deleteMethodAPI(true, this.utilsService.serverVariableService.WAREHOUSE_SAVE_EDIT_DELETE, param, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.deleteWarehouseModal.hide();
        this.getAllWarehouse();
      }
    })
  }

  trackBy(index: number, name: Warehouse): number {
    return name.id;
  }

  //drag drop
  drop(event: CdkDragDrop<any>) {

    if(event.previousIndex === event.currentIndex) {
      return;
    }
    moveItemInArray(this.warehouseList, event.previousIndex, event.currentIndex);

    let seqArr = this.warehouseList.map((v, i) => ({
      id: v.id,
      sortOrder: i + 1
    }));
    const param = {
      warehouses: seqArr
    }

    this.utilsService.postMethodAPI(false, this.utilsService.serverVariableService.WAREHOUSE_SORTORDER, param, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.getAllWarehouse();
      }
    })
  }

  warehouseFormGroup() {
    this.warehouseForm = this.fb.group({
      warehouseName: ['', Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.PATTERN_FOR_ALPHANUMERIC_WITH_SPECIAL_CHAR)])],
      warehouseMobileNo: ['', Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.PATTERN_FOR_PHONE_NO)])],
      warehouseEmail: ['', Validators.compose([Validators.pattern(this.utilsService.validationService.PATTERN_FOR_EMAIL)])],
      warehouseAddress: ['', Validators.compose([Validators.pattern(this.utilsService.validationService.ONLY_SPACE_NOT_ALLOW)])],
      locationLink: [''],
      isMainWarehouse: [false],
      isSalesPoint: [false],
      isActive: [false],
      zipCode: ['', Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.PATTERN_FOR_NUMBER)])],
      countryId: [null, Validators.required],
      stateId: [null, Validators.required],
      cityId: [null, Validators.required],
      userId: [null, Validators.required]
    });
  }

  openAddEditModal(obj: Warehouse, status: string) {
    this.showAddEditModal = true;
    this.warehouseObj = new Warehouse();
    this.warehouseForm.reset();
    this.statusForModal = status;

    if (this.statusForModal === 'Add') {
      this.getWarehouseByID(null);
      setTimeout(() => {
        this.warehouseObj.isActive = true;
        this.warehouseObj.isMainWarehouse = this.warehouseList.length == 0 ? true : false;
      }, 100);
    }

    if (obj) {
      setTimeout(() => {
        this.getWarehouseByID(obj);
      }, 100);
    }

    if (this.router.url.includes('/users/warehouse-management/warehouse-details/')) {
      this.addNewWareHouseDetails.show()
    }

    if (!this.warehouseId && this.router.url === '/users/warehouse-management') {
      this.addNewWareHouse.show();
    }
  }

  getWarehouseByID(obj: Warehouse) {

    let API = obj ? this.utilsService.serverVariableService.WAREHOUSE_DROPDOWN_GET + `?id=${obj.id}` : this.utilsService.serverVariableService.WAREHOUSE_DROPDOWN_GET

    this.utilsService.postMethodAPI(false, API, null, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.dropdownsList.dropdown = response;
        this.dropdownsList.dropdown.countries = this.dropdownsList.dropdown?.countries ? this.utilsService.transformDropdownItems(this.dropdownsList.dropdown?.countries) : [];
        this.dropdownsList.dropdown.states = this.dropdownsList.dropdown?.states ? this.utilsService.transformDropdownItems(this.dropdownsList.dropdown?.states) : [];
        this.dropdownsList.dropdown.cities = this.dropdownsList.dropdown?.cities ? this.utilsService.transformDropdownItems(this.dropdownsList.dropdown?.cities) : [];
        this.dropdownsList.dropdown.users = this.dropdownsList.dropdown?.users ? this.utilsService.transformDropdownItems(this.dropdownsList.dropdown?.users) : [];

        //default india
        const objIndia = this.dropdownsList.dropdown.countries.find(v => v.name.toLowerCase() === ('India').toLowerCase())
        this.warehouseObj.countryId = objIndia ? objIndia.id : null;
        this.dropdownsList.state = objIndia ? (this.dropdownsList.dropdown.states || []).filter(a => a.country?.id == this.warehouseObj.countryId) : [];
        //

        if (response.warehouse) {
          this.warehouseObj = Deserialize(response.warehouse, Warehouse);
          this.warehouseObj.countryId = this.warehouseObj.countryMaster?.id
          this.warehouseObj.stateId = this.warehouseObj?.stateMaster?.id
          this.warehouseObj.cityId = this.warehouseObj.cityMaster?.id
          this.warehouseObj.userId = this.warehouseObj.users?.id

          const availableStates = this.dropdownsList.dropdown.states.filter(state => state.country?.id === this.warehouseObj.countryId);
          this.dropdownsList.state = availableStates ? this.utilsService.transformDropdownItems(availableStates) : [];
          const availableCities = this.dropdownsList.dropdown.cities.filter(city => city.stateMaster?.id === this.warehouseObj.stateId);
          this.dropdownsList.city = availableCities ? this.utilsService.transformDropdownItems(availableCities) : [];

          setTimeout(() => {
            if (response.warehouse) {
              this.dropdownsList.state = this.utilsService.filterIsActive(this.dropdownsList.state, this.warehouseObj.stateId ? this.warehouseObj.stateId : null);
              this.dropdownsList.city = this.utilsService.filterIsActive(this.dropdownsList.city, this.warehouseObj.cityId ? this.warehouseObj.cityId : null);
              this.dropdownsList.dropdown.users = this.utilsService.filterIsActive(this.dropdownsList.dropdown.users, this.warehouseObj.userId ? this.warehouseObj.userId : null);
            }
          }, 100);
        }
        this.dropdownsList.dropdown.countries = this.utilsService.filterIsActive(this.dropdownsList.dropdown?.countries, this.warehouseObj.countryId ? this.warehouseObj.countryId : null);
        this.dropdownsList.dropdown.states = this.utilsService.filterIsActive(this.dropdownsList.dropdown?.states, this.warehouseObj.stateId ? this.warehouseObj.stateId : null);
        this.dropdownsList.dropdown.cities = this.utilsService.filterIsActive(this.dropdownsList.dropdown?.cities, this.warehouseObj.cityId ? this.warehouseObj.cityId : null);
        this.dropdownsList.dropdown.users = this.utilsService.filterIsActive(this.dropdownsList.dropdown.users, this.warehouseObj.userId ? this.warehouseObj.userId : null);

        if (this.warehouseObj.isMainWarehouse == true) {
          this.warehouseForm.controls['isActive'].disable();
          this.warehouseForm.controls['isMainWarehouse'].disable();
          this.warehouseForm.controls['isMainWarehouse'].updateValueAndValidity();
          this.warehouseForm.controls['isActive'].updateValueAndValidity();
        }
        else {
          this.warehouseForm.controls['isActive'].enable();
          this.warehouseForm.controls['isMainWarehouse'].enable();
          this.warehouseForm.controls['isMainWarehouse'].updateValueAndValidity();
          this.warehouseForm.controls['isActive'].updateValueAndValidity();
        }
      }
    })
  }

  //Warehouse Details

  onChangeActive() {
    this.selectedWarehouseIndex = 0;
    this.getWarehouseDetails(this.selectedWarehouseIndex, null);
  }

  onChangeTab(value: string) {
    this.selectedTab = value;
  }

  getWarehouseDetails(index: number, warehouseId: number) {

    let ls_param = null
    ls_param = JSON.parse(localStorage.getItem('param'))

    if (!this.utilsService.isNullUndefinedOrBlank(ls_param)) {
      if (ls_param.pageName === 'warehouse') {
          this.selectedWarehouseIndex = ls_param.selectedWarehouseIndex,
          this.warehouseId = ls_param.warehouseId,
          this.selectedTab = ls_param.selectedTab,
          this.dropOffParam.searchText = ls_param.searchText,
          this.aisleRackName = ls_param.aisleRackName,
          this.activeFlag = ls_param.wareHouseStatus
          this.aisleStatus = ls_param.aisleStatus,
          this.selectedItem = ls_param.itemId,
          this.isExpandedIDs = ls_param.isExpandedIDs
      }
    }

    this.selectedWarehouseIndex = index ? index : 0;
    this.warehouseId = warehouseId ? warehouseId : null;

    const param = {
      id: this.warehouseId,
      searchText: this.dropOffParam.searchText ? this.dropOffParam.searchText : null,
      wareHouseStatus: this.activeFlag,
      aisleStatus: this.aisleStatus,
      aisleRackName: this.aisleRackName,
      itemId: this.selectedItem ? this.selectedItem : [],
      // pageNo: this.warehouseEmpPage.pageNo,
      // pageSize: this.warehouseEmpPage.pageSize,
    }

    this.utilsService.postMethodAPI(false, this.utilsService.serverVariableService.WAREHOUSE_DETAILS, param, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.warehouseDetailsList = Deserialize(response.warehouseList, Warehouse);
        this.selectedWarehouseIndex = this.warehouseDetailsList.findIndex(item => item.id === this.warehouseId)
        this.warehouseForEdit = this.warehouseDetailsList[this.selectedWarehouseIndex ? this.selectedWarehouseIndex : 0]
        //
        if (response?.overview?.warehouse_details) {
          this.warehouseDetails = Deserialize(response.overview.warehouse_details, Warehouse);
        }
        if (response?.overview?.manager_details) {
          this.managerDetails = Deserialize(response.overview.manager_details, User);
        }
        if(response?.DropLocation) {
          this.dropOffLocationList = Deserialize(response.DropLocation.content, DropOffLocation)
        }
        if (response?.AisleAndRacks?.aisle) {
          this.aiscleList = Deserialize(response.AisleAndRacks?.aisle, Aisle);
          this.flagForExpandAllRacks = false;
          if (!this.utilsService.isNullUndefinedOrBlank(this.isExpandedIDs)) {
            this.aiscleList?.forEach(obj => {
              obj.isExpand = this.isExpandedIDs.includes(obj.id);
            });
            this.flagForExpandAllRacks = this.aiscleList.length !== 0 ? this.aiscleList?.every(v => v.isExpand) : false
          }
        }
        if(response.item) {
          this.itemDropdown = response.item;
        }

        //warehouse emp
        if(response?.overview?.employee) {
          this.warehouseEmp = Deserialize(response?.overview?.employee, User)
          this.warehouseEmpPage.pagination = response?.overview?.employee;
          // this.warehouseEmpPage.totalData = response?.overview?.employee
        }

        //aisle rack count
        if(response.numberOfAisles) {
          this.warehouseDetails.numberOfAisles = Serialize(response.numberOfAisles)
          this.warehouseDetails.numberOfRacks = Serialize(response.numberOfRacks)
        }
        //
        localStorage.removeItem('param')
      }
    })
  }

  changeWarehouse(index: number, id: number) {
    this.utilsService.redirectTo('/users/warehouse-management/warehouse-details/' + id)
    this.getWarehouseDetails(index, id);
  }

  openMarkAsPrimaryDetail(item: Warehouse) {
    this.markAsPrimaryObj.item = Serialize(item)
    this.markAsPrimaryDetailModal.show();
  }

  onMarkAsPrimaryDetail() {
    this.utilsService.putMethodAPI(true, this.utilsService.serverVariableService.WAREHOUSE_MARK_AS_PRIMARY + `${this.markAsPrimaryObj.item.id}`, {}, '', (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.markAsPrimaryDetailModal.hide();
        this.getWarehouseDetails(this.selectedWarehouseIndex, this.warehouseId)
      }
    });
  }

  //delete from details
  openWRDeleteModal() {
    this.passwordControl.reset();
    this.deleteWarehouseDetailModal.show();
  }

  deleteERDetail() {

    if (this.passwordControl.invalid) {
      this.passwordControl.markAllAsTouched();
      return;
    }

    const param = {
      id: this.warehouseId,
      password: this.passwordControl.value
    }

    this.utilsService.deleteMethodAPI(true, this.utilsService.serverVariableService.WAREHOUSE_SAVE_EDIT_DELETE, param, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.deleteWarehouseDetailModal.hide();
        this.utilsService.redirectTo('/users/warehouse-management/')
      }
    })
  }

  //markAsPrimary Details
  changeWarehousePrimary() {
    this.utilsService.putMethodAPI(true, this.utilsService.serverVariableService.WAREHOUSE_MARK_AS_PRIMARY + `${this.warehouseId}`, {}, '', (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.getWarehouseDetails(this.selectedWarehouseIndex, this.warehouseId)
      }
    });
  }

  //active inactive from details
  changeActiveInactive() {
    this.utilsService.putMethodAPI(true, this.utilsService.serverVariableService.WAREHOUSE_STATUS + `${this.warehouseId}`, {}, '', (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.getWarehouseDetails(this.selectedWarehouseIndex, this.warehouseId)
      }
    });
  }

  // droplocation

  openAddEditDropOffLoc(obj: DropOffLocation, status: string) {
    this.showAddEditDropOff = true;
    this.dropOffLocObj = new DropOffLocation();
    this.dropOffFormGroup.reset();
    this.statusForModal = status;

    if (this.statusForModal === 'Add') {
      setTimeout(() => {
        this.dropOffLocObj.isActive = true;
      }, 100);
    }

    if (obj) {
      setTimeout(() => {
        this.dropOffLocObj = Serialize(obj)
      }, 100);
    }
    this.addEditDropOff.show()
  }

  dropOffForm() {
    this.dropOffFormGroup = this.fb.group({
      locationName: ['', Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.PATTERN_FOR_ALPHANUMERIC_WITH_SPECIAL_CHAR)])],
      description: ['', Validators.pattern(this.utilsService.validationService.ONLY_SPACE_NOT_ALLOW)],
      isActive: [false]
    })
  }

  onSaveDropOfFLoc() {

    if (this.dropOffFormGroup.invalid) {
      this.dropOffFormGroup.markAllAsTouched();
      return;
    }

    this.dropOffLocObj.warehouseId = Serialize(this.warehouseId)
    let param = this.utilsService.trimObjectValues(Serialize(this.dropOffLocObj));
    this.utilsService.postMethodAPI(true, this.utilsService.serverVariableService.DROPOFF_SAVE_EDIT_DELETE, param, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.addEditDropOff.hide();
        this.getWarehouseDetails(this.selectedWarehouseIndex, this.warehouseId);
      }
    })

  }

  onSearchDO(event: any) {
    this.searchDOSubject.next(event.target.value);
  }
  
  getDropOffQR(obj: DropOffLocation) {

    let API = this.utilsService.serverVariableService.DROPOFF_QR +  `?warehouseId=${this.warehouseId}`;
    if(obj) {
      API  += `&id=${obj.id}`;
    }

    this.utilsService.exportReportGetApi(API).subscribe((response : any) => {
      saveAs(new Blob([response], { type: 'application/pdf' }), 'Drop_off_locations');
    })
  }

  // Delete DropLocation

  openDeleteDropLocationModal(obj: DropOffLocation) {
    this.dropOffLocObj = Serialize(obj)
    this.deleteDropOff.show();
  }

  deleteDropLocation() {

    this.utilsService.deleteMethodAPI(true, this.utilsService.serverVariableService.DROPOFF_SAVE_EDIT_DELETE + `?id=${this.dropOffLocObj.id}`, null, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.deleteDropOff.hide();
        this.getWarehouseDetails(this.selectedWarehouseIndex, this.warehouseId);
      }
    })
  }

  // AISLE RACK

  onSearchAR(event: any) {
    this.searchARSubject.next(event.target.value);
  }

  openDeleteAisleModal(obj: Aisle) {
    this.aisleObj = Serialize(obj)
    this.deleteAisleModal.show();
  }

  onDeleteAisle() {
    this.utilsService.deleteMethodAPI(true, this.utilsService.serverVariableService.AISLE_SAVE_EDIT_DELETE + `?id=${this.aisleObj.id}`, {}, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.deleteAisleModal.hide();
        this.getWarehouseDetails(this.selectedWarehouseIndex, this.warehouseId);
      }
    })
  }

  openDeleteRackModal(obj: Aisle) {
    this.rackObj = Serialize(obj)
    this.deleteRackModal.show();
  }

  onDeleteRack() {
    this.utilsService.deleteMethodAPI(true, this.utilsService.serverVariableService.RACKS_SAVE_EDIT_DELETE + `?id=${this.rackObj.id}`, {}, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.deleteRackModal.hide();
        this.getWarehouseDetails(this.selectedWarehouseIndex, this.warehouseId);
      }
    })
  }

  getAisleQR(obj: Aisle) {

    let API = this.utilsService.serverVariableService.AISLE_QR + `?warehouseId=${this.warehouseId}`;
    if (obj) {
      API += `&id=${obj.id}`;
    }

    this.utilsService.exportReportGetApi(API).subscribe((response : any) => {
      saveAs(new Blob([response], { type: 'application/pdf' }), 'Aisle_QR_Code');
    })
  }

  getRackQR(obj: Racks) {

    let API = this.utilsService.serverVariableService.RACK_QR + `?warehouseId=${this.warehouseId}`;
    if (obj) {
      API += `&id=${obj.id}`;
    }

    this.utilsService.exportReportGetApi(API).subscribe((response : any) => {
        saveAs(new Blob([response], { type: 'application/pdf' }), 'Rack_QR_Code');
    })
  }

  // warehouse emp delete
  openWarehouseEmpDelete(obj: User) {
    this.wareHouseEmpObj = Serialize(obj);
    this.warehouseRemoveModal.show();
  }

  onDeleteEmpW() {
    this.utilsService.deleteMethodAPI(true, this.utilsService.serverVariableService.WAREHOUSE_EMP_DEL + `?wareHouseId=${this.warehouseId}&usersId=${this.wareHouseEmpObj.id}`, {}, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.warehouseRemoveModal.hide();
        if (!this.warehouseEmpPage.pagination?.first && this.warehouseEmpPage.pagination?.last && this.warehouseEmpPage.pagination?.numberOfElements === 1) {
          this.warehouseEmpPage.pageNo = this.warehouseEmpPage.pageNo - 1
        }
        this.getWarehouseDetails(this.selectedWarehouseIndex, this.warehouseId);
      }
    })
  }

  //Redirect to other pages from detail
  redirectFromDetails() {
    let param = null;
    param = {
      id: this.warehouseId,
      searchText: this.dropOffParam.searchText ? this.dropOffParam.searchText : null,
      wareHouseStatus: this.activeFlag,
      aisleStatus: this.aisleStatus,
      aisleRackName: this.aisleRackName,
      itemId: this.selectedItem ? this.selectedItem : [],
      selectedTab: this.selectedTab,
      selectedWarehouseIndex: this.selectedWarehouseIndex,
      warehouseId: this.warehouseId,
      isExpandedIDs: this.isExpandedIDs,
      pageName: 'warehouse'
    }
    localStorage.setItem('param', JSON.stringify(param))
  }
}
