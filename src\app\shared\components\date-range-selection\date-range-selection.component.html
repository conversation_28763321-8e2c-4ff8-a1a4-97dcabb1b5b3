<div ngbDropdown #dropdown="ngbDropdown" autoClose="outside" (openChange)="onDropdownOpenChange($event)">
  <div class="form-group-icon-end">
    <i (click)="!disabled && dropdown.toggle()" class="th th-outline-calendar-1"></i>
    <input #inputField type="text" class="form-control" ngbDropdownToggle [value]="getSelectedDisplayValue()"
      [placeholder]="placeholder || (mode === 'month' ? 'Select month' : 'Select month and day')" [disabled]="disabled"
      [readonly]="true" />
  </div>
  <!-- Dropdown Menu -->
  <div ngbDropdownMenu class="picker-dropdown-menu" (click)="$event.stopPropagation()">
    <div class="month-day-picker">
      <!-- Month Selection Grid (for month-only mode) -->
      <div class="picker-section" *ngIf="mode === 'month'">
        <!-- Month Selection Title -->
        <div class="month-selection-title">
          <h6 class="selection-title">{{ range ? 'Select Month Range' : 'Select Month' }}</h6>
        </div>

        <div class="picker-grid months-grid" role="grid" aria-label="Month selection">
          <div *ngFor="let month of months; trackBy: trackByMonth" class="picker-item month-item"
            [class.selected]="isSelected(month.value)" [class.range-start]="range && isRangeStart(month.value)"
            [class.range-end]="range && isRangeEnd(month.value)" [class.in-range]="range && isMonthInRange(month.value)"
            [class.hover-range-start]="range && isHoverRangeStart(month.value)"
            [class.hover-range-end]="range && isHoverRangeEnd(month.value)"
            [class.in-hover-range]="range && isInHoverRange(month.value)"
            [class.selecting-range]="range && isSelectingRange" [class.today]="isToday(month.value)"
            [class.disabled]="disabled || isMonthDisabled(month.value)" [attr.aria-label]="month.fullName"
            [attr.aria-selected]="isSelected(month.value)"
            [attr.aria-disabled]="disabled || isMonthDisabled(month.value)" role="gridcell" tabindex="0"
            (click)="onMonthSelect(month.value, dropdown)" (mouseenter)="onMonthHover(month.value)"
            (mouseleave)="onMonthLeave()" (keydown.enter)="onMonthSelect(month.value, dropdown)"
            (keydown.space)="onMonthSelect(month.value, dropdown)">
            {{ month.name }}
          </div>
        </div>
      </div>

      <!-- Month Navigation Header (for day-month mode) -->
      <div class="picker-section" *ngIf="mode === 'day-month' && !showMonthSelection">
        <div class="month-navigation-header">
          <button type="button" class="nav-btn nav-btn-prev" (click)="navigateToPreviousMonth()" [disabled]="disabled"
            aria-label="Previous month">
            <i class="th th-outline-arrow-left-2"></i>
          </button>

          <div class="current-month-display">
            <button type="button" class="month-title-btn" (click)="showMonthSelectionView()" [disabled]="disabled"
              [attr.aria-label]="'Select month, currently ' + getCurrentMonthName()">
              <h6 class="month-title">{{ getCurrentMonthName() }}</h6>
            </button>
          </div>

          <button type="button" class="nav-btn nav-btn-next" (click)="navigateToNextMonth()" [disabled]="disabled"
            aria-label="Next month">
            <i class="th th-outline-arrow-right-3"></i>
          </button>
        </div>
      </div>

      <!-- Month Selection Grid (for day-month mode) -->
      <div class="picker-section" *ngIf="mode === 'day-month' && showMonthSelection">
        <!-- Month Selection Title -->
        <div class="month-selection-title">
          <h6 class="selection-title">Select Month</h6>
        </div>

        <div class="picker-grid months-grid" role="grid" aria-label="Month selection">
          <div *ngFor="let month of months; trackBy: trackByMonth" class="picker-item month-item"
            [class.selected]="viewingMonth === month.value" [class.today]="isToday(month.value)"
            [class.disabled]="disabled || isMonthDisabled(month.value)" [attr.aria-label]="month.fullName"
            [attr.aria-selected]="viewingMonth === month.value"
            [attr.aria-disabled]="disabled || isMonthDisabled(month.value)" role="gridcell" tabindex="0"
            (click)="onMonthSelectFromGrid(month.value)" (keydown.enter)="onMonthSelectFromGrid(month.value)"
            (keydown.space)="onMonthSelectFromGrid(month.value)">
            {{ month.name }}
          </div>
        </div>
      </div>

      <!-- Day Selection (only shown in day-month mode) -->
      <div class="picker-section picker-section-days" *ngIf="mode === 'day-month' && !showMonthSelection">
        <!-- Days Grid -->
        <div class="picker-grid days-grid" role="grid" aria-label="Day selection">
          <div *ngFor="let day of getDaysInMonth(); trackBy: trackByDay" class="picker-item day-item"
            [class.selected]="selectedMonth === viewingMonth && isSelected(selectedMonth, day)"
            [class.today]="isToday(viewingMonth, day)" [class.disabled]="disabled || isDayDisabled(day)"
            [attr.aria-label]="'Day ' + day"
            [attr.aria-selected]="selectedMonth === viewingMonth && isSelected(selectedMonth, day)"
            [attr.aria-disabled]="disabled || isDayDisabled(day)" role="gridcell" tabindex="0"
            (click)="onDaySelect(day, dropdown)" (keydown.enter)="onDaySelect(day, dropdown)"
            (keydown.space)="onDaySelect(day, dropdown)">
            {{ day }}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
