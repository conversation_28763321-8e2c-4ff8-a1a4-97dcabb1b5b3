import { moveItemInArray } from '@angular/cdk/drag-drop';
import { Component, OnInit, Input, Output, EventEmitter, ViewChild } from '@angular/core';
import { NgbDropdown } from '@ng-bootstrap/ng-bootstrap';
declare var bootstrap: any;

@Component({
  selector: 'app-table-column-filter-dropdown-new',
  templateUrl: './table-column-filter-dropdown.component.html',
  styleUrls: ['./table-column-filter-dropdown.component.css']
})
export class TableColumnFilterDropdownComponentNew implements OnInit {
  
  @ViewChild('dropdown') dropdown: NgbDropdown;

  @Input('allHeaderArr') allHeaderArr: any[];
  @Input('columnArr') columnArr: any[];
  @Input('draggingColumnIndex') draggingColumnIndex: number;
  @Input('closeHeaderDropdown') closeHeaderDropdown: boolean;
  
  @Output() checkIfAllSelected: EventEmitter<any> = new EventEmitter<any>();
  @Output() saveCol: EventEmitter<any> = new EventEmitter<any>();
  @Output() getHeader: EventEmitter<any> = new EventEmitter<any>();

  isOpen: boolean = false;

  constructor() { }

  ngOnInit() {
  }
  
  onDropdownOpenChange(isOpen: boolean): void {
    if (isOpen) {
      this.isOpen = true;
    } else {
      this.isOpen = false;
      this.closeDropdown(false)
      this.getHeader.emit()
    }
  }

  closeDropdown(isSave: boolean): void {
    if (this.dropdown) {
      this.dropdown.close();

      // if(!isSave) {
      //   this.resetHeaderCol()
      // }
    }
  }

  selectUnselect() {
    this.checkIfAllSelected.emit()
  }

  startDrag(index: number) {
    this.draggingColumnIndex = index;
  }

  dropCol(event) {
    moveItemInArray(this.allHeaderArr, this.draggingColumnIndex, event.currentIndex);
  }

  resetHeaderCol() {
    // this.allHeaderArr = Serialize(this.columnArr)
    // this.checkIfAllSelected.emit();
  }

  //Save Col
  onSaveCol() {
    this.saveCol.emit();
    if (this.closeHeaderDropdown) {
      this.closeDropdown(true);
    }
  }

  removeBrTags(displayName: string): string {
    if(displayName) {
      return displayName.replace(/<br\s*\/?>/gi, '');
    }
    else return ''
  }
}
