import { deserializeAs, serializeAs } from 'cerialize';

export class HsnCode {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('taxId')
    @deserializeAs('taxId')
    private _taxId: number;

    @serializeAs('taxesComplianceId')
    @deserializeAs('taxesComplianceId')
    private _taxesComplianceId: number;

    @serializeAs('createdDate')
    @deserializeAs('createdDate')
    private _createdDate: string;

    @serializeAs('description')
    @deserializeAs('description')
    private _description: string;

    @serializeAs('taxName')
    @deserializeAs('taxName')
    private _taxName: string;

    @serializeAs('hsnCode')
    @deserializeAs('hsnCode')
    private _hsnCode: string;

    @serializeAs('rate')
    @deserializeAs('rate')
    private _rate: any;

    @serializeAs('cgstrate')
    @deserializeAs('cgstrate')
    private _cgstrate: any;

    @serializeAs('sgstrate')
    @deserializeAs('sgstrate')
    private _sgstrate: any;

    @serializeAs('isActive')
    @deserializeAs('isActive')
    private _isActive: boolean;

    @serializeAs('isSelected')
    @deserializeAs('isSelected')
    private _isSelected: boolean;

    @serializeAs('lastModifiedDate')
    @deserializeAs('lastModifiedDate')
    private _lastModifiedDate: string;

    @deserializeAs('isDefault')
    private _isDefault: boolean;

    @deserializeAs('excel')
    private _excel: any;

    constructor() {
        this.isActive = false;
        this.isDefault = false;
        this.isSelected = false;
    }

    /**
     * Getter isDefault
     * @return {boolean}
     */
	public get isDefault(): boolean {
		return this._isDefault;
	}

    /**
     * Setter isDefault
     * @param {boolean} value
     */
	public set isDefault(value: boolean) {
		this._isDefault = value;
	}

    /**
     * Getter excel
     * @return {any}
     */
	public get excel(): any {
		return this._excel;
	}

    /**
     * Setter excel
     * @param {any} value
     */
	public set excel(value: any) {
		this._excel = value;
	}


    /**
     * Getter lastModifiedDate
     * @return {string}
     */
	public get lastModifiedDate(): string {
		return this._lastModifiedDate;
	}

    /**
     * Setter lastModifiedDate
     * @param {string} value
     */
	public set lastModifiedDate(value: string) {
		this._lastModifiedDate = value;
	}

    
    /**
     * Getter taxesComplianceId
     * @return {number}
     */
	public get taxesComplianceId(): number {
		return this._taxesComplianceId;
	}

    /**
     * Setter taxesComplianceId
     * @param {number} value
     */
	public set taxesComplianceId(value: number) {
		this._taxesComplianceId = value;
	}


    /**
     * Getter taxId
     * @return {number}
     */
	public get taxId(): number {
		return this._taxId;
	}

    /**
     * Setter taxId
     * @param {number} value
     */
	public set taxId(value: number) {
		this._taxId = value;
	}
    

    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Getter createdDate
     * @return {string}
     */
	public get createdDate(): string {
		return this._createdDate;
	}

    /**
     * Getter description
     * @return {string}
     */
	public get description(): string {
		return this._description;
	}

    /**
     * Getter taxName
     * @return {string}
     */
	public get taxName(): string {
		return this._taxName;
	}

    /**
     * Getter hsnCode
     * @return {string}
     */
	public get hsnCode(): string {
		return this._hsnCode;
	}

    /**
     * Getter rate
     * @return {any}
     */
	public get rate(): any {
		return this._rate;
	}

    /**
     * Getter cgstrate
     * @return {any}
     */
	public get cgstrate(): any {
		return this._cgstrate;
	}

    /**
     * Getter sgstrate
     * @return {any}
     */
	public get sgstrate(): any {
		return this._sgstrate;
	}

    /**
     * Getter isActive
     * @return {boolean}
     */
	public get isActive(): boolean {
		return this._isActive;
	}

    /**
     * Getter isSelected
     * @return {boolean}
     */
	public get isSelected(): boolean {
		return this._isSelected;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Setter createdDate
     * @param {string} value
     */
	public set createdDate(value: string) {
		this._createdDate = value;
	}

    /**
     * Setter description
     * @param {string} value
     */
	public set description(value: string) {
		this._description = value;
	}

    /**
     * Setter taxName
     * @param {string} value
     */
	public set taxName(value: string) {
		this._taxName = value;
	}

    /**
     * Setter hsnCode
     * @param {string} value
     */
	public set hsnCode(value: string) {
		this._hsnCode = value;
	}

    /**
     * Setter rate
     * @param {any} value
     */
	public set rate(value: any) {
		this._rate = value;
	}

    /**
     * Setter cgstrate
     * @param {any} value
     */
	public set cgstrate(value: any) {
		this._cgstrate = value;
	}

    /**
     * Setter sgstrate
     * @param {any} value
     */
	public set sgstrate(value: any) {
		this._sgstrate = value;
	}

    /**
     * Setter isActive
     * @param {boolean} value
     */
	public set isActive(value: boolean) {
		this._isActive = value;
	}

    /**
     * Setter isSelected
     * @param {boolean} value
     */
	public set isSelected(value: boolean) {
		this._isSelected = value;
	}


}