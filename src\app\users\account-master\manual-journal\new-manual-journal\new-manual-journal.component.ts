import { DatePipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormArray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { Deserialize, Serialize } from 'cerialize';
import dayjs from 'dayjs';
import moment from 'moment';
import { AccountRequestList, ManualJournal } from 'src/app/models/ManualJournal';
import { UtilsService } from 'src/app/shared/services/utils.service';
declare var window: any;

@Component({
  selector: 'app-new-manual-journal',
  templateUrl: './new-manual-journal.component.html',
  styleUrls: ['./new-manual-journal.component.css']
})
export class NewManualJournalComponent implements OnInit {

  mjId: number;
  mjFormGroup: FormGroup;
  mjObj = new ManualJournal();

  accountDropdown: any[] = [];

  //
  deleteMJAccount: any;
  selectedIndex: number;
  deletedAccIds: number[] = [];

  //totals
  debitTotal: number = 0;
  creditTotal: number = 0; 

  maxDateNGB: any;
  minDateNGB: any;

  constructor(public utilsService: UtilsService, private fb: FormBuilder, private route: ActivatedRoute) { 

    this.maxDateNGB = moment().endOf('day').format('DD-MM-YYYY');
    this.minDateNGB = moment().year(2000).startOf('day').format('DD-MM-YYYY');

    // this.maxDateNGB = { 
    //   year: dayjs().startOf('day').get('year'), 
    //   month: dayjs().startOf('day').get('month') + 1,
    //   day: dayjs().startOf('day').get('date') 
    // };
    // this.minDateNGB = { year: dayjs().year(2000), month: 1, day: 1 };

  }

  ngOnInit() {

    this.deleteMJAccount = new window.bootstrap.Modal(
      document.getElementById('deleteMJAccount')
    );

    this.mjId = Number(this.route.snapshot.paramMap.get('id'));
    this.mjForm();

    if(this.mjId) {
      this.getMJByID();
    }
    else {
      this.getRequiredData();
      this.addAccount();
      this.addAccount();
    }

  }

  getRequiredData() {

    this.accountDropdown = []

    this.utilsService.getMethodAPI(false, this.utilsService.serverVariableService.MANUAL_JOURNAL_REQ_DATA, null, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.accountDropdown = response.Account_name;
        // this.accountDropdown = this.accountDropdown ? this.utilsService.transformDropdownItems(this.accountDropdown) : [];
      }
    })
  }

  getMJByID() {

    this.utilsService.getMethodAPI(false, this.utilsService.serverVariableService.MANUAL_JOURNAL_GET_DATA_BY_ID + `?id=${this.mjId}`, null, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.mjObj = Deserialize(response, ManualJournal);
        if (this.mjObj.saveAmountList) {
          this.mjObj.accountRequestList = Serialize(this.mjObj.saveAmountList.map(a => Deserialize(a, AccountRequestList)))
          this.mjObj.accountRequestList.forEach(v => {
            v.credit === 0 ? v.credit = null : v.credit = v.credit
            v.debit === 0 ? v.debit = null : v.debit = v.debit
          })
        }

        this.mjObj.tempDate = Serialize(this.mjObj.date);
        // let date = moment(this.utilsService.convertToDate(this.mjObj.date)).toDate();
        // this.mjObj.tempDate = {
        //   year: date.getFullYear(),
        //   month: date.getMonth() + 1,
        //   day: date.getDate()
        // }

        const fa = (this.mjFormGroup.get('accountInfo') as FormArray);
        this.accountInfo.clear();
        this.mjObj.accountRequestList.map(v => {
          fa.push(this.fb.group({
            accountId: [v.accountId, Validators.compose([Validators.required])],
            credit: [v.credit ? v.credit : null, Validators.compose([Validators.pattern(this.utilsService.validationService.ONLY_GREATER_THAN_ZERO)])],
            debit: [v.debit ? v.debit : null, Validators.compose([Validators.pattern(this.utilsService.validationService.ONLY_GREATER_THAN_ZERO)])],
          }));
        })

        //
        this.getRequiredData();
        this.updateTotals();
      }
    })

  }

  mjForm() {
    this.mjFormGroup = this.fb.group({
      date: ['', Validators.compose([Validators.required])],
      note: ['', Validators.compose([Validators.pattern(this.utilsService.validationService.ONLY_SPACE_NOT_ALLOW)])],
      accountInfo: this.fb.array([])
    })
  }

  get accountInfo() {
    return (this.mjFormGroup.get('accountInfo') as FormArray);
  }

  linkedAccountInfo(): FormGroup {
    return this.fb.group({
      accountId: [null, Validators.compose([Validators.required])],
      credit: [null, Validators.compose([Validators.pattern(this.utilsService.validationService.ONLY_GREATER_THAN_ZERO)])],
      debit: [null, Validators.compose([Validators.pattern(this.utilsService.validationService.ONLY_GREATER_THAN_ZERO)])],
    });
  }

  addAccount() {
    this.accountInfo.push(this.linkedAccountInfo());
    this.mjObj.accountRequestList.push(Serialize(new AccountRequestList()))
  }

  openRemoveModal(index: number) {
    this.selectedIndex = index;
    this.deleteMJAccount.show();
  }

  removeAccount() {
    this.deletedAccIds.push(this.mjObj.accountRequestList.at(this.selectedIndex).id ? this.mjObj.accountRequestList.at(this.selectedIndex).id : null)
    const accountArray = this.mjFormGroup.get('accountInfo') as FormArray;
    accountArray.removeAt(this.selectedIndex);
    this.mjObj.accountRequestList.splice(this.selectedIndex, 1);
    this.deleteMJAccount.hide();
    //
    this.updateTotals();
  }

  onSave() {

    if (this.mjFormGroup.invalid) {
      this.mjFormGroup.markAllAsTouched();
      return;
    }

    if (this.mjObj.accountRequestList.some(v => !v.debit && !v.credit)) {
      this.utilsService.toasterService.error('Debit and Credit both should not be empty', '', {
        positionClass: 'toast-top-right',
        closeButton: true,
        timeOut: 10000
      });
      return;
    }

    if (!this.utilsService.isEverythingUnique(this.mjObj.accountRequestList, 'accountId')) {
      this.utilsService.toasterService.error('Account Name should be unique.', '', {
        positionClass: 'toast-top-right',
        closeButton: true,
        timeOut: 10000
      });
      return;
    }

    if (this.mjObj.accountRequestList.length === 0) {
      this.utilsService.toasterService.error('Minimum one transaction with two accounts is required to save journal entry.', '', {
        positionClass: 'toast-top-right',
        closeButton: true,
        timeOut: 10000
      });
      return;
    }

    this.mjObj.date = Serialize(this.mjObj.tempDate);

    let param = Serialize(this.mjObj) as ManualJournal;

    let deleteIds = [];
    if (this.deletedAccIds) {
      deleteIds = this.deletedAccIds.filter(a => a !== null).map(v => ({ deleteAmountId: v }))
    }

    //
    param.accountRequestList.forEach(v => {
      v.updateAmountId = v.id
      v.credit === 0 ? v.credit = null : v.credit = v.credit
      v.debit === 0 ? v.debit = null : v.debit = v.debit
      delete v.id
    })
    param.accountRequestList = param.accountRequestList.concat(deleteIds);
    
    this.utilsService.postMethodAPI(true, this.utilsService.serverVariableService.MANUAL_JOURNAL_SAVE_EDIT_DELETE, param, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.utilsService.redirectTo('/users/accounts/manual-journal/')
      }
    })

  }
  
  // total
  updateTotals() {
    this.debitTotal = 0;
    this.creditTotal = 0;

    this.accountInfo.controls.forEach((control, index) => {
      const debit = control.get('debit')?.value || 0;
      const credit = control.get('credit')?.value || 0;

      this.debitTotal += parseFloat(debit);
      this.creditTotal += parseFloat(credit);
    });
  }

  onCreditInput(index: number) {
    const entryGroup = this.accountInfo.at(index) as FormGroup;
    const creditValue = entryGroup.get('credit')?.value;

    if (creditValue !== null && creditValue !== '') {
      entryGroup.patchValue({ debit: null });
    }
    this.updateTotals();
  }

  onDebitInput(index: number) {
    const entryGroup = this.accountInfo.at(index) as FormGroup;
    const debitValue = entryGroup.get('debit')?.value;

    if (debitValue !== null && debitValue !== '') {
      entryGroup.patchValue({ credit: null });
    }
    this.updateTotals();
  }


  onInputChange() {
    this.updateTotals();
  }

}
