<div class="page-content" [pageAccess]="{page: utilsService.enumForPage.ITEM, action: utilsService.enumForPage.ADD_ITEM, view: true}">
    <div class="page-title-wrapper">
        <div class="page-title-left">
            <h4>{{itemId ? 'Edit' : 'Add New'}} Item</h4>
        </div>
        <div class="page-title-right">
            <button class="btn btn-sm btn-icon btn-outline-white" [routerLink]="['/users/inventory/items']"
                ngbTooltip="Close" placement="left" container="body" triggers="hover">
                <i class="th th-close"></i>
            </button>
        </div>
    </div>
    <div class="content-area">
        <div class="card card-theme card-forms">
            <div class="card-body" [formGroup]="itemForm">
                <div class="row">
                    <div class="col-lg-4 col-md-6 col-sm-12">
                        <div class="form-group theme-ngselect form-group-inline-control required">
                            <label class="form-label">Market Type</label>
                            <div class="form-control-wrapper">
                                <ng-select placeholder="Select Market Type" [multiple]="true" [clearable]="false"
                                    formControlName="marketTypeIds" [items]="dropdown?.marketType" bindLabel="label"
                                    bindValue="value" [(ngModel)]="itemObj.marketTypeIds">
                                </ng-select>
                                <div class="message error-message"
                                    *ngIf="itemForm.controls['marketTypeIds'].hasError('required') &&  itemForm.controls['marketTypeIds'].touched">
                                    {{utilsService.validationService.MARKET_TYPE_REQ}}
                                </div>
                            </div>
                        </div>
                        <div class="form-group theme-ngselect form-group-inline-control required theme-ngselect-group-list">
                            <label class="form-label">Category</label>
                            <div class="form-control-wrapper">
                                <div class="form-group-button">
                                    <ng-select (change)="onChangeCategory()" placeholder="Select Category" [multiple]="false" [clearable]="false"
                                        [items]="flattenedParentCategory" bindLabel="categoryName" bindValue="id"
                                        [(ngModel)]="itemObj.categoryId" formControlName="categoryId">
                                        <ng-template ng-option-tmp let-item="item">
                                            <span [title]="item.categoryName" [style.padding-left.px]="item.index * 25"
                                                [ngClass]="{'ng-option-child-label' : item.isChild}" [class]="item.className">
                                                {{ item.categoryName }}
                                            </span>
                                        </ng-template>
                                    </ng-select>
                                </div>
                                <div class="message error-message"
                                    *ngIf="itemForm.controls['categoryId'].hasError('required') &&  itemForm.controls['categoryId'].touched">
                                    {{utilsService.validationService.CATEGORY_REQ}}
                                </div>
                            </div>
                        </div>
                        <div class="form-group theme-ngselect form-group-inline-control required">
                            <label class="form-label">Item Group</label>
                            <div class="form-control-wrapper">
                                <div class="form-group-button">
                                    <ng-select placeholder="Select Item Group" [multiple]="false" [clearable]="false"
                                        formControlName="itemGroupId" [items]="dropdown?.itemGroup" bindLabel="label"
                                        bindValue="value" [(ngModel)]="itemObj.itemGroupId">
                                    </ng-select>
                                    <button (click)="openAddEditModalIG()" class="btn btn-outline-white"><i
                                            class="th th-outline-add-circle text-primary"></i></button>
                                </div>
                                <div class="message error-message"
                                    *ngIf="itemForm.controls['itemGroupId'].hasError('required') &&  itemForm.controls['itemGroupId'].touched">
                                    {{utilsService.validationService.ITEM_GROUP_REQ}}
                                </div>
                            </div>
                        </div>
                        <div class="form-group theme-ngselect form-group-inline-control">
                            <label class="form-label">Group Code</label>
                            <div class="form-control-wrapper">
                                <div class="form-group-button">
                                    <ng-select (clear)="onClearGroupCode()" placeholder="Select Group Code" [multiple]="false" [clearable]="true"
                                        formControlName="groupCodeId" [items]="filteredGroupCode" bindLabel="groupCodeName" bindValue="id"
                                        [(ngModel)]="itemObj.groupCodeId">
                                    </ng-select>
                                </div>
                            </div>
                        </div>
                        <div class="form-group form-group-inline-control required">
                            <label class="form-label">Item Name</label>
                            <div class="form-control-wrapper">
                                <input [maxlength]="utilsService.validationService.MAX_200"
                                    [(ngModel)]="itemObj.itemName" formControlName="itemName" type="text"
                                    class="form-control" placeholder="Enter Item Name">
                                <div class="message error-message"
                                    *ngIf="itemForm.controls['itemName'].hasError('required') &&  itemForm.controls['itemName'].touched">
                                    {{utilsService.validationService.ITEM_NAME_REQ}}
                                </div>
                                <div class="message error-message"
                                    *ngIf="!itemForm.controls['itemName'].hasError('required') && !itemForm.controls['itemName'].valid && itemForm.controls['itemName'].touched">
                                    {{utilsService.validationService.ITEM_NAME_INVALID}}
                                </div>
                            </div>
                        </div>
                        <div class="form-group form-group-inline-control required">
                            <label class="form-label">Display Name</label>
                            <div class="form-control-wrapper">
                                <input [maxlength]="utilsService.validationService.MAX_200"
                                    [(ngModel)]="itemObj.displayName" formControlName="displayName" type="text"
                                    class="form-control" placeholder="Enter Display Name">
                                <div class="message error-message"
                                    *ngIf="itemForm.controls['displayName'].hasError('required') &&  itemForm.controls['displayName'].touched">
                                    {{utilsService.validationService.ITEM_DNAME_REQ}}
                                </div>
                                <div class="message error-message"
                                    *ngIf="!itemForm.controls['displayName'].hasError('required') && !itemForm.controls['displayName'].valid && itemForm.controls['displayName'].touched">
                                    {{utilsService.validationService.ITEM_DNAME_INVALID}}
                                </div>
                            </div>
                        </div>
                        <div class="form-group form-group-inline-control required" *ngIf="itemId && !itemObj.isSaveAsDraft">
                            <label class="form-label">SKU</label>
                            <div class="form-control-wrapper">
                                <input [(ngModel)]="itemObj.skuId" type="text" class="form-control" placeholder="SKU"
                                    disabled [ngModelOptions]="{standalone: true}">
                            </div>
                        </div>
                        <div class="form-group theme-ngselect form-group-inline-control required">
                            <label class="form-label">Unit</label>
                            <div class="form-control-wrapper">
                                <div class="form-group-button">
                                    <ng-select formControlName="unitId" placeholder="Select Unit" [multiple]="false"
                                        [clearable]="false" [items]="dropdown?.unitMaster" bindLabel="shortCode"
                                        bindValue="id" [(ngModel)]="itemObj.unitId">
                                    </ng-select>
                                    <button (click)="openAddEditModalUnit()" class="btn btn-outline-white"><i
                                            class="th th-outline-add-circle text-primary"></i></button>
                                </div>
                                <div class="message error-message"
                                    *ngIf="itemForm.controls['unitId'].hasError('required') &&  itemForm.controls['unitId'].touched">
                                    {{utilsService.validationService.UNIT_REQ}}
                                </div>
                            </div>
                        </div>
                        <div class="form-group form-group-inline-control ">
                            <label class="form-label"></label>
                            <div class="form-control-wrapper">
                                <div class="checkbox checkbox-primary checkbox-small form-check-inline">
                                    <input type="checkbox" id="carton" class="material-inputs filled-in"
                                        formControlName="isSellCarton" [(ngModel)]="itemObj.isSellCarton" />
                                    <label for="carton"> Only sell in carton </label>
                                </div>
                                <div class="checkbox checkbox-primary checkbox-small form-check-inline">
                                    <input type="checkbox" id="catalog" class="material-inputs filled-in"
                                        formControlName="isVisible" [(ngModel)]="itemObj.isVisible" />
                                    <label for="catalog"> Visible in catalog </label>
                                </div>
                                <div class="checkbox checkbox-primary checkbox-small form-check-inline">
                                    <input (change)="onChangePerisableAlert(itemObj.isAlert)" type="checkbox" id="alert"
                                        class="material-inputs filled-in" formControlName="isAlert"
                                        [(ngModel)]="itemObj.isAlert" />
                                    <label for="alert"> Set perishable alert</label>
                                </div>
                            </div>
                        </div>

                        <div class="form-group form-group-inline-control" *ngIf="itemObj.isAlert">
                            <label class="form-label"></label>
                            <div class="form-control-wrapper ">
                                <div class="input-group  ">
                                    <input mask="separator.0" thousandSeparator=""
                                        [maxlength]="utilsService.validationService.MAX_10" [(ngModel)]="itemObj.days"
                                        formControlName="days" type="text" placeholder="Enter"
                                        aria-label="Sales Hold By" aria-describedby="button-addon1"
                                        class="form-control">
                                    <span class="input-group-text">Days</span>
                                </div>
                                <div class="message error-message"
                                    *ngIf="itemForm.controls['days'].hasError('required') &&  itemForm.controls['days'].touched">
                                    {{utilsService.validationService.DAYS_REQ}}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-8 col-md-12" (dragover)="onSelectAttachments($event);doc.value = ''"
                        (drop)="onSelectAttachments($event);doc.value = ''" (paste)="onSelectAttachments($event)">
                        <div class="row">
                            <div class="col-lg-6">
                                <div class="form-group required">
                                    <div class="form-label">Upload Images & Videos<i class="th th-outline-info-circle ms-1"
                                            [ngbTooltip]="utilsService.validationService.DOC_INFO_IMG_VID" placement="bottom"
                                            container="body" triggers="hover"></i>
                                    </div>
                                    <div class='attachments-container h-100'>
                                        <div class='attachments-content'>
                                            <button class='btn btn-attachments'><i class="bi bi-upload"></i></button>
                                            <p>Drag and Drop Images & Videos here or <span class='text-primary'>Choose file</span></p>
                                        </div>
                                        <input (paste)="onSelectAttachments($event)" #doc type="file" ref={imageRef} multiple (change)="onSelectAttachments($event);doc.value = ''"
                                            accept="image/x-png,image/jpeg,image/jpg,video/mp4,video/x-m4v" />
                                    </div>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="attachments-wrapper">
                                    <div class='attachments-upload-grid-container attachments-upload-grid-container2' style="min-height: 200px;">
                                        <div class='attachments-upload-row'>
                                            <div class='attachments-upload-col' *ngFor="let item of itemObj.itemDocs; index as i">
                                                <div class='card-attachments-upload'>
                                                    <div class='attachments-image'>
                                                        <ng-container
                                                            *ngIf="utilsService.isImage((item.formattedName && !item.file) ? item.formattedName : item.originalName) || utilsService.isImage(item.file ? item.originalName : item.originalName)">
                                                            <img (click)="openLink(item.formattedName, null)" loading="lazy" *ngIf="!item.file"
                                                                [src]="item.formattedName ? (utilsService.imgPath + item.formattedName) : null" alt="valamji" />
                                                            <img (click)="openLink(null, item.formattedName)" loading="lazy" *ngIf="item.file"
                                                                [src]="item.formattedName ? (item.formattedName) : null" alt="valamji" />
                                                        </ng-container>
                                                        <ng-container
                                                            *ngIf="utilsService.isMedia((item.formattedName && !item.file) ? item.formattedName : item.originalName) || utilsService.isMedia(item?.file ? item?.originalName : item.originalName)">
                                                            <img *ngIf="item.file" (click)="openLink(null, item.formattedName)" src="assets/images/files/file-video.svg"
                                                                alt="valamji" />
                                                            <img *ngIf="!item.file" (click)="openLink(item.formattedName, null)" src="assets/images/files/file-video.svg"
                                                                alt="valamji" />
                                                        </ng-container>
                                                    </div>
                                                    <div class="attachments-text" [ngbTooltip]="item.fileName ? item.fileName : item.originalName" placement="bottom"
                                                        container="body" triggers="hover">
                                                        <h6 class="file-name">{{item.fileName ? item.fileName : item.originalName}}</h6>
                                                    </div>
                                                    <button (click)="removeAttachment(i, item)" class="btn-close" variant="close"><i class='th th-close'></i></button>
                                                    </div>
                
                
                                                <div class="radio radio-primary"
                                                    *ngIf="utilsService.isImage((item.formattedName && !item.file) ? item.formattedName : item.originalName) || utilsService.isImage(item.file ? item.originalName : item.originalName)">
                                                    <input type="radio" [id]="'thumb-' + i" [(ngModel)]="item.isMarkDefault" [ngModelOptions]="{standalone: true}"
                                                        [value]="true" (change)="onPrimaryChange(i)" [name]="'thumb-' + i">
                                                    <label [for]="'thumb-' + i">Mark default</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12 ">
                        <div class='nav-tabs-outer nav-tabs-style2'>
                            <nav>
                                <div class="nav nav-tabs" id="nav-tab" role="tablist">
                                    <button [ngClass]="{'active': selectedTab === enumForTab.DESCRIPTION}"
                                        (click)="onChangeTab(enumForTab.DESCRIPTION)" class="nav-link"
                                        id="nav-description-items-tab" data-bs-toggle="tab"
                                        data-bs-target="#nav-description-items" type="button" role="tab"
                                        aria-controls="nav-description-items" aria-selected="true">
                                        <i class="th th-outline-document-text-1"></i>Description
                                    </button>
                                    <button [ngClass]="{'active': selectedTab === enumForTab.OTHER_DETAILS}"
                                        (click)="onChangeTab(enumForTab.OTHER_DETAILS)" class="nav-link "
                                        id="nav-other-details-items-tab" data-bs-toggle="tab"
                                        data-bs-target="#nav-other-details-items" type="button" role="tab"
                                        aria-controls="nav-other-details-items" aria-selected="true">
                                        <i class="th th-outline-box"></i>Other Details
                                    </button>
                                    <button *ngIf="(itemObj.itemStock || []).length > 0" [ngClass]="{'active': selectedTab === enumForTab.ITEM_STOCK}"
                                        (click)="onChangeTab(enumForTab.ITEM_STOCK)" class="nav-link " id="nav-stock-items-tab" data-bs-toggle="tab"
                                        data-bs-target="#nav-stock-items" type="button" role="tab" aria-controls="nav-stock-items" aria-selected="true">
                                        <i class="th th-outline-house-2"></i>Item Stock
                                    </button>
                                    <button [ngClass]="{'active': selectedTab === enumForTab.BREACH_QTY}"
                                        (click)="onChangeTab(enumForTab.BREACH_QTY)" class="nav-link "
                                        id="nav-breach-items-tab" data-bs-toggle="tab"
                                        data-bs-target="#nav-breach-items" type="button" role="tab"
                                        aria-controls="nav-breach-items" aria-selected="true">
                                        <i class="th th-outline-box"></i>Breach Qty
                                    </button>
                                    <button [ngClass]="{'active': selectedTab === enumForTab.PO_IMAGE_MAPPING}"
                                        (click)="onChangeTab(enumForTab.PO_IMAGE_MAPPING)" class="nav-link "
                                        id="nav-mapping-items-tab" data-bs-toggle="tab"
                                        data-bs-target="#nav-mapping-items" type="button" role="tab"
                                        aria-controls="nav-mapping-items" aria-selected="true">
                                        <i class="th th-outline-box"></i>PO Image Mapping
                                    </button>
                                    <button [ngClass]="{'active': selectedTab === enumForTab.PACKING_INFO}"
                                        (click)="onChangeTab(enumForTab.PACKING_INFO)" class="nav-link "
                                        id="nav-packing-items-tab" data-bs-toggle="tab"
                                        data-bs-target="#nav-packing-items" type="button" role="tab"
                                        aria-controls="nav-packing-items" aria-selected="true">
                                        <i class="th th-outline-bag-2"></i>Packing Information
                                    </button>
                                    <button [ngClass]="{'active': selectedTab === enumForTab.DISCOUNT_SETTING}"
                                        (click)="onChangeTab(enumForTab.DISCOUNT_SETTING)" class="nav-link "
                                        id="nav-setting-items-tab" data-bs-toggle="tab"
                                        data-bs-target="#nav-setting-items" type="button" role="tab"
                                        aria-controls="nav-setting-items" aria-selected="true">
                                        <i class="th th-outline-discount-circle"></i>Discount Setting
                                    </button>
                                </div>
                            </nav>
                            <div class="tab-content" id="nav-tabContent">
                                <div [ngClass]="{'show active': selectedTab === enumForTab.DESCRIPTION}"
                                    class="tab-pane fade" id="nav-description-items" role="tabpanel"
                                    aria-labelledby="nav-description-items-tab">
                                    <app-item-description [itemForm]="itemForm" [itemObj]="itemObj"
                                        [dropdown]="dropdown" (openAddEditModal)="openAddEditModal()" 
                                        (addMediaLink)="addMediaLink()" (removeMediaLink)="removeMediaLink($event)"/>
                                </div>
                                <div [ngClass]="{'show active': selectedTab === enumForTab.OTHER_DETAILS}"
                                    class="tab-pane fade " id="nav-other-details-items" role="tabpanel"
                                    aria-labelledby="nav-other-details-items-tab">
                                    <app-item-other-details [itemForm]="itemForm" [itemObj]="itemObj"
                                        [dropdown]="dropdown" (addBullet)="addBullet()"
                                        (removeBullet)="removeBullet($event)"
                                        (onChangeRegularSeason)="onChangeRegularSeason($event)" />
                                </div>
                                <div [ngClass]="{'show active': selectedTab === enumForTab.ITEM_STOCK}"
                                    class="tab-pane fade " id="nav-stock-items" role="tabpanel"
                                    aria-labelledby="nav-stock-items-tab">
                                    <app-item-stock [itemObj]="itemObj"/>
                                </div>
                                <div [ngClass]="{'show active': selectedTab === enumForTab.BREACH_QTY}"
                                    class="tab-pane fade " id="nav-breach-items" role="tabpanel"
                                    aria-labelledby="nav-breach-items-tab">
                                    <app-item-breach-qty [itemForm]="itemForm" [itemObj]="itemObj" [dropdown]="dropdown"
                                        [breachQty]="breachQty" [levelList]="levelList" />
                                </div>
                                <div [ngClass]="{'show active': selectedTab === enumForTab.PO_IMAGE_MAPPING}" class="tab-pane fade "
                                    id="nav-mapping-items" role="tabpanel" aria-labelledby="nav-mapping-items-tab">
                                    <app-item-po-image-mapping [dropdown]="dropdown" [pOImageMapping]="pOImageMapping" (addPOMapping)="addPOMapping()"
                                        [poImageFGP]="poImageFGP" (openRemovePOModal)="openRemoveCPOModal($event)"
                                        (onColorImageChange)="onColorImageChange($event.event, $event.index)"
                                        (removePOColorAttachment)="removePOColorAttachment($event.index, $event.file, $event.childI)" />
                                </div>
                                <div [ngClass]="{'show active': selectedTab === enumForTab.PACKING_INFO}"
                                    class="tab-pane fade " id="nav-packing-items" role="tabpanel"
                                    aria-labelledby="nav-packing-items-tab">
                                    <app-item-packing-info (addPacketInfo)="addPacketInfo()"
                                        (openRemovePIModal)="openRemovePIModal($event)"
                                        [packingInfoList]="packingInfoList" [packetInfoFG]="packetInfoFG"
                                        [dropdown]="dropdown" />
                                </div>
                                <div [ngClass]="{'show active': selectedTab === enumForTab.DISCOUNT_SETTING}"
                                    class="tab-pane fade " id="nav-setting-items" role="tabpanel"
                                    aria-labelledby="nav-setting-items-tab">
                                    <app-item-discount-setting (addSetting)="addSetting()"
                                        (openRemoveDiscountModal)="openRemoveDiscountModal($event)"
                                        [discountSettingList]="discountSettingList"
                                        [discountSettingFG]="discountSettingFG" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class='bottombar-wrapper bottom-fixed'>
            <div class='bottombar-container'>
                <div class='bottombar-left'>
                    <button (click)="onSaveItem(false)" type="button" class="btn btn-primary btn-icon-text btn-sm"> <i
                            class="th th-outline-tick-circle"></i>
                        {{itemId ? 'Update' : 'Save'}}
                    </button>
                    <button *ngIf="itemObj.isSaveAsDraft" (click)="onSaveItem(true)" type="button"
                        class="btn btn-outline-white btn-icon-text btn-sm"><i
                            class="th th-outline-document-text"></i>Save as Draft</button>
                    <button [routerLink]="['/users/inventory/items']" type="button"
                        class="btn btn-outline-white btn-icon-text btn-sm"><i
                            class="th th-outline-close-circle"></i>Cancel</button>
                </div>
                <div class='bottombar-right'>

                </div>
            </div>
        </div>
    </div>
</div>


<!-- HSN CODE -->
<div class="hsn-code-modal modal modal-theme fade" id="itemAddHsnMasterModal" tabindex="-1"
    aria-labelledby="itemAddHsnMasterModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content" cdkTrapFocusAutoCapture="true" cdkTrapFocus *ngIf="showHsnModal">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">Add New HSN Code</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
                        class='th th-close'></i></button>
            </div>
            <app-hsn-master [hsnObj]="hsnObj" [gstSlabDropdown]="gstSlabDropdown" [hsnGroup]="hsnGroup"
                (onSaveHsnCode)="onSaveHsnCode()" [page]="utilsService.enumForPage.ITEM_GROUP"></app-hsn-master>
        </div>
    </div>
</div>

<!-- Unit Master -->
<div class="hsn-code-modal modal modal-theme fade" id="itemAddUnitMaster" tabindex="-1"
    aria-labelledby="itemAddUnitMasterLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content" cdkTrapFocusAutoCapture="true" cdkTrapFocus *ngIf="showUnitModal">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">Add New Unit</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
                        class='th th-close'></i></button>
            </div>
            <app-unit-master (onSaveUnit)="onSaveUnit()" [unitObj]="unitObj" [unitGroup]="unitGroup"
                [page]="utilsService.enumForPage.ITEM" [allCategories]="allCategories" />
        </div>
    </div>
</div>

<!-- Item Group -->
<div class="hsn-code-modal modal modal-theme fade" id="itemGroupAddModal" tabindex="-1"
    aria-labelledby="itemGroupAddModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content" cdkTrapFocusAutoCapture="true" cdkTrapFocus *ngIf="showItemGroupModal">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">Add New Item Group</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
                        class='th th-close'></i></button>
            </div>
            <app-item-grp-master (getRequiredData)="getRequiredData()" [itemGroupObj]="itemGroupObj"
                [itemGroupForm]="itemGroupForm" [page]="utilsService.enumForPage.ITEM" [hsnCodeDropdown]="hsnCodeDropdown"
                [flattenedParentCategory]="flattenedParentCategory" [modal]="itemGroupAddModal" [itemObj]="itemObj">
            </app-item-grp-master>
        </div>
    </div>
</div>

<!-- ----------------------------------------------------------------------- -->
<!--                           Delete Discount Group                          -->
<!-- ----------------------------------------------------------------------- -->
<div class="modal modal-theme modal-confirmation modal-reject fade" id="deleteDiscountGrp" tabindex="-1"
    aria-labelledby="deleteDiscountGrpLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
                        class='th th-close'></i></button>
            </div>
            <div class="modal-body">
                <div class="modal-confirmation-container">
                    <div class="modal-confirmation-icon">
                        <i class="th th-bold-trash"></i>
                    </div>
                    <div class="modal-confirmation-content">
                        <h5>Are You Sure?</h5>
                        <p>You want to remove the Discount Setting.</p>
                    </div>
                </div>
                <div class="modal-button-group modal-full-width-btn">
                    <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
                    <button (click)="removeDiscountSetting()" type="button" class="btn btn-primary btn-icon-text"> <i
                            class="th th-outline-tick-circle"></i>
                        Remove</button>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                           Delete Discount Group End                     -->
<!-- ----------------------------------------------------------------------- -->

<!-- ----------------------------------------------------------------------- -->
<!--                           Delete Discount Group                          -->
<!-- ----------------------------------------------------------------------- -->
<div class="modal modal-theme modal-confirmation modal-reject fade" id="deletePackingInfo" tabindex="-1"
    aria-labelledby="deletePackingInfoLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
                        class='th th-close'></i></button>
            </div>
            <div class="modal-body">
                <div class="modal-confirmation-container">
                    <div class="modal-confirmation-icon">
                        <i class="th th-bold-trash"></i>
                    </div>
                    <div class="modal-confirmation-content">
                        <h5>Are You Sure?</h5>
                        <p>You want to remove the Packing Information.</p>
                    </div>
                </div>
                <div class="modal-button-group modal-full-width-btn">
                    <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
                    <button (click)="removePI()" type="button" class="btn btn-primary btn-icon-text"> <i
                            class="th th-outline-tick-circle"></i>
                        Remove</button>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                           Delete Discount Group End                     -->
<!-- ----------------------------------------------------------------------- -->

<!-- ----------------------------------------------------------------------- -->
<!--                           Delete PO Mapping                              -->
<!-- ----------------------------------------------------------------------- -->
<div class="modal modal-theme modal-confirmation modal-reject fade" id="deletePoMapping" tabindex="-1"
    aria-labelledby="deletePoMappingLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
                        class='th th-close'></i></button>
            </div>
            <div class="modal-body">
                <div class="modal-confirmation-container">
                    <div class="modal-confirmation-icon">
                        <i class="th th-bold-trash"></i>
                    </div>
                    <div class="modal-confirmation-content">
                        <h5>Are You Sure?</h5>
                        <p>You want to remove the PO Color Image Mapping.</p>
                    </div>
                </div>
                <div class="modal-button-group modal-full-width-btn">
                    <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
                    <button (click)="removePOMapping()" type="button" class="btn btn-primary btn-icon-text"> <i
                            class="th th-outline-tick-circle"></i>
                        Remove</button>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                           Delete PO Mapping                            -->
<!-- ----------------------------------------------------------------------- -->