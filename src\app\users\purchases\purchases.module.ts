import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PurchasesComponent } from './purchases.component';
import { RouterModule, Routes } from '@angular/router';
import { SharedModule } from 'src/app/shared/shared.module';
import { PurchaseOrderLocalComponent } from './purchase-order-local/purchase-order-local.component';
import { NewPurchaseOrderLocalComponent } from './purchase-order-local/new-purchase-order-local/new-purchase-order-local.component';
import { PoLocalCartonMappingComponent } from './purchase-order-local/po-local-carton-mapping/po-local-carton-mapping.component';
import { NewPoLocalExpensesComponent } from './purchase-order-local/new-po-local-expenses/new-po-local-expenses.component';
import { PoLocalGrnListComponent } from './purchase-order-local/po-local-grn-list/po-local-grn-list.component';
import { PurchaseReturnsComponent } from './purchase-returns/purchase-returns.component';
import { NewPurchaseReturnsComponent } from './purchase-returns/new-purchase-returns/new-purchase-returns.component';
import { NewRecordPaymentComponent } from './creditors/new-record-payment/new-record-payment.component';
import { PaymentMadeComponent } from './payment-made/payment-made.component';
import { PoImportGrnListComponent } from './purchase-order-import/po-import-grn-list/po-import-grn-list.component';
import { GstInvoicesComponent } from './gst-invoices/gst-invoices.component';
import { NewGstInvoicesComponent } from './gst-invoices/new-gst-invoices/new-gst-invoices.component';
import { RecordGstInvoicePaymentComponent } from './gst-invoices/record-gst-invoice-payment/record-gst-invoice-payment.component';
import { CreditNoteComponent } from './credit-note/credit-note.component';
import { NewCreditNoteComponent } from './credit-note/new-credit-note/new-credit-note.component';
import { PoImportCartonMappingComponent } from './purchase-order-import/po-import-carton-mapping/po-import-carton-mapping.component';
import { PoLocalBreachQtyComponent } from './purchase-order-local/po-local-breach-qty/po-local-breach-qty.component';
import { PoLocalCompletedComponent } from './purchase-order-local/po-local-completed/po-local-completed.component';
import { PoLocalCreatedComponent } from './purchase-order-local/po-local-created/po-local-created.component';
import { PoLocalDraftComponent } from './purchase-order-local/po-local-draft/po-local-draft.component';
import { PoLocalGrnComponent } from './purchase-order-local/po-local-grn/po-local-grn.component';
import { PoLocalInTransitComponent } from './purchase-order-local/po-local-in-transit/po-local-in-transit.component';
import { PoLocalReceivedInSuratComponent } from './purchase-order-local/po-local-received-in-surat/po-local-received-in-surat.component';

const routes: Routes = [
  { path: '', redirectTo: 'purchases', pathMatch: 'full' },
  { path: 'po-local', component: PurchaseOrderLocalComponent },
  { path: 'new-po-local', component: NewPurchaseOrderLocalComponent },
  { path: 'expenses', canActivate: [], loadChildren: () => import('./expenses/expenses.module').then(m => m.ExpensesModule), title: 'Expenses' },
  { path: 'po-import', canActivate: [], loadChildren: () => import('./purchase-order-import/po-import.module').then(m => m.PoImportModule), title: 'PO Import' },
  { path: 'carton-mapping', component: PoImportCartonMappingComponent, title: 'Carton Mapping' },
  { path: 'carton-mapping/container/:containerId', component: PoImportCartonMappingComponent, title: 'Carton Mapping' },
  { path: 'carton-mapping/warehouse-allocation', component: PoImportCartonMappingComponent, title: 'Carton Mapping' },
  { path: 'po-local-carton-mapping', component: PoLocalCartonMappingComponent },
  { path: 'new-po-local-expenses', component: NewPoLocalExpensesComponent },
  { path: 'po-local-grn-list', component: PoLocalGrnListComponent },
  { path: 'creditors', canActivate: [], loadChildren: () => import('./creditors/creditors.module').then(m => m.CreditorsModule), title: 'Creditors' },
  { path: 'new-record-payment', component: NewRecordPaymentComponent },
  { path: 'purchase-returns', component: PurchaseReturnsComponent },
  { path: 'new-purchase-returns', component: NewPurchaseReturnsComponent },
  { path: 'payment-made', component: PaymentMadeComponent },
  { path: 'po-import-grn-list', component: PoImportGrnListComponent },
  { path: 'payments', loadChildren: () => import('./payments/payments.module').then(m => m.PaymentsModule), title: 'Payments' },
  { path: 'gst-invoices', component: GstInvoicesComponent },
  { path: 'new-gst-invoices', component: NewGstInvoicesComponent },
  { path: 'record-gst-invoice-payment', component: RecordGstInvoicePaymentComponent },
  { path: 'credit-note', component: CreditNoteComponent },
  { path: 'new-credit-note', component: NewCreditNoteComponent },
];

@NgModule({
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    SharedModule.forRoot()
  ],
  declarations: [PurchasesComponent, PoImportCartonMappingComponent, PurchaseOrderLocalComponent, NewPurchaseOrderLocalComponent, PoLocalCartonMappingComponent,
    NewPoLocalExpensesComponent,
    PoLocalGrnListComponent,
    PurchaseReturnsComponent,
    NewPurchaseReturnsComponent,
    NewRecordPaymentComponent,
    PaymentMadeComponent,
    PoImportGrnListComponent,
    GstInvoicesComponent,
    NewGstInvoicesComponent,
    RecordGstInvoicePaymentComponent,
    CreditNoteComponent,
    NewCreditNoteComponent,
    PoLocalBreachQtyComponent,
    PoLocalCompletedComponent,
    PoLocalCreatedComponent,
    PoLocalDraftComponent,
    PoLocalGrnComponent,
    PoLocalInTransitComponent,
    PoLocalReceivedInSuratComponent,
  ]
})
export class PurchasesModule { }
