<div class="page-content">
  <div class="page-title-wrapper">
    <div class="page-title-left">
      <h4>Employee Fault </h4>
    </div>
    <div class="page-title-right">
      <button class="btn btn-sm btn-primary btn-icon-text" [routerLink]="['/users/employee-fault/new-employee-fault']">
        <i class="th th-outline-add-circle"></i>Add New
      </button>
    </div>
  </div>

  <div class="content-area">
    <div class="page-filters border-bottom">
      <div class="page-filters-left">
        <div class="form-group theme-ngselect form-group-sm filter-category">
          <ng-select class="" placeholder="Company Name" [multiple]="false" [clearable]="false" [items]="Option"
            bindLabel="name" bindValue="id" [(ngModel)]="selectedOption">
          </ng-select>
        </div>
        <div class="form-group form-group-sm form-group-inline-control ">
          <div class="form-control-wrapper">
            <div class="form-group-icon-end">
              <i class="th th-outline-calendar-1"></i>
              <input type="text" class="form-control" placeholder="Last 30 days">
            </div>
          </div>
        </div>
        <div class="form-group theme-ngselect form-group-sm filter-category">
          <ng-select class="" placeholder="Registration Type" [multiple]="false" [clearable]="false" [items]="ItemGroup"
            bindLabel="name" bindValue="id" [(ngModel)]="selectedItemGroup">
          </ng-select>
        </div>
        <div class="form-group theme-ngselect form-group-sm">
          <ng-select class="" placeholder="Active" [multiple]="false" [clearable]="false" [items]="Active"
            bindLabel="name" bindValue="id" [(ngModel)]="selectedActive">
          </ng-select>
        </div>
      </div>
      <div class="page-filters-right">
        <div class="form-group theme-ngselect form-group-sm form-group-export">
          <ng-select class="" placeholder="Export" [multiple]="false" [clearable]="false" [items]="Export"
            bindLabel="name" bindValue="id" [(ngModel)]="selectedExport">
          </ng-select>
        </div>

        <div class="table-filter">
          <div class="dropdown">
            <button id="tbl-filter-dropdown" class="btn btn-icon btn-outline-white btn-sm" data-bs-toggle="dropdown"
              aria-expanded="false" data-bs-auto-close="outside" data-bs-popper-config='{"strategy":"fixed"}'>
              <i class="bi bi-sliders"></i>
            </button>

            <div class="table-filter-dialog dropdown-menu" aria-labelledby="tbl-filter-dropdown">
              <div class="table-filter-content">
                <div class="table-filter-header">
                  <h5 class="filter-title">Hide & Show Columns</h5>
                  <button type="button" class="btn-close" aria-label="Close">
                    <i class='th th-close'></i></button>
                </div>
                <div class="table-filter-body">
                  <ul class="filter-items-list drag-control-wrapper2">
                    <li class="drag-control-item">
                      <div class="drag-control-icon">
                        <img src="assets/images/drag.svg" alt="valamji">
                      </div>
                      <div class="drag-control-button">
                        <i class="th th-outline-lock"></i>
                      </div>
                      <div class="drag-control-label">
                        <p>Name & SKU</p>
                      </div>
                    </li>
                    <li class="drag-control-item">
                      <div class="drag-control-icon">
                        <img src="assets/images/drag.svg" alt="valamji">
                      </div>
                      <div class="drag-control-button">
                        <i class="th th-outline-lock"></i>
                      </div>
                      <div class="drag-control-label">
                        <p>Stock On Hand</p>
                      </div>
                    </li>
                    <li class="drag-control-item">
                      <div class="drag-control-icon">
                        <img src="assets/images/drag.svg" alt="valamji">
                      </div>
                      <div class="drag-control-button">
                        <i class="th th-outline-lock"></i>
                      </div>
                      <div class="drag-control-label">
                        <p>Reordered Level</p>
                      </div>
                    </li>
                    <li class="drag-control-item">
                      <div class="drag-control-icon">
                        <img src="assets/images/drag.svg" alt="valamji">
                      </div>
                      <div class="drag-control-button">
                        <i class="th th-outline-lock"></i>
                      </div>
                      <div class="drag-control-label">
                        <p>Actions</p>
                      </div>
                    </li>
                    <li class="drag-control-item selected">
                      <div class="drag-control-icon">
                        <img src="assets/images/drag.svg" alt="valamji">
                      </div>
                      <div class="drag-control-button">
                        <div class="checkbox checkbox-primary checkbox-small form-check-inline">
                          <input type="checkbox" id="checkbox1" class="material-inputs filled-in" />
                          <label for="checkbox1"> </label>
                        </div>
                      </div>
                      <div class="drag-control-label">
                        <p>Suppliers</p>
                      </div>
                    </li>
                    <li class="drag-control-item selected">
                      <div class="drag-control-icon">
                        <img src="assets/images/drag.svg" alt="valamji">
                      </div>
                      <div class="drag-control-button">
                        <div class="checkbox checkbox-primary checkbox-small form-check-inline">
                          <input type="checkbox" id="checkbox1" class="material-inputs filled-in" />
                          <label for="checkbox1"> </label>
                        </div>
                      </div>
                      <div class="drag-control-label">
                        <p>Rates</p>
                      </div>
                    </li>
                    <li class="drag-control-item selected">
                      <div class="drag-control-icon">
                        <img src="assets/images/drag.svg" alt="valamji">
                      </div>
                      <div class="drag-control-button">
                        <div class="checkbox checkbox-primary checkbox-small form-check-inline">
                          <input type="checkbox" id="checkbox1" class="material-inputs filled-in" />
                          <label for="checkbox1"> </label>
                        </div>
                      </div>
                      <div class="drag-control-label">
                        <p>Size & Dimensions</p>
                      </div>
                    </li>
                    <li class="drag-control-item selected">
                      <div class="drag-control-icon">
                        <img src="assets/images/drag.svg" alt="valamji">
                      </div>
                      <div class="drag-control-button">
                        <div class="checkbox checkbox-primary checkbox-small form-check-inline">
                          <input type="checkbox" id="checkbox1" class="material-inputs filled-in" />
                          <label for="checkbox1"> </label>
                        </div>
                      </div>
                      <div class="drag-control-label">
                        <p>Average Price</p>
                      </div>
                    </li>
                    <li class="drag-control-item">
                      <div class="drag-control-icon">
                        <img src="assets/images/drag.svg" alt="valamji">
                      </div>
                      <div class="drag-control-button">
                        <div class="checkbox checkbox-primary checkbox-small form-check-inline">
                          <input type="checkbox" id="checkbox1" class="material-inputs filled-in" />
                          <label for="checkbox1"> </label>
                        </div>
                      </div>
                      <div class="drag-control-label">
                        <p>Description</p>
                      </div>
                    </li>
                    <li class="drag-control-item">
                      <div class="drag-control-icon">
                        <img src="assets/images/drag.svg" alt="valamji">
                      </div>
                      <div class="drag-control-button">
                        <div class="checkbox checkbox-primary checkbox-small form-check-inline">
                          <input type="checkbox" id="checkbox1" class="material-inputs filled-in" />
                          <label for="checkbox1"> </label>
                        </div>
                      </div>
                      <div class="drag-control-label">
                        <p>Manufacturer</p>
                      </div>
                    </li>
                    <li class="drag-control-item">
                      <div class="drag-control-icon">
                        <img src="assets/images/drag.svg" alt="valamji">
                      </div>
                      <div class="drag-control-button">
                        <div class="checkbox checkbox-primary checkbox-small form-check-inline">
                          <input type="checkbox" id="checkbox1" class="material-inputs filled-in" />
                          <label for="checkbox1"> </label>
                        </div>
                      </div>
                      <div class="drag-control-label">
                        <p>Purchase Price</p>
                      </div>
                    </li>
                    <li class="drag-control-item">
                      <div class="drag-control-icon">
                        <img src="assets/images/drag.svg" alt="valamji">
                      </div>
                      <div class="drag-control-button">
                        <div class="checkbox checkbox-primary checkbox-small form-check-inline">
                          <input type="checkbox" id="checkbox1" class="material-inputs filled-in" />
                          <label for="checkbox1"> </label>
                        </div>
                      </div>
                      <div class="drag-control-label">
                        <p>Sales Price</p>
                      </div>
                    </li>
                  </ul>
                </div>
                <div class="table-filter-footer">
                  <div class="table-filter-footer-group full-width-btn">
                    <button type="button" class="btn btn-primary btn-icon-text"> <i
                        class="th th-outline-tick-circle"></i>
                      Save</button>
                    <button type="button" class="btn btn-outline-white" aria-label="Close">Cancel</button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="card card-theme card-table-sticky ">
      <div class="card-body p-0">

        <div class="p-3">
          <div class="card card-theme4 ">
            <div class="card-header">
              <div class="card-header-left">
                <div class="card-header-title">
                  <h2>Employee Faults Report</h2>
                </div>
              </div>
              <div class="card-header-right">
                <div class="form-group theme-ngselect form-group-sm form-border-less bg-transparent">
                  <ng-select class="" placeholder="Active" [multiple]="false" [clearable]="false" [items]="Mistake"
                    bindLabel="name" bindValue="id" [(ngModel)]="selectedMistake">
                  </ng-select>
                </div>
              </div>
            </div>
            <div class="card-body">
              <img class="w-100" src="assets/images/chart.svg" alt="valamji">
            </div>
          </div>
        </div>


        <div class="accordion accordion-group" id="accordionSingle">
          <div class="accordion-item">
            <h2 class="accordion-header" id="accordionSingleHeadingOne">
              <button class="accordion-button" type="button" data-bs-toggle="collapse"
                data-bs-target="#accordionSingleCollapseOne" aria-expanded="true"
                aria-controls="accordionSingleCollapseOne">
                <div class="accordion-header-left">
                  <div class="line-text-divider">
                    <div class="checkbox checkbox-primary checkbox-small">
                      <input type="checkbox" id="tbl-checkbox" class="material-inputs filled-in">
                      <label for="tbl-checkbox"></label>
                    </div>
                    <h6>Nirali</h6>
                    <h6>5 Faults</h6>
                  </div>
                </div>
                <div class="accordion-header-right">


                </div>
              </button>
            </h2>
            <div id="accordionSingleCollapseOne" class="accordion-collapse collapse show"
              aria-labelledby="accordionSingleHeadingOne" data-bs-parent="#accordionSingle">
              <div class="accordion-body tbl-accordion-body">
                <div class="table-responsive">

                  <table class="table-theme table-hover table table-bordered tbl-collapse">
                    <thead class="border-less">
                      <tr>
                        <th>
                          <div class="d-flex align-items-center">
                            <div class="checkbox checkbox-primary checkbox-small">
                              <input type="checkbox" id="tbl-checkbox" class="material-inputs filled-in">
                              <label for="tbl-checkbox"></label>
                            </div>
                            Fault Type
                          </div>
                        </th>
                        <th>Record Date</th>
                        <th>Critical Level</th>
                        <th>Record By</th>
                        <th>Note</th>
                        <th>Action</th>
                      </tr>
                    </thead>
                    <tbody>
                      <ng-container>
                        <tr>
                          <td class="tbl-user">
                            <div class="d-flex align-items-center">
                              <div class="checkbox checkbox-primary checkbox-small">
                                <input type="checkbox" id="tbl-checkbox" class="material-inputs filled-in">
                                <label for="tbl-checkbox"></label>
                              </div>
                              <b class="text-black">Sales Order Packing</b>
                            </div>
                          </td>
                          <td>12/12/2024 11:30AM</td>
                          <td class="text-danger">Critical </td>
                          <td>Jayesh Patel</td>
                          <td>Wrong Qty Packed</td>
                          <td class="tbl-action">
                            <div class="tbl-action-group">
                              <button class="btn btn-xs btn-light-white btn-icon" ngbTooltip="Edit" placement="bottom"
                                container="body" triggers="hover">
                                <i class="th th-outline-edit"></i></button>

                              <button class="btn btn-xs btn-light-danger btn-icon" data-bs-toggle="modal"
                                data-bs-target="#deleteModal" ngbTooltip="Delete" placement="bottom" container="body"
                                triggers="hover">
                                <i class="th th-outline-trash"></i></button>

                              <app-attachment-download-dropdown></app-attachment-download-dropdown>

                            </div>
                          </td>
                        </tr>
                      </ng-container>
                    </tbody>
                  </table>

                </div>
              </div>
            </div>
          </div>

          <div class="accordion-item">
            <h2 class="accordion-header" id="accordionSingleHeadingTwo">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                data-bs-target="#accordionSingleCollapseTwo" aria-expanded="false"
                aria-controls="accordionSingleCollapseTwo">
                <div class="accordion-header-left">
                  <div class="line-text-divider">
                    <div class="checkbox checkbox-primary checkbox-small">
                      <input type="checkbox" id="tbl-checkbox" class="material-inputs filled-in">
                      <label for="tbl-checkbox"></label>
                    </div>
                    <h6>Nirali</h6>
                    <h6>5 Faults</h6>
                  </div>
                </div>
                <div class="accordion-header-right">
                </div>
              </button>
            </h2>
            <div id="accordionSingleCollapseTwo" class="accordion-collapse collapse"
              aria-labelledby="accordionSingleHeadingTwo" data-bs-parent="#accordionSingle">
              <div class="accordion-body tbl-accordion-body">


                <div class="table-responsive">

                  <table class="table-theme table-hover table table-bordered tbl-collapse">
                    <thead class="border-less">
                      <tr>
                        <th>
                          <div class="d-flex align-items-center">
                            <div class="checkbox checkbox-primary checkbox-small">
                              <input type="checkbox" id="tbl-checkbox" class="material-inputs filled-in">
                              <label for="tbl-checkbox"></label>
                            </div>
                            Fault Type
                          </div>
                        </th>
                        <th>Record Date</th>
                        <th>Critical Level</th>
                        <th>Record By</th>
                        <th>Note</th>
                        <th>Action</th>
                      </tr>
                    </thead>
                    <tbody>
                      <ng-container>
                        <tr>
                          <td class="tbl-user">
                            <div class="d-flex align-items-center">
                              <div class="checkbox checkbox-primary checkbox-small">
                                <input type="checkbox" id="tbl-checkbox" class="material-inputs filled-in">
                                <label for="tbl-checkbox"></label>
                              </div>
                              <b class="text-black">Sales Order Packing</b>
                            </div>
                          </td>
                          <td>12/12/2024 11:30AM</td>
                          <td class="text-danger">Critical </td>
                          <td>Jayesh Patel</td>
                          <td>Wrong Qty Packed</td>
                          <td class="tbl-action">
                            <div class="tbl-action-group">
                              <button class="btn btn-xs btn-light-white btn-icon" ngbTooltip="Edit" placement="bottom"
                                container="body" triggers="hover">
                                <i class="th th-outline-edit"></i></button>

                              <button class="btn btn-xs btn-light-danger btn-icon" data-bs-toggle="modal"
                                data-bs-target="#deleteModal" ngbTooltip="Delete" placement="bottom" container="body"
                                triggers="hover">
                                <i class="th th-outline-trash"></i></button>

                              <app-attachment-download-dropdown></app-attachment-download-dropdown>

                            </div>
                          </td>
                        </tr>
                      </ng-container>
                    </tbody>
                  </table>

                </div>

              </div>
            </div>
          </div>

        </div>
      </div>
    </div>

  </div>

</div>




<!-- ----------------------------------------------------------------------- -->
<!--                           Delete Modal Start                            -->
<!-- ----------------------------------------------------------------------- -->
<div class="modal modal-theme modal-confirmation modal-reject fade" id="deleteModal" tabindex="-1"
  aria-labelledby="deleteModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
            class='th th-close'></i></button>
      </div>
      <div class="modal-body">
        <div class="modal-confirmation-container">
          <div class="modal-confirmation-icon">
            <i class="th th-bold-trash"></i>
          </div>
          <div class="modal-confirmation-content">
            <h5>Are You Sure?</h5>
            <p>You want to Delete <b>{{"SelectedReacord"}}</b> Fault Type.</p>
          </div>
        </div>
        <div class="modal-button-group modal-full-width-btn">
          <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
          <button type="button" class="btn btn-primary btn-icon-text"> <i class="th th-outline-tick-circle"></i>
            Delete</button>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                           Delete Modal End                            -->
<!-- ----------------------------------------------------------------------- -->