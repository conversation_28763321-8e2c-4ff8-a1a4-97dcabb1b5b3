import { Directive, HostListener, Input } from '@angular/core';

@Directive({
  selector: '[copyText]'
})
export class CopyTextDirective {

  @Input() public copyText = '';

  @HostListener('click', ['$event'])
  public onClick(event: MouseEvent): void {
    event.preventDefault();

    const listener = (e: ClipboardEvent) => {
      e.clipboardData.setData('text', this.copyText);
      e.preventDefault();
    };

    document.addEventListener('copy', listener, false);
    document.execCommand('copy');
    document.removeEventListener('copy', listener, false);
  }

}