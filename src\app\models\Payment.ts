export type PaymentSave = Omit<PaymentDTO, 'dueAmount' | 'credit' | 'paymentImgs'>;

export interface PaymentDTO {
    id: number;
    supplierId: number;
    paymentDate: string;
    paymentAmountRMB: number;
    conversionRate: number;
    paidBy: string;
    notes: string;
    bankGroupId: number;
    paymentTypeId: number;
    paymentImgs: PaymentImg[];
    deletedDocumentID: number[];
    dueAmount: number;
    credit: number;
}

interface PaymentImg {
    id: number;
    file: File;
    originalName: string;
    formattedName: string;
}
