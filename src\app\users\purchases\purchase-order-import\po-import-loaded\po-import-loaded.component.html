<div class="page-filters">
    <div class="page-filters-left">
        <div class="form-group form-group-sm filter-search">
            <div class="form-group-icon-start">
                <i class="th th-outline-search-normal-1 icon-broder "></i>
                <input (change)="onSearchMarka($event)" [(ngModel)]="paginationRequest.searchText" type="text" class="form-control" placeholder="Search by Marka">
            </div>
        </div>
        <div class="form-group form-group-sm date-range-filter">
            <div class="form-group-icon-end">
                <i (click)="open()" class="th th-outline-calendar ngx-daterangepicker-action"></i>
                <input #pickerDirective class="form-control" type="text" ngxDaterangepickerMd readonly
                    [formControl]="paginationRequest.dateRangeControl" [showCustomRangeLabel]="true" [alwaysShowCalendars]="true"
                    [ranges]="utilsService.ranges" [linkedCalendars]="false" [showClearButton]="false"
                    placeholder="Loaded Date" [autoApply]="true" [showRangeLabelOnInput]="true" startKey="start"
                    endKey="end">
            </div>
        </div>
        <!-- <div class="form-group form-group-sm">
            <div class="form-group-icon-end">
                <i (click)="f.toggle()" class="th th-outline-calendar-1"></i>
                <input (click)="f.toggle()" (dateSelect)="onChangeDate('loaded')" readonly
                    [(ngModel)]="paginationRequest.temp_loadedDate" type="text" class="form-control" placeholder="Import Date"
                    ngbDatepicker #f="ngbDatepicker">
            </div>
        </div> -->
        <div class="form-group form-group-sm">
            <div class="form-group-icon-end">
                <i (click)="e.toggle()" class="th th-outline-calendar-1"></i>
                <input (click)="e.toggle()" (dateSelect)="onChangeDate('delivery')"
                    [(ngModel)]="paginationRequest.temp_expectedDeliveryDate" readonly type="text" class="form-control"
                    placeholder="Delivery Date" ngbDatepicker #e="ngbDatepicker">
            </div>
        </div>
        <!-- <div class="form-group theme-ngselect form-group-sm">
            <ng-select placeholder="Payment Status" [multiple]="false" [clearable]="true" [items]="demo" bindLabel="name"
                bindValue="id">
            </ng-select>
        </div> -->
        <div class="form-group theme-ngselect form-group-sm">
            <ng-select (change)="onChangeImporter()" placeholder="Importer" [multiple]="false" [clearable]="true" [items]="dropdown?.importer"
                bindLabel="label" bindValue="value" [(ngModel)]="paginationRequest.importerId">
            </ng-select>
        </div>
        <button (click)="onClear.emit()" class="btn btn-link btn-sm">Clear</button>
    </div>
    <div class="page-filters-right">
        <!-- <app-table-column-filter-dropdown-new [allHeaderArr]="allHeaderArr" [columnArr]="columnArr"
            (saveCol)="saveCol.emit()" (checkIfAllSelected)="checkIfAllSelected.emit()" /> -->
    </div>
</div>

<div class="card card-theme card-table-sticky3">
    <div class="card-body p-0">

        <div class="table-responsive ">
            <table class="table-theme table-hover table table-bordered tbl-collapse  table-sticky">
                <thead class="border-less">
                    <tr class="">
                        <th>
                            # Container No
                        </th>
                        <th>Loaded Date</th>
                        <th>Expected <br/> Delivery Date</th>
                        <th>Total Loaded <br/> Cartons</th>
                        <th>Importer</th>
                        <th>Total Container Amount <br/> (RMB)</th>
                        <th>Payment Status</th>
                        <th>Note</th>
                        <th class="text-end">Action</th>
                    </tr>
                </thead>
                <tbody>
                    <ng-container *ngFor="let item of poImportList; index as i; trackBy: trackBy">
                        <tr [ngClass]="{'tbl-bg-secondary-two': item.isExpand}" (click)="toggleExpand(i)">
                            <td class=" tbl-user">
                                <div class="tbl-user-checkbox-srno">
                                    <span>{{(i + 1) | padNum}}.</span>
                                    <b class="text-black">
                                        {{item.containerName}}
                                    </b>
                                </div>
                            </td>
                            <td>{{item.loadedDate ? (item.loadedDate | date: 'dd/MM/YYYY') : '-'}}</td>
                            <td>{{item.expectedDeliveryDate ? (item.expectedDeliveryDate | date: 'dd/MM/YYYY') : '-'}}</td>
                            <td>{{item.totalLoadedCarton ? item.totalLoadedCarton : '-'}}</td>
                            <td>{{item.importerName}}</td>
                            <td>{{item.totalContainerAmount ? (item.totalContainerAmount | indianCurrency) : 0}}</td>
                            <td>- </td>
                            <td class="tbl-description">{{item.note ? item.note : '-'}}</td>
                            <td class="tbl-action" (click)="$event.stopPropagation()">
                                <div class="tbl-action-group justify-content-end">
                                    <button (click)="onPacking(item.id)" class="btn btn-outline-primary btn-icon-text btn-sm"> <i
                                            class="bi bi-download "></i>
                                        Download Packing </button>
                                    <button [pageAccess]="{page: utilsService.enumForPage.PO, action: utilsService.enumForPage.MOVE_TO_RELEASED}"
                                        (click)="openModalLoadedToRelease(item)" class="btn btn-xs btn-light-primary btn-icon"
                                        ngbTooltip="Convert To Release" placement="bottom" container="body" triggers="hover">
                                        <i class="th-outline-arrow-right-1"></i>
                                    </button>

                                    <div class="dropdown">
                                        <button class="btn btn-xs btn-light-white btn-icon" id="actionDropDown"
                                            data-bs-toggle="dropdown" aria-expanded="false"
                                            data-bs-popper-config='{"strategy":"fixed"}' ngbTooltip="More Option"
                                            placement="bottom" container="body" triggers="hover">
                                            <i class="th th-outline-more"></i>
                                        </button>
                                        <ul class="dropdown-menu" aria-labelledby="actionDropDown">
                                            <li [pageAccess]="{page: utilsService.enumForPage.PO, action: utilsService.enumForPage.EDIT_CONTAINER_NO}">
                                                <a class="dropdown-item" (click)="openLoadedEditCon(item)"><i
                                                        class="th th-outline-edit"></i>Edit Container No
                                                </a>
                                            </li>
                                            <!-- <li><a class="dropdown-item"><i class="th th-outline-barcode"></i>Print QR
                                                    Code</a>
                                            </li> -->
                                            <li>
                                                <a (click)="redirectToExp(item.containerId)" class="dropdown-item"><i class="th th-outline-add-circle"></i>Add
                                                    Expense
                                                </a>
                                            </li>
                                            <!-- <li><a class="dropdown-item"
                                                                                        [routerLink]="['/users/purchases/po-import-carton-mapping']"><i
                                                                                            class="th th-outline-box"></i>Carton
                                                                                        Mapping</a></li>
                                                                                <li><a class="dropdown-item"
                                                                                        [routerLink]="['/users/audit-tickets/list']"><i
                                                                                            class="th th-outline-box-1"></i>Audit
                                                                                        Ticket</a></li> -->
                                            <li><a class="dropdown-item"
                                                    [routerLink]="['/users/purchases/new-payments']"><i
                                                        class="th th-outline-dollar-circle"></i>Make
                                                    Payment</a>
                                            </li>
                                            <!-- <li><a class="dropdown-item"><i
                                                                                            class="th th-outline-tick-circle"></i>Mark
                                                                                        As Received</a>
                                                                                </li>
                                                                                <li><a class="dropdown-item"><i
                                                                                            class="th th-outline-close-circle"></i>Cancel
                                                                                        Shipment</a>
                                                                                </li> -->
                                        </ul>

                                    </div>

                                    <button (click)="toggleExpand(i)" class="btn btn-xs text-color btn-icon btn-link"
                                        data-bs-toggle="collapse" [ngClass]="{'collapse-arrow': item.isExpand}"
                                        role="button" aria-expanded="false" [attr.data.target]="'#table-collapse-2'+ i"
                                        [attr.aria-controls]="'table-collapse-2'+ i">
                                        <i class="th th-outline-arrow-right-3"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>

                        <tr *ngIf="item.isExpand" class="collapse" [id]="'table-collapse-2' + i" [ngClass]="{'show': item.isExpand}">
                            <td colspan="30" class="p-0 tbl-collapse-child tbl-collapse-child-responsive">

                                <div class="table-responsive">
                                    <table class="table-theme table-hover table table-bordered table-sticky">
                                        <thead class="border-less">

                                            <tr>
                                                <ng-container *ngFor="let th of headerObj?.optionsArray | filterByShippingType: item?.shippingTypes?.value; index as k">
                                                    <th *ngIf="th.show" [class]="th.class" [innerHTML]="th.displayName">
                                                    </th>
                                                </ng-container>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr [ngClass]="{'tbl-bg-danger': child.purchaseRationFlag}"
                                                *ngFor="let child of item.poImportItemList; index as l; trackBy: trackByChild">
                                                <ng-container *ngFor="let column of headerObj.columnArr | filterByShippingType: item?.shippingTypes?.value;">
                                                    <td class="tbl-user" *ngIf="column.show">
                                                        <ng-container [ngSwitch]="column.key">
                                                            <ng-container *ngSwitchCase="0">
                                                                <div class="tbl-user-checkbox-srno">
                                                                    <span>{{(l + 1) | padNum}}.</span>
                                                                    <div class="tbl-user-wrapper">
                                                                        <div class="tbl-user-image" *ngIf="child?.item">
                                                                            <img *ngIf="child.item?.formattedName" loading="lazy"
                                                                                [src]="child.item.formattedName ? (utilsService.imgPath + child.item.formattedName) : ''"
                                                                                alt="valamji">
                                                                            <ng-container *ngIf="!child.item?.formattedName">{{
                                                                                child.displayName?.charAt(0).toUpperCase()
                                                                                }}
                                                                            </ng-container>
                                                                        </div>
                                                                        <div class="tbl-user-text-action">
                                                                            <div class="tbl-user-text">
                                                                                <p>{{child.item?.skuId}}</p>
                                                                                <span class="tbl-description">{{child.item.displayName}}</span>
                                                                            </div>
                                                                        </div>
                                                                        <div class="dropdown" *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.VIEW_ITEM])">
                                                                            <button class="btn btn-xs btn-light-white btn-icon" id="actionDropDown"
                                                                                data-bs-toggle="dropdown" aria-expanded="false" data-bs-popper-config='{"strategy":"fixed"}'
                                                                                ngbTooltip="More Option" placement="bottom" container="body" triggers="hover"
                                                                                ngbTooltip="More Option" placement="bottom" container="body" triggers="hover">
                                                                                <i class="th th-outline-more"></i>
                                                                            </button>
                                                                            <ul class="dropdown-menu" aria-labelledby="actionDropDown">
                                                                                <li [pageAccess]="{page: utilsService.enumForPage.ITEM, action: utilsService.enumForPage.VIEW_ITEM}">
                                                                                    <a class="dropdown-item" (click)="utilsService.openItemDetailsInNewTab(child.itemId)">
                                                                                        <i class="th th-outline-eye"></i>View Item Details
                                                                                    </a>
                                                                                </li>
                                                                                <li>
                                                                                    <a class="dropdown-item" (click)="onStatusOpen(child, item, null)">
                                                                                        <i class="th th-outline-status"></i>
                                                                                        Change Status
                                                                                    </a>
                                                                                </li>
                                                                            </ul>
                                                    
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </ng-container>
                                                    
                                                    
                                                            <ng-container *ngSwitchCase="1">
                                                                <span class="d-flex flex-column align-items-start">
                                                                    <div>{{child.marka ? child.marka : '-'}}</div>
                                                                    <div>
                                                                        <ng-container>
                                                                            {{child.cartonLength ? child.cartonLength : '-'}} X
                                                                            {{child.cartonWidth ? child.cartonWidth : '-'}} X
                                                                            {{child.cartonHeight ? child.cartonHeight : '-'}}
                                                                            {{child?.cartonDimensionUnit ? child?.cartonDimensionUnit?.shortCode : ''}}
                                                                        </ng-container>
                                                                    </div>
                                                                    <div>
                                                                        <p>{{child.pricePerCarton ? child.pricePerCarton : '-'}}</p>
                                                                    </div>
                                                                    <div>
                                                                        <p class="tbl-po-notes">{{child.chinaComment ? child.chinaComment : ''}}</p>
                                                                    </div>
                                                                </span>
                                                            </ng-container>
                                                    
                                                            <ng-container *ngSwitchCase="2">
                                                                <span class="w-100 d-block" *ngFor="let v of child.colorName">
                                                                    {{!utilsService.isEmptyObjectOrNullUndefined(v) ? v : ''}}
                                                                </span>
                                                            </ng-container>
                                                    
                                                            <ng-container *ngSwitchCase="3">
                                                                -
                                                            </ng-container>
                                                    
                                                            <ng-container *ngSwitchCase="4">
                                                                <div>
                                                                    {{child.poDate ?
                                                                    (child.poDate |
                                                                    date: 'dd/MM/YYY') : '-'}}
                                                                </div>
                                                            </ng-container>
                                                    
                                                            <ng-container *ngSwitchCase="5">
                                                                <div>
                                                                    {{child.expectedDeliveryDate ? (child.expectedDeliveryDate | date: 'dd/MM/YYYY') : '-'}}
                                                                </div>
                                                            </ng-container>
                                                    
                                                            <ng-container *ngSwitchCase="6">
                                                                <div>
                                                                    {{child.poCarton ? (child.poCarton) : 0}}
                                                                </div>
                                                            </ng-container>
                                                    
                                                            <ng-container *ngSwitchCase="7">
                                                                <div>
                                                                    {{child.loadedQty ? child.loadedQty : 0}}
                                                                </div>
                                                            </ng-container>
                                                            <ng-container *ngSwitchCase="8">
                                                                <span class="w-100 d-block" *ngFor="let v of (child.rcId)">
                                                                    {{!utilsService.isEmptyObjectOrNullUndefined(v) ? v : ''}}
                                                                </span>
                                                            </ng-container>
                                                            <ng-container *ngSwitchCase="9">
                                                                {{child.pricePerCarton ? child.pricePerCarton : 0}}
                                                            </ng-container>
                                                            <ng-container *ngSwitchCase="10">
                                                                {{(child.totalPcsQty) ? (child.totalPcsQty) : 0}}
                                                            </ng-container>
                                                            <ng-container *ngSwitchCase="11">
                                                                <div>
                                                                    {{child.pricePerItem ? child.pricePerItem : 0}}
                                                                </div>
                                                            </ng-container>
                                                            <ng-container *ngSwitchCase="12">
                                                                <div>
                                                                    {{child.Total_Price ? (child.Total_Price | indianCurrency) :0}}
                                                                </div>
                                                            </ng-container>
                                                            
                                                            <ng-container *ngSwitchCase="35">
                                                                <div>
                                                                    {{child.rcCarton ? (child.rcCarton) : 0}}
                                                                </div>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="40">
                                                                <div>
                                                                    {{child.totalLoadedQtyAmount ? (child.totalLoadedQtyAmount | indianCurrency) :0}}
                                                                </div>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="13">
                                                                <div>
                                                                    {{child.expDeliveryCost ?
                                                                    (child.expDeliveryCost | indianCurrency) : 0}}
                                                                </div>
                                                            </ng-container>
                                                            <ng-container *ngSwitchCase="14">
                                                                <div>
                                                                    {{child.totalAmountWithExp ?
                                                                    (child.totalAmountWithExp | indianCurrency) : 0}}
                                                                </div>
                                                            </ng-container>
                                                            <ng-container *ngSwitchCase="15">
                                                                <!-- {{child.conversationRateCurrencyName
                                                                    ?
                                                                    child.conversationRateCurrencyName
                                                                    : ''}} -->
                                                                    {{child.conversationRate ?
                                                                    child.conversationRate : '-'}}
                                                            </ng-container>
                                                            <ng-container *ngSwitchCase="16">
                                                                {{child.totalAmountWithExpInINR ?
                                                                    (child.totalAmountWithExpInINR | indianCurrency) :
                                                                    0}}
                                                            </ng-container>
                                                            <ng-container *ngSwitchCase="17">
                                                                {{child.totalAmountWithExpInINR ? (child.totalAmountWithExpInINR | indianCurrency) : 0}}
                                                            </ng-container>
                                                            <ng-container *ngSwitchCase="18">
                                                                {{item.shippingTypes ? (item.shippingTypes.value) : '-'}}
                                                            </ng-container>
                                                            <ng-container *ngSwitchCase="19">
                                                                {{child.cartonLength ? child.cartonLength : '-'}} x {{child.cartonWidth ? child.cartonWidth : '-'}}
                                                                x
                                                                {{child.cartonHeight ? child.cartonHeight : '-'}} {{child?.cartonDimensionUnit ? child?.cartonDimensionUnit?.shortCode : ''}}
                                                            </ng-container>
                                                            <ng-container *ngSwitchCase="20">
                                                                {{child.dimAge}}
                                                            </ng-container>
                                                            <ng-container *ngSwitchCase="21">
                                                                {{child.cbmCarton ? (child.cbmCarton | indianCurrency) : 0}}
                                                            </ng-container>
                                                            <ng-container *ngSwitchCase="22">
                                                                {{child.totalCbm ? (child.totalCbm | indianCurrency) : 0}}
                                                            </ng-container>
                                                            <ng-container *ngSwitchCase="23">
                                                                {{item.cbmPrice ? (item.cbmPrice | indianCurrency) : 0}}
                                                            </ng-container>
                                                            <ng-container *ngSwitchCase="24">
                                                                {{child.totalCBMExpense ? (child.totalCBMExpense | indianCurrency) : 0}}
                                                            </ng-container>
                                                            <ng-container *ngSwitchCase="25">
                                                                {{child.shippingExpense ? (child.shippingExpense | indianCurrency) : 0}}
                                                            </ng-container>
                                                            <ng-container *ngSwitchCase="26">
                                                                <ng-container *ngFor="let ls of child?.lastRecord">
                                                                    {{ls?.supplierShortCode}}, {{ls?.pricePerCarton}}, {{ls?.poCarton}} <br />
                                                                </ng-container>
                                                            </ng-container>

                                                            <ng-container *ngSwitchCase="27">
                                                                -
                                                            </ng-container>
                                                            <ng-container *ngSwitchCase="28">
                                                                -
                                                            </ng-container>
                                                            <ng-container *ngSwitchCase="29">
                                                                {{child.gst_amount ? (child.gst_amount | indianCurrency ): 0}}
                                                            </ng-container>
                                                            <ng-container *ngSwitchCase="30">
                                                                {{child.gstPer ? (child.gstPer ): 0}}
                                                            </ng-container>
                                                            <ng-container *ngSwitchCase="31">
                                                                {{child.extraExpense ? (child.extraExpense | indianCurrency) : 0}}
                                                            </ng-container>
                                                            <ng-container *ngSwitchCase="32">
                                                                {{child.purchaseRatio ? child.purchaseRatio : 0}}
                                                            </ng-container>


                                                            <ng-container>
                                                                <ng-container *ngIf="column.key =='cartonWeight'">
                                                                    {{child.Weight_Carton ?
                                                                    child.Weight_Carton : 0}}
                                                                </ng-container>
                                                                <ng-container *ngIf="column.key =='totalWeight'">
                                                                    {{child.total_Weight ? child.total_Weight : 0}}
                                                                </ng-container>
                                                                <ng-container *ngIf="column.key =='cartonWeightRu'">
                                                                    {{child.Weight_kg ?
                                                                    child.Weight_kg : 0}}
                                                                </ng-container>
                                                                <ng-container *ngIf="column.key =='totalLoadAmt'">
                                                                    {{child.total_load_amt_Ru ? child.total_load_amt_Ru : 0}}
                                                                </ng-container>
                                                                <ng-container *ngIf="column.key =='shippingCostperPieceINR'">
                                                                    {{child.shipingCost ? child.shipingCost : 0}}
                                                                </ng-container>
                                                                <ng-container *ngIf="column.key =='totalShippingExpWeight'">
                                                                    {{child.totalShippingExpense ? child.totalShippingExpense : 0}}
                                                                </ng-container>
                                                            </ng-container>
                                                            
                                                            <ng-container>
                                                            
                                                                <ng-container *ngIf="column.key =='percentage'">
                                                                    {{child.Percentage ? child.Percentage :
                                                                    0}}
                                                                </ng-container>
                                                                <ng-container *ngIf="column.key =='totalExpPCSper'">
                                                                    {{child.totalExpense ?
                                                                    (child.totalExpense | indianCurrency): 0}}
                                                                </ng-container>
                                                                <ng-container *ngIf="column.key =='totalFinalCostPCSper'">
                                                                    {{child.totalFinalCost ?
                                                                    (child.totalFinalCost | indianCurrency) : 0}}
                                                                </ng-container>
                                                            
                                                            </ng-container>

                                                            <ng-container *ngIf="column.key =='expensePcs'">
                                                                {{child.Expense_PCS ?
                                                                (child.Expense_PCS | indianCurrency) :
                                                                0}}
                                                            </ng-container>
                                                            <ng-container *ngIf="column.key =='totalItemAmt'">
                                                                {{child.totalItemAmount ? (child.totalItemAmount | indianCurrency) :
                                                                0}}
                                                            </ng-container>
                                                            
                                                            <ng-container>
                                                                <ng-container *ngIf="column.key ==50">
                                                                    {{child.item_Amount ?
                                                                    (child.item_Amount | indianCurrency) : 0}}
                                                                </ng-container>
                                                                <ng-container *ngIf="column.key ==51">
                                                                    {{child.item_Amount ?
                                                                    (child.item_Amount | indianCurrency) : 0}}
                                                                </ng-container>
                                                            </ng-container>

                                                
                                                        </ng-container>
                                                    </td>
                                                </ng-container>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                            </td>
                        </tr>
                    </ng-container>
                    <ng-container>
                        <tr *ngIf="utilsService.isEmptyObjectOrNullUndefined(poImportList)">
                            <td colspan="20" class="text-center">
                                <span
                                    class="truncate">{{utilsService.serverVariableService.STRING_WHEN_NO_RECORDS_FOUND}}</span>
                            </td>
                        </tr>
                    </ng-container>

                </tbody>
            </table>
        </div>

    </div>
</div>

<div class="paginationbox pagination-fixed">
    <app-pagination (pagesizeData)="addPageSizeData($event)" (pageNumber)="pageNumber($event)"
        [page]="paginationRequest.pageNo" [pageSize]="paginationRequest.pageSize"
        [totalData]="paginationRequest.totalData"></app-pagination>
</div>