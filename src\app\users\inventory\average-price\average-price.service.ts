import { inject, Injectable, signal } from '@angular/core';
import { UtilsService } from '@service/utils.service';
import { map, Observable, of } from 'rxjs';
import { AVERAGE_PRICE } from 'src/app/shared/constants/constant';
import { AveragePrice } from '@modal/AveragePrice';
import { saveAs } from 'file-saver';

type Dropdown = {
  container: Container[]
}

type Container = {
  id: number,
  containerName: string
}

class AveragePriceRequest {
  containerId: number
  flagForSelectAll: boolean
  avgPriceTo: number
  avgPriceFrom: number
  searchByItemName: string
}

@Injectable()

export class AveragePriceService {

  utilsService = inject(UtilsService)

  containerList = signal<Container[]>([]);
  averagePriceList = signal<AveragePrice[]>([]);

  averagePrice = AVERAGE_PRICE;
  paginationRequest = signal(new AveragePriceRequest())
  selectedIds: number[] = [];

  approveAvgPriceModal: any;
  rollBackAvgPriceModal: any;

  constructor() { }

  getContainersList = (): Observable<Dropdown> => {
    return this.utilsService.get(this.utilsService.serverVariableService.AVERAGE_PRICE_REQ_DATA, { toast: false }).pipe(
      map((response) => (response?.data || []) as Dropdown)
    );
  }

  getAveragePriceListing = (): Observable<AveragePrice[]> => {
    return this.utilsService.post(this.utilsService.serverVariableService.AVERAGE_PRICE_LISTING, this.paginationRequest(), { toast: false }).pipe(
      map((response) => response?.data?.['content'] || [] as AveragePrice[])
    )
  }

  exportExcel = () => {
    const param = {
      ids: this.selectedIds ? this.selectedIds : [],
      containerId: this.paginationRequest().containerId
    }

    this.utilsService.exportReport(param, this.utilsService.serverVariableService.AVG_PRICE_EXPORT).subscribe((data: any) => {
      saveAs(new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' }), 'Average Price Sheet');
    });
  }

  saveAveragePriceList = (saveStage: 'save' | 'approve' | 'rollback') => {

    if (saveStage === 'save') {
      const staffSPEmpty = this.averagePriceList().every(a => a.newSPStaff === null || a.newSPStaff === "" as any);
      if (staffSPEmpty) {
        this.utilsService.toasterService.error('Sales Price for at least one item is required', '', {
          positionClass: 'toast-top-right',
          closeButton: true,
          timeOut: 10000
        });
        return of(null);
      }
    }

    const API = {
      save: this.utilsService.serverVariableService.AVG_PRICE_STAFF_SAVE,
      approve: this.utilsService.serverVariableService.AVG_PRICE_APPROVE,
      rollback: this.utilsService.serverVariableService.AVG_PRICE_APPROVE
    }[saveStage];

    let param = null;

    switch (saveStage) {
      case 'save':
        param = this.averagePriceList().map(a => ({
          itemId: a.item.id,
          containerId: this.paginationRequest().containerId,
          newSPStaff: a.newSPStaff
        }));
        break;
      case 'approve':
        param = this.averagePriceList().map(a => ({
          itemId: a.item.id,
          containerId: this.paginationRequest().containerId,
          newSP: a.newSP,
          totalQty: a.G_totalQty,
          newAvgPriceWithGST: a.newAvgPriceWithGST,
          newAvgPriceWithoutGST: a.newAvgPriceWithoutGST
        }));
        break;
      case 'rollback':
        param = this.averagePriceList().map(a => ({
          itemId: a.item.id,
          containerId: this.paginationRequest().containerId,
          isRollback: true
        }));
        break;
    }
    return this.utilsService.post(API, param, { toast: true })
  }

  onClearStaffB = () => { 
    for(const item of this.averagePriceList()){
      item.newSPStaff = null;
    }
    this.averagePriceList.set(this.averagePriceList());
  }

}
