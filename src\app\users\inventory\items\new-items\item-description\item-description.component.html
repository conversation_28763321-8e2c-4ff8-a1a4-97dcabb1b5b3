<div class="card card-theme card-forms">
    <div class="card-body p-0">
        <div class="row" [formGroup]="itemForm">
            <div class="col-xl-10">
                <div class="row">
                    <div class="col-lg-6">
                        <div class="form-group  form-group-inline-control theme-ngselect">
                            <label class="form-label">Open Item Dim.</label>
                            <div class="form-control-wrapper">
                                <div class="input-group input-group-select">
                                    <input formControlName="itemLength" [(ngModel)]="itemObj.itemLength"
                                        mask="separator.5" thousandSeparator="" separatorLimit="99999999" type="text"
                                        class="form-control" aria-label="Text input with segmented dropdown button"
                                        placeholder="Length">
                                    <input formControlName="itemWidth" [(ngModel)]="itemObj.itemWidth"
                                        mask="separator.5" thousandSeparator="" separatorLimit="99999999" type="text"
                                        class="form-control" aria-label="Text input with segmented dropdown button"
                                        placeholder="Width">
                                    <input formControlName="itemHeight" [(ngModel)]="itemObj.itemHeight"
                                        mask="separator.5" thousandSeparator="" separatorLimit="99999999" type="text"
                                        class="form-control" aria-label="Text input with segmented dropdown button"
                                        placeholder="Height">
                                    <ng-select class="" placeholder="Unit" [multiple]="false" [clearable]="false" [items]="dropdown?.itemDimUnit"
                                        bindLabel="shortCode" bindValue="id" formControlName="itemsDimId" [(ngModel)]="itemObj.itemsDimId">
                                    </ng-select>
                                    <!-- <button type="button" class="btn dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" aria-expanded="false">
                                        {{itemObj.itemsDim ? itemObj.itemsDim : 'Unit'}}
                                        <span class="visually-hidden">Toggle Dropdown</span>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li (click)="onChangeUnit('itemsDim', item)" *ngFor="let item of dropdown?.itemDimUnit">
                                            <a [ngClass]="{'active': itemObj.itemsDim?.toLowerCase() === item.label?.toLowerCase(), 'disabled': !item.isActive}"
                                                class="dropdown-item">{{item.label}}</a>
                                        </li>
                                    </ul> -->
                                </div>
                            </div>
                        </div>
                        <div class="form-group form-group-inline-control theme-ngselect">
                            <label class="form-label">With Box Dimensions</label>
                            <div class="form-control-wrapper">
                                <div class="input-group input-group-select">
                                    <input (input)="onChangeBoxDim()" formControlName="boxLength" [(ngModel)]="itemObj.boxLength" type="text"
                                        class="form-control" mask="separator.5" thousandSeparator="" separatorLimit="99999999"
                                        aria-label="Text input with segmented dropdown button" placeholder="Length">
                                    <input (input)="onChangeBoxDim()" formControlName="boxWidth" [(ngModel)]="itemObj.boxWidth" type="text"
                                        class="form-control" mask="separator.5" thousandSeparator="" separatorLimit="99999999"
                                        aria-label="Text input with segmented dropdown button" placeholder="Width">
                                    <input (input)="onChangeBoxDim()" formControlName="boxHeight" [(ngModel)]="itemObj.boxHeight" type="text"
                                        class="form-control" mask="separator.5" thousandSeparator="" separatorLimit="99999999"
                                        aria-label="Text input with segmented dropdown button" placeholder="Height">
                                    <ng-select (change)="onChangeBoxDim()" class="" placeholder="Unit" [multiple]="false" [clearable]="false" [items]="dropdown?.boxUnit"
                                        bindLabel="shortCode" bindValue="id" formControlName="boxDimId" [(ngModel)]="itemObj.boxDimId">
                                    </ng-select>
                                    <!-- <button type="button" class="btn dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" aria-expanded="false">
                                        {{itemObj.boxDim ? itemObj.boxDim : 'Unit'}}
                                        <span class="visually-hidden">Toggle Dropdown</span>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li (click)="onChangeUnit('boxDim', item)" *ngFor="let item of dropdown?.boxUnit">
                                            <a [ngClass]="{'active': itemObj.boxDim?.toLowerCase() === item.label?.toLowerCase(), 'disabled': !item.isActive}"
                                                class="dropdown-item">{{item.label}}</a>
                                        </li>
                                    </ul> -->
                                </div>
                            </div>
                        </div>
                        <!-- <div class="form-group theme-ngselect form-group-inline-control ">
                            <label class="form-label">Packing Type</label>
                            <div class="form-control-wrapper">
                                <div class="form-group-button">
                                    <ng-select placeholder="Packing Type" [multiple]="false" [clearable]="false"
                                        [items]="dropdown?.packingMaster" bindLabel="name" bindValue="id" appendTo="body">
                                    </ng-select>
                                    <button class="btn btn-outline-white"><i class="th th-outline-add-circle text-primary"></i></button>
                                </div>
                            </div>
                        </div> -->
                        <div class="form-group theme-ngselect form-group-inline-control required">
                            <label class="form-label">HSN Code</label>
                            <div class="form-control-wrapper">
                                <div class="form-group-button">
                                    <ng-select placeholder="Select HSN Code" [multiple]="false" [clearable]="true" formControlName="hsnCodeId"
                                        [items]="dropdown?.hsnCodeMaster" bindValue="id" [(ngModel)]="itemObj.hsnCodeId" [virtualScroll]="true"
                                        class="hsn-select" [searchFn]="customSearchFn">
                        
                                        <ng-template ng-header-tmp>
                                            <div class="d-flex fw-bold bg-light border-bottom py-2 px-3">
                                                <div class="w-25">HSN Code</div>
                                                <div class="w-25">Percentage (%)</div>
                                                <div class="w-50">Description</div>
                                            </div>
                                        </ng-template>
                        
                                        <ng-template ng-option-tmp let-item="item">
                                            <div class="d-flex align-items-center border-bottom py-2 px-3">
                                                <div class="w-25">{{ item.hsnCode }}</div>
                                                <div class="w-25">{{ item.rate }}%</div>
                                                <div class="w-50 hsn-description">
                                                    <div [title]="item.description ? item.description : ''">
                                                        {{ item.description ? item.description : '-' }}
                                                    </div>
                                                </div>
                                            </div>
                                        </ng-template>
                        
                                        <ng-template ng-notfound-tmp>
                                            <div class="d-flex align-items-center border-bottom py-2 px-3">
                                                <div>No HSN Code Found</div>
                                            </div>
                                        </ng-template>
                        
                                        <ng-template ng-label-tmp let-item="item">
                                            {{ item.hsnCode }} ({{ item.rate }}%)
                                        </ng-template>
                                    </ng-select>
                        
                                    <button (click)="openHsnModal()" class="btn btn-outline-white"><i
                                            class="th th-outline-add-circle text-primary"></i></button>
                                </div>
                                <div class="message error-message"
                                    *ngIf="itemForm.controls['hsnCodeId'].hasError('required') &&  itemForm.controls['hsnCodeId'].touched">
                                    {{utilsService.validationService.HSN_NAME_REQ}}
                                </div>
                            </div>
                        </div>
                        <div class="form-group form-group-inline-control" formArrayName="mediaLinks">
                            <label class="form-label">Add Media Links</label>
                            <div class="form-control-wrapper">
                                <div class="form-group-button-wrapper">
                                    <ng-container *ngFor="let arr of mediaLinks.controls; index as i" [formGroupName]="i">
                                        <div class="form-control-wrapper">
                                            <div class="form-group-button">
                                                <input [maxlength]="utilsService.validationService.MAX_1000" formControlName="link" type="text"
                                                    class="form-control" placeholder="Enter Link" [(ngModel)]="itemObj.mediaLinks[i].link">
                                                <button (click)="addMediaLink.emit()" class="btn btn-outline-white btn-icon text-black"><i
                                                        class="th th-outline-add-circle"></i></button>
                                                <button *ngIf="mediaLinks.length > 1" (click)="removeMedia(i)"
                                                    class="btn btn-outline-white btn-icon text-danger"><i
                                                        class="th th-outline-minus-cirlce"></i></button>
                                            </div>
                                            <div *ngIf="!arr.get('link').hasError('required') && !arr.get('link').valid && arr.get('link').touched"
                                                class="message error-message">
                                                {{utilsService.validationService.LINK_INVALID}}
                                            </div>
                                        </div>
                                    </ng-container>
                                </div>
                            </div>
                        </div>
                        <!-- <div class="form-group form-group-inline-control ">
                            <label class="form-label">YouTube Link</label>
                            <div class="form-control-wrapper">
                                <input formControlName="youtubeLink" type="text" class="form-control" placeholder="Enter YouTube link"
                                    [(ngModel)]="itemObj.youtubeLink">
                                <div class="message error-message"
                                    *ngIf="!itemForm.controls['youtubeLink'].hasError('required') && !itemForm.controls['youtubeLink'].valid && itemForm.controls['youtubeLink'].touched">
                                    {{utilsService.validationService.YT_LINK_INVALID}}
                                </div>
                            </div>
                        </div> -->
                        <div class="form-group theme-ngselect form-group-inline-control">
                            <label class="form-label">Item Belong To</label>
                            <div class="form-control-wrapper">
                                <ng-select formControlName="itemsBelongTo" placeholder="Select Item Belong To"
                                    [multiple]="false" [clearable]="true" [items]="dropdown?.itemsBelong"
                                    bindLabel="label" bindValue="value" [(ngModel)]="itemObj.itemsBelongTo">
                                </ng-select>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6 ">
                        <div class="form-group  form-group-inline-control theme-ngselect">
                            <label class="form-label">Open Item Weight</label>
                            <div class="form-control-wrapper">
                                <div class="input-group input-group-select">
                                    <input mask="separator.5" thousandSeparator="" [maxlength]="utilsService.validationService.MAX_15" type="text"
                                        class="form-control" [(ngModel)]="itemObj.itemsWeight" formControlName="itemsWeight" placeholder="Enter weight">
                                    <ng-select class="" placeholder="Unit" [multiple]="false" [clearable]="false" [items]="dropdown?.itemWeightDimUnit"
                                        bindLabel="shortCode" bindValue="id" formControlName="itemsWeightDimId" [(ngModel)]="itemObj.itemsWeightDimId">
                                        <ng-template ng-label-tmp let-item="item">
                                            <span class="ng-value-label" [title]="item.unitName">{{ item.shortCode }}</span>
                                        </ng-template>
                                        <ng-template ng-option-tmp let-item="item">
                                            <span class="ng-option-label" [title]="item.unitName">{{ item.shortCode }}</span>
                                        </ng-template>
                                    </ng-select>
                                    <!-- <button type="button" class="btn dropdown-toggle dropdown-toggle-split"
                                        data-bs-toggle="dropdown" aria-expanded="false">
                                        {{itemObj.itemsWeightDim ? itemObj.itemsWeightDim : 'Unit'}}
                                        <span class="visually-hidden">Toggle Dropdown</span>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li (click)="onChangeUnit('itemsWeightDim', item)" *ngFor="let item of dropdown?.itemWeightDimUnit">
                                            <a [ngClass]="{'active': itemObj.itemsWeightDim?.toLowerCase() === item.label?.toLowerCase(), 'disabled': !item.isActive}"
                                                class="dropdown-item">{{item.label}}</a>
                                        </li>
                                    </ul> -->
                                </div>
                            </div>
                        </div>
                        <div class="form-group  form-group-inline-control theme-ngselect">
                            <label class="form-label">With Box Weight</label>
                            <div class="form-control-wrapper">
                                <div class="input-group input-group-select">
                                    <input type="text" class="form-control" mask="separator.5" thousandSeparator=""
                                        [maxlength]="utilsService.validationService.MAX_15" aria-label="Text input with segmented dropdown button"
                                        [(ngModel)]="itemObj.boxWeight" formControlName="boxWeight" placeholder="Enter weight">
                                    <ng-select class="" placeholder="Unit" [multiple]="false" [clearable]="false" [items]="dropdown?.boxWeightUnit"
                                        bindLabel="shortCode" bindValue="id" formControlName="boxWeightDimId" [(ngModel)]="itemObj.boxWeightDimId">
                                    </ng-select>
                                    <!-- <button type="button" class="btn dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown"
                                        aria-expanded="false">
                                        {{itemObj.boxWeightDim ? itemObj.boxWeightDim : 'Unit'}}
                                        <span class="visually-hidden">Toggle Dropdown</span>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li (click)="onChangeUnit('boxWeightDim', item)" *ngFor="let item of dropdown?.boxWeightUnit">
                                            <a [ngClass]="{'active': itemObj.boxWeightDim?.toLowerCase() === item.label?.toLowerCase(), 'disabled': !item.isActive}"
                                                class="dropdown-item">{{item.label}}</a>
                                        </li>
                                    </ul> -->
                                </div>
                            </div>
                        </div>
                        <div class="form-group form-group-inline-control ">
                            <label class="form-label">Volumetric Weight (kg)</label>
                            <div class="form-control-wrapper">
                                <input mask="separator.3" thousandSeparator=""
                                    [maxlength]="utilsService.validationService.MAX_15"
                                    formControlName="singlePieceVolume" [(ngModel)]="itemObj.singlePieceVolume"
                                    type="text" class="form-control" placeholder="Volumetric Weight (kg)">
                            </div>
                        </div>
                        <!-- <div class="form-group form-group-inline-control ">
                            <label class="form-label">Purchase Ratio</label>
                            <div class="form-control-wrapper">
                                <input mask="separator.5" thousandSeparator=""
                                    [maxlength]="utilsService.validationService.MAX_15" formControlName="purchaseRation"
                                    type="text" class="form-control" placeholder="Purchase Ratio"
                                    [(ngModel)]="itemObj.purchaseRation">
                            </div>
                        </div> -->
                        <div class="form-group theme-ngselect form-group-inline-control">
                            <label class="form-label">QC Checklist</label>
                            <div class="form-control-wrapper">
                                <ng-select (clear)="onClearQC()" placeholder="Select QC Checklist" [multiple]="false" [clearable]="true"
                                    [items]="dropdown?.qcChecklist" bindLabel="label" bindValue="value"
                                    [(ngModel)]="itemObj.qcChecklistId" formControlName="qcChecklistId">
                                </ng-select>
                            </div>
                        </div>
                    </div>

                    <hr>

                    <div class="inner-title-wrapper">
                        <div class="inner-title-left">
                            <div class="inner-title-text">
                                <h6 class="">Sales Information</h6>
                            </div>
                        </div>
                        <div class="inner-title-rigth">
                        </div>
                    </div>
                    <div class="col-lg-6 ">
                        <div class="form-group  form-group-inline-control">
                            <div class="form-label">Price/Item</div>
                            <div class="form-control-wrapper">
                                <div class="input-group ">
                                    <button class="btn btn-sm btn-outline-secondary" type="button"
                                        id="button-INR">INR</button>
                                    <input formControlName="itemPrice" [(ngModel)]="itemObj.itemPrice"
                                        mask="separator.5" thousandSeparator=""
                                        [maxlength]="utilsService.validationService.MAX_15" type="text"
                                        class="form-control" placeholder="Enter Price" aria-label=""
                                        aria-describedby="button-INR">
                                </div>
                            </div>
                        </div>
                        <div class="form-group  form-group-inline-control">
                            <div class="form-label">Price/Carton</div>
                            <div class="form-control-wrapper">
                                <div class="input-group ">
                                    <button class="btn btn-sm btn-outline-secondary" type="button"
                                        id="button-INR">INR</button>
                                    <input formControlName="itemCarton" [(ngModel)]="itemObj.itemCarton"
                                        mask="separator.5" thousandSeparator=""
                                        [maxlength]="utilsService.validationService.MAX_15" type="text"
                                        class="form-control" placeholder="Enter Carton Price" aria-label=""
                                        aria-describedby="button-INR">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6 ">
                        <div class="form-group  form-group-inline-control">
                            <div class="form-label">GST Price/Item</div>
                            <div class="form-control-wrapper">
                                <div class="input-group ">
                                    <button class="btn btn-sm btn-outline-secondary" type="button"
                                        id="button-INR">INR</button>
                                    <input formControlName="gstItemPrice" [(ngModel)]="itemObj.gstItemPrice"
                                        mask="separator.5" thousandSeparator=""
                                        [maxlength]="utilsService.validationService.MAX_15" type="text"
                                        class="form-control" placeholder="Enter GST Price" aria-label=""
                                        aria-describedby="button-INR">
                                </div>
                            </div>
                        </div>
                        <div class="form-group  form-group-inline-control">
                            <div class="form-label">GST Price/Carton</div>
                            <div class="form-control-wrapper">
                                <div class="input-group ">
                                    <button class="btn btn-sm btn-outline-secondary" type="button"
                                        id="button-INR">INR</button>
                                    <input formControlName="gstItemCarton" [(ngModel)]="itemObj.gstItemCarton"
                                        mask="separator.5" thousandSeparator=""
                                        [maxlength]="utilsService.validationService.MAX_15" type="text"
                                        class="form-control" placeholder="Enter Carton GST Price " aria-label=""
                                        aria-describedby="button-INR">
                                </div>
                            </div>
                        </div>
                    </div>

                    <hr>

                    <div class="inner-title-wrapper">
                        <div class="inner-title-left">
                            <div class="inner-title-text">
                                <h6 class="">Coins Information</h6>
                            </div>
                        </div>
                        <div class="inner-title-rigth">
                        </div>
                    </div>
                    <div class="col-lg-6 ">
                        <div class="form-group  form-group-inline-control">
                            <div class="form-label">Discounter Coins</div>
                            <div class="form-control-wrapper">
                                <div class="input-group ">
                                    <button class="btn btn-sm btn-outline-secondary" type="button"
                                        id="button-INR">Coin</button>
                                    <input mask="separator.0" thousandSeparator=""
                                        [maxlength]="utilsService.validationService.MAX_15"
                                        formControlName="discounterCoins" [(ngModel)]="itemObj.discounterCoins"
                                        type="text" class="form-control" placeholder="Enter Discounter Coins"
                                        aria-label="" aria-describedby="button-INR">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6 ">
                        <div class="form-group  form-group-inline-control">
                            <div class="form-label">Loose Goods Coins</div>
                            <div class="form-control-wrapper">
                                <div class="input-group ">
                                    <button class="btn btn-sm btn-outline-secondary" type="button"
                                        id="button-INR">Coin</button>
                                    <input mask="separator.0" thousandSeparator=""
                                        [maxlength]="utilsService.validationService.MAX_15"
                                        formControlName="loosesGoodsCoins" [(ngModel)]="itemObj.loosesGoodsCoins"
                                        type="text" class="form-control" placeholder="Enter Loose Goods Coins"
                                        aria-label="" aria-describedby="button-INR">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>