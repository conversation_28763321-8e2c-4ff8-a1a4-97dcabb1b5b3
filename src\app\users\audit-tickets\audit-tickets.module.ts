import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AuditTicketsComponent } from './audit-tickets.component';
import { RouterModule, Routes } from '@angular/router';
import { SharedModule } from 'src/app/shared/shared.module';
import { AuditTicketsListComponent } from './audit-tickets-list/audit-tickets-list.component';
import { NewAuditTicketsComponent } from './new-audit-tickets/new-audit-tickets.component';
import { AuditTicketsDetailsComponent } from './audit-tickets-details/audit-tickets-details.component';
import { AuditTicketDeletedComponent } from './audit-tickets-list/audit-ticket-deleted/audit-ticket-deleted.component';
import { AuditTicketNewComponent } from './audit-tickets-list/audit-ticket-new/audit-ticket-new.component';
import { AuditTicketResolvedComponent } from './audit-tickets-list/audit-ticket-resolved/audit-ticket-resolved.component';


const routes: Routes = [
  { path: '', redirectTo: 'audit-tickets', pathMatch: 'full' },
  { path: 'list', component: AuditTicketsListComponent },
  { path: 'new-audit-tickets', component: NewAuditTicketsComponent },
  { path: 'audit-tickets-details', component: AuditTicketsDetailsComponent },
];

@NgModule({
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    SharedModule.forRoot()
  ],
  declarations: [
    AuditTicketsComponent, AuditTicketsListComponent, NewAuditTicketsComponent, AuditTicketsDetailsComponent, 
    AuditTicketDeletedComponent, 
    AuditTicketNewComponent, 
    AuditTicketResolvedComponent
  ]
})
export class AuditTicketsModule { }