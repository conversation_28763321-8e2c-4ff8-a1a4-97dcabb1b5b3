<div ngbDropdown #dropdown="ngbDropdown" autoClose="outside" (openChange)="onDropdownOpenChange($event)">
  <div class="form-group-icon-end">
    <i (click)="!disabled && dropdown.toggle()" class="th th-outline-calendar-1"></i>
    <input #inputField type="text" class="form-control" ngbDropdownToggle [value]="getSelectedDisplayValue()"
      [placeholder]="placeholder" [disabled]="disabled" [readonly]="true" />
  </div>

  <!-- Dropdown Menu -->
  <div ngbDropdownMenu class="picker-dropdown-menu"
    [class.time-only-dropdown]="isTimeOnlyMode()"
    [class.time-right-layout]="timePosition === 'right' && timer && !isTimeOnlyMode()"
    (click)="$event.stopPropagation()">
    <div class="date-time-picker"
      [class.time-only-picker]="isTimeOnlyMode()"
      [class.time-right-picker]="timePosition === 'right' && timer && !isTimeOnlyMode()">

      <!-- Main Content Container for Right Layout -->
      <div class="picker-content"
        [class.picker-content-horizontal]="timePosition === 'right' && timer && !isTimeOnlyMode()">

        <!-- Date Selection Section -->
        <div class="picker-section date-section"
          [class.date-section-right-layout]="timePosition === 'right' && timer && !isTimeOnlyMode()"
          *ngIf="!isTimeOnlyMode()">

          <!-- Month/Year Navigation Header -->
          <div class="picker-header">
            <button type="button" class="btn-nav" (click)="previousMonth()" [disabled]="disabled || isPreviousMonthDisabled()">
              <i class="th th-outline-arrow-left-2"></i>
            </button>

            <div class="header-center">
              <button type="button" class="btn-month" (click)="toggleMonthSelection()" [disabled]="disabled">
                {{ getCurrentMonthName() }}
              </button>
              <button type="button" class="btn-year" (click)="toggleYearSelection()" [disabled]="disabled">
                {{ viewingYear }}
              </button>
            </div>

            <button type="button" class="btn-nav" (click)="nextMonth()" [disabled]="disabled || isNextMonthDisabled()">
              <i class="th th-outline-arrow-right-3"></i>
            </button>
          </div>

          <!-- Month Selection Grid -->
          <div class="picker-grid months-grid" *ngIf="showMonthSelection" role="grid" aria-label="Month selection">
            <div *ngFor="let month of months; trackBy: trackByMonth" class="picker-item month-item"
              [class.selected]="isSelectedMonth(month.value)"
              [class.current]="isCurrentMonth(month.value)"
              [class.disabled]="disabled || isMonthDisabled(month.value)"
              [attr.aria-label]="month.fullName"
              (click)="onMonthSelect(month.value)">
              {{ month.name }}
            </div>
          </div>

          <!-- Year Selection Grid -->
          <div class="picker-grid years-grid" *ngIf="showYearSelection" role="grid" aria-label="Year selection" #yearGrid>
            <div *ngFor="let year of years; trackBy: trackByDay" class="picker-item year-item"
              [class.selected]="isSelectedYear(year)"
              [class.current]="isCurrentYear(year)"
              [class.disabled]="disabled || isYearDisabled(year)"
              [attr.aria-label]="year"
              [attr.data-year]="year"
              (click)="onYearSelect(year)">
              {{ year }}
            </div>
          </div>

          <!-- Days Grid -->
          <div class="picker-grid days-grid" *ngIf="!showMonthSelection && !showYearSelection" role="grid" aria-label="Day selection">
            <!-- Week day headers -->
            <div class="picker-item day-header">Mo</div>
            <div class="picker-item day-header">Tu</div>
            <div class="picker-item day-header">We</div>
            <div class="picker-item day-header">Th</div>
            <div class="picker-item day-header">Fr</div>
            <div class="picker-item day-header">Sa</div>
            <div class="picker-item day-header">Su</div>

            <!-- Days -->
            <div *ngFor="let dayObj of getCalendarGrid(); trackBy: trackByDayObj" class="picker-item day-item"
              [class.selected]="isSelectedDayObj(dayObj)"
              [class.today]="isTodayDayObj(dayObj)"
              [class.disabled]="disabled || isDayObjDisabled(dayObj)"
              [class.other-month]="!dayObj.isCurrentMonth"
              [class.prev-month]="dayObj.isPrevMonth"
              [class.next-month]="dayObj.isNextMonth"
              [attr.aria-label]="'Day ' + dayObj.day + (dayObj.isCurrentMonth ? '' : dayObj.isPrevMonth ? ' (previous month)' : ' (next month)')"
              (click)="onDayObjSelect(dayObj, dropdown)">
              {{ dayObj.day }}
            </div>
          </div>
        </div>

        <!-- Time Selection Section -->
        <div class="picker-section time-section"
          [class.time-section-right-layout]="timePosition === 'right' && !isTimeOnlyMode()"
          *ngIf="timer">
          <!-- Time Header for time-only mode -->
          <div class="time-header" *ngIf="isTimeOnlyMode()">
            <span class="time-label">Select Time</span>
          </div>

          <!-- Time Header for right layout -->
          <div class="time-header" *ngIf="timePosition === 'right' && !isTimeOnlyMode()">
            <span class="time-label">Time</span>
          </div>

          <!-- Time Controls Container for Right Layout -->
          <div class="time-controls-container" *ngIf="timePosition === 'right' && !isTimeOnlyMode()">
            <div class="time-controls time-right-controls"
              [class.time-24hour]="timeFormat === '24-hour'">
              <!-- Hour Selection -->
              <div class="time-control">
                <label class="time-control-label">{{ 'Hr' }}</label>
                <select
                  class="form-select form-select-sm"
                  [disabled]="disabled"
                  [ngModel]="selectedHour === null ? '' : selectedHour"
                  (change)="onHourChange(($any($event.target).value) === '' ? null : +($any($event.target).value))"
                  [attr.aria-label]="timeFormat === '12-hour' ? 'Select hour' : 'Select hour (24-hour format)'">
                  <option value="" disabled>{{ 'Hr' }}</option>
                  <option *ngFor="let hour of (timeFormat === '12-hour' ? hours12 : hours24); trackBy: trackByNumber"
                          [value]="hour"
                          [disabled]="isHourDisabled(hour)">
                    {{ formatHourDisplay(hour) }}
                  </option>
                </select>
              </div>

              <!-- Minute Selection -->
              <div class="time-control">
                <label class="time-control-label">{{ 'Min' }}</label>
                <select
                  class="form-select form-select-sm"
                  [disabled]="disabled"
                  [ngModel]="selectedMinute === null ? '' : selectedMinute"
                  (change)="onMinuteChange(($any($event.target).value) === '' ? null : +($any($event.target).value))"
                  [attr.aria-label]="'Select minute'">
                  <option value="" disabled>{{ 'Min' }}</option>
                  <option *ngFor="let minute of minutes; trackBy: trackByNumber"
                          [value]="minute"
                          [disabled]="isMinuteDisabled(minute)">
                    {{ formatMinuteDisplay(minute) }}
                  </option>
                </select>
              </div>

              <!-- AM/PM Toggle Button (12-hour format only) -->
              <div class="time-control period-control" *ngIf="timeFormat === '12-hour'">
                <button type="button"
                  class="period-toggle-btn"
                  [class.am-selected]="selectedPeriod === 'AM'"
                  [class.pm-selected]="selectedPeriod === 'PM'"
                  [disabled]="disabled"
                  (click)="togglePeriod()"
                  [attr.aria-label]="'Toggle between AM and PM, currently ' + selectedPeriod">
                  <span class="period-option" [class.active]="selectedPeriod === 'AM'" [class.disabled]="isPeriodDisabled('AM')">AM</span>
                  <span class="period-divider">|</span>
                  <span class="period-option" [class.active]="selectedPeriod === 'PM'" [class.disabled]="isPeriodDisabled('PM')">PM</span>
                </button>
              </div>
            </div>
          </div>

          <!-- Time Controls for Other Layouts (Bottom and Time-Only) -->
          <div class="time-controls"
            [class.time-only-controls]="isTimeOnlyMode()"
            [class.time-24hour]="timeFormat === '24-hour'"
            *ngIf="timePosition !== 'right' || isTimeOnlyMode()">
            <!-- Hour Selection -->
            <div class="time-control">
              <label class="time-control-label">{{ 'Hr' }}</label>
              <select
                class="form-select form-select-sm"
                [disabled]="disabled"
                [ngModel]="selectedHour === null ? '' : selectedHour"
                (change)="onHourChange(($any($event.target).value) === '' ? null : +($any($event.target).value))"
                [attr.aria-label]="timeFormat === '12-hour' ? 'Select hour' : 'Select hour (24-hour format)'">
                <option value="" disabled>{{ 'Hr' }}</option>
                <option *ngFor="let hour of (timeFormat === '12-hour' ? hours12 : hours24); trackBy: trackByNumber"
                        [value]="hour"
                        [disabled]="isHourDisabled(hour)">
                  {{ formatHourDisplay(hour) }}
                </option>
              </select>
            </div>

            <!-- Minute Selection -->
            <div class="time-control">
              <label class="time-control-label">{{ 'Min' }}</label>
              <select
                class="form-select form-select-sm"
                [disabled]="disabled"
                [ngModel]="selectedMinute === null ? '' : selectedMinute"
                (change)="onMinuteChange(($any($event.target).value) === '' ? null : +($any($event.target).value))"
                [attr.aria-label]="'Select minute'">
                <option value="" disabled>{{ 'Min' }}</option>
                <option *ngFor="let minute of minutes; trackBy: trackByNumber"
                        [value]="minute"
                        [disabled]="isMinuteDisabled(minute)">
                  {{ formatMinuteDisplay(minute) }}
                </option>
              </select>
            </div>

            <!-- AM/PM Toggle Button (12-hour format only) -->
            <div class="time-control period-control" *ngIf="timeFormat === '12-hour'">
              <button type="button"
                class="period-toggle-btn"
                [class.am-selected]="selectedPeriod === 'AM'"
                [class.pm-selected]="selectedPeriod === 'PM'"
                [disabled]="disabled"
                (click)="togglePeriod()"
                [attr.aria-label]="'Toggle between AM and PM, currently ' + selectedPeriod">
                <span class="period-option" [class.active]="selectedPeriod === 'AM'" [class.disabled]="isPeriodDisabled('AM')">AM</span>
                <span class="period-divider">|</span>
                <span class="period-option" [class.active]="selectedPeriod === 'PM'" [class.disabled]="isPeriodDisabled('PM')">PM</span>
              </button>
            </div>
          </div>
        </div>

      </div>
    </div>
  </div>
</div>
