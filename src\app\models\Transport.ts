import { deserializeAs, serializeAs } from 'cerialize';
import { TransportBranchSheet } from './TransportBranchSheet';

export class Transport {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('createdDate')
    @deserializeAs('createdDate')
    private _createdDate: string;

    @serializeAs('transporterName')
    @deserializeAs('transporterName')
    private _transporterName: string;

    @serializeAs('shortCode')
    @deserializeAs('shortCode')
    private _shortCode: string;

    @serializeAs('storeName')
    @deserializeAs('storeName')
    private _storeName: string;

    @serializeAs('modeOfTransport')
    @deserializeAs('modeOfTransport')
    private _modeOfTransport: string;

    @serializeAs('modeOfTransports')
    @deserializeAs('modeOfTransports')
    private _modeOfTransports: any;

    @serializeAs('cityCount')
    @deserializeAs('cityCount')
    private _cityCount: number;

    @serializeAs('countBranches')
    @deserializeAs('countBranches')
    private _countBranches: number;

    @serializeAs('isActive')
    @deserializeAs('isActive')
    private _isActive: boolean;

    // @serializeAs('isSelected')
    @deserializeAs('isSelected')
    private _isSelected: boolean;

    // @serializeAs('isEWayBill')
    @deserializeAs('isEWayBill')
    private _isEWayBill: boolean;

    // @serializeAs('isExpand')
    @deserializeAs('isExpand')
    private _isExpand: boolean;

    @deserializeAs('mot')
    private _mot: string[];

    @serializeAs('branchTransportsGroupedByCity')
    @deserializeAs('branchTransportsGroupedByCity')
    private _branchTransportsGroupedByCity: TransportGrp[];

    @serializeAs('lastModifiedDate')
    @deserializeAs('lastModifiedDate')
    private _lastModifiedDate: string;

    @serializeAs('transporterID')
    @deserializeAs('transporterID')
    private _transporterID: string;

    // @serializeAs('excel')
    @deserializeAs('excel')
    private _excel: any;

    @deserializeAs('isDefault')
    private _isDefault: boolean;

    constructor() {
        this.isActive = false;
        this.isSelected = false;
        this.isEWayBill = false;
        this.isExpand = false;
        this.isDefault = false;
    }

    /**
     * Getter isDefault
     * @return {boolean}
     */
	public get isDefault(): boolean {
		return this._isDefault;
	}

    /**
     * Setter isDefault
     * @param {boolean} value
     */
	public set isDefault(value: boolean) {
		this._isDefault = value;
	}

    /**
     * Getter modeOfTransports
     * @return {any}
     */
	public get modeOfTransports(): any {
		return this._modeOfTransports;
	}

    /**
     * Setter modeOfTransports
     * @param {any} value
     */
	public set modeOfTransports(value: any) {
		this._modeOfTransports = value;
	}
    

    /**
     * Getter mot
     * @return {string[]}
     */
	public get mot(): string[] {
		return this._mot;
	}

    /**
     * Setter mot
     * @param {string[]} value
     */
	public set mot(value: string[]) {
		this._mot = value;
	}


    /**
     * Getter excel
     * @return {any}
     */
	public get excel(): any {
		return this._excel;
	}

    /**
     * Setter excel
     * @param {any} value
     */
	public set excel(value: any) {
		this._excel = value;
	}


    /**
     * Getter transporterID
     * @return {string}
     */
	public get transporterID(): string {
		return this._transporterID;
	}

    /**
     * Setter transporterID
     * @param {string} value
     */
	public set transporterID(value: string) {
		this._transporterID = value;
	}


    /**
     * Getter storeName
     * @return {string}
     */
	public get storeName(): string {
		return this._storeName;
	}

    /**
     * Setter storeName
     * @param {string} value
     */
	public set storeName(value: string) {
		this._storeName = value;
	}


    /**
     * Getter lastModifiedDate
     * @return {string}
     */
	public get lastModifiedDate(): string {
		return this._lastModifiedDate;
	}

    /**
     * Setter lastModifiedDate
     * @param {string} value
     */
	public set lastModifiedDate(value: string) {
		this._lastModifiedDate = value;
	}

    

    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Getter createdDate
     * @return {string}
     */
	public get createdDate(): string {
		return this._createdDate;
	}

    /**
     * Getter transporterName
     * @return {string}
     */
	public get transporterName(): string {
		return this._transporterName;
	}

    /**
     * Getter shortCode
     * @return {string}
     */
	public get shortCode(): string {
		return this._shortCode;
	}

    /**
     * Getter modeOfTransport
     * @return {string}
     */
	public get modeOfTransport(): string {
		return this._modeOfTransport;
	}

    /**
     * Getter cityCount
     * @return {number}
     */
	public get cityCount(): number {
		return this._cityCount;
	}

    /**
     * Getter countBranches
     * @return {number}
     */
	public get countBranches(): number {
		return this._countBranches;
	}

    /**
     * Getter isActive
     * @return {boolean}
     */
	public get isActive(): boolean {
		return this._isActive;
	}

    /**
     * Getter isSelected
     * @return {boolean}
     */
	public get isSelected(): boolean {
		return this._isSelected;
	}

    /**
     * Getter isEWayBill
     * @return {boolean}
     */
	public get isEWayBill(): boolean {
		return this._isEWayBill;
	}

    /**
     * Getter isExpand
     * @return {boolean}
     */
	public get isExpand(): boolean {
		return this._isExpand;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Setter createdDate
     * @param {string} value
     */
	public set createdDate(value: string) {
		this._createdDate = value;
	}

    /**
     * Setter transporterName
     * @param {string} value
     */
	public set transporterName(value: string) {
		this._transporterName = value;
	}

    /**
     * Setter shortCode
     * @param {string} value
     */
	public set shortCode(value: string) {
		this._shortCode = value;
	}

    /**
     * Setter modeOfTransport
     * @param {string} value
     */
	public set modeOfTransport(value: string) {
		this._modeOfTransport = value;
	}

    /**
     * Setter cityCount
     * @param {number} value
     */
	public set cityCount(value: number) {
		this._cityCount = value;
	}

    /**
     * Setter countBranches
     * @param {number} value
     */
	public set countBranches(value: number) {
		this._countBranches = value;
	}

    /**
     * Setter isActive
     * @param {boolean} value
     */
	public set isActive(value: boolean) {
		this._isActive = value;
	}

    /**
     * Setter isSelected
     * @param {boolean} value
     */
	public set isSelected(value: boolean) {
		this._isSelected = value;
	}

    /**
     * Setter isEWayBill
     * @param {boolean} value
     */
	public set isEWayBill(value: boolean) {
		this._isEWayBill = value;
	}

    /**
     * Setter isExpand
     * @param {boolean} value
     */
	public set isExpand(value: boolean) {
		this._isExpand = value;
	}


    /**
     * Getter branchTransportsGroupedByCity
     * @return {TransportGrp[]}
     */
	public get branchTransportsGroupedByCity(): TransportGrp[] {
		return this._branchTransportsGroupedByCity;
	}

    /**
     * Setter branchTransportsGroupedByCity
     * @param {TransportGrp[]} value
     */
	public set branchTransportsGroupedByCity(value: TransportGrp[]) {
		this._branchTransportsGroupedByCity = value;
	}



}

export class TransportGrp {

    @serializeAs('city')
    @deserializeAs('city')
    private _city: string;

    @serializeAs('isExpand')
    @deserializeAs('isExpand')
    private _isExpand: boolean;

    @serializeAs('branches')
    @deserializeAs('branches')
    private _branches: TransportBranchSheet[];

    constructor() {
        this.isExpand = false;
    }


    /**
     * Getter isExpand
     * @return {boolean}
     */
	public get isExpand(): boolean {
		return this._isExpand;
	}

    /**
     * Setter isExpand
     * @param {boolean} value
     */
	public set isExpand(value: boolean) {
		this._isExpand = value;
	}


    /**
     * Getter city
     * @return {string}
     */
	public get city(): string {
		return this._city;
	}

    /**
     * Setter city
     * @param {string} value
     */
	public set city(value: string) {
		this._city = value;
	}


    /**
     * Getter branches
     * @return {TransportBranchSheet[]}
     */
	public get branches(): TransportBranchSheet[] {
		return this._branches;
	}

    /**
     * Setter branches
     * @param {TransportBranchSheet[]} value
     */
	public set branches(value: TransportBranchSheet[]) {
		this._branches = value;
	}

}