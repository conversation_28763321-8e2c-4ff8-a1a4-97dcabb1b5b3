import { deserializeAs, serializeAs } from 'cerialize';

export class CodeSubCode {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('parentCodeId')
    @deserializeAs('parentCodeId')
    private _parentCodeId: number;

    @serializeAs('isActive')
    @deserializeAs('isActive')
    private _isActive: boolean;

    // @serializeAs('isSelected')
    @deserializeAs('isSelected')
    private _isSelected: boolean;

    @serializeAs('lastModifiedDate')
    @deserializeAs('lastModifiedDate')
    private _lastModifiedDate: string;

    @serializeAs('codeName')
    @deserializeAs('codeName')
    private _codeName: string;

    @serializeAs('subCodeName')
    @deserializeAs('subCodeName')
    private _subCodeName: string;

    @serializeAs('parentCodeName')
    @deserializeAs('parentCodeName')
    private _parentCodeName: any;

    @deserializeAs('isDefault')
    private _isDefault: boolean;

    constructor() {
        this.isActive = false;
        this.isSelected = false;
        this.isDefault = false;
    }

    /**
     * Getter isDefault
     * @return {boolean}
     */
	public get isDefault(): boolean {
		return this._isDefault;
	}

    /**
     * Setter isDefault
     * @param {boolean} value
     */
	public set isDefault(value: boolean) {
		this._isDefault = value;
	}


    /**
     * Getter parentCodeName
     * @return {any}
     */
	public get parentCodeName(): any {
		return this._parentCodeName;
	}

    /**
     * Setter parentCodeName
     * @param {any} value
     */
	public set parentCodeName(value: any) {
		this._parentCodeName = value;
	}
   

    /**
     * Getter parentCodeId
     * @return {number}
     */
	public get parentCodeId(): number {
		return this._parentCodeId;
	}

    /**
     * Getter subCodeName
     * @return {string}
     */
	public get subCodeName(): string {
		return this._subCodeName;
	}

    /**
     * Setter parentCodeId
     * @param {number} value
     */
	public set parentCodeId(value: number) {
		this._parentCodeId = value;
	}

    /**
     * Setter subCodeName
     * @param {string} value
     */
	public set subCodeName(value: string) {
		this._subCodeName = value;
	}


    /**
     * Getter codeName
     * @return {string}
     */
	public get codeName(): string {
		return this._codeName;
	}

    /**
     * Setter codeName
     * @param {string} value
     */
	public set codeName(value: string) {
		this._codeName = value;
	}


    /**
     * Getter lastModifiedDate
     * @return {string}
     */
	public get lastModifiedDate(): string {
		return this._lastModifiedDate;
	}

    /**
     * Setter lastModifiedDate
     * @param {string} value
     */
	public set lastModifiedDate(value: string) {
		this._lastModifiedDate = value;
	}


    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Getter isActive
     * @return {boolean}
     */
	public get isActive(): boolean {
		return this._isActive;
	}

    /**
     * Getter isSelected
     * @return {boolean}
     */
	public get isSelected(): boolean {
		return this._isSelected;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Setter isActive
     * @param {boolean} value
     */
	public set isActive(value: boolean) {
		this._isActive = value;
	}

    /**
     * Setter isSelected
     * @param {boolean} value
     */
	public set isSelected(value: boolean) {
		this._isSelected = value;
	}


}