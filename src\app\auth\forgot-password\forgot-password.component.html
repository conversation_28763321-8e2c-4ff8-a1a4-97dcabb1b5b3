<div class="authentication">

  <div class="authentication-wrapper authentication-wrapper2" *ngIf="stepForForgotPassword === enumForStep.EMAIL">

    <div class="authentication-inner-wrapper">
      <div class="authentication-left">

        <div class="authentication-image">
          <img src="assets/images/ForgotPassword.svg" alt="valamji">
        </div>
      </div>
      <div class="authentication-right">


        <div class="auth-logo">
          <img src="assets/images/auth-logo.png" alt="valamji">
        </div>
        <div class="auth-card">
          <div class="auth-card-icon">
            <i class="th th-outline-lock"></i>
          </div>
          <div class="auth-card-title">
            <h2>Forgot Password</h2>
          </div>

          <div class="auth-form" [formGroup]="forgotPassForm">
            <div class="form-group">
              <div class="form-label">Email / Mobile No</div>
              <div class="form-group-icon-start">
                <i class="th th-outline-user"></i>
                <input [(ngModel)]="userObj.email" (keyup.enter)="otpGeneration(false)" formControlName="email" type="text"
                  placeholder="Enter Email / Mobile No" class="form-control">
              </div>
              <div class="message error-message"
                *ngIf="forgotPassForm.controls['email'].hasError('required') &&  forgotPassForm.controls['email'].touched">
                {{utilsService.validationService.EMAIL_MOBILE_REQ}}
              </div>
              <div class="message error-message"
                *ngIf="!forgotPassForm.controls['email'].hasError('required') && !forgotPassForm.controls['email'].valid && forgotPassForm.controls['email'].touched">
                {{utilsService.validationService.EMAIL_MOBILE_INVALID}}
              </div>
            </div>
          </div>



          <div class="auth-btn-group">
            <button (click)="otpGeneration(false)" class="btn btn-primary">Continue</button>
          </div>


          <div class="auth-form-link">
            <p>Go back to <a (click)="utilsService.redirectTo('/auth/login')">Login</a></p>
          </div>


        </div>
      </div>
    </div>

    <div class="authentication-footer">
      <p>{{utilsService.version}}</p>
    </div>





  </div>

  <div class="authentication-wrapper authentication-wrapper2"
    *ngIf="stepForForgotPassword === enumForStep.OTP_VERIFICATION">

    <div class="authentication-inner-wrapper">
      <div class="authentication-left">

        <div class="authentication-image">
          <img src="assets/images/Verify-Email.svg" alt="valamji">
        </div>
      </div>
      <div class="authentication-right">


        <div class="auth-logo">
          <img src="assets/images/auth-logo.png" alt="valamji">
        </div>
        <div class="auth-card">
          <div class="auth-card-icon">
            <i class="th th-outline-sms-notification"></i>
          </div>
          <div class="auth-card-title">
            <h2>Verify your {{!isMobile ? 'Email' : 'Mobile No'}}</h2>
            <p>We have sent 4-Digit OTP to your {{!isMobile ? 'Email' : 'Mobile No'}}: <br>
              <span class="text-secondary-two">{{!isMobile ?
                userObj.email : (userObj.email| asterick: 8)}}</span>
            </p>
          </div>
          <div class="form-group">
            <ng-otp-input (keyup.enter)="onSubmitOTP()" (onInputChange)="onOtpChange($event)" [config]="otpInputConfig"></ng-otp-input>
          </div>
          <div class="auth-form-link">
            <p>
              {{timerAction === 'start' ? 'Resend Code In': ''}}
              <ng-container *ngIf="timerAction === 'start'">
                <a>
                  <countdown #cd (event)="handleEvent($event)" [config]="{leftTime: 60, format: 'mm:ss'}" />
                </a>
              </ng-container>
              <ng-container *ngIf="timerAction === 'done'">
                <a (click)="resendOtp()">Resend Code</a>
              </ng-container>
            </p>
          </div>

          <div class="auth-btn-group">
            <button (click)="onSubmitOTP()" class="btn btn-primary">Verify</button>
          </div>

          <div class="auth-form-link">
            <p>Don’t want to reset? <a [routerLink]="['/auth/login']">Login</a></p>

          </div>

        </div>
      </div>
    </div>





    <div class="authentication-footer">
      <p>{{utilsService.version}}</p>
    </div>


  </div>

</div>
