import { deserializeAs, serializeAs } from 'cerialize';

export class Role {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('idOfDepartment')
    @deserializeAs('idOfDepartment')
    private _idOfDepartment: number;

    @serializeAs('privToBeInsert')
    @deserializeAs('privToBeInsert')
    private _privToBeInsert: number[];

    @serializeAs('privToBeDelete')
    @deserializeAs('privToBeDelete')
    private _privToBeDelete: number[];

    @serializeAs('roleName')
    @deserializeAs('roleName')
    private _roleName: string;

    @serializeAs('departmentName')
    @deserializeAs('departmentName')
    private _departmentName: string;

    @serializeAs('description')
    @deserializeAs('description')
    private _description: string;

    @serializeAs('name')
    @deserializeAs('name')
    private _name: string;

    @serializeAs('isActive')
    @deserializeAs('isActive')
    private _isActive: boolean;

    @serializeAs('isSelected')
    @deserializeAs('isSelected')
    private _isSelected: boolean;

    @serializeAs('isDefault')
    @deserializeAs('isDefault')
    private _isDefault: boolean;

    @serializeAs('lastModifiedDate')
    @deserializeAs('lastModifiedDate')
    private _lastModifiedDate: string;

    @serializeAs('lastUpdatedDateTime')
    @deserializeAs('lastUpdatedDateTime')
    private _lastUpdatedDateTime: string;

    constructor() {
        this.isActive = false;
        this.isSelected = false;
        this.isDefault = false;
    }


    /**
     * Getter isDefault
     * @return {boolean}
     */
	public get isDefault(): boolean {
		return this._isDefault;
	}

    /**
     * Setter isDefault
     * @param {boolean} value
     */
	public set isDefault(value: boolean) {
		this._isDefault = value;
	}


    /**
     * Getter departmentName
     * @return {string}
     */
	public get departmentName(): string {
		return this._departmentName;
	}

    /**
     * Setter departmentName
     * @param {string} value
     */
	public set departmentName(value: string) {
		this._departmentName = value;
	}


    /**
     * Getter idOfDepartment
     * @return {number}
     */
	public get idOfDepartment(): number {
		return this._idOfDepartment;
	}

    /**
     * Getter privToBeInsert
     * @return {number[]}
     */
	public get privToBeInsert(): number[] {
		return this._privToBeInsert;
	}

    /**
     * Getter privToBeDelete
     * @return {number[]}
     */
	public get privToBeDelete(): number[] {
		return this._privToBeDelete;
	}

    /**
     * Getter roleName
     * @return {string}
     */
	public get roleName(): string {
		return this._roleName;
	}

    /**
     * Getter description
     * @return {string}
     */
	public get description(): string {
		return this._description;
	}

    /**
     * Setter idOfDepartment
     * @param {number} value
     */
	public set idOfDepartment(value: number) {
		this._idOfDepartment = value;
	}

    /**
     * Setter privToBeInsert
     * @param {number[]} value
     */
	public set privToBeInsert(value: number[]) {
		this._privToBeInsert = value;
	}

    /**
     * Setter privToBeDelete
     * @param {number[]} value
     */
	public set privToBeDelete(value: number[]) {
		this._privToBeDelete = value;
	}

    /**
     * Setter roleName
     * @param {string} value
     */
	public set roleName(value: string) {
		this._roleName = value;
	}

    /**
     * Setter description
     * @param {string} value
     */
	public set description(value: string) {
		this._description = value;
	}


    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Getter name
     * @return {string}
     */
	public get name(): string {
		return this._name;
	}

    /**
     * Getter isActive
     * @return {boolean}
     */
	public get isActive(): boolean {
		return this._isActive;
	}

    /**
     * Getter isSelected
     * @return {boolean}
     */
	public get isSelected(): boolean {
		return this._isSelected;
	}

    /**
     * Getter lastModifiedDate
     * @return {string}
     */
	public get lastModifiedDate(): string {
		return this._lastModifiedDate;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Setter name
     * @param {string} value
     */
	public set name(value: string) {
		this._name = value;
	}

    /**
     * Setter isActive
     * @param {boolean} value
     */
	public set isActive(value: boolean) {
		this._isActive = value;
	}

    /**
     * Setter isSelected
     * @param {boolean} value
     */
	public set isSelected(value: boolean) {
		this._isSelected = value;
	}

    /**
     * Setter lastModifiedDate
     * @param {string} value
     */
	public set lastModifiedDate(value: string) {
		this._lastModifiedDate = value;
	}


    /**
     * Getter lastUpdatedDateTime
     * @return {string}
     */
	public get lastUpdatedDateTime(): string {
		return this._lastUpdatedDateTime;
	}

    /**
     * Setter lastUpdatedDateTime
     * @param {string} value
     */
	public set lastUpdatedDateTime(value: string) {
		this._lastUpdatedDateTime = value;
	}


}