<div class="card card-theme card-forms">
    <div class="card-body p-0">
        <div class="row" [formGroup]="itemForm">
            <div class="col-xl-10">
                <div class="row">
                    <div class="col-lg-6">
                        <div class="form-group form-group-inline-control required ">
                            <label class="form-label">Item Season Type</label>
                            <div class="form-control-wrapper">
                                <div class="radio radio-primary form-check-inline" *ngFor="let item of dropdown?.itemSeasonTypes; index as i">
                                    <input (change)="onChangeRS(itemObj.itemsSeason === 'SEASONAL')" type="radio" [id]="'regular-' + i" [value]="item.value"
                                        [(ngModel)]="itemObj.itemsSeason" formControlName="itemsSeason" />
                                    <label [for]="'regular-' + i">{{item.label}}</label>
                                </div>
                                <!-- <div class="radio radio-primary form-check-inline">
                                    <input (change)="onChangeRS(itemObj.itemsSeason)" type="radio" id="seasonal" [value]="true"
                                        [(ngModel)]="itemObj.itemsSeason" formControlName="itemsSeason" />
                                    <label for="seasonal">Seasonal</label>
                                </div> -->
                                <div class="form-group  theme-ngselect mb-0" *ngIf="itemObj.itemsSeason === 'SEASONAL'">
                                    <ng-select (change)="onChangeSeason(itemObj.seasonMasterId)" placeholder="Item Season Type" [multiple]="false"
                                        [clearable]="false" formControlName="seasonMasterId" [items]="dropdown?.seasonMaster" bindLabel="label"
                                        bindValue="value" [(ngModel)]="itemObj.seasonMasterId">
                                    </ng-select>
                                    <div class="message error-message"
                                        *ngIf="itemForm.controls['seasonMasterId'].hasError('required') &&  itemForm.controls['seasonMasterId'].touched">
                                        {{utilsService.validationService.SEASON_TYPE_REQ}}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group form-group-inline-control required">
                            <label class="form-label">Title</label>
                            <div class="form-control-wrapper">
                                <input [maxlength]="utilsService.validationService.MAX_100" [(ngModel)]="itemObj.title" formControlName="title"
                                    type="text" class="form-control" placeholder="Enter Title">
                                <div class="message error-message"
                                    *ngIf="itemForm.controls['title'].hasError('required') &&  itemForm.controls['title'].touched">
                                    {{utilsService.validationService.TITLE_REQ}}
                                </div>
                                <div class="message error-message"
                                    *ngIf="!itemForm.controls['title'].hasError('required') && !itemForm.controls['title'].valid && itemForm.controls['title'].touched">
                                    {{utilsService.validationService.TITLE_INVALID}}
                                </div>
                            </div>
                        </div>
                        <div class="form-group form-group-inline-control required">
                            <label class="form-label">Search Keyword</label>
                            <div class="form-control-wrapper">
                                <input [maxlength]="utilsService.validationService.MAX_200" [(ngModel)]="itemObj.searchKeyWords"
                                    formControlName="searchKeyWords" type="text" class="form-control" placeholder="Enter search keyword">
                                <div class="message error-message"
                                    *ngIf="itemForm.controls['searchKeyWords'].hasError('required') &&  itemForm.controls['searchKeyWords'].touched">
                                    {{utilsService.validationService.SEARCH_KEYWORD_REQ}}
                                </div>
                                <div class="message error-message"
                                    *ngIf="!itemForm.controls['searchKeyWords'].hasError('required') && !itemForm.controls['searchKeyWords'].valid && itemForm.controls['searchKeyWords'].touched">
                                    {{utilsService.validationService.SEARCH_KEYWORD_INVALID}}
                                </div>
                            </div>
                        </div>
                        <div class="form-group theme-ngselect form-group-inline-control" formArrayName="bulletPts">
                            <label class="form-label">Add Bullet Points</label>
                            <div class="form-control-wrapper">
                                <div class="form-group-button-wrapper">
                                    <ng-container *ngFor="let arr of bulletPts.controls; index as i" [formGroupName]="i">
                                        <div class="form-control-wrapper">
                                            <div class="form-group-button">
                                                <input [maxlength]="utilsService.validationService.MAX_500" formControlName="point" type="text" class="form-control"
                                                    placeholder="Enter Bullet Point" [(ngModel)]="itemObj.bulletPoints[i].points">
                                                <button (click)="addBullet.emit()" class="btn btn-outline-white btn-icon text-black"><i
                                                        class="th th-outline-add-circle"></i></button>
                                                <button *ngIf="bulletPts.length > 1" (click)="removeBulletPt(i)"
                                                    class="btn btn-outline-white btn-icon text-danger"><i class="th th-outline-minus-cirlce"></i></button>
                                            </div>
                                            <div *ngIf="arr.get('point').hasError('required') && arr.get('point').touched" class="message error-message">
                                                {{utilsService.validationService.BP_REQ}}
                                            </div>
                                            <div *ngIf="!arr.get('point').hasError('required') && !arr.get('point').valid && arr.get('point').touched"
                                                class="message error-message">
                                                {{utilsService.validationService.BP_INVALID}}
                                            </div>
                                        </div>
                                    </ng-container>
                                </div>
                            </div>
                        </div>
                        <div class="form-group form-group-inline-control ">
                            <label class="form-label">Material</label>
                            <div class="form-control-wrapper">
                                <input [maxlength]="utilsService.validationService.MAX_50" [(ngModel)]="itemObj.material" formControlName="material"
                                    type="text" class="form-control" placeholder="Enter Material">
                                <div class="message error-message"
                                    *ngIf="!itemForm.controls['material'].hasError('required') && !itemForm.controls['material'].valid && itemForm.controls['material'].touched">
                                    {{utilsService.validationService.MATERIAL_INVALID}}
                                </div>
                            </div>
                        </div>
                        <div class="form-group form-group-inline-control ">
                            <label class="form-label">Sub Material</label>
                            <div class="form-control-wrapper">
                                <input formControlName="subMaterial" [maxlength]="utilsService.validationService.MAX_50"
                                    [(ngModel)]="itemObj.subMaterial" type="text" class="form-control" placeholder="Enter Sub Material">
                                <div class="message error-message"
                                    *ngIf="!itemForm.controls['subMaterial'].hasError('required') && !itemForm.controls['subMaterial'].valid && itemForm.controls['subMaterial'].touched">
                                    {{utilsService.validationService.SUB_MATERIAL_INVALID}}
                                </div>
                            </div>
                        </div>
                        <!-- <div class="form-group theme-ngselect form-group-inline-control ">
                            <label class="form-label">Color</label>
                            <div class="form-control-wrapper">
                                <div class="form-group-button">
                                    <ng-select placeholder="Select Color" [multiple]="false" [clearable]="false"
                                        [items]="demo" bindLabel="name" bindValue="id" [(ngModel)]="selectedDemo1">
                                    </ng-select>
                                    <button class="btn btn-outline-white"><i
                                            class="th th-outline-add-circle text-primary"></i></button>
                                </div>
                            </div>
                        </div> -->
                        <div class="form-group theme-ngselect form-group-inline-control ">
                            <label class="form-label">Pack Of</label>
                            <div class="form-control-wrapper">
                                <ng-select (clear)="onClearPackoff()" placeholder="Pack Of" [multiple]="false" [clearable]="true" [items]="dropdown?.packOf"
                                    bindLabel="label" bindValue="value" [(ngModel)]="itemObj.packOfId" formControlName="packOf">
                                </ng-select>
                            </div>
                        </div>
                        <div class="form-group form-group-inline-control ">
                            <label class="form-label">Enter Pack Qty</label>
                            <div class="form-control-wrapper">
                                <input [maxlength]="utilsService.validationService.MAX_50"
                                    [(ngModel)]="itemObj.packQty" formControlName="packQty" type="text" class="form-control"
                                    placeholder="Enter Pack Qty">
                            </div>
                        </div>
                        <div class="form-group form-group-inline-control ">
                            <label class="form-label">Item Description</label>
                            <div class="form-control-wrapper">
                                <textarea [maxlength]="utilsService.validationService.MAX_4000" formControlName="itemsDescription"
                                    [(ngModel)]="itemObj.itemsDescription" class="form-control" rows="3"
                                    placeholder="Enter Item Description">
                                </textarea>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="form-group  form-group-inline-control required" *ngIf="itemObj.itemsSeason === 'SEASONAL'">
                            <label class="form-label">Advance PO Date</label>
                            <div class="form-control-wrapper">
                                <div class="form-group-icon-end">
                                    <i class="th th-outline-calendar-1"></i>
                                    <input [ngModelOptions]="{standalone: true}" [(ngModel)]="itemObj.tempAdvPODate" type="text"
                                        class="form-control bg-gray" placeholder="Advance PO Date" disabled>
                                </div>
                            </div>
                        </div>
                        <div class="form-group form-group-inline-control ">
                            <label class="form-label">Number Of Battery</label>
                            <div class="form-control-wrapper">
                                <input mask="separator.0" thousandSeparator="" [maxlength]="utilsService.validationService.MAX_12"
                                    formControlName="numberOfBattery" [(ngModel)]="itemObj.numberOfBattery" type="text" class="form-control"
                                    placeholder="Enter Number Of Battery ">
                            </div>
                        </div>
                        <div class="form-group theme-ngselect form-group-inline-control ">
                            <label class="form-label">Battery Type</label>
                            <div class="form-control-wrapper">
                                <ng-select (clear)="onClearBatteryType()" placeholder="Select Battery Type" [multiple]="false" [clearable]="true" [items]="dropdown?.batteryType"
                                    bindLabel="label" bindValue="value" [(ngModel)]="itemObj.batteryTypeId" formControlName="batteryType">
                                </ng-select>
                            </div>
                        </div>
                        <div class="form-group theme-ngselect form-group-inline-control ">
                            <label class="form-label">Remote Control</label>
                            <div class="form-control-wrapper">
                                <ng-select placeholder="Remote Control" [multiple]="false" [clearable]="false" [items]="statusYesNo"
                                    bindLabel="label" bindValue="value" [(ngModel)]="itemObj.isRemoteControl" formControlName="isRemoteControl">
                                </ng-select>
                            </div>
                        </div>
                        <div class="form-group form-group-inline-control ">
                            <label class="form-label">Capacity</label>
                            <div class="form-control-wrapper">
                                <input mask="separator.5" thousandSeparator="" [maxlength]="utilsService.validationService.MAX_15"
                                    formControlName="capacity" [(ngModel)]="itemObj.capacity" type="text" class="form-control"
                                    placeholder="Enter Capacity">
                            </div>
                        </div>
                        <div class="form-group form-group-inline-control ">
                            <label class="form-label">Voltage</label>
                            <div class="form-control-wrapper">
                                <input mask="separator.5" thousandSeparator="" [maxlength]="utilsService.validationService.MAX_15"
                                    formControlName="voltage" [(ngModel)]="itemObj.voltage" type="text" class="form-control"
                                    placeholder="Enter Voltage">
                            </div>
                        </div>
                        <div class="form-group form-group-inline-control ">
                            <label class="form-label">Wattage</label>
                            <div class="form-control-wrapper">
                                <input mask="separator.5" thousandSeparator="" [maxlength]="utilsService.validationService.MAX_15"
                                    formControlName="wattage" [(ngModel)]="itemObj.wattage" type="text" class="form-control"
                                    placeholder="Enter Wattage">
                            </div>
                        </div>
                        <div class="form-group theme-ngselect form-group-inline-control ">
                            <label class="form-label">Mounting Type</label>
                            <div class="form-control-wrapper">
                                <ng-select formControlName="mountingType" (clear)="onClearMType()" placeholder="Select Mounting Type"
                                    [multiple]="false" [clearable]="true" [items]="dropdown?.mountingType" bindLabel="label" bindValue="value"
                                    [(ngModel)]="itemObj.mountingTypeId">
                                </ng-select>
                            </div>
                        </div>
                        <div class="form-group theme-ngselect form-group-inline-control ">
                            <label class="form-label">Size</label>
                            <div class="form-control-wrapper">
                                <ng-select (clear)="onClearSize()" placeholder="Select Size" [multiple]="false" [clearable]="true"
                                    [items]="dropdown?.sizeMaster" bindLabel="label" bindValue="value" [(ngModel)]="itemObj.sizeMasterId"
                                    formControlName="sizeMasterId">
                                </ng-select>
                            </div>
                        </div>
                        <div class="form-group form-group-inline-control ">
                            <label class="form-label">Accessories</label>
                            <div class="form-control-wrapper">
                                <input [maxlength]="utilsService.validationService.MAX_50" [(ngModel)]="itemObj.accessories"
                                    formControlName="accessories" type="text" class="form-control" placeholder="Enter Accessories">
                            </div>
                        </div>
                        <div class="form-group form-group-inline-control ">
                            <label class="form-label">Special Features</label>
                            <div class="form-control-wrapper">
                                <textarea formControlName="specialFeatures" [(ngModel)]="itemObj.specialFeatures"
                                    [maxlength]="utilsService.validationService.MAX_500" class="form-control" rows="2"
                                    placeholder="Enter Special Features"></textarea>
                            </div>
                        </div>
                    </div>


                </div>
            </div>
        </div>
    </div>
</div>