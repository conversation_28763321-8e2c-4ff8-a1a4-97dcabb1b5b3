import { RouterModule, Routes } from "@angular/router";
import { StockWarehouseTransferComponent } from "./stock-warehouse-transfer.component";
import { SharedModule } from "src/app/shared/shared.module";
import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { SwtMissingItemByUsReportComponent } from "./swt-missing-item-by-us-report/swt-missing-item-by-us-report.component";
import { SwtRequestedBySelfComponent } from "./swt-requested-by-self/swt-requested-by-self.component";
import { SwtRequestedByUsComponent } from "./swt-requested-by-us/swt-requested-by-us.component";

const routes: Routes = [
    { path: '', component: StockWarehouseTransferComponent },
]

@NgModule({
    imports: [
        CommonModule,
        SharedModule.forRoot(),
        RouterModule.forChild(routes)
    ],
    declarations: [StockWarehouseTransferComponent, SwtRequestedByUsComponent, SwtRequestedBySelfComponent, SwtMissingItemByUsReportComponent]
})

export class StockWarehouseTransferModule { }
