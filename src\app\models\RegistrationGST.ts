import { deserializeAs, serializeAs } from 'cerialize';

export class RegistrationGST {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('gstNo')
    @deserializeAs('gstNo')
    private _gstNo: string;

    @serializeAs('billingAddress')
    @deserializeAs('billingAddress')
    private _billingAddress: string;

    @serializeAs('reason')
    @deserializeAs('reason')
    private _reason: string;

    @serializeAs('isActive')
    @deserializeAs('isActive')
    private _isActive: boolean;

    @serializeAs('gstDetails')
    @deserializeAs('gstDetails')
    private _gstDetails: RegistrationGSTDetails[];

    constructor() {
        this.gstDetails = [];
        this.isActive = true
    }


    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}


    /**
     * Getter gstNo
     * @return {string}
     */
	public get gstNo(): string {
		return this._gstNo;
	}

    /**
     * Getter billingAddress
     * @return {string}
     */
	public get billingAddress(): string {
		return this._billingAddress;
	}

    /**
     * Getter reason
     * @return {string}
     */
	public get reason(): string {
		return this._reason;
	}

    /**
     * Getter isActive
     * @return {boolean}
     */
	public get isActive(): boolean {
		return this._isActive;
	}

    /**
     * Getter gstDetails
     * @return {RegistrationGSTDetails[]}
     */
	public get gstDetails(): RegistrationGSTDetails[] {
		return this._gstDetails;
	}

    /**
     * Setter gstNo
     * @param {string} value
     */
	public set gstNo(value: string) {
		this._gstNo = value;
	}

    /**
     * Setter billingAddress
     * @param {string} value
     */
	public set billingAddress(value: string) {
		this._billingAddress = value;
	}

    /**
     * Setter reason
     * @param {string} value
     */
	public set reason(value: string) {
		this._reason = value;
	}

    /**
     * Setter isActive
     * @param {boolean} value
     */
	public set isActive(value: boolean) {
		this._isActive = value;
	}

    /**
     * Setter gstDetails
     * @param {RegistrationGSTDetails[]} value
     */
	public set gstDetails(value: RegistrationGSTDetails[]) {
		this._gstDetails = value;
	}


}

export class RegistrationGSTDetails {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('addressLineName')
    @deserializeAs('addressLineName')
    private _addressLineName: string;

    @serializeAs('zipCode')
    @deserializeAs('zipCode')
    private _zipCode: string;

    @serializeAs('isDefault')
    @deserializeAs('isDefault')
    private _isDefault: boolean;

    @serializeAs('isActive')
    @deserializeAs('isActive')
    private _isActive: boolean;

    @serializeAs('countryId')
    @deserializeAs('countryId')
    private _countryId: number;

    @serializeAs('stateId')
    @deserializeAs('stateId')
    private _stateId: number;

    @serializeAs('cityId')
    @deserializeAs('cityId')
    private _cityId: number;

    @deserializeAs('countryDropdown')
    private _countryDropdown: any[];

    @deserializeAs('stateDropdown')
    private _stateDropdown: any[];

    @deserializeAs('cityDropdown')
    private _cityDropdown: any[];

    @deserializeAs('country')
    private _country: any;

    @deserializeAs('state')
    private _state: any;

    @deserializeAs('city')
    private _city: any;

    @deserializeAs('addressLine')
    private _addressLine: any;

    constructor() {
        this.isActive = true;
        this.isDefault = false;
        this.countryDropdown = [];
        this.stateDropdown = [];
        this.cityDropdown = []
    }


    /**
     * Getter addressLine
     * @return {any}
     */
	public get addressLine(): any {
		return this._addressLine;
	}

    /**
     * Setter addressLine
     * @param {any} value
     */
	public set addressLine(value: any) {
		this._addressLine = value;
	}
    

    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}
    

    /**
     * Getter country
     * @return {any}
     */
	public get country(): any {
		return this._country;
	}

    /**
     * Setter country
     * @param {any} value
     */
	public set country(value: any) {
		this._country = value;
	}

    /**
     * Getter state
     * @return {any}
     */
	public get state(): any {
		return this._state;
	}

    /**
     * Setter state
     * @param {any} value
     */
	public set state(value: any) {
		this._state = value;
	}

    /**
     * Getter city
     * @return {any}
     */
	public get city(): any {
		return this._city;
	}

    /**
     * Setter city
     * @param {any} value
     */
	public set city(value: any) {
		this._city = value;
	}


    /**
     * Getter countryDropdown
     * @return {any[]}
     */
	public get countryDropdown(): any[] {
		return this._countryDropdown;
	}

    /**
     * Setter countryDropdown
     * @param {any[]} value
     */
	public set countryDropdown(value: any[]) {
		this._countryDropdown = value;
	}

    /**
     * Getter stateDropdown
     * @return {any[]}
     */
	public get stateDropdown(): any[] {
		return this._stateDropdown;
	}

    /**
     * Setter stateDropdown
     * @param {any[]} value
     */
	public set stateDropdown(value: any[]) {
		this._stateDropdown = value;
	}

    /**
     * Getter cityDropdown
     * @return {any[]}
     */
	public get cityDropdown(): any[] {
		return this._cityDropdown;
	}

    /**
     * Setter cityDropdown
     * @param {any[]} value
     */
	public set cityDropdown(value: any[]) {
		this._cityDropdown = value;
	}


    /**
     * Getter cityId
     * @return {number}
     */
	public get cityId(): number {
		return this._cityId;
	}

    /**
     * Setter cityId
     * @param {number} value
     */
	public set cityId(value: number) {
		this._cityId = value;
	}


    /**
     * Getter countryId
     * @return {number}
     */
	public get countryId(): number {
		return this._countryId;
	}

    /**
     * Setter countryId
     * @param {number} value
     */
	public set countryId(value: number) {
		this._countryId = value;
	}

    /**
     * Getter stateId
     * @return {number}
     */
	public get stateId(): number {
		return this._stateId;
	}

    /**
     * Setter stateId
     * @param {number} value
     */
	public set stateId(value: number) {
		this._stateId = value;
	}


    /**
     * Getter addressLineName
     * @return {string}
     */
	public get addressLineName(): string {
		return this._addressLineName;
	}

    /**
     * Setter addressLineName
     * @param {string} value
     */
	public set addressLineName(value: string) {
		this._addressLineName = value;
	}

    /**
     * Getter zipCode
     * @return {string}
     */
	public get zipCode(): string {
		return this._zipCode;
	}

    /**
     * Setter zipCode
     * @param {string} value
     */
	public set zipCode(value: string) {
		this._zipCode = value;
	}

    /**
     * Getter isDefault
     * @return {boolean}
     */
	public get isDefault(): boolean {
		return this._isDefault;
	}

    /**
     * Setter isDefault
     * @param {boolean} value
     */
	public set isDefault(value: boolean) {
		this._isDefault = value;
	}

    /**
     * Getter isActive
     * @return {boolean}
     */
	public get isActive(): boolean {
		return this._isActive;
	}

    /**
     * Setter isActive
     * @param {boolean} value
     */
	public set isActive(value: boolean) {
		this._isActive = value;
	}
    

}