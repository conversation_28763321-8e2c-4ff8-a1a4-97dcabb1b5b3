<div class="page-filters">
    <div class="page-filters-left">
        <div class="form-group form-group-sm filter-search">
            <div class="form-group-icon-start">
                <i class="th th-outline-search-normal-1 icon-broder "></i>
                <input type="text" class="form-control" placeholder="Search by ID">
            </div>
        </div>
        <div class="form-group form-group-sm form-group-inline-control ">
            <div class="form-control-wrapper">
                <div class="form-group-icon-end">
                    <i class="th th-outline-calendar-1"></i>
                    <input type="text" class="form-control" placeholder="Last 30 days">
                </div>
            </div>
        </div>
        <div class="form-group theme-ngselect form-group-sm">
            <ng-select placeholder="Ticket Subject" [multiple]="false" [clearable]="false" [items]="demo"
                bindLabel="name" bindValue="id" [(ngModel)]="selectedDemo1">
            </ng-select>
        </div>
        <div class="form-group theme-ngselect form-group-sm">
            <ng-select placeholder="Assign to" [multiple]="false" [clearable]="false" [items]="demo" bindLabel="name"
                bindValue="id" [(ngModel)]="selectedDemo1">
            </ng-select>
        </div>
        <div class="form-group theme-ngselect form-group-sm">
            <ng-select placeholder="Ticket Type" [multiple]="false" [clearable]="false" [items]="demo" bindLabel="name"
                bindValue="id" [(ngModel)]="selectedDemo1">
            </ng-select>
        </div>
        <div class="form-group theme-ngselect form-group-sm">
            <ng-select placeholder="Subject Status" [multiple]="false" [clearable]="false" [items]="demo"
                bindLabel="name" bindValue="id" [(ngModel)]="selectedDemo1">
            </ng-select>
        </div>
    </div>
    <div class="page-filters-right">
        <div class="dropdown export-dropdown">
            <button type="button" class="btn btn-sm btn-outline-white dropdown-toggle" data-bs-toggle="dropdown"
                aria-expanded="false">
                Export
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="#">Action</a></li>
                <li><a class="dropdown-item" href="#">Another action</a></li>
                <li><a class="dropdown-item" href="#">Something else here</a></li>
            </ul>
        </div>
        <app-table-column-filter-dropdown />

    </div>
</div>

<div class="card card-theme card-table-sticky3">
    <div class="card-body p-0">
        <div class="accordion accordion-group" id="accordionSingle">
            <div class="accordion-item">
                <h2 class="accordion-header" id="accordionSingleHeadingOne">
                    <button class="accordion-button" type="button" data-bs-toggle="collapse"
                        data-bs-target="#accordionSingleCollapseOne" aria-expanded="true"
                        aria-controls="accordionSingleCollapseOne">
                        <div class="accordion-header-left">
                            <ul class="accordion-header-item">
                                <li>Valamji Main Branch</li>
                            </ul>
                        </div>
                        <div class="accordion-header-right">

                        </div>
                    </button>
                </h2>
                <div id="accordionSingleCollapseOne" class="accordion-collapse collapse show"
                    aria-labelledby="accordionSingleHeadingOne" data-bs-parent="#accordionSingle">
                    <div class="accordion-body tbl-accordion-body p-0">
                        <div class="table-responsive">
                            <table class="table-theme table-hover table table-bordered table-sticky">
                                <thead class="border-less">
                                    <tr>
                                        <th class="d-flex align-items-center gap-2">
                                            <div class="checkbox checkbox-primary checkbox-small">
                                                <input type="checkbox" id="tbl-checkbox2"
                                                    class="material-inputs filled-in" />
                                                <label for="tbl-checkbox2"></label>
                                            </div>
                                            Ticket ID
                                        </th>
                                        <th>Ticket Subject</th>
                                        <th>Date & Time</th>
                                        <th>Ticket Type</th>
                                        <th>Subject Status</th>
                                        <th>Created By</th>
                                        <th>Assign to</th>
                                        <th>Note</th>
                                        <th class="text-center">Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="tbl-user tbl-bold">
                                            <a href="" class="text-black"
                                                [routerLink]="'/users/audit-tickets/audit-tickets-details'">
                                                <div class="tbl-user-checkbox-srno">
                                                    <div class="checkbox checkbox-primary checkbox-small">
                                                        <input type="checkbox" id="tbl-checkbox2"
                                                            class="material-inputs filled-in" />
                                                        <label for="tbl-checkbox2"></label>
                                                    </div>
                                                    #15458545
                                                </div>
                                            </a>
                                        </td>
                                        <td>Calculate Stock</td>
                                        <td>27/11/2024, 09:17 AM</td>
                                        <td class="text-primary"> Manual</td>
                                        <td>
                                            <button class="btn btn-xs btn-light-warning btn-icon-text">
                                                In Progress
                                            </button>
                                        </td>
                                        <td>Alpeshbhai</td>
                                        <td>
                                            <span class="w-100 d-block">Nilesh Patel</span>
                                            <span class="w-100 d-block">3 Days Ago</span>
                                        </td>
                                        <td>Hdhfdjhsd hsjksd sjkdhkfjsd sdfsfh</td>
                                        <td class="tbl-action">
                                            <div class="tbl-action-group">
                                                <button disabled="" class="btn btn-xs  btn-icon btn-outline-info"
                                                    ngbTooltip="Edit" placement="bottom" container="body"
                                                    triggers="hover">
                                                    <i class="th th-outline-edit"></i>
                                                </button>
                                                <button class="btn btn-xs btn-light-success btn-icon"
                                                    data-bs-toggle="modal" data-bs-target="#successModal"
                                                    ngbTooltip="Tooltip" placement="bottom" container="body"
                                                    triggers="hover">
                                                    <i class="th th-outline-tick-circle"></i>
                                                </button>
                                                <button class="btn btn-xs btn-light-danger btn-icon"
                                                    data-bs-toggle="modal" data-bs-target="#delete1Modal"
                                                    ngbTooltip="Delete" placement="left" container="body"
                                                    triggers="hover">
                                                    <i class="th th-outline-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="tbl-user tbl-bold">
                                            <a href="" class="text-black"
                                                [routerLink]="'/users/audit-tickets/audit-tickets-details'">

                                                <div class="tbl-user-checkbox-srno">
                                                    <div class="checkbox checkbox-primary checkbox-small">
                                                        <input type="checkbox" id="tbl-checkbox2"
                                                            class="material-inputs filled-in" />
                                                        <label for="tbl-checkbox2"></label>
                                                    </div>
                                                    #15458545
                                                </div>
                                            </a>
                                        </td>
                                        <td>Calculate Stock</td>
                                        <td>27/11/2024, 09:17 AM</td>
                                        <td class="text-warning"> Breach</td>
                                        <td>
                                            <button class="btn btn-xs btn-light-success btn-icon-text"
                                                data-bs-toggle="modal" data-bs-target="#successModal">
                                                Completed
                                            </button>
                                        </td>
                                        <td>Alpeshbhai</td>
                                        <td>
                                            <span class="w-100 d-block">Nilesh Patel</span>
                                            <span class="w-100 d-block">3 Days Ago</span>
                                        </td>
                                        <td>Hdhfdjhsd hsjksd sjkdhkfjsd sdfsfh</td>
                                        <td class="tbl-action">
                                            <div class="tbl-action-group">
                                                <button class="btn btn-xs btn-icon btn-outline-white" ngbTooltip="Edit"
                                                    placement="bottom" container="body" triggers="hover">
                                                    <i class="th th-outline-edit"></i>
                                                </button>
                                                <button class="btn btn-xs btn-light-success btn-icon"
                                                    data-bs-toggle="modal" data-bs-target="#successModal"
                                                    ngbTooltip="Tooltip" placement="bottom" container="body"
                                                    triggers="hover">
                                                    <i class="th th-outline-tick-circle"></i>
                                                </button>
                                                <button class="btn btn-xs btn-light-danger btn-icon"
                                                    data-bs-toggle="modal" data-bs-target="#delete1Modal"
                                                    ngbTooltip="Delete" placement="left" container="body"
                                                    triggers="hover">
                                                    <i class="th th-outline-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="tbl-user tbl-bold">
                                            <a href="" class="text-black"
                                                [routerLink]="'/users/audit-tickets/audit-tickets-details'">
                                                <div class="tbl-user-checkbox-srno">
                                                    <div class="checkbox checkbox-primary checkbox-small">
                                                        <input type="checkbox" id="tbl-checkbox2"
                                                            class="material-inputs filled-in" />
                                                        <label for="tbl-checkbox2"></label>
                                                    </div>
                                                    #15458545
                                                </div>
                                            </a>
                                        </td>
                                        <td>Calculate Stock</td>
                                        <td>27/11/2024, 09:17 AM</td>
                                        <td class="text-success"> PO</td>
                                        <td>
                                            <button class="btn btn-xs btn-light-primary btn-icon-text">
                                                New
                                            </button>
                                        </td>
                                        <td>Alpeshbhai</td>
                                        <td>
                                            <span class="w-100 d-block text-danger">Not Assigned</span>
                                        </td>
                                        <td>Hdhfdjhsd hsjksd sjkdhkfjsd sdfsfh</td>
                                        <td class="tbl-action">
                                            <div class="tbl-action-group">
                                                <button class="btn btn-xs btn-icon btn-outline-white" ngbTooltip="Edit"
                                                    placement="bottom" container="body" triggers="hover">
                                                    <i class="th th-outline-edit"></i>
                                                </button>
                                                <button class="btn btn-xs btn-light-warning btn-icon"
                                                    data-bs-toggle="modal" data-bs-target="#assignModal"
                                                    ngbTooltip="Tooltip" placement="bottom" container="body"
                                                    triggers="hover">
                                                    <i class="th th-outline-user-add"></i>
                                                </button>
                                                <button class="btn btn-xs btn-light-success btn-icon"
                                                    data-bs-toggle="modal" data-bs-target="#successModal"
                                                    ngbTooltip="Tooltip" placement="bottom" container="body"
                                                    triggers="hover">
                                                    <i class="th th-outline-tick-circle"></i>
                                                </button>
                                                <button class="btn btn-xs btn-light-danger btn-icon"
                                                    data-bs-toggle="modal" data-bs-target="#delete1Modal"
                                                    ngbTooltip="Delete" placement="left" container="body"
                                                    triggers="hover">
                                                    <i class="th th-outline-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="tbl-user tbl-bold">
                                            <a href="" class="text-black"
                                                [routerLink]="'/users/audit-tickets/audit-tickets-details'">
                                                <div class="tbl-user-checkbox-srno">
                                                    <div class="checkbox checkbox-primary checkbox-small">
                                                        <input type="checkbox" id="tbl-checkbox2"
                                                            class="material-inputs filled-in" />
                                                        <label for="tbl-checkbox2"></label>
                                                    </div>
                                                    #15458545
                                                </div>
                                            </a>
                                        </td>
                                        <td>Calculate Stock</td>
                                        <td>27/11/2024, 09:17 AM</td>
                                        <td class="text-danger"> QC Check</td>
                                        <td>
                                            <button class="btn btn-xs btn-light-primary btn-icon-text">
                                                New
                                            </button>
                                        </td>
                                        <td>Alpeshbhai</td>
                                        <td>
                                            <span class="w-100 d-block text-danger">Not Assigned</span>
                                        </td>
                                        <td>Hdhfdjhsd hsjksd sjkdhkfjsd sdfsfh</td>
                                        <td class="tbl-action">
                                            <div class="tbl-action-group">
                                                <button class="btn btn-xs btn-icon btn-outline-white" ngbTooltip="Edit"
                                                    placement="bottom" container="body" triggers="hover">
                                                    <i class="th th-outline-edit"></i>
                                                </button>
                                                <button class="btn btn-xs btn-light-warning btn-icon"
                                                    data-bs-toggle="modal" data-bs-target="#assignModal"
                                                    ngbTooltip="Tooltip" placement="bottom" container="body"
                                                    triggers="hover">
                                                    <i class="th th-outline-user-add"></i>
                                                </button>
                                                <button class="btn btn-xs btn-light-success btn-icon"
                                                    data-bs-toggle="modal" data-bs-target="#successModal"
                                                    ngbTooltip="Tooltip" placement="bottom" container="body"
                                                    triggers="hover">
                                                    <i class="th th-outline-tick-circle"></i>
                                                </button>
                                                <button class="btn btn-xs btn-light-danger btn-icon"
                                                    data-bs-toggle="modal" data-bs-target="#delete1Modal"
                                                    ngbTooltip="Delete" placement="left" container="body"
                                                    triggers="hover">
                                                    <i class="th th-outline-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="tbl-user tbl-bold">
                                            <a href="" class="text-black"
                                                [routerLink]="'/users/audit-tickets/audit-tickets-details'">
                                                <div class="tbl-user-checkbox-srno">
                                                    <div class="checkbox checkbox-primary checkbox-small">
                                                        <input type="checkbox" id="tbl-checkbox2"
                                                            class="material-inputs filled-in" />
                                                        <label for="tbl-checkbox2"></label>
                                                    </div>
                                                    #15458545
                                                </div>
                                            </a>
                                        </td>
                                        <td>Calculate Stock</td>
                                        <td>27/11/2024, 09:17 AM</td>
                                        <td class="text-black"> QC Check</td>
                                        <td>
                                            <button class="btn btn-xs btn-light-primary btn-icon-text">
                                                New
                                            </button>
                                        </td>
                                        <td>Alpeshbhai</td>
                                        <td>
                                            <span class="w-100 d-block text-danger">Not Assigned</span>
                                        </td>
                                        <td>Hdhfdjhsd hsjksd sjkdhkfjsd sdfsfh</td>
                                        <td class="tbl-action">
                                            <div class="tbl-action-group">
                                                <button class="btn btn-xs btn-icon btn-outline-white" ngbTooltip="Edit"
                                                    placement="bottom" container="body" triggers="hover">
                                                    <i class="th th-outline-edit"></i>
                                                </button>
                                                <button class="btn btn-xs btn-light-success btn-icon"
                                                    data-bs-toggle="modal" data-bs-target="#successModal"
                                                    ngbTooltip="Tooltip" placement="bottom" container="body"
                                                    triggers="hover">
                                                    <i class="th th-outline-tick-circle"></i>
                                                </button>
                                                <button class="btn btn-xs btn-light-danger btn-icon"
                                                    data-bs-toggle="modal" data-bs-target="#delete1Modal"
                                                    ngbTooltip="Delete" placement="left" container="body"
                                                    triggers="hover">
                                                    <i class="th th-outline-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="accordion-item">
                <h2 class="accordion-header" id="accordionSingleHeadingTwo">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                        data-bs-target="#accordionSingleCollapseTwo" aria-expanded="false"
                        aria-controls="accordionSingleCollapseTwo">
                        <div class="accordion-header-left">
                            <ul class="accordion-header-item">
                                <li>Valamji Main Branch</li>
                            </ul>
                        </div>
                        <div class="accordion-header-right">

                        </div>
                    </button>
                </h2>
                <div id="accordionSingleCollapseTwo" class="accordion-collapse collapse"
                    aria-labelledby="accordionSingleHeadingTwo" data-bs-parent="#accordionSingle">
                    <div class="accordion-body tbl-accordion-body p-0">
                        <div class="table-responsive">
                            <table class="table-theme table-hover table table-bordered table-sticky">
                                <thead class="border-less">
                                    <tr>
                                        <th class="d-flex align-items-center gap-2">
                                            <div class="checkbox checkbox-primary checkbox-small">
                                                <input type="checkbox" id="tbl-checkbox2"
                                                    class="material-inputs filled-in" />
                                                <label for="tbl-checkbox2"></label>
                                            </div>
                                            Ticket ID
                                        </th>
                                        <th>Ticket Subject</th>
                                        <th>Date & Time</th>
                                        <th>Ticket Type</th>
                                        <th>Subject Status</th>
                                        <th>Created By</th>
                                        <th>Assign to</th>
                                        <th>Note</th>
                                        <th class="text-center">Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="tbl-user tbl-bold">
                                            <a href="" class="text-black"
                                                [routerLink]="'/users/audit-tickets/audit-tickets-details'">
                                                <div class="tbl-user-checkbox-srno">
                                                    <div class="checkbox checkbox-primary checkbox-small">
                                                        <input type="checkbox" id="tbl-checkbox2"
                                                            class="material-inputs filled-in" />
                                                        <label for="tbl-checkbox2"></label>
                                                    </div>
                                                    #15458545
                                                </div>
                                            </a>
                                        </td>
                                        <td>Calculate Stock</td>
                                        <td>27/11/2024, 09:17 AM</td>
                                        <td class="text-primary"> Manual</td>
                                        <td>
                                            <button class="btn btn-xs btn-light-warning btn-icon-text">
                                                In Progress
                                            </button>
                                        </td>
                                        <td>Alpeshbhai</td>
                                        <td>
                                            <span class="w-100 d-block">Nilesh Patel</span>
                                            <span class="w-100 d-block">3 Days Ago</span>
                                        </td>
                                        <td>Hdhfdjhsd hsjksd sjkdhkfjsd sdfsfh</td>
                                        <td class="tbl-action">
                                            <div class="tbl-action-group">
                                                <button disabled="" class="btn btn-xs  btn-icon btn-outline-info"
                                                    ngbTooltip="Edit" placement="bottom" container="body"
                                                    triggers="hover">
                                                    <i class="th th-outline-edit"></i>
                                                </button>
                                                <button class="btn btn-xs btn-light-success btn-icon"
                                                    data-bs-toggle="modal" data-bs-target="#successModal"
                                                    ngbTooltip="Tooltip" placement="bottom" container="body"
                                                    triggers="hover">
                                                    <i class="th th-outline-tick-circle"></i>
                                                </button>
                                                <button class="btn btn-xs btn-light-danger btn-icon"
                                                    data-bs-toggle="modal" data-bs-target="#delete1Modal"
                                                    ngbTooltip="Delete" placement="left" container="body"
                                                    triggers="hover">
                                                    <i class="th th-outline-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="tbl-user tbl-bold">
                                            <a href="" class="text-black"
                                                [routerLink]="'/users/audit-tickets/audit-tickets-details'">

                                                <div class="tbl-user-checkbox-srno">
                                                    <div class="checkbox checkbox-primary checkbox-small">
                                                        <input type="checkbox" id="tbl-checkbox2"
                                                            class="material-inputs filled-in" />
                                                        <label for="tbl-checkbox2"></label>
                                                    </div>
                                                    #15458545
                                                </div>
                                            </a>
                                        </td>
                                        <td>Calculate Stock</td>
                                        <td>27/11/2024, 09:17 AM</td>
                                        <td class="text-warning"> Breach</td>
                                        <td>
                                            <button class="btn btn-xs btn-light-success btn-icon-text"
                                                data-bs-toggle="modal" data-bs-target="#successModal">
                                                Completed
                                            </button>
                                        </td>
                                        <td>Alpeshbhai</td>
                                        <td>
                                            <span class="w-100 d-block">Nilesh Patel</span>
                                            <span class="w-100 d-block">3 Days Ago</span>
                                        </td>
                                        <td>Hdhfdjhsd hsjksd sjkdhkfjsd sdfsfh</td>
                                        <td class="tbl-action">
                                            <div class="tbl-action-group">
                                                <button class="btn btn-xs btn-icon btn-outline-white">
                                                    <i class="th th-outline-edit" ngbTooltip="Edit" placement="bottom"
                                                        container="body" triggers="hover"></i>
                                                </button>
                                                <button class="btn btn-xs btn-light-success btn-icon"
                                                    data-bs-toggle="modal" data-bs-target="#successModal"
                                                    ngbTooltip="Tooltip" placement="bottom" container="body"
                                                    triggers="hover">
                                                    <i class="th th-outline-tick-circle"></i>
                                                </button>
                                                <button class="btn btn-xs btn-light-danger btn-icon"
                                                    data-bs-toggle="modal" data-bs-target="#delete1Modal"
                                                    ngbTooltip="Delete" placement="left" container="body"
                                                    triggers="hover">
                                                    <i class="th th-outline-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="tbl-user tbl-bold">
                                            <a href="" class="text-black"
                                                [routerLink]="'/users/audit-tickets/audit-tickets-details'">
                                                <div class="tbl-user-checkbox-srno">
                                                    <div class="checkbox checkbox-primary checkbox-small">
                                                        <input type="checkbox" id="tbl-checkbox2"
                                                            class="material-inputs filled-in" />
                                                        <label for="tbl-checkbox2"></label>
                                                    </div>
                                                    #15458545
                                                </div>
                                            </a>
                                        </td>
                                        <td>Calculate Stock</td>
                                        <td>27/11/2024, 09:17 AM</td>
                                        <td class="text-success"> PO</td>
                                        <td>
                                            <button class="btn btn-xs btn-light-primary btn-icon-text">
                                                New
                                            </button>
                                        </td>
                                        <td>Alpeshbhai</td>
                                        <td>
                                            <span class="w-100 d-block text-danger">Not Assigned</span>
                                        </td>
                                        <td>Hdhfdjhsd hsjksd sjkdhkfjsd sdfsfh</td>
                                        <td class="tbl-action">
                                            <div class="tbl-action-group">
                                                <button class="btn btn-xs btn-icon btn-outline-white" ngbTooltip="Edit"
                                                    placement="bottom" container="body" triggers="hover">
                                                    <i class="th th-outline-edit"></i>
                                                </button>
                                                <button class="btn btn-xs btn-light-warning btn-icon"
                                                    ngbTooltip="Tooltip" placement="bottom" container="body"
                                                    triggers="hover">
                                                    <i class="th th-outline-user-add"></i>
                                                </button>
                                                <button class="btn btn-xs btn-light-success btn-icon"
                                                    data-bs-toggle="modal" data-bs-target="#successModal"
                                                    ngbTooltip="Tooltip" placement="bottom" container="body"
                                                    triggers="hover">
                                                    <i class="th th-outline-tick-circle"></i>
                                                </button>
                                                <button class="btn btn-xs btn-light-danger btn-icon"
                                                    data-bs-toggle="modal" data-bs-target="#delete1Modal"
                                                    ngbTooltip="Delete" placement="left" container="body"
                                                    triggers="hover">
                                                    <i class="th th-outline-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="tbl-user tbl-bold">
                                            <a href="" class="text-black"
                                                [routerLink]="'/users/audit-tickets/audit-tickets-details'">
                                                <div class="tbl-user-checkbox-srno">
                                                    <div class="checkbox checkbox-primary checkbox-small">
                                                        <input type="checkbox" id="tbl-checkbox2"
                                                            class="material-inputs filled-in" />
                                                        <label for="tbl-checkbox2"></label>
                                                    </div>
                                                    #15458545
                                                </div>
                                            </a>
                                        </td>
                                        <td>Calculate Stock</td>
                                        <td>27/11/2024, 09:17 AM</td>
                                        <td class="text-danger"> QC Check</td>
                                        <td>
                                            <button class="btn btn-xs btn-light-primary btn-icon-text">
                                                New
                                            </button>
                                        </td>
                                        <td>Alpeshbhai</td>
                                        <td>
                                            <span class="w-100 d-block text-danger">Not Assigned</span>
                                        </td>
                                        <td>Hdhfdjhsd hsjksd sjkdhkfjsd sdfsfh</td>
                                        <td class="tbl-action">
                                            <div class="tbl-action-group">
                                                <button class="btn btn-xs btn-icon btn-outline-white" ngbTooltip="Edit"
                                                    placement="bottom" container="body" triggers="hover">
                                                    <i class="th th-outline-edit"></i>
                                                </button>
                                                <button class="btn btn-xs btn-light-success btn-icon"
                                                    data-bs-toggle="modal" data-bs-target="#successModal"
                                                    ngbTooltip="Tooltip" placement="bottom" container="body"
                                                    triggers="hover">
                                                    <i class="th th-outline-tick-circle"></i>
                                                </button>
                                                <button class="btn btn-xs btn-light-danger btn-icon"
                                                    data-bs-toggle="modal" data-bs-target="#delete1Modal"
                                                    ngbTooltip="Delete" placement="left" container="body"
                                                    triggers="hover">
                                                    <i class="th th-outline-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="tbl-user tbl-bold">
                                            <a href="" class="text-black"
                                                [routerLink]="'/users/audit-tickets/audit-tickets-details'">
                                                <div class="tbl-user-checkbox-srno">
                                                    <div class="checkbox checkbox-primary checkbox-small">
                                                        <input type="checkbox" id="tbl-checkbox2"
                                                            class="material-inputs filled-in" />
                                                        <label for="tbl-checkbox2"></label>
                                                    </div>
                                                    #15458545
                                                </div>
                                            </a>
                                        </td>
                                        <td>Calculate Stock</td>
                                        <td>27/11/2024, 09:17 AM</td>
                                        <td class="text-black"> QC Check</td>
                                        <td>
                                            <button class="btn btn-xs btn-light-primary btn-icon-text">
                                                New
                                            </button>
                                        </td>
                                        <td>Alpeshbhai</td>
                                        <td>
                                            <span class="w-100 d-block text-danger">Not Assigned</span>
                                        </td>
                                        <td>Hdhfdjhsd hsjksd sjkdhkfjsd sdfsfh</td>
                                        <td class="tbl-action">
                                            <div class="tbl-action-group">
                                                <button class="btn btn-xs btn-icon btn-outline-white" ngbTooltip="Edit"
                                                    placement="bottom" container="body" triggers="hover">
                                                    <i class="th th-outline-edit"></i>
                                                </button>
                                                <button class="btn btn-xs btn-light-success btn-icon"
                                                    data-bs-toggle="modal" data-bs-target="#successModal"
                                                    ngbTooltip="Tooltip" placement="bottom" container="body"
                                                    triggers="hover">
                                                    <i class="th th-outline-tick-circle"></i>
                                                </button>
                                                <button class="btn btn-xs btn-light-danger btn-icon"
                                                    data-bs-toggle="modal" data-bs-target="#delete1Modal"
                                                    ngbTooltip="Delete" placement="left" container="body"
                                                    triggers="hover">
                                                    <i class="th th-outline-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="paginationbox pagination-fixed">
    <app-pagination></app-pagination>
</div>

<!-- ----------------------------------------------------------------------- -->
<!--                     Re-Assign Audit ticket Start                      -->
<!-- ----------------------------------------------------------------------- -->
<div class="assign-modal modal modal-theme fade" id="assignModal" tabindex="-1" aria-labelledby="assignModalLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">

        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">Re-Assign Audit ticket</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
                        class='th th-close'></i></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-12">
                        <div class="form-group theme-ngselect ">
                            <label class="form-label">User</label>
                            <ng-select class="" placeholder="Select Demo" [multiple]="false" [clearable]="false"
                                [items]="demo" bindLabel="name" bindValue="id" [(ngModel)]="selectedDemo1"
                                appendTo="body">
                            </ng-select>
                        </div>
                    </div>
                </div>

            </div>
            <div class="modal-footer">
                <div class="modal-footer-group full-width-btn">
                    <button type="button" class="btn btn-primary btn-icon-text"> <i
                            class="th th-outline-tick-circle"></i>
                        Save</button>
                    <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                      Re-Assign Audit ticket End                       -->
<!-- ----------------------------------------------------------------------- -->

<!-- ----------------------------------------------------------------------- -->
<!--                           Delete Modal Start                            -->
<!-- ----------------------------------------------------------------------- -->
<div class="modal modal-theme modal-confirmation modal-reject fade" id="delete1Modal" tabindex="-1"
    aria-labelledby="delete1ModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
                        class='th th-close'></i></button>
            </div>
            <div class="modal-body">
                <div class="modal-confirmation-container">
                    <div class="modal-confirmation-icon">
                        <i class="th th-bold-trash"></i>
                    </div>
                    <div class="modal-confirmation-content">
                        <h5>Are You Sure?</h5>
                        <p>You want to Delete <b>{{"SelectedRecords"}}</b> Audit Ticket.</p>
                    </div>
                </div>
                <div class="modal-button-group modal-full-width-btn">
                    <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary btn-icon-text"> <i
                            class="th th-outline-tick-circle"></i>
                        Delete</button>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                           Delete Modal End                            -->
<!-- ----------------------------------------------------------------------- -->
<!-- ----------------------------------------------------------------------- -->
<!--                           success Modal Start                            -->
<!-- ----------------------------------------------------------------------- -->
<div class="modal modal-theme modal-confirmation modal-approve fade" id="successModal" tabindex="-1"
    aria-labelledby="successModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
                        class='th th-close'></i></button>
            </div>
            <div class="modal-body">

                <div class="modal-confirmation-container">
                    <div class="modal-confirmation-icon">
                        <i class="th th-outline-tick-circle"></i>
                    </div>
                    <div class="modal-confirmation-content">
                        <h5>Are You Sure?</h5>
                        <p>Detail description of the
                            success modal</p>
                    </div>
                </div>



                <div class="modal-button-group modal-full-width-btn">
                    <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary btn-icon-text"> <i
                            class="th th-outline-tick-circle"></i> success</button>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                           success Modal End                            -->
<!-- ----------------------------------------------------------------------- -->