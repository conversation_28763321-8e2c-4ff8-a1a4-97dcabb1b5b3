import { serializeAs } from "cerialize";
import { Subject } from "rxjs";

export class ItemGroupPagination {

    @serializeAs('sortOrder')
    private _sortOrder: string;

    @serializeAs('sortColumn')
    private _sortColumn: string;

    @serializeAs('searchText')
    private _searchText: string;

    @serializeAs('pageNo')
    private _pageNo: number;

    @serializeAs('pageSize')
    private _pageSize: string;

    @serializeAs('categoryId')
    private _categoryId: number;

    @serializeAs('isActive')
    private _isActive: boolean;

    private _flagForSelectAll: boolean;

    private _totalData: number;

    private _searchSubject: Subject<string>;

    private _pagination: any;

    private _dropdown: any[];

    constructor() {
        this.pageNo = 1;
        this.pageSize = "100"
        this.isActive = true;
        this.flagForSelectAll = false;
        this.searchSubject = new Subject<string>();
        this.pagination = null;
        this.dropdown = [];
    }


    /**
     * Getter categoryId
     * @return {number}
     */
	public get categoryId(): number {
		return this._categoryId;
	}

    /**
     * Setter categoryId
     * @param {number} value
     */
	public set categoryId(value: number) {
		this._categoryId = value;
	}


    /**
     * Getter dropdown
     * @return {any[]}
     */
	public get dropdown(): any[] {
		return this._dropdown;
	}

    /**
     * Setter dropdown
     * @param {any[]} value
     */
	public set dropdown(value: any[]) {
		this._dropdown = value;
	}


    /**
     * Getter sortOrder
     * @return {string}
     */
	public get sortOrder(): string {
		return this._sortOrder;
	}

    /**
     * Getter sortColumn
     * @return {string}
     */
	public get sortColumn(): string {
		return this._sortColumn;
	}

    /**
     * Getter searchText
     * @return {string}
     */
	public get searchText(): string {
		return this._searchText;
	}

    /**
     * Getter pageNo
     * @return {number}
     */
	public get pageNo(): number {
		return this._pageNo;
	}

    /**
     * Getter pageSize
     * @return {string}
     */
	public get pageSize(): string {
		return this._pageSize;
	}

    /**
     * Getter isActive
     * @return {boolean}
     */
	public get isActive(): boolean {
		return this._isActive;
	}

    /**
     * Getter flagForSelectAll
     * @return {boolean}
     */
	public get flagForSelectAll(): boolean {
		return this._flagForSelectAll;
	}

    /**
     * Getter totalData
     * @return {number}
     */
	public get totalData(): number {
		return this._totalData;
	}

    /**
     * Getter searchSubject
     * @return {Subject<string>}
     */
	public get searchSubject(): Subject<string> {
		return this._searchSubject;
	}

    /**
     * Getter pagination
     * @return {any}
     */
	public get pagination(): any {
		return this._pagination;
	}

    /**
     * Setter sortOrder
     * @param {string} value
     */
	public set sortOrder(value: string) {
		this._sortOrder = value;
	}

    /**
     * Setter sortColumn
     * @param {string} value
     */
	public set sortColumn(value: string) {
		this._sortColumn = value;
	}

    /**
     * Setter searchText
     * @param {string} value
     */
	public set searchText(value: string) {
		this._searchText = value;
	}

    /**
     * Setter pageNo
     * @param {number} value
     */
	public set pageNo(value: number) {
		this._pageNo = value;
	}

    /**
     * Setter pageSize
     * @param {string} value
     */
	public set pageSize(value: string) {
		this._pageSize = value;
	}

    /**
     * Setter isActive
     * @param {boolean} value
     */
	public set isActive(value: boolean) {
		this._isActive = value;
	}

    /**
     * Setter flagForSelectAll
     * @param {boolean} value
     */
	public set flagForSelectAll(value: boolean) {
		this._flagForSelectAll = value;
	}

    /**
     * Setter totalData
     * @param {number} value
     */
	public set totalData(value: number) {
		this._totalData = value;
	}

    /**
     * Setter searchSubject
     * @param {Subject<string>} value
     */
	public set searchSubject(value: Subject<string>) {
		this._searchSubject = value;
	}

    /**
     * Setter pagination
     * @param {any} value
     */
	public set pagination(value: any) {
		this._pagination = value;
	}

}