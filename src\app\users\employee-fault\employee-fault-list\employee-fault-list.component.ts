import { Component, OnInit } from '@angular/core';

@Component({
  selector: 'app-employee-fault-list',
  templateUrl: './employee-fault-list.component.html',
  styleUrls: ['./employee-fault-list.component.css']
})
export class EmployeeFaultListComponent implements OnInit {

  isExpanded: boolean = true;
  toggleExpand(): void {
    this.isExpanded = !this.isExpanded;
  }

  Option = [
    { id: 1, name: 'Employee' },
    { id: 2, name: 'Employee 2' },
    { id: 3, name: 'Employee 3' },
  ];

  ItemGroup = [
    { id: 1, name: 'Critical Level' },
    { id: 2, name: 'Critical Level 2' },
    { id: 3, name: 'Critical Level 3' },
  ];

  Active = [
    { id: 1, name: 'Record By' },
    { id: 2, name: 'Record By 2' },
    { id: 3, name: 'Record By 3' },
  ];
  Mistake = [
    { id: 1, name: 'By Mistake Count' },
    { id: 2, name: 'By Mistake Count 2' },
    { id: 3, name: 'By Mistake Count 3' },
  ];

  Export = [
    { id: 1, name: 'Export' },
    { id: 2, name: 'Export 2' },
    { id: 3, name: 'Export 3' },
  ];
  pagination = [
    { id: 1, name: '1' },
    { id: 2, name: '2' },
    { id: 3, name: '3' },
    { id: 3, name: '4' },
    { id: 3, name: '5' },
  ];

  selectedOption: number = 1;
  selectedItemGroup: number = 1;
  selectedActive: number = 1;
  selectedMistake: number = 1;
  selectedExport: number = 1;
  selectedPagination: number = 1;


  constructor() { }

  ngOnInit() {
  }

}
