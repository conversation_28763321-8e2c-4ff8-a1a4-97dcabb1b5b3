import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';

@Component({
  selector: 'app-pagination',
  templateUrl: './pagination.component.html',
  styleUrls: ['./pagination.component.css']
})
export class PaginationComponent implements OnInit {

  @Output() pageNumber = new EventEmitter<string>();
  @Output() pagesizeData = new EventEmitter<string>();
  @Input() page: number;
  @Input() totalData: number;
  @Input() pageSize: string;
  @Input() smallTable: boolean = false;
  totalPage: any;
  ChangeArray: any[] = [];
  paginatedArray: any[] = [];

  pageSizeList: any[] = [
    { label : 5, value : '5'},
    { label : 10, value : '10'},
    { label : 20, value : '20'},
    { label : 100, value : '100'},
    { label : 250, value : '250'},
    { label : 500, value : '500'},
    { label : 1000, value : '1000'},
  ];

  constructor() { }

  ngOnInit() {
  }

  ngOnChanges() {
    this.pageCount()
  }

  pageCount() {
    this.totalPage = Math.round(Math.ceil(+this.totalData / +this.pageSize));

    if (Math.round(this.totalPage) <= 1) {
      this.ChangeArray = [];
      this.ChangeArray.push('1')
      this.paginatedArray = [];
      this.paginatedArray.push('1')
    } else {
      this.ChangeArray = [];
      this.paginatedArray = [];
      for (let i = 1; i <= this.totalPage; i++) {
        this.ChangeArray.push(i)
        this.paginatedArray.push(i)
      }
      this.changeArray(this.paginatedArray, this.page)
    }
  }
  changeArray(array, page) {
    let totalcount = array.length;
    let arr1 = []
    if (array.length >= 4) {
      arr1.push(1);
      if (page > 3) { arr1.push("...") }
      if (page == totalcount) { arr1.push(page - 2); }
      if (page > 2) { arr1.push(page - 1); }
      if (page != 1 && page != totalcount) { arr1.push(page); }
      if (page < totalcount - 1) { arr1.push(page + 1); }
      if (page == 1) { arr1.push(page + 2) }
      if (page < totalcount - 2) { arr1.push("...") }
      arr1.push(totalcount);
      this.ChangeArray = arr1;
    }
  }

  onPrevious() {
    const page = this.page - 1;
    this.onPaginationClick(page);
  }
  onNext() {
    const page = this.page + 1;
    this.onPaginationClick(page);
  }
  onPaginationClick(value) {
    if (value !== '...') {
      if(value == this.page){
        return;
      }
      this.changeArray(this.paginatedArray, value);
      this.pageNumber.emit(value);
    }
  }
  onSelectPageLength() {
    this.pageSize = this.pageSize;
    this.pageCount()
    this.pagesizeData.emit(this.pageSize)
  }

  onKeyDown(event: KeyboardEvent) {
    if (event.key  === 'ArrowDown' || event.key === 'ArrowUp') {
      event.preventDefault();
    }
  }
}
