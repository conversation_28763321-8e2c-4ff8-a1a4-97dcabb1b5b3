import { deserializeAs, serializeAs } from 'cerialize';
import { User } from './User';

export class Warehouse {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('stateId')
    @deserializeAs('stateId')
    private _stateId: number;

    @serializeAs('countryId')
    @deserializeAs('countryId')
    private _countryId: number;

    @serializeAs('cityId')
    @deserializeAs('cityId')
    private _cityId: number;

    @serializeAs('userId')
    @deserializeAs('userId')
    private _userId: number;

    @serializeAs('warehouseName')
    @deserializeAs('warehouseName')
    private _warehouseName: string;

    @serializeAs('warehouseCode')
    @deserializeAs('warehouseCode')
    private _warehouseCode: string;

    @serializeAs('warehouseMobileNo')
    @deserializeAs('warehouseMobileNo')
    private _warehouseMobileNo: string;

    @serializeAs('warehouseEmail')
    @deserializeAs('warehouseEmail')
    private _warehouseEmail: string;

    @serializeAs('warehouseAddress')
    @deserializeAs('warehouseAddress')
    private _warehouseAddress: string;

    @serializeAs('locationLink')
    @deserializeAs('locationLink')
    private _locationLink: string;

    @serializeAs('isMainWarehouse')
    @deserializeAs('isMainWarehouse')
    private _isMainWarehouse: boolean;

    @serializeAs('idOfBranch')
    @deserializeAs('idOfBranch')
    private _idOfBranch: number;

    @serializeAs('isSalesPoint')
    @deserializeAs('isSalesPoint')
    private _isSalesPoint: boolean;
    
    @deserializeAs('isSelected')
    private _isSelected: boolean;

    @serializeAs('isActive')
    @deserializeAs('isActive')
    private _isActive: boolean;

    // @serializeAs('stateName')
    @deserializeAs('stateName')
    private _stateName: string;

    // @serializeAs('cityName')
    @deserializeAs('cityName')
    private _cityName: string;

    // @serializeAs('userName')
    @deserializeAs('userName')
    private _userName: string;

    @serializeAs('zipCode')
    @deserializeAs('zipCode')
    private _zipCode: string;

    // @serializeAs('stateMaster')
    @deserializeAs('stateMaster')
    private _stateMaster: any;

    // @serializeAs('countryMaster')
    @deserializeAs('countryMaster')
    private _countryMaster: any;

    // @serializeAs('cityMaster')
    @deserializeAs('cityMaster')
    private _cityMaster: any;

    // @serializeAs('users')
    @deserializeAs('users')
    private _users: User;

    private _numberOfAisles: number;
    private _numberOfRacks: number;

    constructor() {
        this.isActive = false;
        this.isSelected = false;
        this.isSalesPoint = false;
        this.isMainWarehouse = false;
    }


    /**
     * Getter idOfBranch
     * @return {number}
     */
	public get idOfBranch(): number {
		return this._idOfBranch;
	}

    /**
     * Setter idOfBranch
     * @param {number} value
     */
	public set idOfBranch(value: number) {
		this._idOfBranch = value;
	}


    /**
     * Getter numberOfAisles
     * @return {number}
     */
	public get numberOfAisles(): number {
		return this._numberOfAisles;
	}

    /**
     * Getter numberOfRacks
     * @return {number}
     */
	public get numberOfRacks(): number {
		return this._numberOfRacks;
	}

    /**
     * Setter numberOfAisles
     * @param {number} value
     */
	public set numberOfAisles(value: number) {
		this._numberOfAisles = value;
	}

    /**
     * Setter numberOfRacks
     * @param {number} value
     */
	public set numberOfRacks(value: number) {
		this._numberOfRacks = value;
	}


    /**
     * Getter users
     * @return {User}
     */
	public get users(): User {
		return this._users;
	}

    /**
     * Setter users
     * @param {User} value
     */
	public set users(value: User) {
		this._users = value;
	}


    /**
     * Getter stateMaster
     * @return {any}
     */
	public get stateMaster(): any {
		return this._stateMaster;
	}

    /**
     * Getter countryMaster
     * @return {any}
     */
	public get countryMaster(): any {
		return this._countryMaster;
	}

    /**
     * Getter cityMaster
     * @return {any}
     */
	public get cityMaster(): any {
		return this._cityMaster;
	}

    /**
     * Setter stateMaster
     * @param {any} value
     */
	public set stateMaster(value: any) {
		this._stateMaster = value;
	}

    /**
     * Setter countryMaster
     * @param {any} value
     */
	public set countryMaster(value: any) {
		this._countryMaster = value;
	}

    /**
     * Setter cityMaster
     * @param {any} value
     */
	public set cityMaster(value: any) {
		this._cityMaster = value;
	}


    /**
     * Getter zipCode
     * @return {string}
     */
	public get zipCode(): string {
		return this._zipCode;
	}

    /**
     * Setter zipCode
     * @param {string} value
     */
	public set zipCode(value: string) {
		this._zipCode = value;
	}


    /**
     * Getter stateId
     * @return {number}
     */
	public get stateId(): number {
		return this._stateId;
	}

    /**
     * Getter countryId
     * @return {number}
     */
	public get countryId(): number {
		return this._countryId;
	}

    /**
     * Getter cityId
     * @return {number}
     */
	public get cityId(): number {
		return this._cityId;
	}

    /**
     * Getter userId
     * @return {number}
     */
	public get userId(): number {
		return this._userId;
	}

    /**
     * Setter stateId
     * @param {number} value
     */
	public set stateId(value: number) {
		this._stateId = value;
	}

    /**
     * Setter countryId
     * @param {number} value
     */
	public set countryId(value: number) {
		this._countryId = value;
	}

    /**
     * Setter cityId
     * @param {number} value
     */
	public set cityId(value: number) {
		this._cityId = value;
	}

    /**
     * Setter userId
     * @param {number} value
     */
	public set userId(value: number) {
		this._userId = value;
	}


    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Getter warehouseName
     * @return {string}
     */
	public get warehouseName(): string {
		return this._warehouseName;
	}

    /**
     * Getter warehouseCode
     * @return {string}
     */
	public get warehouseCode(): string {
		return this._warehouseCode;
	}

    /**
     * Getter warehouseMobileNo
     * @return {string}
     */
	public get warehouseMobileNo(): string {
		return this._warehouseMobileNo;
	}

    /**
     * Getter warehouseEmail
     * @return {string}
     */
	public get warehouseEmail(): string {
		return this._warehouseEmail;
	}

    /**
     * Getter warehouseAddress
     * @return {string}
     */
	public get warehouseAddress(): string {
		return this._warehouseAddress;
	}

    /**
     * Getter locationLink
     * @return {string}
     */
	public get locationLink(): string {
		return this._locationLink;
	}

    /**
     * Getter isMainWarehouse
     * @return {boolean}
     */
	public get isMainWarehouse(): boolean {
		return this._isMainWarehouse;
	}

    /**
     * Getter isSalesPoint
     * @return {boolean}
     */
	public get isSalesPoint(): boolean {
		return this._isSalesPoint;
	}

    /**
     * Getter isSelected
     * @return {boolean}
     */
	public get isSelected(): boolean {
		return this._isSelected;
	}

    /**
     * Getter isActive
     * @return {boolean}
     */
	public get isActive(): boolean {
		return this._isActive;
	}

    /**
     * Getter stateName
     * @return {string}
     */
	public get stateName(): string {
		return this._stateName;
	}

    /**
     * Getter cityName
     * @return {string}
     */
	public get cityName(): string {
		return this._cityName;
	}

    /**
     * Getter userName
     * @return {string}
     */
	public get userName(): string {
		return this._userName;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Setter warehouseName
     * @param {string} value
     */
	public set warehouseName(value: string) {
		this._warehouseName = value;
	}

    /**
     * Setter warehouseCode
     * @param {string} value
     */
	public set warehouseCode(value: string) {
		this._warehouseCode = value;
	}

    /**
     * Setter warehouseMobileNo
     * @param {string} value
     */
	public set warehouseMobileNo(value: string) {
		this._warehouseMobileNo = value;
	}

    /**
     * Setter warehouseEmail
     * @param {string} value
     */
	public set warehouseEmail(value: string) {
		this._warehouseEmail = value;
	}

    /**
     * Setter warehouseAddress
     * @param {string} value
     */
	public set warehouseAddress(value: string) {
		this._warehouseAddress = value;
	}

    /**
     * Setter locationLink
     * @param {string} value
     */
	public set locationLink(value: string) {
		this._locationLink = value;
	}

    /**
     * Setter isMainWarehouse
     * @param {boolean} value
     */
	public set isMainWarehouse(value: boolean) {
		this._isMainWarehouse = value;
	}

    /**
     * Setter isSalesPoint
     * @param {boolean} value
     */
	public set isSalesPoint(value: boolean) {
		this._isSalesPoint = value;
	}

    /**
     * Setter isSelected
     * @param {boolean} value
     */
	public set isSelected(value: boolean) {
		this._isSelected = value;
	}

    /**
     * Setter isActive
     * @param {boolean} value
     */
	public set isActive(value: boolean) {
		this._isActive = value;
	}

    /**
     * Setter stateName
     * @param {string} value
     */
	public set stateName(value: string) {
		this._stateName = value;
	}

    /**
     * Setter cityName
     * @param {string} value
     */
	public set cityName(value: string) {
		this._cityName = value;
	}

    /**
     * Setter userName
     * @param {string} value
     */
	public set userName(value: string) {
		this._userName = value;
	}

}