import { Component, OnInit, inject } from '@angular/core';
import { Form<PERSON>rray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { Category } from '@modal/Category';
import { GroupCode, GroupCodeItem } from '@modal/GroupCode';
import { UtilsService } from '@service/utils.service';
import { Deserialize, Serialize } from 'cerialize';
declare var window: any;

@Component({
  selector: 'app-new-group-code',
  templateUrl: './new-group-code.component.html',
  styleUrls: ['./new-group-code.component.css']
})
export class NewGroupCodeComponent implements OnInit {

  utilsService = inject(UtilsService);

  groupCodeForm: FormGroup;
  groupCodeId: number;
  groupCodeObj = new GroupCode();

  categoryList: Category[] = [];
  flattenedParentCategory: any[];
  itemDropdown: any[] = [];
  associatedItems: GroupCodeItem[] = [];

  itemObj = new GroupCodeItem();
  selectedItemIndex: number;
  groupCodeAssociateItemRemove: any;
  newAssociatedItemModal: any;

  constructor(private fb: FormBuilder, private route: ActivatedRoute) { }

  ngOnInit() {
    this.groupCodeId = +(this.route.snapshot.paramMap.get('id'));
    this.initForm();
    this.getRequiredData();

    this.groupCodeAssociateItemRemove = new window.bootstrap.Modal(
      document.getElementById('groupCodeAssociateItemRemove')
    );

    this.newAssociatedItemModal = new window.bootstrap.Modal(
      document.getElementById('newAssociatedItemModal')
    );

  }

  initForm = () => {
    this.groupCodeForm = this.fb.group({
      groupName: [null, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.PATTERN_FOR_ALPHANUMERIC_WITH_SPECIAL_CHAR)])],
      isActive: [true],
      categoryId: [null, Validators.compose([Validators.required])],
      item: this.fb.array([])
    })
  }

  getRequiredData = () => {

    let API = null;
    if (this.groupCodeId) {
      API = this.utilsService.serverVariableService.GROUP_CODE_DATA_BY_ID + `?id=${this.groupCodeId}`;
    } else {
      API = this.utilsService.serverVariableService.GROUP_CODE_DATA_BY_ID;
    }

    this.utilsService.getMethodAPI(false, API, null, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.categoryList = (response.category);
        this.itemDropdown = response.items;

        this.groupCodeObj.isActive = true;
        this.addItem()

        if (this.groupCodeId) {
          this.groupCodeObj = Deserialize(response.groupCodeObj, GroupCode);
          this.groupCodeObj.groupName = response.groupCodeObj.groupCodeName;
          this.groupCodeObj.docs = (response.groupCodeDocs || []);
          this.groupCodeObj.linkedItemID = this.groupCodeObj.linkedItemID ? this.groupCodeObj.linkedItemID : [];

          if (response.linkedItems) {
            this.items.clear();
            this.associatedItems = Deserialize(response.linkedItems, GroupCodeItem);
            for (const item of this.associatedItems) {
              this.items.push(this.fb.group({
                itemId: [item.id],
              }));
              item.itemId = Serialize(item.id)
              this.onChangeItem(this.associatedItems.indexOf(item));
            }
          }
        }

        setTimeout(() => {
          this.getParentCategory();
        }, 150);
        // this.itemDropdown = this.itemDropdown ? this.utilsService.transformDropdownItems(this.itemDropdown) : [];
      }
    })
  }

  // documents

  trackByIndex(index: number, item: any): number {
    return index;
  }

  onSelectAttachments = (event): void => {

    let selectedFiles: FileList | null = null;
    if (event.type === 'drop') {
      event.preventDefault();
      selectedFiles = event.dataTransfer?.files;
    }

    if (event.type === 'dragover') {
      event.preventDefault();
    }

    if (event.type === 'change') {
      selectedFiles = event.target.files;
      event.preventDefault();
    }

    if (event.type === 'paste') {
      event.preventDefault();
      const items = (event.clipboardData.items);
      const dataTransfer = new DataTransfer();

      for (const item of items) {
        if (item.type.indexOf('image') === 0) {
          const blob = item.getAsFile();
          const fileName = blob?.name;
          if (blob !== null && fileName) {
            const fileName = `${Date.now()}-image.${item.type.split('/')[1]}`;
            const fileFromBlob = new File([blob], fileName, { type: item.type });
            dataTransfer.items.add(fileFromBlob);
          }
        }
      }
      selectedFiles = dataTransfer.files;
    }

    if (!selectedFiles || selectedFiles.length === 0) {
      return;
    }

    const max_file_size = 5242880;

    Array.from(selectedFiles).forEach((file: File) => {
      const ext = file.name.substr(file.name.lastIndexOf('.') + 1).toLowerCase();

      if (['jpeg', 'png', 'jpg', 'jfif', 'm4v', 'mp4', 'webp', 'avif'].includes(ext)) {
        if (file.size > max_file_size) {
          this.utilsService.toasterService.error(this.utilsService.validationService.FILE_MAX_SIZE);
        } else {
          let fileUrl = URL.createObjectURL(file);
          let fileData = null;
          if (this.utilsService.isImage(file.name)) {
            fileData = {
              id: null,
              file: file,
              isMarkDefault: (this.groupCodeObj.docs.every(doc => doc.isMarkDefault === false)) ? true : false,
              originalName: file.name,
              formattedName: fileUrl,
            };
          }
          if (this.utilsService.isMedia(file.name)) {
            fileData = {
              id: null,
              file: file,
              isMarkDefault: false,
              originalName: file.name,
              formattedName: fileUrl,
            };
          }

          if (this.groupCodeObj?.docs.length >= 10) {
            this.utilsService.toasterService.error('You have exceeded the file upload limit of 10', '', {
              positionClass: 'toast-top-right',
              closeButton: true,
              timeOut: 10000
            });
            selectedFiles = null;
            return;
          }

          this.groupCodeObj?.docs.push(fileData);
          selectedFiles = null;
        }
      } else {
        this.utilsService.toasterService.error(this.utilsService.validationService.FILE_INVALID_EXTENSION);
      }
    });
  }

  removeAttachment = (i: number, file) => {
    this.groupCodeObj.docs.splice(i, 1)
    if (file.id) {
      this.groupCodeObj.deletedDocsID.push(file.id)
    }
  }

  onPrimaryChange(currentIndex: number) {
    const obj = this.groupCodeObj.docs.map((request, index) => {
      request.isMarkDefault = (index === currentIndex);
    });
  }

  openLink(link, newUpload: any) {
    const filePreview = link ? `${this.utilsService.imgPath}${link}` : null

    if (!this.utilsService.isEmptyObjectOrNullUndefined(newUpload)) {
      window.open(newUpload, "_blank");
      return;
    }

    if (!this.utilsService.isEmptyObjectOrNullUndefined(filePreview)) {
      window.open(filePreview, "_blank");
      return;
    }
  }

  // Save

  onSave(fromModal: boolean = false) {

    const formData = new FormData();
    if (this.groupCodeForm.invalid) {
      this.groupCodeForm.markAllAsTouched();
      return;
    }

    const noMarkAsPrimary = this.groupCodeObj.docs.every(v => !v.isMarkDefault)

    if (this.groupCodeObj.docs.length === 0) {
      this.utilsService.toasterService.error('Minimum one image is required to save group code.', '', {
        positionClass: 'toast-top-right',
        closeButton: true,
        timeOut: 10000
      });
      return;
    }

    if (noMarkAsPrimary) {
      this.utilsService.toasterService.error('Minimum one image with Marked As default is required', '', {
        positionClass: 'toast-top-right',
        closeButton: true,
        timeOut: 10000
      });
      return;
    }
    
    if (!this.utilsService.isEverythingUnique(this.associatedItems, 'itemId')) {
      this.utilsService.toasterService.error('Items should be unique.', '', {
        positionClass: 'toast-top-right',
        closeButton: true,
        timeOut: 10000
      });
      return;
    }

    let fileRes = {}

    const obj = this.groupCodeObj.docs.filter(a => !a.id).map((v, i) => {
      if (v.file) {
        if (v.isMarkDefault) {
          fileRes[i] = v.file.name;
          formData.set('defaultDocMap', JSON.stringify(fileRes));
        }
        formData.append('itemGroupCodeImgs', v.file);
      }
    })

    this.groupCodeObj.docs.map((v, i) => {
      if (!v.file) {
        if (v.isMarkDefault) {
          formData.set('defaultDocID', JSON.stringify(v.id));
        }
      }
    })

    this.groupCodeObj.linkedItemID = this.associatedItems.map(a => a.itemId ? a.itemId : null).filter(a => a !== null)
    formData.set('itemGroupCodeInfo', JSON.stringify(Serialize(this.groupCodeObj)));

    if(this.associatedItems.some(a => !a.id) && !fromModal && this.groupCodeId){
      this.openNewAssociatedItemModal();
      return;
    }

    this.utilsService.postMethodAPI(true, this.utilsService.serverVariableService.GROUP_CODE_SAVE_EDIT_DELETE, formData, (response) => {
      if (!this.utilsService.isEmptyObjectOrNullUndefined(response)) {
        this.newAssociatedItemModal.hide();
        this.utilsService.redirectTo('/users/inventory/group-code/')
      }
    })

  }

  openNewAssociatedItemModal = () => {
    this.newAssociatedItemModal.show();
  }

  onModalConfirmation = () => {
    this.onSave(true);
  }

  get items() {
    return (this.groupCodeForm.get('item') as FormArray);
  }

  // Add Item
  addItem = () => {
    this.items.push(this.fb.group({
      itemId: [null, Validators.compose([Validators.required])],
    }));
    const obj = new GroupCodeItem()
    obj.itemDropdown = this.itemDropdown;
    // obj.itemDropdown = obj.itemDropdown.filter(item => item.categoryId === this.groupCodeForm.get('categoryId').value);
    this.associatedItems.push(obj)
  }

  openRemoveAIModal = (index: number, item: GroupCodeItem) => {
    this.itemObj = item
    if(!item.id) {
      this.itemObj.displayName = this.itemObj.itemDropdown.find(item => item.id === this.itemObj.itemId)?.displayName
    }
    this.selectedItemIndex = index;
    this.groupCodeAssociateItemRemove.show();
  }

  removeItem = () => {
    if (this.associatedItems.at(this.selectedItemIndex)?.id) {
      this.groupCodeObj.deleteLinkedItemID.push(this.associatedItems.at(this.selectedItemIndex).id)
    }
    this.items.removeAt(this.selectedItemIndex);
    this.associatedItems.splice(this.selectedItemIndex, 1)
    this.groupCodeAssociateItemRemove.hide();
  }

  onChangeItem = (index: number) => {
    const obj = this.associatedItems[index].itemDropdown.find(v => v.id === this.items.controls[index].get('itemId').value);
    if (obj) {
      this.associatedItems[index].skuId = obj.skuId;
      this.associatedItems[index].hsnCode = obj.hsnCode;
    }
  }

  onClearItem = (index: number) => {
    this.items.controls[index].reset();
  }

  // custom search
  customSearchFn(term: string, item: GroupCodeItem) {
    term = term.toLocaleLowerCase();

    const skuId = item.skuId?.toLocaleLowerCase() || '';
    const displayName = item.displayName?.toLocaleLowerCase() || '';
    const combined = (item.skuId ? item.skuId + ' - ' : '') + item.displayName;
    const combinedLower = combined.toLocaleLowerCase();

    return skuId.indexOf(term) > -1 ||
      displayName.indexOf(term) > -1 ||
      combinedLower.indexOf(term) > -1;
  }

  //// category dropdown
  modifyParentResponse(category: any[]) {
    let flatList = [];
    for (let parent of (category || [])) {
      parent.className = "ng-select-option-" + (parent.index + 1);
      flatList.push(parent);
      if (parent.categoryList && parent.categoryList.length > 0) {
        let data = (parent.categoryList || []).filter(p => p.index < 4);
        for (let d of data) {
          d.className = "ng-select-option-" + (d.index + 1);
          d.isChild = true;
        }
        flatList = flatList.concat(this.modifyParentResponse(data));
      }
    }
    return flatList;
  }

  getParentCategory() {
    let data = this.assignCategoryIndex(this.categoryList)
    data = (data || []).filter(list => list.index == 0);
    this.flattenedParentCategory = this.modifyParentResponse(data);
    this.flattenedParentCategory = this.flattenedParentCategory ? this.utilsService.transformDropdownItems(this.flattenedParentCategory) : [];
    this.flattenedParentCategory = this.utilsService.filterIsActive(this.flattenedParentCategory, this.groupCodeForm.get('categoryId').value ? this.groupCodeForm.get('categoryId').value : null);
  }

  assignCategoryIndex(categories: Category[], depth: number = 0): any[] {
    return categories.map((category) => {
      category.index = depth;
      if (category.categoryList && category.categoryList.length > 0) {
        category.categoryList = this.assignCategoryIndex(category.categoryList, depth + 1);
      }
      return category;
    });
  }

  onChangeCategory = () => {
    // for(const item of this.associatedItems) {
    //   item.itemId = null;
    //   item.itemDropdown = this.itemDropdown.filter(item => item.categoryId === this.groupCodeForm.get('categoryId').value);
    // }
  }

}
