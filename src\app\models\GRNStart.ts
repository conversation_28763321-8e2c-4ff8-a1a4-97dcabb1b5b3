import { deserializeAs, serializeAs } from 'cerialize';
import { POImportItem } from './POImportItem';

export class GRNStart {

    @serializeAs('grnId')
    @deserializeAs('grnId')
    private _grnId: number;

    @serializeAs('cartonQtyVehicle')
    @deserializeAs('cartonQtyVehicle')
    private _cartonQtyVehicle: number;

    @serializeAs('containersName')
    @deserializeAs('containersName')
    private _containersName: string[];

    @serializeAs('grnno')
    @deserializeAs('grnno')
    private _grnno: string;

    @serializeAs('tempoNo')
    @deserializeAs('tempoNo')
    private _tempoNo: string;

    @serializeAs('allBranches')
    @deserializeAs('allBranches')
    private _allBranches: any[];

    @serializeAs('grnItems')
    @deserializeAs('grnItems')
    private _grnItems: POImportItem[];

    constructor() {
        this.containersName = []
        this.allBranches = []
        this.grnItems = []
    }


    /**
     * Getter grnItems
     * @return {POImportItem[]}
     */
	public get grnItems(): POImportItem[] {
		return this._grnItems;
	}

    /**
     * Setter grnItems
     * @param {POImportItem[]} value
     */
	public set grnItems(value: POImportItem[]) {
		this._grnItems = value;
	}


    /**
     * Getter allBranches
     * @return {any[]}
     */
	public get allBranches(): any[] {
		return this._allBranches;
	}

    /**
     * Setter allBranches
     * @param {any[]} value
     */
	public set allBranches(value: any[]) {
		this._allBranches = value;
	}


    /**
     * Getter grnId
     * @return {number}
     */
	public get grnId(): number {
		return this._grnId;
	}

    /**
     * Getter cartonQtyVehicle
     * @return {number}
     */
	public get cartonQtyVehicle(): number {
		return this._cartonQtyVehicle;
	}

    /**
     * Getter containersName
     * @return {string[]}
     */
	public get containersName(): string[] {
		return this._containersName;
	}

    /**
     * Getter grnno
     * @return {string}
     */
	public get grnno(): string {
		return this._grnno;
	}

    /**
     * Getter tempoNo
     * @return {string}
     */
	public get tempoNo(): string {
		return this._tempoNo;
	}

    /**
     * Setter grnId
     * @param {number} value
     */
	public set grnId(value: number) {
		this._grnId = value;
	}

    /**
     * Setter cartonQtyVehicle
     * @param {number} value
     */
	public set cartonQtyVehicle(value: number) {
		this._cartonQtyVehicle = value;
	}

    /**
     * Setter containersName
     * @param {string[]} value
     */
	public set containersName(value: string[]) {
		this._containersName = value;
	}

    /**
     * Setter grnno
     * @param {string} value
     */
	public set grnno(value: string) {
		this._grnno = value;
	}

    /**
     * Setter tempoNo
     * @param {string} value
     */
	public set tempoNo(value: string) {
		this._tempoNo = value;
	}


}