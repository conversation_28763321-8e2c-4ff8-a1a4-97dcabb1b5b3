<div class="page-content" [pageAccess]="{page: utilsService.enumForPage.BRANCH, action: utilsService.enumForPage.VIEW_BRANCH, view: true}">
  <div class="page-title-wrapper">
    <div class="page-title-left">
      <h4>{{branchId ? 'Edit' : 'Add New'}} Branch</h4>
    </div>
    <div class="page-title-right">
      <button [routerLink]="'/users/branch-management/'" class="btn btn-sm btn-icon btn-outline-white">
        <i class="th th-close"></i>
      </button>
    </div>
  </div>

  <div class="content-area">
    <div class="card card-theme">
      <div class="card-body" [formGroup]="branchForm">
        <div class="row">

          <div class="col-md-4">
            <div class="form-group form-group-inline-control required">
              <div class="form-label">Branch Name</div>
              <div class="form-control-wrapper">
                <input id="f" [maxlength]="utilsService.validationService.MAX_30" [(ngModel)]="branchObj.branchName"
                  formControlName="branchName" type="text" class="form-control" placeholder="Enter Branch Name">
                <div class="message error-message"
                  *ngIf="branchForm.controls['branchName'].hasError('required') &&  branchForm.controls['branchName'].touched">
                  {{utilsService.validationService.BRANCH_NAME_REQ}}
                </div>
                <div class="message error-message"
                  *ngIf="!branchForm.controls['branchName'].hasError('required') && !branchForm.controls['branchName'].valid && branchForm.controls['branchName'].touched">
                  {{utilsService.validationService.BRANCH_NAME_INVALID}}
                </div>
              </div>
            </div>
            <div class="form-group form-group-inline-control required">
              <div class="form-label">Short Code</div>
              <div class="form-control-wrapper">
                <input [maxlength]="utilsService.validationService.MAX_20" [(ngModel)]="branchObj.shortCode"
                  formControlName="shortCode" type="text" class="form-control" placeholder="Enter Short Code">
                <div class="message error-message"
                  *ngIf="branchForm.controls['shortCode'].hasError('required') &&  branchForm.controls['shortCode'].touched">
                  {{utilsService.validationService.SHORT_CODE_REQ}}
                </div>
                <div class="message error-message"
                  *ngIf="!branchForm.controls['shortCode'].hasError('required') && !branchForm.controls['shortCode'].valid && branchForm.controls['shortCode'].touched">
                  {{utilsService.validationService.SHORT_CODE_INVALID}}
                </div>
              </div>
            </div>
            <div class="form-group form-group-inline-control theme-ngselect required">
              <div class="form-label">Representative</div>
              <div class="form-control-wrapper">
                <ng-select (clear)="onClearManager()" [(ngModel)]="branchObj.idOfUser" formControlName="userId" class=""
                  placeholder="Select Manager" [multiple]="false" [clearable]="false" [items]="dropdown?.users"
                  bindLabel="name" bindValue="id">
                </ng-select>
                <div class="message error-message"
                  *ngIf="branchForm.controls['userId'].hasError('required') &&  branchForm.controls['userId'].touched">
                  {{utilsService.validationService.REPRESENTATIVE_REQUIRED}}
                </div>
              </div>
            </div>
            <div class="form-group form-group-inline-control ">
              <div class="form-label">Email Address</div>
              <div class="form-control-wrapper">
                <input [maxlength]="utilsService.validationService.MAX_50" [(ngModel)]="branchObj.branchEmail" formControlName="branchEmail" type="text"
                  class="form-control" placeholder="Enter Email">
                <div class="message error-message"
                  *ngIf="!branchForm.controls['branchEmail'].hasError('required') && !branchForm.controls['branchEmail'].valid && branchForm.controls['branchEmail'].touched">
                  {{utilsService.validationService.EMAIL_INVALID}}
                </div>
              </div>
            </div>
            <div class="form-group form-group-inline-control ">
              <div class="form-label">Phone No</div>
              <div class="form-control-wrapper">
                <input [(ngModel)]="branchObj.branchPhone" formControlName="branchPhone" type="text"
                  class="form-control" placeholder="Enter Phone No">
                <div class="message error-message"
                  *ngIf="!branchForm.controls['branchPhone'].hasError('required') && !branchForm.controls['branchPhone'].valid && branchForm.controls['branchPhone'].touched">
                  {{utilsService.validationService.PHONE_NUMBER_INVALID}}
                </div>
              </div>
            </div>

            <div class="form-group form-group-inline-control theme-ngselect required">
              <div class="form-label">Type of Company</div>
              <div class="form-control-wrapper">
                <ng-select [(ngModel)]="branchObj.companyType" formControlName="companyType" class=""
                  placeholder="Select Company Type" [multiple]="false" [clearable]="false"
                  [items]="dropdown?.companyTypes" bindLabel="label" bindValue="value">
                </ng-select>
                <div class="message error-message"
                  *ngIf="branchForm.controls['companyType'].hasError('required') &&  branchForm.controls['companyType'].touched">
                  {{utilsService.validationService.TOC_REQ}}
                </div>
              </div>
            </div>
            <div class="form-group form-group-inline-control theme-ngselect">
              <div class="form-label">Currency</div>
              <div class="form-control-wrapper">
                <ng-select (clear)="onClearCurrency()" [(ngModel)]="branchObj.idOfCurrency" formControlName="idOfCurrency" class=""
                  placeholder="Select Currency" [multiple]="false" [clearable]="true" [items]="dropdown?.currencies"
                  bindLabel="currencyName" bindValue="id">
                </ng-select>
                <div class="message error-message"
                  *ngIf="branchForm.controls['idOfCurrency'].hasError('required') &&  branchForm.controls['idOfCurrency'].touched">
                  {{utilsService.validationService.CURRENCY_REQ}}
                </div>
              </div>
            </div>

            <!-- <div class="form-group form-group-inline-control required">
              <div class="form-label">Financial Yr</div>
              <div class="form-group-button-wrapper">
                <div class="form-control-wrapper">
                  <div class="form-group-icon-end">
                    <i (click)="openDatepicker()" class="th th-outline-calendar ngx-daterangepicker-action"></i>
                    <input [(ngModel)]="branchObj.date" (keydown.space)="openDatepicker()" #pickerDirective [maxDate]="maxDate" [minDate]="minDate" readonly #date
                      id="input" formControlName="fy" type="text" ngxDaterangepickerMd startKey="start" [showDropdowns]="true"
                      endKey="end" [opens]="'down'" [autoApply]="true" [closeOnAutoApply]="true" class="form-control" [drops]="'down'"
                      placeholder="Financial Year">
                  </div>
                  <div class="message error-message" *ngIf="branchForm.controls['fy'].touched && !branchObj.date">
                    {{utilsService.validationService.FINANCIAL_YR_REQ}}
                  </div>
                </div>
              </div>
            </div> -->

            <div class="form-group form-group-inline-control theme-ngselect">
              <div class="form-label">Bank Groups</div>
              <div class="form-control-wrapper">
                <ng-select (clear)="onClearBG()" [(ngModel)]="branchObj.idsOfBankGroup" formControlName="bankGroup" class=""
                  placeholder="Select Bank Groups" [multiple]="true" [clearable]="true" [items]="dropdown?.bankGroups"
                  bindLabel="bankName" bindValue="id" [closeOnSelect]="false">
                </ng-select>
                <div class="message error-message"
                  *ngIf="branchForm.controls['bankGroup'].hasError('required') &&  branchForm.controls['bankGroup'].touched">
                  {{utilsService.validationService.BANK_GROUP_REQ}}
                </div>
              </div>
            </div>

            <!-- <div class="form-group form-group-inline-control" formArrayName="gstin">
              <div class="form-label">GSTIN No.</div>
              <div class="form-group-button-wrapper">
                <ng-container *ngFor="let arr of gstin.controls; index as i" [formGroupName]="i">
                  <div class="form-control-wrapper">
                    <div class="form-group-button">
                      <input [maxlength]="utilsService.validationService.MAX_20" formControlName="number" type="text" class="form-control"
                        placeholder="GSTIN No." [(ngModel)]="branchObj.gstNO[i]">
                      <button (click)="addGSTIN()" class="btn btn-outline-white btn-icon text-black"><i
                          class="th th-outline-add-circle"></i></button>
                      <button *ngIf="gstin.length > 1" (click)="removeGSTIN(i)" class="btn btn-outline-white btn-icon text-danger"><i
                          class="th th-outline-minus-cirlce"></i></button>
                    </div>
                    <div *ngIf="arr.get('number').hasError('required') &&  arr.get('number').touched" class="message error-message">
                      {{utilsService.validationService.GSTNO_REQ}}
                    </div>
                    <div *ngIf="!arr.get('number').hasError('required') && !arr.get('number').valid && arr.get('number').touched"
                      class="message error-message">
                      {{utilsService.validationService.GSTNO_INVALID}}
                    </div>
                  </div>
                </ng-container>
              </div>
            </div> -->

            <div class="form-group form-group-inline required">
              <div class="form-label">Status</div>
              <div class="switch-box">
                <label htmlfor="switch" class="switch">
                  <input type="checkbox" id="switch" formControlName="isActive" [(ngModel)]="branchObj.isActive ">
                  <div class="slider round"></div>
                </label>
              </div>
            </div>
            <div class="form-group form-group-inline ">
              <div class="form-label">Is Main Branch ?</div>
              <div class="switch-box">
                <label htmlfor="switch2" class="switch">
                  <input (change)="onChangeMainBranch()" type="checkbox" id="switch2" formControlName="isMainBranch"
                    [(ngModel)]="branchObj.isMainBranch">
                  <div class="slider round"></div>
                </label>
              </div>
            </div>
            <div class="form-group form-group-inline ">
              <div class="form-label">Allow Local Purchase</div>
              <div class="switch-box">
                <label htmlfor="switch3" class="switch">
                  <input type="checkbox" id="switch3" formControlName="isAllowLocalPurchase"
                    [(ngModel)]="branchObj.isAllowLocalPurchase">
                  <div class="slider round"></div>
                </label>
              </div>
            </div>
            <div class="form-group form-group-inline ">
              <div class="form-label">Allow Import Purchase</div>
              <div class="switch-box">
                <label htmlfor="switch3" class="switch">
                  <input type="checkbox" id="switch3" formControlName="isAllowImportPurchase"
                    [(ngModel)]="branchObj.isAllowImportPurchase">
                  <div class="slider round"></div>
                </label>
              </div>
            </div>
            <div class="form-group form-group-inline ">
              <div class="form-label">Consider Average Price With GST During SO</div>
              <div class="switch-box">
                <label htmlfor="switch4" class="switch">
                  <input formControlName="isConsiderGSTAvgPrice" type="checkbox" id="switch4"
                    [(ngModel)]="branchObj.isConsiderGSTAvgPrice">
                  <div class="slider round"></div>
                </label>
              </div>
            </div>
          </div>
          <div class="col-md-4" (paste)="onSelectAttachments($event);doc.value"
            (drop)="onSelectAttachments($event);doc.value = ''" (dragover)="onSelectAttachments($event);doc.value = ''">
            <div class="form-group">
              <div class="form-label">Upload Documents<i class="th th-outline-info-circle ms-1"
                  [ngbTooltip]="utilsService.validationService.DOC_INFO" placement="bottom" container="body" triggers="hover"></i>
              </div>
              <div class='attachments-container'>
                <div class='attachments-content'>
                  <button class='btn btn-attachments'><i class="bi bi-upload"></i></button>
                  <p>Drag and Drop file here or <span class='text-primary'>Choose file</span></p>
                </div>
                <input accept=".xls,.xlsx,.xlss,.csv,image/*,.pdf" #doc type="file" ref={imageRef} [multiple]="true"
                  (change)="onSelectAttachments($event);doc.value = ''" />
              </div>
            </div>
            <div class="form-group">
              <div class='attachments-upload-grid-container attachments-upload-grid-container2'>
                <div class='attachments-upload-row'>
                  <div class='attachments-upload-col' *ngFor="let item of branchObj.branchDoc; index as i">
                    <div class='card-attachments-upload'>
                      <div class='attachments-image'
                        *ngIf="utilsService.isImage(item.fileName ? item.fileName : item.originalName)">
                        <img (click)="openLink(item.formattedName, item.originalname)" *ngIf="!item.file"
                          [src]="item.formattedName ? (utilsService.imgPath + item.formattedName) : null" alt="valamji" />
                        <img (click)="openLink(null, item.originalname)" loading="lazy" *ngIf="item.file"
                          [src]="item.originalname ? (item.originalname) : null" alt="valamji" />
                      </div>
                      <div (click)="openLink(item.formattedName, item.originalname)" class='attachments-image'
                        *ngIf="utilsService.isDocument(item.fileName ? item.fileName : item.originalName)">
                        <img src="assets/images/files/file-pdf.svg" alt="valamji" />
                      </div>
                      <div (click)="openLink(item.formattedName, item.originalname)" class='attachments-image'
                        *ngIf="utilsService.isExcel(item.fileName ? item.fileName : item.originalName)">
                        <img src="assets/images/files/file-excel.svg" alt="valamji" />
                      </div>
                      <div class="attachments-text" [ngbTooltip]="item.fileName ? item.fileName : item.originalName"
                        placement="bottom" container="body" triggers="hover">
                        <h6 class="file-name">{{item.fileName ? item.fileName : item.originalName}}</h6>
                      </div>
                      <button (click)="removeAttachment(i, item)" class="btn-close" variant="close"><i
                          class='th th-close'></i></button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <hr>
          <div class="col-md-4">
            <div class="form-group form-group-inline-control theme-ngselect">
              <div class="form-label">Country</div>
              <div class="form-control-wrapper">
                <ng-select (clear)="onClearCountry()" (change)="onChangeCountry()" formControlName="idOfCountry" class=""
                  placeholder="Select Country" [multiple]="false" [clearable]="true" [items]="dropdown?.country"
                  bindLabel="name" bindValue="id" [(ngModel)]="branchObj.idOfCountry">
                </ng-select>
              </div>
            </div>
            <div class="form-group form-group-inline-control theme-ngselect">
              <div class="form-label">State</div>
              <div class="form-control-wrapper">
                <ng-select (clear)="onClearState()" (change)="onChangeState()" formControlName="idOfState" class="" placeholder="Select State"
                  [multiple]="false" [clearable]="true" [items]="state" bindLabel="name" bindValue="id"
                  [(ngModel)]="branchObj.idOfState">
                </ng-select>
              </div>
            </div>
            <div class="form-group form-group-inline-control theme-ngselect">
              <div class="form-label">City</div>
              <div class="form-control-wrapper">
                <ng-select (clear)="onClearCity()" formControlName="idOfCity" class="" placeholder="Select City" [multiple]="false"
                  [clearable]="true" [items]="city" bindLabel="name" bindValue="id" [(ngModel)]="branchObj.idOfCity">
                </ng-select>
              </div>
            </div>
            <div class="form-group form-group-inline-control ">
              <div class="form-label">ZIP Code</div>
              <div class="form-control-wrapper">
                <input [maxlength]="utilsService.validationService.MAX_15" [(ngModel)]="branchObj.zipCode"
                  formControlName="zipCode" type="text" class="form-control" placeholder="Enter ZIP Code">
                <div class="message error-message"
                  *ngIf="!branchForm.controls['zipCode'].hasError('required') && !branchForm.controls['zipCode'].valid && branchForm.controls['zipCode'].touched">
                  {{utilsService.validationService.ZIP_CODE_INVALID}}
                </div>
              </div>
            </div>
            <div class="form-group form-group-inline-control ">
              <div class="form-label">Address</div>
              <input [maxlength]="utilsService.validationService.MAX_100" [(ngModel)]="branchObj.address"
                formControlName="address" type="text" class="form-control" placeholder="Enter Address">
            </div>
            <div class="form-group form-group-inline-control ">
              <div class="form-label">Landmark</div>
              <input [maxlength]="utilsService.validationService.MAX_50" [(ngModel)]="branchObj.landmark"
                formControlName="landmark" type="text" class="form-control" placeholder="Enter Landmark">
            </div>
            <div class="form-group form-group-inline-control ">
              <div class="form-label">Location Link</div>
              <input [maxlength]="utilsService.validationService.MAX_500" [(ngModel)]="branchObj.locationLink"
                formControlName="locationLink" type="text" class="form-control" placeholder="Enter Link">
            </div>

          </div>
        </div>
      </div>
    </div>
    <div class='bottombar-wrapper bottom-fixed'>
      <div class='bottombar-container'>
        <div class='bottombar-left'>
          <button (click)="onSaveNewBranch()" type="button" class="btn btn-primary btn-icon-text btn-sm"> <i
              class="th th-outline-tick-circle"></i>
              {{branchId ? 'Update' : 'Save'}}</button>
          <button [routerLink]="'/users/branch-management/'" type="button"
            class="btn btn-outline-white btn-icon-text btn-sm"><i class="th th-outline-close-circle"></i>Cancel</button>
        </div>
        <div class='bottombar-right'>
        </div>
      </div>
    </div>
  </div>
</div>