import { deserializeAs, serializeAs } from 'cerialize';

export class POImportTempo {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('vehicleNo')
    @deserializeAs('vehicleNo')
    private _vehicleNo: string;

    @serializeAs('cartonQtyVehicle')
    @deserializeAs('cartonQtyVehicle')
    private _cartonQtyVehicle: number;

    @serializeAs('containerIDs')
    @deserializeAs('containerIDs')
    private _containerIDs: number[];

    @serializeAs('date')
    @deserializeAs('date')
    private _date: any;

    @deserializeAs('tempDate')
    private _tempDate: any;

    @deserializeAs('status')
    private _status: any;

    @deserializeAs('releasedCarton')
    private _releasedCarton: string;

    @deserializeAs('availableCarton')
    private _availableCarton: string;

    constructor() {
        this.containerIDs = []
    }


    /**
     * Getter containerIDs
     * @return {number[]}
     */
	public get containerIDs(): number[] {
		return this._containerIDs;
	}

    /**
     * Setter containerIDs
     * @param {number[]} value
     */
	public set containerIDs(value: number[]) {
		this._containerIDs = value;
	}


    /**
     * Getter status
     * @return {any}
     */
	public get status(): any {
		return this._status;
	}

    /**
     * Setter status
     * @param {any} value
     */
	public set status(value: any) {
		this._status = value;
	}


    /**
     * Getter releasedCarton
     * @return {string}
     */
	public get releasedCarton(): string {
		return this._releasedCarton;
	}

    /**
     * Getter availableCarton
     * @return {string}
     */
	public get availableCarton(): string {
		return this._availableCarton;
	}

    /**
     * Setter releasedCarton
     * @param {string} value
     */
	public set releasedCarton(value: string) {
		this._releasedCarton = value;
	}

    /**
     * Setter availableCarton
     * @param {string} value
     */
	public set availableCarton(value: string) {
		this._availableCarton = value;
	}


    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Getter vehicleNo
     * @return {string}
     */
	public get vehicleNo(): string {
		return this._vehicleNo;
	}

    /**
     * Getter cartonQtyVehicle
     * @return {number}
     */
	public get cartonQtyVehicle(): number {
		return this._cartonQtyVehicle;
	}

    /**
     * Getter date
     * @return {any}
     */
	public get date(): any {
		return this._date;
	}

    /**
     * Getter tempDate
     * @return {any}
     */
	public get tempDate(): any {
		return this._tempDate;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Setter vehicleNo
     * @param {string} value
     */
	public set vehicleNo(value: string) {
		this._vehicleNo = value;
	}

    /**
     * Setter cartonQtyVehicle
     * @param {number} value
     */
	public set cartonQtyVehicle(value: number) {
		this._cartonQtyVehicle = value;
	}

    /**
     * Setter date
     * @param {any} value
     */
	public set date(value: any) {
		this._date = value;
	}

    /**
     * Setter tempDate
     * @param {any} value
     */
	public set tempDate(value: any) {
		this._tempDate = value;
	}

}