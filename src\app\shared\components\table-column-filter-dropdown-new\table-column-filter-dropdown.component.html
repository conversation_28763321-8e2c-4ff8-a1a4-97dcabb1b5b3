<div class="table-filter">
  <div placement="bottom-end" ngbDropdown #dropdown="ngbDropdown" autoClose="outside" (openChange)="onDropdownOpenChange($event)">
    <button id="tbl-filter-dropdown" class="btn btn-icon btn-outline-white btn-sm no-caret" ngbDropdownToggle
      ngbTooltip="Columns Filter" placement="left" container="body" triggers="hover">
      <i class="bi bi-sliders center-btn"></i>
    </button>
    <div ngbDropdownMenu class="table-filter-dialog overflow-auto" (click)="$event.stopPropagation()">
      <div class="table-filter-content">
        <div class="table-filter-header">
          <h5 class="filter-title">Hide & Show Columns</h5>
          <button type="button" class="btn-close" aria-label="Close" (click)="closeDropdown(false)">
            <i class='th th-close'></i>
          </button>
        </div>
        <div class="table-filter-body" *ngIf="isOpen" cdkDropListGroup cdkScrollable>
          <ul class="filter-items-list drag-control-wrapper2" cdkDropList [cdkDropListData]="allHeaderArr"
            (cdkDropListDropped)="dropCol($event)" [cdkDropListAutoScrollStep]="15">
            <li class="drag-control-item" *ngFor="let item of allHeaderArr; index as i"
              [ngClass]="{'selected': item.isSelected}" cdkDrag cdkDragLockAxis="y" cdkDragPreviewContainer="parent"
              (cdkDragStarted)="startDrag(i)">
              <div class="drag-control-icon" cdkDragHandle>
                <img src="assets/images/drag.svg" alt="valamji">
              </div>
              <div class="drag-control-button">
                <div class="checkbox checkbox-primary checkbox-small form-check-inline">
                  <input [(ngModel)]="item.isSelected" (change)="selectUnselect()" type="checkbox" id="checkboxA-{{i}}"
                    class="material-inputs filled-in" />
                  <label for="checkboxA-{{i}}"> </label>
                </div>
              </div>
              <div class="drag-control-label">
                <p>{{removeBrTags(item.displayName)}}</p>
              </div>
            </li>
          </ul>
        </div>
        <div class="table-filter-footer">
          <div class="table-filter-footer-group full-width-btn">
            <button (click)="onSaveCol()" type="button" class="btn btn-sm btn-primary btn-icon-text">
              <i class="th th-outline-tick-circle"></i> Save
            </button>
            <button type="button" class="btn btn-sm btn-outline-white" (click)="closeDropdown(false)">Cancel</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>