.form-group {
    margin-bottom: 16px;
    position: relative;

    .form-label {
        margin-bottom: 5px;
        color: $text_black_color;
        font-weight: 500;
        font-size: 14px;
    }

    &.required {
        .form-label {
            &::after {
                content: '*';
                color: $danger_color;
                margin-left: 3px;
            }
        }
    }

    .form-control,
    .form-select {
        box-shadow: none !important;
        outline: 0 !important;
        background-color: $white_color;
        border-color: $stock_light;
        border-radius: 6px;
        color: $text_black_color;


        &:hover,
        &:focus {
            border-color: $primary_color !important
        }

        &:disabled,
        .disabled {
            background-color: $bg_grey !important;
            border-color: $bg_grey !important;

            &:hover,
            &:focus {
                border-color: $bg_grey !important
            }
        }
    }

    textarea {
        resize: none;
        min-height: 100px;
    }

    .otp-group {
        display: flex;
        column-gap: 11px;
        flex-wrap: nowrap;
        align-items: center;
        justify-content: center;

        input {
            border: 0px solid $stock_light;
            border-bottom: 1px solid $stock_light;
            color: $text_black_color;
            text-align: center;
            border-radius: 0px;
            border-top-left-radius: 10px;
            border-top-right-radius: 10px;
            background-color: $bg_light;


            &.ng-dirty {
                background-color: $primary_light_color;
                border-color: $primary_color;
            }
        }

    }

    &.form-error {

        .form-control,
        .form-select {

            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12 22.75C6.07 22.75 1.25 17.93 1.25 12C1.25 6.07 6.07 1.25 12 1.25C17.93 1.25 22.75 6.07 22.75 12C22.75 17.93 17.93 22.75 12 22.75ZM12 2.75C6.9 2.75 2.75 6.9 2.75 12C2.75 17.1 6.9 21.25 12 21.25C17.1 21.25 21.25 17.1 21.25 12C21.25 6.9 17.1 2.75 12 2.75Z' fill='%23FF5D5D'/%3e%3cpath d='M9.16999 15.5801C8.97999 15.5801 8.78999 15.5101 8.63999 15.3601C8.34999 15.0701 8.34999 14.5901 8.63999 14.3001L14.3 8.64011C14.59 8.35011 15.07 8.35011 15.36 8.64011C15.65 8.93011 15.65 9.41011 15.36 9.70011L9.69998 15.3601C9.55998 15.5101 9.35999 15.5801 9.16999 15.5801Z' fill='%23FF5D5D'/%3e%3cpath d='M14.83 15.5801C14.64 15.5801 14.45 15.5101 14.3 15.3601L8.63999 9.70011C8.34999 9.41011 8.34999 8.93011 8.63999 8.64011C8.92999 8.35011 9.40998 8.35011 9.69998 8.64011L15.36 14.3001C15.65 14.5901 15.65 15.0701 15.36 15.3601C15.21 15.5101 15.02 15.5801 14.83 15.5801Z' fill='%23FF5D5D'/%3e%3c/svg%3e ");
            background-repeat: no-repeat;
            background-position: right calc(0.375em + 0.1875rem) center;
            background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
            border-color: $danger_color;
            color: $danger_color;
            padding-right: 40px;

            &:hover,
            &:focus {
                border-color: $danger_hover_color !important
            }
        }


        .form-group-icon-end {

            .form-select,
            .form-control {
                padding-right: 60px;
                background-position: right calc(1.8em + 0.1875rem) center;
            }
        }
    }



    &.form-success {

        .form-control,
        .form-select {
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12 22.75C6.07 22.75 1.25 17.93 1.25 12C1.25 6.07 6.07 1.25 12 1.25C17.93 1.25 22.75 6.07 22.75 12C22.75 17.93 17.93 22.75 12 22.75ZM12 2.75C6.9 2.75 2.75 6.9 2.75 12C2.75 17.1 6.9 21.25 12 21.25C17.1 21.25 21.25 17.1 21.25 12C21.25 6.9 17.1 2.75 12 2.75Z' fill='%230F9D58'/%3e%3cpath d='M10.5799 15.5801C10.3799 15.5801 10.1899 15.5001 10.0499 15.3601L7.21994 12.5301C6.92994 12.2401 6.92994 11.7601 7.21994 11.4701C7.50994 11.1801 7.98994 11.1801 8.27994 11.4701L10.5799 13.7701L15.7199 8.6301C16.0099 8.3401 16.4899 8.3401 16.7799 8.6301C17.0699 8.9201 17.0699 9.4001 16.7799 9.6901L11.1099 15.3601C10.9699 15.5001 10.7799 15.5801 10.5799 15.5801Z' fill='%230F9D58'/%3e%3c/svg%3e ");
            background-repeat: no-repeat;
            background-position: right calc(0.375em + 0.1875rem) center;
            background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
            border-color: $success_color;
            color: $success_color;
            padding-right: 40px;

            &:hover,
            &:focus {
                border-color: $success_hover_color !important
            }
        }
    }

    .message {
        font-size: 14px;
        font-weight: 400;
        line-height: 16.8px;
        text-align: left;
        padding: 0 5px;
        color: $text_color;
        margin-top: 5px;
        width: fit-content;
        border-radius: 6px;


        &.error-message {
            color: $danger_color;
            background-color: $danger_light_color;
        }

        &.success-message {
            color: $success_color;
            background-color: $success_light_color;
        }
    }

    .form-group-icon-start,
    .form-group-icon-end {
        position: relative;
        width: 100%;

        i {
            position: absolute;
            top: 0px;
            bottom: 0;
            display: flex;
            align-items: center;
            font-size: 16px;
            z-index: 8;
        }

        // span {
        //     font-size: 12px;
        //     line-height: normal;
        //     color: $text_black_color;
        //     position: absolute;
        //     top: 50%;
        //     transform: translateY(-50%);
        //     bottom: 0;
        //     display: inline-flex;
        //     width: 21px;
        //     height: 21px;
        //     border: 1px solid $stock_light;
        //     border-radius: 4px;
        //     align-items: center;
        //     justify-content: center;

        //     i {
        //         top: 50%;
        //         left: 50%;
        //         transform: translate(-50%, -50%);
        //     }
        // }
    }


    .form-group-icon-input {
        .input-icon {
            font-size: 12px;
            line-height: normal;
            color: $text_black_color;
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            bottom: 0;
            display: inline-flex;
            width: 21px;
            height: 21px;
            border: 1px solid $stock_light;
            border-radius: 4px;
            align-items: center;
            justify-content: center;

            i {
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
            }
        }
    }

    .form-group-icon-start {

        i {
            left: 12px;
        }

        span {
            left: 10px;
        }

        .form-select,
        .form-control {
            padding-left: 38px;
        }
    }

    .form-group-icon-end {
        i {
            right: 12px;
        }

        span {
            right: 10px;
        }

        .form-select,
        .form-control {
            padding-right: 38px;
        }
    }

    .form-group-password {
        position: relative;

        .form-control {
            padding-right: 40px;
            font-family: revert-layer;

            &::placeholder {
                font-family: 'Gellix' !important;
            }

            &::-ms-input-placeholder {
                font-family: 'Gellix' !important;
            }
        }

        .btn-password {
            background-color: initial;
            border: 0;
            color: #6F767E;
            padding: 0;
            position: absolute;
            right: 12px;
            top: 0;
            bottom: 0;

            i {
                left: unset;
                right: unset;
                position: relative;
            }

            &:hover {
                color: $black_color;
            }
        }



    }

    .form-group-button-wrapper {
        display: flex;
        align-items: center;
        gap: 10px;
        flex-direction: column;
        width: 100%;
    }

    .form-group-button {
        display: flex;
        align-items: center;
        gap: 0 10px;
        width: 100%;

        .btn-white {
            color: $primary_color;
        }

        >div {
            width: 100%;
        }
    }

    .form-floating {
        >label {
            color: #5A6A85;
            font-weight: 600;
        }

        .form-control,
        .form-select {
            background-color: #EDF7FE;
            border-radius: 10px;

            &:focus~label::after,
            &:not(:placeholder-shown)~label::after {
                background-color: transparent;
            }
        }
    }

    &.required {
        .form-label {
            &::after {
                content: '*';
                color: $danger_color;
                margin-left: 3px;
            }
        }
    }

    input[type="password"]::-ms-reveal,
    input[type="password"]::-ms-clear {
        display: none;
    }

    .input-group {

        >.form-select,
        >.input-group-text,
        >.btn {
            background-color: $bg_light;
            border-color: $stock_light;
            color: $text_color ;
        }

        >.form-control {
            border-color: $stock_light;
            color: $text_color ;
        }

        .btn {

            font-weight: 400;
            gap: 0 7px;

            &:hover,
            &:focus {
                border-color: $stock_light;
                border-color: $primary_color;
            }
        }

        .input-group-text {
            background-color: $bg_light;
            color: $text_color ;
        }


        &.input-group-sm {

            >.form-select,
            >.input-group-text,
            >.btn,
            >.form-control {
                border-radius: 6px;
                font-size: 14px;
            }
        }

        &.input-group-select {
            flex-wrap: nowrap;

            .ng-select {
                width: 85px;

                .ng-select-container {
                    border-top-right-radius: 0;
                    border-bottom-right-radius: 0;
                }
            }
        }

        &.tbl-input-group {
            .form-control {
                min-width: 65px;
                width: 100%;
            }

            .required {
                border-color: $danger_color;
                color: $danger_color;

                &:hover,
                &:focus {
                    border-color: $danger_hover_color !important
                }
            }

            .required.ng-invalid.ng-touched {
                .ng-select-container {
                    border-color: $danger_color;
                    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 0 3px #fde6e8;
                }
            }

            .required.ng-invalid.ng-untouched {
                .ng-select-container {
                    border-color: $danger_color;
                    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 0 3px #fde6e8;
                }
            }
        }

    }


    &.theme-ngselect {
        &.ng-select-opened {
            .ng-select-container {
                border: 1px solid $primary_color !important;
                background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg width='16' height='16' viewBox='0 0 16 16' fill='none' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M2 11L8 5L14 11' stroke='%23343A40' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3e%3c/svg%3e ");
            }
        }

        .ng-select-container {
            min-height: 38px;
            box-shadow: none !important;
            outline: 0 !important;
            background-color: #FFFFFF;
            border-color: #E6EAEE;
            border-radius: 6px;
            color: #292D32;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 0.75rem center;
            background-size: 16px 12px;

            .ng-clear-wrapper {
                margin-left: 6px;
            }

            .ng-arrow-wrapper {
                margin-left: 6px;

                .ng-arrow {
                    border-color: transparent;
                }
            }

            &:hover {
                border: 1px solid $primary_color !important;
            }
        }

        .ng-select-disabled {
            .ng-select-container {
                background-color: $bg_grey;
                border-color: $bg_grey;

                &:hover {
                    border-color: $bg_grey !important;
                }
            }
        }

        .ng-dropdown-panel {
            border: 1px solid $primary_color !important;

            .ng-dropdown-panel-items {
                .ng-option {
                    text-align: left;
                    padding: 6px;

                    .ng-option-label {
                        word-wrap: break-word;
                        word-break: break-word;
                        white-space: normal;
                        font-size: 14px;
                    }
                }

                .ng-option-marked {
                    background-color: $primary_light_color;
                    color: $primary_color;
                }
            }

        }


        .ng-select-multiple {
            .ng-select-container {
                .ng-value-container {
                    .ng-placeholder {
                        top: 7px;
                    }

                    .ng-value {
                        color: $primary_color;
                        background-color: $primary_light_color;
                        border-radius: 4px;
                        margin-right: 5px;
                        font-weight: 600;

                        .ng-value-label {
                            padding: 1px 10px 1px 5px;
                        }
                    }
                }
            }
        }

        .form-group-icon-start {
            .ng-input {
                padding-left: 38px !important;
            }

            .ng-value {
                padding-left: 28px !important;
            }
        }

        .required.ng-invalid.ng-touched {
            .ng-select-container {
                border-color: $danger_color;
                box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 0 3px #fde6e8;
            }
        }

        .required.ng-invalid.ng-untouched {
            .ng-select-container {
                border-color: $danger_color;
                box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 0 3px #fde6e8;
            }
        }


        .ng-select.ng-select-opened .ng-select-container {
            z-index: 99;
        }
    }

    &.theme-ngselect-group {
        .ng-optgroup {
            background-color: #f5f5f5;

        }

        .ng-option-child {
            padding-left: 25px !important;

            .ng-option-label {
                position: relative;
                padding-left: 12px;
                text-transform: capitalize;

                &::after {
                    content: '';
                    position: absolute;
                    left: 1px;
                    top: 50%;
                    transform: translateX(-50%);
                    width: 4px;
                    height: 4px;
                    border-radius: 50%;
                    background-color: #636D83;
                }

            }
        }
    }


    &.theme-ngselect-group-list {
        .ng-option {
            font-weight: 500;
            color: #636D83;
            text-transform: capitalize;
        }

        .ng-option-child-label {
            position: relative;
            padding-left: 12px;

            &::after {
                content: '';
                position: absolute;
                left: 0;
                top: 50%;
                transform: translateX(-50%);
                width: 4px;
                height: 4px;
                border-radius: 50%;
                background-color: #636D83;
            }

            &.ng-select-option-2 {
                &::after {
                    left: 12px;
                }
            }

            &.ng-select-option-3 {
                &::after {
                    left: 34px;
                }
            }

            &.ng-select-option-4 {
                &::after {
                    left: 64px;
                }
            }
        }
    }

    &.theme-ngselect-user-list {
        .tbl-user {

            .tbl-user-wrapper {
                display: flex;
                align-items: center;
                gap: 0 8px;
                width: 100%;

                .tbl-user-image {
                    width: 32px;
                    height: 32px;
                    min-width: 32px;
                    border-radius: 4px;
                    overflow: hidden;
                    background-color: #F9F9F9;
                    border: 1px solid $stock_light;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    position: relative;

                    img {
                        width: 100%;
                        height: 100%;
                        background-size: cover;
                    }

                    i {
                        font-size: 18px;
                    }
                }

                .tbl-user-text {
                    width: 100%;
                    display: flex;
                    flex-direction: column;

                    p {
                        font-weight: 600;
                        color: $text_black_color;
                        font-size: 12px;
                        margin-bottom: 0px;
                    }

                    span {
                        color: $text_color;
                        font-size: 12px;
                    }

                    .tbl-user-counter {
                        background-color: #F0F3F5;
                        height: 24px;
                        width: 24px;
                        display: inline-flex;
                        flex-direction: row;
                        align-items: center;
                        justify-content: center;
                        color: $text_black_color;
                        font-size: 13px;
                        font-weight: 600;
                        border-radius: 4px;
                        font-size: 13px;
                    }
                }

                .tbl-user-text-action {
                    display: flex;
                    align-items: stretch;
                    justify-content: flex-start;
                    gap: 0 5px;
                    width: 100%;

                    .user-action,
                    .tbl-user-text {
                        height: 100%;
                    }
                }
            }

            // &.tbl-user-item {
            //     padding: 10px;
            //     border-radius: 10px;
            //     border-bottom: 1px solid $stock_light;

            //     &:hover {
            //         background-color: $primary_color;

            //         .tbl-user-wrapper {
            //             .tbl-user-text {

            //                 p,
            //                 span {
            //                     color: $white_color !important;
            //                 }

            //             }
            //         }
            //     }

            //     &:last-child {
            //         border-bottom: 0px solid $stock_light;
            //     }

            // }

        }
    }

    &.form-group-export {
        .ng-select-container {
            width: 100%;
            min-width: 100px;
        }

        .ng-select {
            .ng-arrow-wrapper {
                width: 36px;
                height: 18px;
                margin-left: 5px;
                padding-right: 0;
                border-left: 1px solid $stock_light;
            }
        }
    }

    &.form-group-sm {
        margin-bottom: 10px;

        .form-label {
            font-size: 12px;
            margin-bottom: 4px;
        }

        .form-control,
        .form-select {
            padding: 4px 10px;
            min-height: 30px;
            font-size: 14px;
        }

        .form-group-password {
            .btn-password {

                right: 22px;


            }
        }

        .form-group-icon-start,
        .form-group-icon-end {
            i {
                font-size: 14px;
            }
        }

        .form-group-icon-start {
            i {
                left: 10px;
            }

            .form-control,
            .form-select {
                padding-left: 32px;
            }

            .icon-broder {
                padding-right: 5px;

                &::after {
                    content: "";
                    position: absolute;
                    right: 0px;
                    width: 1px;
                    height: 50%;
                    background-color: $stock_dark;
                    top: 50%;
                    transform: translateY(-50%);

                }
            }
        }

        .form-group-icon-end {
            i {
                right: 10px;
            }

            .form-control,
            .form-select {
                padding-right: 32px;
            }
        }

        &.form-error {

            .form-control,
            .form-select {
                background-position: right calc(0.3em + 0.10rem) center;
                background-size: calc(0.65em + 0.375rem) calc(0.65em + 0.375rem);
                padding-right: 22px;
            }

        }

        &.form-success {

            .form-control,
            .form-select {
                background-position: right calc(0.3em + 0.10rem) center;
                background-size: calc(0.65em + 0.375rem) calc(0.65em + 0.375rem);
                padding-right: 22px;
            }
        }

        &.theme-ngselect,
        .theme-ngselect {
            .ng-select-container {
                height: 31px;
                min-height: 31px;


                .ng-value-container {
                    font-size: 14px;
                    color: $text_color;
                }

            }

            .form-group-icon-start {
                .ng-input {
                    padding-left: 32px !important;

                }

                .ng-value {
                    padding-left: 22px !important;
                }
            }


        }

        &.form-group-export {
            .ng-select-container {
                width: 100%;
                min-width: 95px;
            }

            .ng-select {
                .ng-arrow-wrapper {
                    width: 33px;
                    height: 16px;
                }
            }
        }

        .otp-group {
            input {
                width: 30px;
            }
        }

        .ng-select-multiple {
            .ng-select-container {
                .ng-value-container {
                    .ng-placeholder {
                        top: 4px;
                    }

                }
            }
        }
    }



    &.form-group-lg {
        margin-bottom: 10px;

        .form-label {}

        .form-control,
        .form-select {
            padding: 12px 15px;
            min-height: 14px;
            font-size: 16px;
        }

        .form-group-icon-start,
        .form-group-icon-end {
            i {
                font-size: 16px;
            }
        }

        .form-group-icon-start {

            .form-control,
            .form-select {
                padding-left: 38px;
            }

            .icon-broder {
                padding-right: 5px;

                &::after {
                    content: "";
                    position: absolute;
                    right: 0px;
                    width: 1px;
                    height: 50%;
                    background-color: $stock_dark;
                    top: 50%;
                    transform: translateY(-50%);

                }
            }
        }

        .form-group-icon-end {


            .form-control,
            .form-select {
                padding-right: 38px;
            }
        }

        &.theme-ngselect,
        .theme-ngselect {
            .ng-select-container {
                height: 43px;
                min-height: 43px;


                .ng-value-container {
                    font-size: 16px;
                    color: $text_color;
                }

            }

        }

        &.form-group-export {
            .ng-select-container {
                width: 100%;
                min-width: 95px;
            }

            .ng-select {
                .ng-arrow-wrapper {
                    width: 33px;
                    height: 16px;
                }
            }
        }

        .otp-group {
            input {
                width: 43px;
            }
        }
    }

    input[type="color"] {
        -webkit-appearance: none;
        border-radius: 5px;
        border: 1px solid #E6EAEE;
        width: 95px;
        height: 37px;
        padding: 8px 11px;
    }

    &.form-group-inline {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
    }

    &.form-group-inline-control {
        display: flex;
        align-items: center;
        gap: 0 20px;

        .form-label {
            width: 100%;
            max-width: 175px;
            margin-bottom: 0;
        }

        .form-control-wrapper {
            width: 100%;

            ng-select {
                width: 100%;
            }
        }


    }

    &.form-border-less {

        .form-control,
        .form-select {
            border: transparent;

            &:hover,
            &:focus {
                border-color: transparent !important
            }

            &:disabled,
            .disabled {
                background-color: $bg_grey !important;
                border-color: transparent !important;

                &:hover,
                &:focus {
                    border-color: transparent !important
                }
            }
        }


        &.theme-ngselect {
            &.ng-select-opened {
                .ng-select-container {
                    border: 1px solid transparent !important;
                }
            }

            .ng-select-container {
                border-color: transparent;

                &:hover {
                    border: 1px solid transparent !important;
                }
            }

            .ng-dropdown-panel {
                border: 1px solid $primary_color !important;
            }

            .ng-value {
                font-size: 12px;
            }
        }


        &.bg-transparent {
            &.theme-ngselect {


                .ng-select-container {
                    background-color: transparent;
                }

            }
        }
    }

    &.form-group-100 {
        min-width: 65px;
    }

    &.form-group-200 {
        min-width: 130px;
    }
}

// month drp-animate
.month {
    .dropdowns {
        &:nth-child(2) {
            display: none;
        }
    }
}



// comment-list start
.comment-list-wrapper {
    .comment-user {
        display: flex;
        flex-direction: column;
        gap: 10px 0;

        .comment-user-wrapper {
            display: flex;
            align-items: center;
            gap: 0 8px;
            width: 100%;
            padding-left: 20px;
            position: relative;

            &:last-child {
                &::before {
                    content: none;
                }
            }

            &::after {
                content: '\F287';
                font-family: bootstrap-icons;
                position: absolute;
                left: 0;
                top: 50%;
                transform: translateY(-50%);
                font-size: 12px;
                color: $success_color;
                z-index: 2;
            }

            &::before {
                content: '';
                position: absolute;
                height: 90%;
                width: 2px;
                left: 5px;
                bottom: -25px;
                background: linear-gradient(to top, transparent 50%, #B8C3CF 50%);
                background-size: 15px 8px;
                z-index: 1;
            }

            .comment-user-image {
                width: 32px;
                height: 32px;
                min-width: 32px;
                border-radius: 4px;
                overflow: hidden;
                background-color: #F9F9F9;
                border: 1px solid $stock_light;
                display: flex;
                align-items: center;
                justify-content: center;

                img {
                    width: 100%;
                    height: 100%;
                    background-size: cover;
                }

                i {
                    font-size: 18px;
                }
            }

            .comment-user-text {
                width: 100%;

                p {
                    font-weight: 600;
                    color: $text_black_color;
                    font-size: 12px;
                    line-height: normal;
                    margin-bottom: 0px;
                }

                span {
                    color: $text_color;
                    font-size: 12px;
                }

                .comment-user-counter {
                    background-color: #F0F3F5;
                    height: 24px;
                    width: 24px;
                    display: inline-flex;
                    flex-direction: row;
                    align-items: center;
                    justify-content: center;
                    color: $text_black_color;
                    font-size: 13px;
                    font-weight: 600;
                    border-radius: 4px;
                    font-size: 13px;
                }
            }

            .comment-user-text-action {
                display: flex;
                align-items: center;
                justify-content: flex-start;
                gap: 0 5px;
                width: 100%;

                .user-action,
                .comment-user-text {
                    height: 100%;
                }
            }
        }


    }
}

// comment-list end

// drag-control-container
.drag-control-container {
    margin-bottom: 16px;

    &:last-child {
        margin-bottom: 0px;
    }

    .drag-control-wrapper {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 10px;
        margin-bottom: 16px;

        &:last-child {
            margin-bottom: 0px;
        }

        .drag-control-icon {}

        .form-group {
            margin-bottom: 0px !important;
            width: 100%;
        }

        .drag-control-delete {}
    }
}


// input type search hide close button from all browser.
input[type="search"]::-webkit-search-decoration,
input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-results-button,
input[type="search"]::-webkit-search-results-decoration {
    -webkit-appearance: none;
}

// drag-control-wrapper2
.drag-control-wrapper2 {
    display: flex;
    flex-direction: column;
    gap: 7px;

    .drag-control-item {
        padding: 10px 12px;
        border-radius: 7px;
        background-color: $bg_grey;
        display: flex;
        align-items: center;
        gap: 0 8px;
        cursor: pointer;

        &.selected,
        &:hover {
            background-color: $primary_light_color;
        }

        .drag-control-icon {
            cursor: move;
            display: flex;
            align-items: center;
            justify-content: center;

            img {
                width: auto;
                height: 16px;
                background-size: contain;
                background-position: center center;
            }
        }

        .drag-control-button {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 18px;

            i {
                font-size: 18px;
                color: $text_color;
            }

            .checkbox {
                margin: 0;

                label {
                    padding: 9px;
                }
            }
        }


        .drag-control-label {
            p {
                font-size: 14px;
                font-weight: 600;
                line-height: 18.2px;
                color: $text_black_color;
                margin: 0;
            }
        }

    }
}

// branch-alert-container
.branch-alert-container {
    display: grid;
    gap: 7px;
    margin-bottom: 12px;

    .branch-alert-wrapper {
        padding: 7px;
        border-radius: 6px;
        background-color: $bg_blue;
        overflow: hidden;



        .branch-alert-item {
            margin-left: -10px;
            margin-right: -10px;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .branch-alert-item-col {
                flex: 0 0 25%;
                max-width: 25%;

                padding-left: 10px;
                padding-right: 10px;
            }

            .branch-alert-details {
                position: relative;

                // &:last-child {
                //     &::after {
                //         content: none;
                //     }
                // }

                &::after {
                    content: '';
                    position: absolute;
                    right: -10px;
                    top: 50%;
                    transform: translateY(-50%);
                    width: 1px;
                    height: 20px;
                    background-color: $stock_light;
                }

                .form-group {
                    margin-bottom: 0;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                }

                .branch-alert-inline-control {
                    display: flex;
                    align-items: baseline;
                    gap: 0 6px;

                    .form-label {
                        width: 100%;
                        max-width: 184px;
                        font-size: 11px;
                        color: $text_color;
                    }

                    .form-control-wrapper {
                        width: 100%;
                        max-width: 110px;

                        .form-control {
                            font-size: 11px;
                        }
                    }
                }
            }
        }

    }
}



/* -------------------------------------------------------------------------- */
/*                       Attachments - File Upload Start                      */
/* -------------------------------------------------------------------------- */
.attachments-wrapper {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.attachments-container {
    position: relative;
    border: 2px dashed $stock_dark;
    padding: 15px 25px;
    min-height: auto;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;

    &.attachments-container-style2 {
        border-color: $stock_light;
        background-color: $primary_light_color;

        .attachments-content {
            p {
                color: $text_color;
            }
        }
    }


    .attachments-content {
        text-align: center;

        .btn-attachments {
            background-color: $primary_light_color;
            border: 1px solid $primary_light_color;
            color: $text_black_color;
            height: 37px;
            width: 37px;
            padding: 0;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        p {
            font-size: 14px;
            font-weight: 600;
            margin-top: 16px;
        }
    }

    input[type="file"] {
        opacity: 0;
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        cursor: pointer;
    }

    &:hover {
        background-color: $bg_light;
        border-color: $primary_color;

        .btn-attachments {
            background-color: $primary_color;
            border-color: $primary_color;
            color: $white_color;
        }

        p {
            color: $black_color;
        }
    }

    &.attachments-container2 {
        height: 107px !important;
    }

}

.attachments-upload-grid-container {
    margin-top: 15px;

    .attachments-upload-row {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        gap: 14px;

        .attachments-upload-col {
            max-width: 100px;
            padding: 0 0px;
        }

        .card-attachments-upload {
            border: 1px solid $stock_light;
            border-radius: 7px;
            overflow: hidden;
            position: relative;

            .attachments-image {
                height: 100px;
                width: 100%;
                border-radius: 0px;

                img {
                    height: 100%;
                    width: 100%;
                    object-fit: cover;
                    object-position: center;
                }
            }

            .attachments-body {
                padding: 10px;

                h6 {
                    font-size: 14px;
                    font-weight: 400;
                    color: $text_black_color;
                    margin-bottom: 3px;
                }

                p {
                    font-size: 12px;
                    color: $text_color;
                }
            }

            &.attachments-file {
                .attachments-image {
                    background-color: $bg_light;
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;

                    img {
                        height: 50px;
                        width: 50px;
                    }
                }
            }

            .btn-close,
            .btn-download {
                background: 0;
                font-size: 12px;
                color: $text_color;
                opacity: 1;
                box-shadow: none;
                border: 0;
                padding: 0;
                height: 16px;
                width: 16px;
                border-radius: 50%;
                position: absolute;
                top: 10px;
                right: 10px;
                display: flex;
                align-items: center;
                justify-content: center;
                background-color: #F4F5F6;

                i {
                    font-size: 10px;
                    transition: 0.70s;
                    -webkit-text-stroke: 0.5px;
                }

                &:hover {
                    background-color: #F4F5F6;
                    color: $black_color;

                    i {
                        transform: rotate(180deg);
                    }
                }
            }

            .btn-download {
                &:hover {
                    i {
                        transform: inherit;
                    }
                }
            }
        }
    }


    &.attachments-upload-grid-container2 {
        .attachments-upload-row {
            .attachments-upload-col {
                width: 100%;

                .radio [type=radio]+label {
                    font-size: 12px;
                }
            }

            .card-attachments-upload {
                border: 1px solid $stock_light;
                background-color: $white_color;
                height: 95px;

                .attachments-image {
                    height: 62px;

                    img {
                        background-size: cover;
                    }

                    border-bottom-right-radius: 0;
                    border-bottom-left-radius: 0;
                }

                .attachments-text {
                    padding: 5px 10px;

                    h6 {
                        font-size: 12px;
                        font-weight: 600;
                        line-height: 15.6px;
                        color: $text_black_color;
                        margin-bottom: 0px;
                        -webkit-line-clamp: 1;
                        -webkit-box-orient: vertical;
                        display: -webkit-box;
                        white-space: normal;
                        overflow: hidden;
                    }

                    p {

                        font-size: 10px;
                        font-weight: 400;
                        line-height: 17px;
                        margin-bottom: 0px;
                        color: $text_color;
                        -webkit-line-clamp: 1;
                        -webkit-box-orient: vertical;
                        display: -webkit-box;
                        white-space: normal;
                        overflow: hidden;

                    }
                }
            }
        }
    }

    &.attachments-upload-grid-container-full {
        .attachments-upload-row {
            .attachments-upload-col {
                max-width: 100%;

                .card-attachments-upload {
                    display: flex;
                    align-items: center;

                    .attachments-image {
                        height: 100%;
                        max-width: 100px;
                    }
                }
            }
        }
    }
}





/* -------------------------------------------------------------------------- */
/*                        Attachments - File Upload End                       */
/* -------------------------------------------------------------------------- */

/* -------------------------------------------------------------------------- */
/*                         Textbox - File Upload Start                        */
/* -------------------------------------------------------------------------- */
.form-group-inner-file-upload {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    gap: 15px;

    .inner-file-upload-left {
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 15px;

        p {
            font-size: 14px;
            color: $text_color;
        }
    }

    .inner-file-upload-right {
        p {
            font-size: 14px;
            font-weight: 500;
            color: $text_color;
        }
    }
}

.btn-fileupload {
    position: relative;
    display: flex;
    align-items: center;
    gap: 0 10px;
    font-weight: 400;

    input[type="file"] {
        opacity: 0;
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        cursor: pointer;
    }

    &.btn-fileupload-white {
        position: relative;
        background-color: white;
        border: 1px dotted $stock_dark;
        color: $text_color;

        input[type="file"] {
            opacity: 0;
            position: absolute;
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;
            cursor: pointer;
        }

        &:hover {
            background-color: $bg_light;
            border-color: $primary_color;
        }
    }
}

/* -------------------------------------------------------------------------- */
/*                          Textbox - File Upload End                         */
/* -------------------------------------------------------------------------- */




/* -------------------------------------------------------------------------- */
/*                           Checkbox & Radio Button                          */
/* -------------------------------------------------------------------------- */

/* --------------------------------- .radio --------------------------------- */
.radio {
    padding-left: 0;
    margin: 3px 0
}

.radio [type=radio]:checked,
.radio [type=radio]:not(:checked) {
    position: absolute;
    left: -9999px;
    opacity: 0
}

.radio label {
    display: inline-block;
    position: relative;
    padding-left: 5px
}

.radio [type=radio]+label {
    position: relative;
    padding-left: 20px;
    cursor: pointer;
    display: inline-block;
    height: 21px;
    line-height: 19px;
    font-size: 14px;
    margin: 0;
    font-weight: 500;
    -webkit-user-select: none;
    -moz-user-select: none;
    -khtml-user-select: none;
    -ms-user-select: none
}

.radio label::before {
    content: "";
    display: inline-block;
    position: absolute;
    width: 16px;
    height: 16px;
    left: 0;
    top: 2px;
    border: 1px solid #a0a7b5;
    border-radius: 50%;
    background-color: #fff;
    margin: 0;
    -webkit-transition: border .15s ease-in-out;
    -o-transition: border .15s ease-in-out;
    transition: border .15s ease-in-out
}

.radio label::after {
    display: inline-block;
    position: absolute;
    content: " ";
    width: 10px;
    height: 10px;
    left: 3px;
    top: 5px;
    border-radius: 50%;
    background-color: #555;
    margin: 0;
    -webkit-transform: scale(0, 0);
    -ms-transform: scale(0, 0);
    -o-transform: scale(0, 0);
    transform: scale(0, 0);
    -webkit-transition: -webkit-transform .1s cubic-bezier(.8, -.33, .2, 1.33);
    -moz-transition: -moz-transform .1s cubic-bezier(.8, -.33, .2, 1.33);
    -o-transition: -o-transform .1s cubic-bezier(.8, -.33, .2, 1.33);
    transition: transform .1s cubic-bezier(.8, -.33, .2, 1.33)
}

.radio input[type=radio] {
    opacity: 0
}

.radio input[type=radio]:focus+label::before {
    outline: thin dotted;
    outline: 0;
    outline-offset: -2px
}

.radio input[type=radio]:checked+label::after {
    -webkit-transform: scale(1, 1);
    -ms-transform: scale(1, 1);
    -o-transform: scale(1, 1);
    transform: scale(1, 1)
}

.radio input[type=radio]:disabled+label {
    opacity: .65
}

.radio input[type=radio]:disabled+label::before {
    cursor: not-allowed
}

.radio.radio-inline {
    margin-top: 0
}

.radio-primary input[type=radio]+label::after {
    background-color: $primary_color
}

.radio-primary input[type=radio]:checked+label::before {
    border-color: $primary_color
}

.radio-primary input[type=radio]:checked+label::after {
    background-color: $primary_color
}

.radio.form-check-inline {
    display: inline-block;
    margin-right: 1rem
}

.radio.radio-small [type=radio]+label {
    height: 17px
}

/* --------------------------------- .radio --------------------------------- */



/* -------------------------------- checkbox -------------------------------- */
.checkbox {
    margin: 5px 0;
}

.checkbox.form-check-inline {
    display: inline-block;
    margin-right: 1rem
}

.checkbox [type=checkbox]:checked,
.checkbox [type=checkbox]:not(:checked) {
    left: -9999px;
    opacity: 0;
    position: absolute
}

.checkbox [type=checkbox]+label {
    cursor: pointer;
    display: inline-block;
    margin: 0;
    -webkit-user-select: none;
    -moz-user-select: none;
    -khtml-user-select: none;
    -ms-user-select: none;
    color: $text_black_color;


    position: relative;
    font-size: 16px;
    font-weight: 400;
    line-height: 16px;
    padding-left: 25px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
}

.checkbox.checkbox-small [type=checkbox]+label {
    cursor: pointer;
    display: inline-block;
    margin: 0;
    position: relative;
    -webkit-user-select: none;
    -moz-user-select: none;
    -khtml-user-select: none;
    -ms-user-select: none;
    color: $text_black_color;

    font-size: 12px;
    font-weight: 400;
    line-height: 16px;
    padding-left: 22px;
    height: 15px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
}

.checkbox [type=checkbox]+label:before,
.checkbox [type=checkbox]:not(.filled-in)+label:after {
    border: 1px solid #5a5a5a;
    border-radius: 1px;
    content: "";
    height: 18px;
    left: 0;
    margin-top: 2px;
    position: absolute;
    top: 0;
    transition: .2s;
    width: 18px;
    z-index: 0
}

.checkbox [type=checkbox]:not(.filled-in)+label:after {
    border: 0;
    -webkit-transform: scale(0);
    transform: scale(0)
}

.checkbox [type=checkbox]:not(:checked):disabled+label:before {
    background-color: #00000042;
    border: none
}

.checkbox [type=checkbox].tabbed:focus+label:after {
    background-color: #0000001a;
    border: 0;
    border-radius: 50%;
    box-shadow: 0 0 0 10px #0000001a;
    -webkit-transform: scale(1);
    transform: scale(1)
}

.checkbox [type=checkbox]:checked+label:before {
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    border-bottom: 2px solid $primary_color;
    border-left: 2px solid transparent;
    border-right: 2px solid $primary_color;
    border-top: 2px solid transparent;
    height: 22px;
    left: -5px;
    top: -4px;
    -webkit-transform: rotate(40deg);
    transform: rotate(40deg);
    -webkit-transform-origin: 100% 100%;
    transform-origin: 100% 100%;
    width: 12px
}

.checkbox [type=checkbox]:checked:disabled+label:before {
    border-bottom: 2px solid rgba(0, 0, 0, .26);
    border-right: 2px solid rgba(0, 0, 0, .26)
}

.checkbox [type=checkbox]:indeterminate+label:before {
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    border: none;
    border-right: 2px solid $primary_color;
    height: 22px;
    left: -12px;
    top: -11px;
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg);
    -webkit-transform-origin: 100% 100%;
    transform-origin: 100% 100%;
    width: 10px
}

.checkbox [type=checkbox]:indeterminate:disabled+label:before {
    background-color: initial;
    border-right: 2px solid rgba(0, 0, 0, .26)
}

.checkbox [type=checkbox].filled-in+label:after {
    border-radius: 4px
}

.checkbox [type=checkbox].filled-in+label:after,
.checkbox [type=checkbox].filled-in+label:before {
    content: "";
    left: 0;
    position: absolute;
    z-index: 1
}

.checkbox [type=checkbox].filled-in:not(:checked)+label:before {
    border: 3px solid transparent;
    height: 0;
    left: 6px;
    top: 10px;
    -webkit-transform: rotate(37deg);
    transform: rotate(37deg);
    -webkit-transform-origin: 100% 100%;
    transform-origin: 100% 100%;
    width: 0
}

.checkbox [type=checkbox].filled-in:not(:checked)+label:after {
    background-color: initial;
    border: 1px solid $stock_dark;
    height: 20px;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    z-index: 0
}

.checkbox [type=checkbox].filled-in:checked+label:before {
    border-color: transparent #fff #fff transparent;
    border-style: solid;
    border-width: 2px;
    height: 13px;
    left: 1px;
    top: 41%;
    transform: translateY(-50%) rotate(37deg);
    -webkit-transform: translateY(-50%) rotate(37deg);
    -webkit-transform-origin: 100% 100%;
    transform-origin: 100% 100%;
    width: 8px
}

.checkbox.checkbox-small [type=checkbox].filled-in:checked+label:before {

    top: 32%;
    transform: translateY(-50%) rotate(37deg);
    -webkit-transform: translateY(-50%) rotate(37deg);

}

.checkbox [type=checkbox].filled-in:checked+label:after {
    background-color: $primary_color;
    border: 1px solid $primary_color;
    height: 20px;
    width: 20px;
    z-index: 0
}

.checkbox [type=checkbox].filled-in.tabbed:focus+label:after {
    background-color: #0000001a;
    border-color: #5a5a5a;
    border-radius: 2px
}

.checkbox [type=checkbox].filled-in.tabbed:checked:focus+label:after {
    background-color: $primary_color;
    border-color: $primary_color;
    border-radius: 2px
}

.checkbox [type=checkbox].filled-in:disabled:not(:checked)+label:before {
    background-color: initial;
    border: 2px solid transparent
}

.checkbox [type=checkbox].filled-in:disabled:checked+label:before {
    background-color: initial
}

.checkbox [type=checkbox].filled-in:disabled:checked+label:after {
    background-color: $stock_dark;
    border-color: $stock_dark
}

.checkbox.checkbox-small [type=checkbox].filled-in:checked+label:after,
.checkbox.checkbox-small [type=checkbox].filled-in:not(:checked)+label:after {
    height: 15px;
    width: 15px
}

.checkbox.checkbox-small [type=checkbox].filled-in:checked+label:before {
    height: 10px;
    width: 6px
}

.checkbox.checkbox-inline {
    display: inline-block;
    margin-right: 1rem
}

.checkbox.checkbox-white [type=checkbox].filled-in:not(:checked)+label:after {
    border: 1px solid $white_color
}



.checkbox {
    &.checkbox-right {
        width: fit-content;

        [type=checkbox]+label {
            padding-right: 25px;
            padding-left: 0px;
        }

        &.checkbox-small [type=checkbox]+label {
            padding-right: 22px;
            padding-left: 0px;
        }

        [type=checkbox].filled-in:not(:checked)+label:before {
            left: unset;
            right: -6px;
        }

        [type=checkbox].filled-in+label:after {
            left: unset;
            right: 0;
        }

        [type=checkbox].filled-in+label:before {
            left: unset;
            right: 8px;
        }
    }
}

/* -------------------------------- checkbox -------------------------------- */






/* -------------------------------------------------------------------------- */
/*                           Checkbox & Radio Button                          */
/* -------------------------------------------------------------------------- */




/* -------------------------------------------------------------------------- */
/*                                Switch Start                                */
/* -------------------------------------------------------------------------- */
.switch-box {
    display: flex;
    align-items: center
}

.switch-box span {
    margin: 0 10px
}

.switch-box .switch {
    display: inline-block;
    height: 20px;
    position: relative;
    width: 40px;
    margin-bottom: 0
}

.switch-box .switch input {
    display: none
}

.switch-box .slider {
    background-color: #E6EAEE;
    bottom: 0;
    cursor: pointer;
    left: 0;
    position: absolute;
    right: 0;
    top: 0;
    transition: .4s
}

.switch-box .slider:before {
    background-color: #FFFFFF;
    bottom: 4px;
    content: "";
    height: 12px;
    left: 4px;
    position: absolute;
    transition: .4s;
    width: 12px
}

.switch-box input:checked+.slider {
    background-color: $primary_color
}

.switch-box input:checked+.slider:before {
    transform: translateX(20px);
    background-color: $white_color
}

.switch-box .slider.round {
    border-radius: 34px
}

.switch-box .slider.round:before {
    border-radius: 50%
}

.switch-box.switch-notallowed .slider {
    cursor: not-allowed
}

.switch-box.switch-box-warning input:checked+.slider {
    background-color: $warning_color;
}

.switch-box.switch-box-danger input:checked+.slider {
    background-color: $danger_color;
}

.switch-box.switch-box-success input:checked+.slider {
    background-color: $success_color;
}


.switch-box {
    &.switch-lg {
        .switch {
            height: 30px;
            width: 60px;

            .slider {
                &:before {
                    height: 22px;
                    left: 4px;
                    width: 22px;
                }
            }
        }

        input:checked+.slider:before {
            transform: translateX(30px);
            background-color: #FFFFFF;
        }
    }


    input:disabled+.slider {
        opacity: 0.3;
        cursor: no-drop;
    }
}

/* -------------------------------------------------------------------------- */
/*                                 Switch End                                 */
/* -------------------------------------------------------------------------- */



/* ----------------------------- .checkbox card ----------------------------- */
.checkbox-card-wrapper {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;

    .checkbox-card {
        padding: 12px;
        border-radius: 6px;
        border: 1px solid $stock_light;
        background-color: #F9FAFC;
        display: flex;
        gap: 0 8px;
        min-width: 280px;
        cursor: pointer;

        .check-icon {
            margin-top: -3px;

            i {
                font-size: 16px;
                line-height: 16px;
                color: $text_color;
            }
        }

        .check-text {
            h6 {
                font-size: 12px;
                font-weight: 600;
                line-height: 15.6px;
                text-align: left;
                margin-bottom: 5px;
                color: $text_black_color;
            }

            p {
                font-size: 12px;
                font-weight: 400;
                line-height: 14.4px;
                margin-bottom: 0px;
                color: $text_color;

            }
        }


        &.checked {
            border-color: $primary_color;

            .check-icon {
                i {
                    color: $primary_color;
                }
            }
        }

        &:hover {
            border-color: $primary_color;
        }

    }
}

/* ----------------------------- .checkbox card ----------------------------- */

/* ------------------------------- Date Picker ------------------------------ */
ngb-datepicker {
    width: 250px;
    border-radius: 0.25rem;
    display: inline-block;

    .ngb-dp-content {
        justify-content: center;
    }

    .ngb-dp-day,
    .ngb-dp-weekday,
    .ngb-dp-week-number {
        font-style: normal;
    }

    .ngb-dp-weekday {
        color: $primary_color;
    }
}

.date-range-filter {
    .md-drppicker.shown {
        width: 620px !important;
        top: 50px !important;
    }
}

.md-drppicker.shown {
    width: 500px !important;
}

.single-date {
    .md-drppicker.shown {
        width: 250px !important;
        top: 50px !important;
        left: 0 !important;
        right: auto !important;
    }
}

.md-drppicker td:hover {
    color: #fff !important;
}

.md-drppicker td.active,
.md-drppicker td.active:hover {
    background-color: $primary_color !important;
}

td.active,
.md-drppicker td.available:hover {
    background-color: $primary_color !important;
}

.md-drppicker .ranges ul li button.active {
    background-color: $primary_color !important;
    color: $white_color;
}

ngx-daterangepicker-material {
    .month {
        .dropdowns {
            width: 70px;
        }
    }
}

.md-drppicker {
    .btn {
        background-color: $primary_color !important;
    }

    .buttons {
        text-align: right;
        margin: 0 5px 5px 0;
    }
}

/* ------------------------------- Date Picker ------------------------------ */

.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value.ng-value-disabled .ng-value-icon {
    display: unset !important;
}

// Ng select TAG
.form-group-tag {
    .ng-select {
        // max-width: 90%;
        &.ng-select-multiple {
            .ng-select-container {
                .ng-value-container {
                    flex-wrap: wrap;
                    min-width: 0;
    
                    .ng-value {
                        // white-space: nowrap;
                        min-width: 0;
                        display: flex;
                        word-wrap: break-word;
                        word-break: break-word;
                        white-space: normal;
                        .ng-value-label {
                            // white-space: nowrap;
                            overflow: hidden;
                            text-overflow: ellipsis;
                        }
                    }
                }
                .ng-arrow-wrapper {
                    display: none;
                }
            }
        }
    }
}

// group code ng select
.form-group-group-code {
    .ng-select {
        width: 225px !important;
    }
}

// HSN DROPDOWN
.hsn-select .ng-dropdown-panel {
    width: 650px !important;
}

.hsn-select .ng-option {
    padding: 0 !important;
}

.hsn-select .hsn-description {
    width: 500px;
    word-wrap: break-word;
    word-break: break-word;
    white-space: normal;

    div {
        -webkit-line-clamp: 4;
        line-clamp: 4;
        -webkit-box-orient: vertical;
        display: -webkit-box;
        white-space: normal;
        overflow: hidden;
    }
}

// PO DROPDOWN

.new-po-select .ng-dropdown-panel {
    width: 650px !important;
}

.new-po-select .ng-option {
    padding: 0 !important;
}

.new-po-select {
    width: 500px !important;

    .po-description {
        width: 500px;
        word-wrap: break-word;
        word-break: break-word;
        white-space: normal;

        div {
            -webkit-line-clamp: 4;
            line-clamp: 4;
            -webkit-box-orient: vertical;
            display: -webkit-box;
            white-space: normal;
            overflow: hidden;
        }
    }
}

//CARTON MAPPING
.cm-dropdown {
    min-width: 150px !important;
    max-width: 150px !important;
}

.custom-cm-class {
    width: 200px !important;
}

.ng-expense {
    min-width: 300px;
}