{"name": "valamji-frontend", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build --configuration production --base-href ./", "watch": "ng build --watch --configuration development", "testing-build": "ng build --configuration testing --base-href /valamji_design/", "testing-dev": "ng build --configuration testing --base-href /valamji/", "prod-build": "ng build --configuration production --base-href /valamji/", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^18.2.13", "@angular/cdk": "^18.1.0", "@angular/common": "^18.2.13", "@angular/compiler": "^18.2.13", "@angular/core": "^18.2.13", "@angular/forms": "^18.2.13", "@angular/platform-browser": "^18.2.13", "@angular/platform-browser-dynamic": "^18.2.13", "@angular/router": "^18.2.13", "@danielmoncada/angular-datetime-picker": "^18.1.0", "@ng-bootstrap/ng-bootstrap": "^17.0.1", "@ng-select/ng-select": "^13.2.0", "@popperjs/core": "^2.11.8", "@stomp/rx-stomp": "^2.0.0", "@stomp/stompjs": "^7.0.0", "@types/jquery": "^3.5.32", "@types/slick-carousel": "^1.6.40", "bootstrap": "^5.3.2", "cerialize": "^0.1.18", "dayjs": "^1.11.13", "file-saver": "^2.0.5", "jquery": "^3.7.1", "moment": "^2.30.1", "ng-otp-input": "^1.9.3", "ngx-autosize": "^2.0.4", "ngx-countdown": "^16.0.0", "ngx-daterangepicker-material": "^6.0.4", "ngx-mask": "^17.1.8", "ngx-print": "^1.5.0", "ngx-slick-carousel": "^18.0.0", "ngx-toastr": "^19.0.0", "rxjs": "~7.8.0", "simplebar-angular": "^3.2.4", "slick-carousel": "^1.8.1", "tslib": "^2.3.0", "zone.js": "~0.14.10"}, "devDependencies": {"@angular-devkit/build-angular": "^18.2.20", "@angular/cli": "^18.2.20", "@angular/compiler-cli": "^18.2.13", "@angular/localize": "^18.2.13", "@types/file-saver": "^2.0.7", "@types/jasmine": "~4.3.0", "jasmine-core": "~4.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.0.0", "typescript": "~5.4.5"}}