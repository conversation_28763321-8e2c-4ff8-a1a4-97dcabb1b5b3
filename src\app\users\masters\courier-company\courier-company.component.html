<div class="page-content"
    [pageAccess]="{page: utilsService.enumForPage.MASTER, action: utilsService.enumForPage.VIEW_MASTER, view: true}">
    <div class="page-title-wrapper">
        <div class="page-title-left">
            <h4>Courier Company Master</h4>
        </div>
        <div class="page-title-right">
            <button class="btn btn-sm btn-primary btn-icon-text"
                [pageAccess]="{page: utilsService.enumForPage.MASTER, action: utilsService.enumForPage.ADD_MASTER}"
                (click)="openAddEditModal(null, 'Add')">
                <i class="th th-outline-add-circle"></i>Add New
            </button>
            <button (click)="getCourierList()" class="btn btn-sm btn-icon btn-outline-white" ngbTooltip="Refresh"
                placement="left" container="body" triggers="hover">
                <i class="th th-outline-refresh-2"></i>
            </button>
        </div>
    </div>
    <!-- ---------------------------- content-area ----------------------------- -->
    <div class="content-area">
        <div class="page-filters">
            <div class="page-filters-left">
                <div class="form-group form-group-sm filter-search">
                    <div class="form-group-icon-start">
                        <i class="th th-outline-search-normal-1 icon-broder "></i>
                        <input (input)="onSearch($event, true)" [(ngModel)]="searchText" type="search"
                            class="form-control" placeholder="Search by name & Mobile">
                    </div>
                </div>

                <div class="form-group form-group-sm">
                    <div class="form-control-wrapper">
                        <input mask="separator.5" thousandSeparator="" separatorLimit="99999999999999"
                            (input)="onSearch($event, false)" [(ngModel)]="searchPrice" type="search"
                            class="form-control" placeholder="Price/kg">
                    </div>
                </div>
                <div class="form-group theme-ngselect form-group-sm">
                    <ng-select (change)="onChangeActive()" class="" placeholder="Status" [multiple]="false"
                        [clearable]="true" [items]="activeInactiveStatus" bindLabel="label" bindValue="value"
                        [(ngModel)]="activeFlag" [hideSelected]="false">
                    </ng-select>
                </div>

            </div>
            <div class="page-filters-right">
                <div class="form-group theme-ngselect form-group-sm form-group-export">
                    <div class="dropdown export-dropdown">
                        <button [disabled]="utilsService.isEmptyObjectOrNullUndefined(courierList)" type="button"
                            class="btn btn-sm btn-outline-white dropdown-toggle" data-bs-toggle="dropdown"
                            aria-expanded="false">
                            Export
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" (click)="exportReport()">Excel</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="card card-theme card-table-sticky ">
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table-theme table-hover table table-bordered table-sticky">
                        <thead class="border-less">
                            <tr>
                                <th *ngFor="let th of courierTH; index as j" [class]="th.class"
                                    [ngClass]="{'sorting-asc': sortColumn==th.keyName && sortOrder === enumForSortOrder.A, 
                                                'sorting-desc': sortColumn==th.keyName && sortOrder === enumForSortOrder.D }"
                                    (click)="!utilsService.isNullUndefinedOrBlank(th.keyName) ? onSortTH(th.keyName) : $event.preventDefault()">
                                    <div (click)="$event.stopPropagation()" *ngIf="j === 0"
                                        class="checkbox checkbox-primary checkbox-small">
                                        <input [disabled]="utilsService.isEmptyObjectOrNullUndefined(courierList)"
                                            (change)="selectAll()" [(ngModel)]="flagForSelectAll" type="checkbox"
                                            id="tbl-checkbox" class="material-inputs filled-in" />
                                        <label for="tbl-checkbox"></label>
                                    </div>
                                    {{th.displayName}}
                                </th>
                                <!-- <th class="d-flex align-items-center gap-2">
                                    <div class=" checkbox checkbox-primary checkbox-small">
                                        <input type="checkbox" id="tbl-checkbox" class="material-inputs filled-in" />
                                        <label for="tbl-checkbox"></label>
                                    </div>
                                    Name
                                </th>
                                <th>Mobile No</th>
                                <th>Address</th>
                                <th>Price</th>
                                <th>Date Created</th> -->
                                <th class="tbl-switch"
                                    *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.EDIT_MASTER])">
                                    Status</th>
                                <th class="text-center"
                                    *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.EDIT_MASTER, this.utilsService.enumForPage.DELETE_MASTER])">
                                    Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr *ngFor="let item of courierList; index as i; trackBy: trackBy">
                                <td class="tbl-user">
                                    <div class="tbl-user-checkbox-srno">
                                        <div class="checkbox checkbox-primary checkbox-small">
                                            <input (change)="selectUnselect(item.id, i, item.isSelected)"
                                                [(ngModel)]="item.isSelected" type="checkbox" id="tbl-checkbox2-{{i}}"
                                                class="material-inputs filled-in" />
                                            <label for="tbl-checkbox2-{{i}}"></label>
                                        </div>
                                        <div class="tbl-user-wrapper tbl-courier-wrapper">
                                            <div class="tbl-user-image">
                                                <!-- <img src="assets/images/Company-Logo.png" alt="valamji"> -->
                                                <img *ngIf="item.logoImg"
                                                    [src]="item.logoImg ? (utilsService.imgPath + item.logoImg) : 'assets/images/Company-Logo.png'"
                                                    alt="valamji">
                                                <ng-container *ngIf="!item.logoImg">
                                                    {{ item.courierName?.charAt(0).toUpperCase() }}
                                                </ng-container>
                                            </div>
                                            <div class="tbl-user-text-action">
                                                <div class="tbl-user-text">
                                                    <p>{{item.courierName}}</p>
                                                    <span>{{item.email}}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td>{{item.contactExtension?.countryExtension}} {{item.mobileNo}}</td>
                                <td class="tbl-description" [ngbTooltip]="item.address" placement="bottom" container="body" triggers="hover">
                                    <div>
                                        {{item.address}}
                                    </div>
                                </td>
                                <td>{{item.priceKg}} / kg</td>
                                <td>{{item.lastModifiedDate | date: 'dd/MM/YYYY h:mm a'}}</td>
                                <td class="tbl-switch"
                                    *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.EDIT_MASTER])">
                                    <div class="switch-box">
                                        <label class="switch" htmlFor="switch-{{i}}">
                                            <input (change)="onChangeStatus(item, item.isActive, i)"
                                                [(ngModel)]="item.isActive" type="checkbox" id='switch-{{i}}' />
                                            <div class="slider round"></div>
                                        </label>
                                    </div>
                                </td>
                                <td class="tbl-action"
                                    *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.EDIT_MASTER, this.utilsService.enumForPage.DELETE_MASTER])">
                                    <div class="tbl-action-group">
                                        <button class="btn btn-xs btn-light-white btn-icon"
                                            [pageAccess]="{page: this.utilsService.enumForPage.MASTER, action: this.utilsService.enumForPage.EDIT_MASTER}"
                                            (click)="openAddEditModal(item, 'Edit')" ngbTooltip="Edit"
                                            placement="bottom" container="body" triggers="hover">
                                            <i class="th th-outline-edit"></i>
                                        </button>
                                        <button *ngIf="!item.isDefault"
                                            class="btn btn-xs btn-light-danger btn-icon"
                                            [pageAccess]="{page: this.utilsService.enumForPage.MASTER, action: this.utilsService.enumForPage.DELETE_MASTER}"
                                            (click)="openDeleteCourierModal(item)" ngbTooltip="Delete" placement="left"
                                            container="body" triggers="hover">
                                            <i class="th th-outline-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr *ngIf="utilsService.isEmptyObjectOrNullUndefined(courierList)">
                                <td colspan="20" class="text-center">
                                    <span
                                        class="truncate">{{utilsService.serverVariableService.STRING_WHEN_NO_RECORDS_FOUND}}</span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="paginationbox pagination-fixed">
            <app-pagination (pagesizeData)="addPageSizeData($event)" (pageNumber)="pageNumber($event)" [page]="pageNo"
                [pageSize]="pageSize" [totalData]="totalData"></app-pagination>
        </div>
    </div>
</div>

<!-- ----------------------------------------------------------------------- -->
<!--                     Add and Edit Forms Modal Start                      -->
<!-- ----------------------------------------------------------------------- -->
<div class="modal modal-theme fade" id="courierMasterModal" tabindex="-1" aria-labelledby="courierMasterModalLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content" cdkTrapFocusAutoCapture="true" cdkTrapFocus>
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">{{statusForModal === 'Add' ? 'Add New' : 'Edit'}} Courier
                    Company</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
                        class='th th-close'></i></button>
            </div>
            <div class="modal-body" [formGroup]="courierGroup">
                <div class="row">
                    <div class="col-12">
                        <div class="form-group required">
                            <label class="form-label">Courier Company Name </label>
                            <input (keyup.enter)="onSaveCourier()" id="f"
                                [maxlength]="utilsService.validationService.MAX_50" [(ngModel)]="courierObj.courierName"
                                formControlName="name" type="text" class="form-control"
                                placeholder="Enter Courier Company Name">
                            <div class="message error-message"
                                *ngIf="courierGroup.controls['name'].hasError('required') &&  courierGroup.controls['name'].touched">
                                {{utilsService.validationService.CC_NAME_REQ}}
                            </div>
                            <div class="message error-message"
                                *ngIf="!courierGroup.controls['name'].hasError('required') && !courierGroup.controls['name'].valid && courierGroup.controls['name'].touched">
                                {{utilsService.validationService.CC_NAME_INVALID}}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">Contact Person Name </label>
                            <input (keyup.enter)="onSaveCourier()" [maxlength]="utilsService.validationService.MAX_50"
                                [(ngModel)]="courierObj.contactName" formControlName="contact_person_name" type="text"
                                class="form-control" placeholder="Enter Contact Person Name ">
                            <div class="message error-message"
                                *ngIf="courierGroup.controls['contact_person_name'].hasError('required') &&  courierGroup.controls['contact_person_name'].touched">
                                {{utilsService.validationService.CONTACT_PERSON_NAME_REQ}}
                            </div>
                            <div class="message error-message"
                                *ngIf="!courierGroup.controls['contact_person_name'].hasError('required') && !courierGroup.controls['contact_person_name'].valid && courierGroup.controls['contact_person_name'].touched">
                                {{utilsService.validationService.CONTACT_PERSON_NAME_INVALID}}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">Price / kg</label>
                            <input (keyup.enter)="onSaveCourier()" mask="separator.5" thousandSeparator=""
                                separatorLimit="99999999999999" [maxlength]="utilsService.validationService.MAX_10"
                                [(ngModel)]="courierObj.priceKg" formControlName="price" type="text"
                                class="form-control" placeholder="Eg. 10">
                            <div class="message error-message"
                                *ngIf="courierGroup.controls['price'].hasError('required') &&  courierGroup.controls['price'].touched">
                                {{utilsService.validationService.PRICE_KG_REQ}}
                            </div>
                            <div class="message error-message"
                                *ngIf="!courierGroup.controls['price'].hasError('required') && !courierGroup.controls['price'].valid && courierGroup.controls['price'].touched">
                                {{utilsService.validationService.PRICE_KG_INVALID}}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group theme-ngselect">
                            <label class="form-label">Mobile No</label>
                            <div class="form-control-wrapper">
                                <div class="input-group input-group-select">
                                    <ng-select (search)="onSearchCountryCode($event)" class="" placeholder="Ph." [multiple]="false" [clearable]="false" [items]="dropdown?.countryPh"
                                        bindLabel="countryExtension" bindValue="id" formControlName="countryExtension"
                                        [(ngModel)]="courierObj.contactCountryExtensionId">
                                    </ng-select>
                                    <input [(ngModel)]="courierObj.mobileNo" formControlName="mobile" type="text" placeholder="Enter Mobile No"
                                        class="form-control">
                                </div>
                                <div class="message error-message"
                                    *ngIf="courierGroup.controls['countryExtension'].hasError('required') && courierGroup.controls['countryExtension'].touched">
                                    {{utilsService.validationService.PHONE_NO_EXTENSION_REQ}}
                                </div>
                                <div class="message error-message"
                                    *ngIf="courierGroup.controls['mobile'].hasError('required') &&  courierGroup.controls['mobile'].touched && courierGroup.controls['countryExtension'].valid">
                                    {{utilsService.validationService.MOBILE_NUMBER_REQUIRED}}
                                </div>
                                <div class="message error-message"
                                    *ngIf="!courierGroup.controls['mobile'].hasError('required') && !courierGroup.controls['mobile'].valid && courierGroup.controls['mobile'].touched && courierGroup.controls['countryExtension'].valid">
                                    {{utilsService.validationService.MOBILE_NUMBER_INVALID}}
                                </div>
                            </div>
                            <!-- <input (keyup.enter)="onSaveCourier()" [(ngModel)]="courierObj.mobileNo"
                                formControlName="mobile" type="text" class="form-control"
                                placeholder="Enter Mobile No ">
                            <div class="message error-message"
                                *ngIf="courierGroup.controls['mobile'].hasError('required') &&  courierGroup.controls['mobile'].touched">
                                {{utilsService.validationService.MOBILE_NUMBER_REQUIRED}}
                            </div>
                            <div class="message error-message"
                                *ngIf="!courierGroup.controls['mobile'].hasError('required') && !courierGroup.controls['mobile'].valid && courierGroup.controls['mobile'].touched">
                                {{utilsService.validationService.MOBILE_NUMBER_INVALID}}
                            </div> -->
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">Email Address</label>
                            <input (keyup.enter)="onSaveCourier()" [(ngModel)]="courierObj.email"
                                formControlName="email" type="text" class="form-control"
                                placeholder="Enter Email Address ">
                            <div class="message error-message"
                                *ngIf="courierGroup.controls['email'].hasError('required') &&  courierGroup.controls['email'].touched">
                                {{utilsService.validationService.EMAIL_REQUIRED}}
                            </div>
                            <div class="message error-message"
                                *ngIf="!courierGroup.controls['email'].hasError('required') && !courierGroup.controls['email'].valid && courierGroup.controls['email'].touched">
                                {{utilsService.validationService.EMAIL_INVALID}}
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="form-group ">
                            <label class="form-label">Company Logo</label>
                            <button class='btn btn-fileupload btn-fileupload-white  mb-3 fw-400'>
                                <i class="bi bi-upload"></i>
                                Upload Company Logo
                                <input #logo (change)="onSelectLogo($event)" type="file" ref={imageRef}
                                    accept="image/x-png,image/jpeg,image/jpg">
                            </button>
                            <!-- <div *ngIf="filenameForLogo" class="message success-message">
                                {{filenameForLogo}}
                            </div>
                            <div (click)="openLink(courierObj.logoImg)" *ngIf="!filenameForLogo && courierObj.logoImg"
                                class="message success-message">
                                {{courierObj.logoImg}}
                            </div> -->
                        </div>
                        <div class="form-group">
                            <div class='attachments-upload-grid-container attachments-upload-grid-container2'
                                *ngIf="courierObj.logoImg">
                                <div class='attachments-upload-row'>
                                    <div class='attachments-upload-col'>
                                        <div class='card-attachments-upload'>
                                            <div class='attachments-image'>
                                                <img [src]="fileLocal ? fileLocal : (utilsService.imgPath + courierObj.logoImg)"
                                                    alt="valamji" />
                                            </div>
                                            <div class="attachments-text"
                                                [ngbTooltip]="filenameForLogo ? filenameForLogo : courierObj.originalName"
                                                placement="bottom" container="body" triggers="hover">
                                                <h6 class="file-name">{{courierObj.originalName ?
                                                    courierObj.originalName : filenameForLogo}}</h6>
                                            </div>
                                            <button (click)="removeProfile(null)" class="btn-close" variant="close"><i
                                                    class='th th-close'></i></button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="form-group required">
                            <label class="form-label">Address Line</label>
                            <input [maxlength]="utilsService.validationService.MAX_200" [(ngModel)]="courierObj.address"
                                formControlName="address" type="text" class="form-control"
                                placeholder="Enter Address Line">
                            <div class="message error-message"
                                *ngIf="courierGroup.controls['address'].hasError('required') &&  courierGroup.controls['address'].touched">
                                {{utilsService.validationService.ADDRESS_REQ}}
                            </div>
                            <div class="message error-message"
                                *ngIf="!courierGroup.controls['address'].hasError('required') && !courierGroup.controls['address'].valid && courierGroup.controls['address'].touched">
                                {{utilsService.validationService.ADDRESS_INVALID}}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group theme-ngselect  required">
                            <div class="form-label">Country</div>
                            <ng-select (change)="onChangeCountryState(true)" formControlName="country" class=""
                                placeholder="Select" [multiple]="false" [clearable]="false" [items]="dropdown?.country"
                                bindLabel="name" bindValue="id" [(ngModel)]="courierObj.countryId">
                            </ng-select>
                            <div class="message error-message"
                                *ngIf="courierGroup.controls['country'].hasError('required') &&  courierGroup.controls['country'].touched">
                                {{utilsService.validationService.COUNTRY_REQ}}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group theme-ngselect  required">
                            <div class="form-label">State</div>
                            <ng-select (change)="onChangeCountryState(false)" formControlName="state" class=""
                                placeholder="Select" [multiple]="false" [clearable]="false" [items]="dropdown?.state"
                                bindLabel="label" bindValue="value" [(ngModel)]="courierObj.stateId">
                            </ng-select>
                            <div class="message error-message"
                                *ngIf="courierGroup.controls['state'].hasError('required') &&  courierGroup.controls['state'].touched">
                                {{utilsService.validationService.STATE_REQ}}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group theme-ngselect  required">
                            <div class="form-label">City</div>
                            <ng-select formControlName="city" class="" placeholder="Select" [multiple]="false"
                                [clearable]="false" [items]="dropdown?.city" bindLabel="label" bindValue="value"
                                [(ngModel)]="courierObj.cityId">
                            </ng-select>
                            <div class="message error-message"
                                *ngIf="courierGroup.controls['city'].hasError('required') &&  courierGroup.controls['city'].touched">
                                {{utilsService.validationService.CITY_REQ}}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group  required">
                            <div class="form-label">Zip Code</div>
                            <input [maxlength]="utilsService.validationService.MAX_10" [(ngModel)]="courierObj.zipCode"
                                formControlName="zip" type="text" class="form-control" placeholder="Zip Code">
                            <div class="message error-message"
                                *ngIf="courierGroup.controls['zip'].hasError('required') &&  courierGroup.controls['zip'].touched">
                                {{utilsService.validationService.ZIP_REQ}}
                            </div>
                            <div class="message error-message"
                                *ngIf="!courierGroup.controls['zip'].hasError('required') && !courierGroup.controls['zip'].valid && courierGroup.controls['zip'].touched">
                                {{utilsService.validationService.ZIP_INVALID}}
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="form-group d-flex justify-content-between required ">
                            <label class="form-label">Status</label>
                            <div class="switch-box">
                                <label class="switch" htmlFor="switch">
                                    <input type="checkbox" id='switch' formControlName="status"
                                        [(ngModel)]="courierObj.isActive" />
                                    <div class="slider round"></div>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal-footer">
                <div class="modal-footer-group full-width-btn">
                    <button (click)="onSaveCourier()" type="button" class="btn btn-primary btn-icon-text"> <i
                            class="th th-outline-tick-circle"></i>
                        {{statusForModal === 'Add' ? 'Save' : 'Update'}}</button>
                    <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                      Add and Edit Forms Modal End                       -->
<!-- ----------------------------------------------------------------------- -->

<!-- ----------------------------------------------------------------------- -->
<!--                           Delete Modal Start                            -->
<!-- ----------------------------------------------------------------------- -->
<div class="modal modal-theme modal-confirmation modal-reject fade" id="deleteCourierMasterModal" tabindex="-1"
    aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
                        class='th th-close'></i></button>
            </div>
            <div class="modal-body">
                <div class="modal-confirmation-container">
                    <div class="modal-confirmation-icon">
                        <i class="th th-bold-trash"></i>
                    </div>
                    <div class="modal-confirmation-content">
                        <h5>Are You Sure?</h5>
                        <p>You want to Delete <b>{{courierObj.courierName}}</b> courier-company.</p>
                    </div>
                </div>
                <div class="modal-button-group modal-full-width-btn">
                    <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
                    <button (click)="deleteCourierCompany()" type="button" class="btn btn-primary btn-icon-text"> <i
                            class="th th-outline-tick-circle"></i>
                        Delete</button>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                           Delete Modal End                            -->
<!-- ----------------------------------------------------------------------- -->