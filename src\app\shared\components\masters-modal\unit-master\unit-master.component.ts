import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { Unit } from 'src/app/models/Unit';
import { UtilsService } from 'src/app/shared/services/utils.service';

@Component({
  selector: 'app-unit-master',
  templateUrl: './unit-master.component.html',
  styleUrls: ['./unit-master.component.css']
})
export class UnitMasterComponent implements OnInit {

  @Input({ alias: 'unitGroup', required: true }) unitGroup: FormGroup;
  @Input({ alias: 'unitObj', required: true }) unitObj: Unit;
  @Input({ alias: 'page', required: true }) page: string;
  @Input({ alias: 'allCategories', required: true }) allCategories: any[];

  @Output() onSaveUnit: EventEmitter<any> = new EventEmitter<any>();

  constructor(public utilsService: UtilsService) { }

  ngOnInit() {
  }

  onChangeCategory() {
    this.unitObj.baseUnit = "";
    if (this.unitObj.unitMasterCategory) {
      this.unitObj.baseUnit = this.allCategories.find(a => a.value == this.unitObj.unitMasterCategory)?.baseUnit;
    }
  }

}
