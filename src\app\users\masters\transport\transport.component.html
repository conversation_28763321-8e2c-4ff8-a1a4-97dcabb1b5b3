<div class="page-content"
    [pageAccess]="{page: utilsService.enumForPage.MASTER, action: utilsService.enumForPage.VIEW_MASTER, view: true}">
    <div class="page-title-wrapper">
        <div class="page-title-left">
            <h4>Transport Master</h4>
        </div>
        <div class="page-title-right">
            <button (click)="openAddEditModal(null, 'Add')" class="btn btn-sm btn-primary btn-icon-text"
                [pageAccess]="{page: utilsService.enumForPage.MASTER, action: utilsService.enumForPage.ADD_MASTER}">
                <i class="th th-outline-add-circle"></i>Add New
            </button>
            <button (click)="getTransportList()" class="btn btn-sm btn-icon btn-outline-white" ngbTooltip="Refresh"
                placement="left" container="body" triggers="hover">
                <i class="th th-outline-refresh-2"></i>
            </button>
        </div>
    </div>
    <!-- ---------------------------- content-area ----------------------------- -->
    <div class="content-area">
        <div class="page-filters">
            <div class="page-filters-left">
                <div class="form-group form-group-sm filter-search w-25">
                    <div class="form-group-icon-start">
                        <i class="th th-outline-search-normal-1 icon-broder"></i>
                        <input (input)="onSearch($event, true)" [(ngModel)]="branchCodeSearch" type="search" class="form-control"
                            placeholder="Search by branch code">
                    </div>
                </div>
                <div class="form-group form-group-sm filter-search">
                    <div class="form-group-icon-start">
                        <i class="th th-outline-search-normal-1 icon-broder"></i>
                        <input (input)="onSearch($event, false)" [(ngModel)]="searchText" type="search" class="form-control"
                            placeholder="Search by mobile">
                    </div>
                </div>
                <div class="form-group theme-ngselect form-group-sm">
                    <ng-select (change)="onChangeActive()" class="" placeholder="Transporter" [multiple]="false"
                        [clearable]="true" [items]="dropdown?.TransporterName" bindLabel="label" bindValue="value"
                        [(ngModel)]="transporterName">
                    </ng-select>
                </div>
                <div class="form-group theme-ngselect form-group-sm">
                    <ng-select (change)="onChangeActive()" class="" placeholder="Store Name" [multiple]="false"
                        [clearable]="true" [items]="dropdown?.StoreName" bindLabel="label" bindValue="value"
                        [(ngModel)]="storeName">
                    </ng-select>
                </div>
                <div class="form-group theme-ngselect form-group-sm">
                    <ng-select (change)="onChangeActive()" class="" placeholder="Booking" [multiple]="false"
                        [clearable]="true" [items]="bookingdeliveryDropdown" bindLabel="label" bindValue="value"
                        [(ngModel)]="isBooking">
                    </ng-select>
                </div>
                <div class="form-group theme-ngselect form-group-sm">
                    <ng-select (change)="onChangeActive()" class="" placeholder="Delivery" [multiple]="false"
                        [clearable]="true" [items]="bookingdeliveryDropdown" bindLabel="label" bindValue="value"
                        [(ngModel)]="isDelivery">
                    </ng-select>
                </div>
                <div class="form-group theme-ngselect form-group-sm">
                    <ng-select (change)="onChangeActive()" class="" placeholder="Status" [multiple]="false"
                        [clearable]="true" [items]="activeInactiveStatus" bindLabel="label" bindValue="value"
                        [(ngModel)]="activeFlag" [hideSelected]="false">
                    </ng-select>
                </div>
            </div>
            <div class="page-filters-right">
                <div class="form-group theme-ngselect form-group-sm form-group-export">
                    <div class="dropdown export-dropdown">
                        <button [disabled]="utilsService.isEmptyObjectOrNullUndefined(transportList)" type="button"
                            class="btn btn-sm btn-outline-white dropdown-toggle" data-bs-toggle="dropdown"
                            aria-expanded="false">
                            Export
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" (click)="exportReport()">Excel</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="card card-theme card-table-sticky ">
            <div class="card-body p-0">
                <div class="table-responsive ">
                    <table class="table-theme table-hover table table-bordered tbl-collapse table-sticky">
                        <thead class="border-less">
                            <tr class="">
                                <th *ngFor="let th of transportTH; index as j" [class]="th.class"
                                    [ngClass]="{'sorting-asc': sortColumn==th.keyName && sortOrder === enumForSortOrder.A, 
                                                                            'sorting-desc': sortColumn==th.keyName && sortOrder === enumForSortOrder.D }"
                                    (click)="!utilsService.isNullUndefinedOrBlank(th.keyName) ? onSortTH(th.keyName) : $event.preventDefault()">
                                    <div (click)="$event.stopPropagation()" *ngIf="j === 0"
                                        class="checkbox checkbox-primary checkbox-small">
                                        <input [disabled]="utilsService.isEmptyObjectOrNullUndefined(transportList)"
                                            (change)="selectAll()" [(ngModel)]="flagForSelectAll" type="checkbox"
                                            id="tbl-checkbox" class="material-inputs filled-in" />
                                        <label for="tbl-checkbox"></label>
                                    </div>
                                    {{th.displayName}}
                                </th>
                                <!-- <th class="d-flex align-items-center gap-2">
                                    <div class=" checkbox checkbox-primary checkbox-small">
                                        <input type="checkbox" id="tbl-checkbox" class="material-inputs filled-in" />
                                        <label for="tbl-checkbox"></label>
                                    </div>
                                    Transport Name
                                </th>
                                <th>Transporter ID</th>
                                <th>Store Name</th>
                                <th>Mode Of Transport</th>
                                <th>Available in cities</th>
                                <th>No of Branches</th>
                                <th>Date</th> -->
                                <th class="tbl-switch"
                                    *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.EDIT_MASTER])">
                                    Status</th>
                                <th class="text-center">Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <ng-container *ngFor="let item of transportList; index as i; trackBy: trackBy">
                                <tr>
                                    <ng-container>
                                        <td class="tbl-user">
                                            <div class="tbl-user-checkbox-srno">
                                                <div class="checkbox checkbox-primary checkbox-small">
                                                    <input type="checkbox" id="tbl-checkbox2-{{i}}"
                                                        (change)="selectUnselect(item.id, i, item.isSelected)"
                                                        class="material-inputs filled-in" [(ngModel)]="item.isSelected" />
                                                    <label for="tbl-checkbox2-{{i}}"></label>
                                                </div>
                                                <div class="tbl-user-wrapper tbl-courier-wrapper">
                                                    <div class="tbl-user-image">
                                                        <!-- <img src="assets/images/Company-Logo.png" alt="valamji"> -->
                                                        {{ item.transporterName?.charAt(0).toUpperCase() }}
                                                    </div>
                                                    <div class="tbl-user-text-action">
                                                        <div class="tbl-user-text">
                                                            <p>{{item.transporterName}}</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>{{item.transporterID}}</td>
                                        <td>{{item.storeName}}</td>
                                        <td>{{item.modeOfTransport}}</td>
                                        <td>{{item.cityCount ? item.cityCount : '-'}}</td>
                                        <td>{{item.countBranches ? item.countBranches : '-'}}</td>
                                        <td>{{item.lastModifiedDate | date: 'dd/MM/YYYY h:mm a'}}</td>
                                        <td class="tbl-switch"
                                            *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.EDIT_MASTER])">
                                            <div class="switch-box ">
                                                <label class="switch" htmlFor="switch-{{i}}">
                                                    <input type="checkbox" id='switch-{{i}}' [(ngModel)]="item.isActive"
                                                        (change)="onChangeStatus(item, item.isActive, i)" />
                                                    <div class="slider round"></div>
                                                </label>
                                            </div>
                                        </td>
                                        <td class="tbl-action">
                                            <div class="tbl-action-group">
                                                <button (click)="openAddEditModal(item, 'Edit')"
                                                    [pageAccess]="{page: this.utilsService.enumForPage.MASTER, action: this.utilsService.enumForPage.EDIT_MASTER}"
                                                    class="btn btn-xs btn-light-white btn-icon" ngbTooltip="Edit"
                                                    placement="bottom" container="body" triggers="hover">
                                                    <i class="th th-outline-edit"></i>
                                                </button>
                                                <button *ngIf="!item.isDefault"
                                                    (click)="openDeleteTransportModal(item)"
                                                    [pageAccess]="{page: this.utilsService.enumForPage.MASTER, action: this.utilsService.enumForPage.DELETE_MASTER}"
                                                    class="btn btn-xs btn-light-danger btn-icon" ngbTooltip="Delete"
                                                    placement="left" container="body" triggers="hover">
                                                    <i class="th th-outline-trash"></i>
                                                </button>
                                                <button *ngIf="item.branchTransportsGroupedByCity"
                                                    (click)="onCollapse(i)"
                                                    class="btn btn-xs btn-light-primary  btn-icon"
                                                    [ngClass]="{'collapse-arrow': item.isExpand}" data-bs-toggle="collapse"
                                                    href="#table-collapse" role="button"
                                                    aria-expanded="false" [attr.data.target]="'#table-collapse'+ i"
                                                    [attr.aria-controls]="'table-collapse'+ i">
                                                    <i class="th th-outline-arrow-right-3"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </ng-container>
                                </tr>
                                <ng-container *ngIf="item.branchTransportsGroupedByCity">
                                    <tr class="collapse" [id]="'table-collapse' + i"
                                        *ngFor="let child of item.branchTransportsGroupedByCity; index as j"
                                        [ngClass]="{'show': item.isExpand}" (click)="onCollapseChild(i, j)">

                                        <td colspan="30" class="p-0">

                                            <div class="tbl-collapse-child">
                                                <div class="tbl-collapse-child-header tbl-space-start">
                                                    <div class="tbl-collapse-child-header-left">
                                                        <ul class="accordion-header-item accordion-header-item-normal">
                                                            <li> <b>{{child.city}}</b> </li>
                                                        </ul>
                                                    </div>
                                                    <div class="tbl-collapse-child-header-right">
                                                        <button 
                                                            [ngClass]="{'collapse-arrow': child.isExpand}"
                                                            class="btn btn-xs text-color btn-icon btn-link"
                                                            data-bs-toggle="collapse" href="#table-collapse-child"
                                                            role="button" aria-expanded="false"
                                                            [attr.data.target]="'#table-collapse-child'+ j"
                                                            [attr.aria-controls]="'table-collapse-child'+ j">
                                                            <i class="th th-outline-arrow-right-3"></i>
                                                        </button>
                                                    </div>
                                                </div>

                                                <div (click)="$event.stopPropagation()" class="tbl-collapse-child-body" [id]="'table-collapse-child' + j"
                                                    [ngClass]="{'collapse': !child.isExpand}">
                                                    <table class="table-theme table-hover table table-bordered ">
                                                        <thead class="border-less">
                                                            <tr>
                                                                <th
                                                                    class="d-flex align-items-center gap-2 tbl-space-start">
                                                                    BRANCH CODE
                                                                </th>
                                                                <th>ADDRESS</th>
                                                                <th>PIN CODE</th>
                                                                <th>NAME / MOBILE</th>
                                                                <th>EMAIL</th>
                                                                <th>LANDLINE</th>
                                                                <th class="text-center">BOOKING</th>
                                                                <th class="text-center">DELIVERY</th>
                                                                <th>LOCATION LINK</th>
                                                                <th class="tbl-switch"
                                                                    *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.EDIT_MASTER])">
                                                                    Status</th>
                                                                <th class="text-center"
                                                                    *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.EDIT_MASTER, this.utilsService.enumForPage.DELETE_MASTER])">
                                                                    Action</th>
                                                            </tr>
                                                        </thead>


                                                        <tbody>
                                                            <tr *ngFor="let branch of child.branches; index as k">
                                                                <td class="tbl-user tbl-bold tbl-space-start ">
                                                                    {{branch.branchCode}}
                                                                </td>
                                                                <td class="tbl-description" [ngbTooltip]="branch.address"
                                                                        placement="bottom" container="body" triggers="hover">
                                                                       <div>{{branch.address}}</div>
                                                                </td>
                                                                <td>{{branch.pinCode}}</td>
                                                                <td>
                                                                    <span class="w-100 d-block"
                                                                        *ngFor="let v of getContacts(i, j, k)">{{!utilsService.isEmptyObjectOrNullUndefined(v)
                                                                        ?
                                                                        v : ''}}</span>
                                                                </td>
                                                                <td>
                                                                    <span class="w-100 d-block"
                                                                        *ngFor="let v of getEmails(i, j, k)">{{!utilsService.isEmptyObjectOrNullUndefined(v)
                                                                        ? v
                                                                        : ''}}</span>

                                                                </td>
                                                                <td>
                                                                    <span class="w-100 d-block"
                                                                        *ngFor="let v of getLandlines(i, j, k)">{{!utilsService.isEmptyObjectOrNullUndefined(v)
                                                                        ? v : ''}}</span>
                                                                </td>
                                                                <td [ngClass]="{'text-primary': branch.booking, 'text-danger': !branch.booking}"
                                                                    class="text-center">{{branch.booking ?
                                                                    'Y'
                                                                    : 'N'}}</td>
                                                                <td [ngClass]="{'text-primary': branch.delivery, 'text-danger': !branch.delivery}"
                                                                    class="text-center">{{branch.delivery ?
                                                                    'Y'
                                                                    : 'N'}}</td>
                                                                <td class="">
                                                                    <!-- <button
                                                                        (click)="branch.locationLink ? utilsService.openURL(branch.locationLink) : null"
                                                                        class="btn-link text-primary">
                                                                        {{branch.locationLink ? branch.locationLink :
                                                                        '-'}} </button> -->
                                                                    <span>
                                                                        <a (click)="branch.locationLink ? utilsService.openURL(branch.locationLink) : null" class="text-link">
                                                                            {{branch.locationLink ? 'Link' : '-'}}
                                                                        </a>
                                                                    </span>
                                                                </td>
                                                                <td class="tbl-switch"
                                                                    *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.EDIT_MASTER])">
                                                                    <div class=" switch-box ">
                                                                        <label class="switch" htmlFor="switch">
                                                                            <input type="checkbox" id='switch'
                                                                                [(ngModel)]="branch.isActive"
                                                                                (change)="onChangeBranchStatus(branch, branch.isActive, j, i, k)" />
                                                                            <div class="slider round"></div>
                                                                        </label>
                                                                    </div>
                                                                </td>
                                                                <td class="tbl-action"
                                                                    *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.EDIT_MASTER, this.utilsService.enumForPage.DELETE_MASTER])">
                                                                    <div class="tbl-action-group">
                                                                        <button (click)="redirectToDetails()"
                                                                            class="btn btn-xs btn-light-white btn-icon"
                                                                            [routerLink]="['/users/masters/transporter/edit-branch-transport/' + branch.id]"
                                                                            ngbTooltip="Edit" placement="bottom"
                                                                            container="body" triggers="hover">
                                                                            <i class="th th-outline-edit"></i>
                                                                        </button>
                                                                        <button
                                                                            *ngIf="!item.isDefault"
                                                                            (click)="openDeleteTransportBModal(branch)"
                                                                            class="btn btn-xs btn-light-danger btn-icon"
                                                                            ngbTooltip="Delete" placement="left"
                                                                            container="body" triggers="hover">
                                                                            <i class="th th-outline-trash"></i>
                                                                        </button>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>

                                        </td>
                                    </tr>
                                </ng-container>
                            </ng-container>
                            <tr *ngIf="utilsService.isEmptyObjectOrNullUndefined(transportList)">
                                <td colspan="20" class="text-center">
                                    <span
                                        class="truncate">{{utilsService.serverVariableService.STRING_WHEN_NO_RECORDS_FOUND}}</span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="paginationbox pagination-fixed">
            <app-pagination (pagesizeData)="addPageSizeData($event)" (pageNumber)="pageNumber($event)" [page]="pageNo"
                [pageSize]="pageSize" [totalData]="totalData"></app-pagination>
        </div>
    </div>
</div>

<!-- ----------------------------------------------------------------------- -->
<!--                     Add and Edit Forms Modal Start                      -->
<!-- ----------------------------------------------------------------------- -->
<div class="transport-modal modal modal-theme fade" id="transportAddModal" tabindex="-1"
    aria-labelledby="transportAddModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content" cdkTrapFocusAutoCapture="true" cdkTrapFocus>
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">{{statusForModal === 'Add' ? 'Add New' : 'Edit'}}
                    Transporter</h5>
                <button type="button" class="btn-close" (click)="transportAddModal.hide()"><i
                        class='th th-close'></i></button>
            </div>
            <div class="modal-body" [formGroup]="transportGroup">
                <div class="row">
                    <div class="col-12">
                        <div class="form-group required">
                            <label class="form-label"> Transporter Name </label>
                            <input id="f" [maxlength]="utilsService.validationService.MAX_50"
                                formControlName="transporterName" [(ngModel)]="transportObj.transporterName" type="text"
                                class="form-control" placeholder="Enter transporter name">
                            <div class="message error-message"
                                *ngIf="transportGroup.controls['transporterName'].hasError('required') &&  transportGroup.controls['transporterName'].touched">
                                {{utilsService.validationService.TRANSPORT_NAME_REQ}}
                            </div>
                            <div class="message error-message"
                                *ngIf="!transportGroup.controls['transporterName'].hasError('required') && !transportGroup.controls['transporterName'].valid && transportGroup.controls['transporterName'].touched">
                                {{utilsService.validationService.TRANSPORT_NAME_INVALID}}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group required">
                            <label class="form-label">Store Name</label>
                            <input [maxlength]="utilsService.validationService.MAX_50" formControlName="storeName"
                                [(ngModel)]="transportObj.storeName" type="text" class="form-control"
                                placeholder="Enter store name">
                            <div class="message error-message"
                                *ngIf="transportGroup.controls['storeName'].hasError('required') &&  transportGroup.controls['storeName'].touched">
                                {{utilsService.validationService.STORE_NAME_REQ}}
                            </div>
                            <div class="message error-message"
                                *ngIf="!transportGroup.controls['storeName'].hasError('required') && !transportGroup.controls['storeName'].valid && transportGroup.controls['storeName'].touched">
                                {{utilsService.validationService.STORE_NAME_INVALID}}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group theme-ngselect  required">
                            <div class="form-label">Mode Of Transport</div>
                            <ng-select formControlName="mode_of_transport" class="" placeholder="Mode Of Transport"
                                [multiple]="true" [clearable]="false" [items]="dropdown?.ModeOfTransport"
                                bindLabel="label" bindValue="value" [(ngModel)]="transportObj.mot">
                            </ng-select>
                            <div class="message error-message"
                                *ngIf="transportGroup.controls['mode_of_transport'].hasError('required') &&  transportGroup.controls['mode_of_transport'].touched">
                                {{utilsService.validationService.MOT_REQ}}
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="form-group theme-ngselect">
                            <div class="form-label">Upload Branch Sheet </div>
                            <div class='attachments-container'>
                                <div class='attachments-content'>
                                    <button class='btn btn-attachments'><i class="bi bi-upload"></i></button>
                                    <p>Drag and Drop Excel file here or <span class='text-primary'>Choose file</span>
                                    </p>
                                </div>
                                <input #excel (change)="onSelectExcel($event)" type="file" ref={imageRef}
                                    [multiple]="false" accept=".xlsx, .xls" />
                            </div>
                            <div *ngIf="transportObj?.excel" class="message success-message">
                                {{transportObj?.excel?.fileName}}
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="file-upload-details form-group d-flex justify-content-between">
                            <div></div>
                            <button (click)="downloadSampleFile()" type="button" class="btn btn-sm btn-outline-primary btn-icon-text">
                                <i class="bi bi-download"></i>Download Sample file
                            </button>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="form-group d-flex justify-content-between required ">
                            <label class="form-label">Status</label>

                            <div class="switch-box">
                                <label class="switch" htmlFor="switch">
                                    <input type="checkbox" id='switch' [(ngModel)]="transportObj.isActive"
                                        formControlName="status" />
                                    <div class="slider round"></div>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <div class="modal-footer-group full-width-btn">
                    <button (click)="onSaveTransport()" type="button" class="btn btn-primary btn-icon-text"> <i
                            class="th th-outline-tick-circle"></i>
                        {{statusForModal === 'Add' ? 'Save' : 'Update'}}</button>
                    <button type="button" class="btn btn-outline-white"
                        (click)="transportAddModal.hide()">Cancel</button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- ----------------------------------------------------------------------- -->
<!--                           Delete Modal Start                            -->
<!-- ----------------------------------------------------------------------- -->
<div class="modal modal-theme modal-confirmation modal-reject fade" id="deleteTransportModal" tabindex="-1"
    aria-labelledby="deleteTransportLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
                        class='th th-close'></i></button>
            </div>
            <div class="modal-body">
                <div class="modal-confirmation-container">
                    <div class="modal-confirmation-icon">
                        <i class="th th-bold-trash"></i>
                    </div>
                    <div class="modal-confirmation-content">
                        <h5>Are You Sure?</h5>
                        <p>You want to Delete <b>{{transportObj.transporterName}}</b> transport.</p>
                    </div>
                </div>
                <div class="modal-button-group modal-full-width-btn">
                    <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
                    <button (click)="deleteTransport()" type="button" class="btn btn-primary btn-icon-text"> <i
                            class="th th-outline-tick-circle"></i>
                        Delete</button>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                           Delete Modal End                            -->
<!-- ----------------------------------------------------------------------- -->

<!-- ----------------------------------------------------------------------- -->
<!--                          branch Delete Modal Start                            -->
<!-- ----------------------------------------------------------------------- -->
<div class="modal modal-theme modal-confirmation modal-reject fade" id="branchdeleteModal" tabindex="-1"
    aria-labelledby="branchdeleteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
                        class='th th-close'></i></button>
            </div>
            <div class="modal-body">
                <div class="modal-confirmation-container">
                    <div class="modal-confirmation-icon">
                        <i class="th th-bold-trash"></i>
                    </div>
                    <div class="modal-confirmation-content">
                        <h5>Are You Sure?</h5>
                        <p>You want to Delete <b>{{transportBranchObj.branchCode}}</b> branch.</p>
                    </div>
                </div>
                <div class="modal-button-group modal-full-width-btn">
                    <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
                    <button (click)="deleteTransportB()" type="button" class="btn btn-primary btn-icon-text"> <i
                            class="th th-outline-tick-circle"></i>
                        Delete</button>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                          branch Delete Modal End                            -->
<!-- ----------------------------------------------------------------------- -->


<!-- ERROR MODAL -->
<div class="transport-modal modal modal-theme fade" id="transportFileErrorModal" tabindex="-1"
    aria-labelledby="transportFileErrorModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">Error Logs ({{errorsList?.length ? errorsList?.length : 0}})</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
                        class='th th-close'></i></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table-theme table-hover table table-bordered table-sticky">
                        <thead class="border-less">
                            <tr>
                                <th>Row No</th>
                                <th>Column name</th>
                                <th>Error description</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr *ngFor="let item of errorsList; index as i">
                                <td class="tbl-bold">{{item.lineNo}}</td>
                                <td>{{item.columnName}}</td>
                                <td class="tbl-description">
                                    <div>{{item.error}}</div>
                                </td>
                                <!-- <td>
                                    <span class="w-100 d-block tbl-description" *ngFor="let v of item.error">{{v}}</span>
                                </td> -->
                            </tr>

                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <div class="modal-footer-group full-width-btn">
                    <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
</div>