import { deserializeAs, serializeAs } from 'cerialize';

export class Level {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('outerId')
    @deserializeAs('outerId')
    private _outerId: number;

    @serializeAs('createdDate')
    @deserializeAs('createdDate')
    private _createdDate: string;

    @serializeAs('levelName')
    @deserializeAs('levelName')
    private _levelName: string;

    @serializeAs('shortCode')
    @deserializeAs('shortCode')
    private _shortCode: string;

    @serializeAs('modifiedDate')
    @deserializeAs('modifiedDate')
    private _modifiedDate: string;

    @serializeAs('cityName')
    @deserializeAs('cityName')
    private _cityName: string;

    @serializeAs('assignPerson')
    @deserializeAs('assignPerson')
    private _assignPerson: string;

    @serializeAs('isActive')
    @deserializeAs('isActive')
    private _isActive: boolean;

    @serializeAs('isSelected')
    @deserializeAs('isSelected')
    private _isSelected: boolean;

    @serializeAs('cities')
    @deserializeAs('cities')
    private _cities: LocationData[];

    @serializeAs('deletedObjects')
    @deserializeAs('deletedObjects')
    private _deletedObjects: number[];

    @serializeAs('dropdown')
    @deserializeAs('dropdown')
    private _dropdown: any;

    @serializeAs('isExpand')
    @deserializeAs('isExpand')
    private _isExpand: boolean;

    @serializeAs('levelCityLinkMasterList')
    @deserializeAs('levelCityLinkMasterList')
    private _levelCityLinkMasterList: LevelCityLinkMasterList[];

    @deserializeAs('userName')
    private _userName: any;

    @deserializeAs('breachQtys')
    private _breachQtys: number;

    @deserializeAs('isDefault')
    private _isDefault: boolean;

    constructor() {
        this.isActive = false;
        this.isSelected = false;
        this.cities = [];
        this.isExpand = false;
        this.dropdown = null;
        this.levelCityLinkMasterList = []
        this.isDefault = false;
    }

    /**
     * Getter isDefault
     * @return {boolean}
     */
	public get isDefault(): boolean {
		return this._isDefault;
	}
    
    /**
     * Setter isDefault
     * @param {boolean} value
     */
	public set isDefault(value: boolean) {
		this._isDefault = value;
	}

    /**
     * Getter breachQtys
     * @return {number}
     */
	public get breachQtys(): number {
		return this._breachQtys;
	}

    /**
     * Setter breachQtys
     * @param {number} value
     */
	public set breachQtys(value: number) {
		this._breachQtys = value;
	}


    /**
     * Getter userName
     * @return {any}
     */
	public get userName(): any {
		return this._userName;
	}

    /**
     * Setter userName
     * @param {any} value
     */
	public set userName(value: any) {
		this._userName = value;
	}


    /**
     * Getter levelCityLinkMasterList
     * @return {LevelCityLinkMasterList[]}
     */
	public get levelCityLinkMasterList(): LevelCityLinkMasterList[] {
		return this._levelCityLinkMasterList;
	}

    /**
     * Setter levelCityLinkMasterList
     * @param {LevelCityLinkMasterList[]} value
     */
	public set levelCityLinkMasterList(value: LevelCityLinkMasterList[]) {
		this._levelCityLinkMasterList = value;
	}


    /**
     * Getter isExpand
     * @return {boolean}
     */
	public get isExpand(): boolean {
		return this._isExpand;
	}

    /**
     * Setter isExpand
     * @param {boolean} value
     */
	public set isExpand(value: boolean) {
		this._isExpand = value;
	}


    /**
     * Getter outerId
     * @return {number}
     */
	public get outerId(): number {
		return this._outerId;
	}

    /**
     * Setter outerId
     * @param {number} value
     */
	public set outerId(value: number) {
		this._outerId = value;
	}


    /**
     * Getter dropdown
     * @return {any}
     */
	public get dropdown(): any {
		return this._dropdown;
	}

    /**
     * Setter dropdown
     * @param {any} value
     */
	public set dropdown(value: any) {
		this._dropdown = value;
	}


    /**
     * Getter deletedObjects
     * @return {number[]}
     */
	public get deletedObjects(): number[] {
		return this._deletedObjects;
	}

    /**
     * Setter deletedObjects
     * @param {number[]} value
     */
	public set deletedObjects(value: number[]) {
		this._deletedObjects = value;
	}


    /**
     * Getter cities
     * @return {LocationData[]}
     */
	public get cities(): LocationData[] {
		return this._cities;
	}

    /**
     * Setter cities
     * @param {LocationData[]} value
     */
	public set cities(value: LocationData[]) {
		this._cities = value;
	}


    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Getter createdDate
     * @return {string}
     */
	public get createdDate(): string {
		return this._createdDate;
	}

    /**
     * Getter levelName
     * @return {string}
     */
	public get levelName(): string {
		return this._levelName;
	}

    /**
     * Getter shortCode
     * @return {string}
     */
	public get shortCode(): string {
		return this._shortCode;
	}

    /**
     * Getter modifiedDate
     * @return {string}
     */
	public get modifiedDate(): string {
		return this._modifiedDate;
	}

    /**
     * Getter cityName
     * @return {string}
     */
	public get cityName(): string {
		return this._cityName;
	}

    /**
     * Getter assignPerson
     * @return {string}
     */
	public get assignPerson(): string {
		return this._assignPerson;
	}

    /**
     * Getter isActive
     * @return {boolean}
     */
	public get isActive(): boolean {
		return this._isActive;
	}

    /**
     * Getter isSelected
     * @return {boolean}
     */
	public get isSelected(): boolean {
		return this._isSelected;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Setter createdDate
     * @param {string} value
     */
	public set createdDate(value: string) {
		this._createdDate = value;
	}

    /**
     * Setter levelName
     * @param {string} value
     */
	public set levelName(value: string) {
		this._levelName = value;
	}

    /**
     * Setter shortCode
     * @param {string} value
     */
	public set shortCode(value: string) {
		this._shortCode = value;
	}

    /**
     * Setter modifiedDate
     * @param {string} value
     */
	public set modifiedDate(value: string) {
		this._modifiedDate = value;
	}

    /**
     * Setter cityName
     * @param {string} value
     */
	public set cityName(value: string) {
		this._cityName = value;
	}

    /**
     * Setter assignPerson
     * @param {string} value
     */
	public set assignPerson(value: string) {
		this._assignPerson = value;
	}

    /**
     * Setter isActive
     * @param {boolean} value
     */
	public set isActive(value: boolean) {
		this._isActive = value;
	}

    /**
     * Setter isSelected
     * @param {boolean} value
     */
	public set isSelected(value: boolean) {
		this._isSelected = value;
	}


}

export class LocationData {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('idOfCountry')
    @deserializeAs('idOfCountry')
    private _idOfCountry: number;

    @serializeAs('idOfState')
    @deserializeAs('idOfState')
    private _idOfState: number;

    @serializeAs('idOfCity')
    @deserializeAs('idOfCity')
    private _idOfCity: number;

    @serializeAs('userIds')
    @deserializeAs('userIds')
    private _userIds: number[];

    @serializeAs('dropdown')
    @deserializeAs('dropdown')
    private _dropdown: any;

    @serializeAs('stateList')
    @deserializeAs('stateList')
    private _stateList: any[];

    @serializeAs('cityList')
    @deserializeAs('cityList')
    private _cityList: any[];


    constructor() {
        this.userIds = []
        this.dropdown = null;
        this.id = null;
        this.idOfCity = null;
        this.idOfState = null;
        this.idOfCountry = null;
        this.stateList = [];
        this.cityList = [];
    }


    /**
     * Getter cityList
     * @return {any[]}
     */
	public get cityList(): any[] {
		return this._cityList;
	}

    /**
     * Setter cityList
     * @param {any[]} value
     */
	public set cityList(value: any[]) {
		this._cityList = value;
	}


    /**
     * Getter stateList
     * @return {any[]}
     */
	public get stateList(): any[] {
		return this._stateList;
	}

    /**
     * Setter stateList
     * @param {any[]} value
     */
	public set stateList(value: any[]) {
		this._stateList = value;
	}


    /**
     * Getter dropdown
     * @return {any}
     */
	public get dropdown(): any {
		return this._dropdown;
	}

    /**
     * Setter dropdown
     * @param {any} value
     */
	public set dropdown(value: any) {
		this._dropdown = value;
	}


    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Getter idOfCountry
     * @return {number}
     */
	public get idOfCountry(): number {
		return this._idOfCountry;
	}

    /**
     * Setter idOfCountry
     * @param {number} value
     */
	public set idOfCountry(value: number) {
		this._idOfCountry = value;
	}

    /**
     * Getter idOfState
     * @return {number}
     */
	public get idOfState(): number {
		return this._idOfState;
	}

    /**
     * Setter idOfState
     * @param {number} value
     */
	public set idOfState(value: number) {
		this._idOfState = value;
	}

    /**
     * Getter idOfCity
     * @return {number}
     */
	public get idOfCity(): number {
		return this._idOfCity;
	}

    /**
     * Setter idOfCity
     * @param {number} value
     */
	public set idOfCity(value: number) {
		this._idOfCity = value;
	}

    /**
     * Getter userIds
     * @return {number[]}
     */
	public get userIds(): number[] {
		return this._userIds;
	}

    /**
     * Setter userIds
     * @param {number[]} value
     */
	public set userIds(value: number[]) {
		this._userIds = value;
	}
    

}

export class OuterLevel {

    @serializeAs('outerId')
    @deserializeAs('outerId')
    private _outerId: number;

    @serializeAs('levelMasterDTO')
    @deserializeAs('levelMasterDTO')
    private _levelMasterDTO: Level;

    constructor() {
        this.levelMasterDTO = new Level()
    }


    /**
     * Getter outerId
     * @return {number}
     */
	public get outerId(): number {
		return this._outerId;
	}

    /**
     * Setter outerId
     * @param {number} value
     */
	public set outerId(value: number) {
		this._outerId = value;
	}

    /**
     * Getter levelMasterDTO
     * @return {Level}
     */
	public get levelMasterDTO(): Level {
		return this._levelMasterDTO;
	}

    /**
     * Setter levelMasterDTO
     * @param {Level} value
     */
	public set levelMasterDTO(value: Level) {
		this._levelMasterDTO = value;
	}
    
}

export class LevelCityLinkMasterList {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('cityMaster')
    @deserializeAs('cityMaster')
    private _cityMaster: any;

    @serializeAs('stateMaster')
    @deserializeAs('stateMaster')
    private _stateMaster: any;

    @serializeAs('countryMaster')
    @deserializeAs('countryMaster')
    private _countryMaster: any;

    @serializeAs('userName')
    @deserializeAs('userName')
    private _userName: any;

    constructor() {

    }


    /**
     * Getter userName
     * @return {any}
     */
	public get userName(): any {
		return this._userName;
	}

    /**
     * Setter userName
     * @param {any} value
     */
	public set userName(value: any) {
		this._userName = value;
	}


    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Getter cityMaster
     * @return {any}
     */
	public get cityMaster(): any {
		return this._cityMaster;
	}

    /**
     * Setter cityMaster
     * @param {any} value
     */
	public set cityMaster(value: any) {
		this._cityMaster = value;
	}

    /**
     * Getter stateMaster
     * @return {any}
     */
	public get stateMaster(): any {
		return this._stateMaster;
	}

    /**
     * Setter stateMaster
     * @param {any} value
     */
	public set stateMaster(value: any) {
		this._stateMaster = value;
	}

    /**
     * Getter countryMaster
     * @return {any}
     */
	public get countryMaster(): any {
		return this._countryMaster;
	}

    /**
     * Setter countryMaster
     * @param {any} value
     */
	public set countryMaster(value: any) {
		this._countryMaster = value;
	}


}