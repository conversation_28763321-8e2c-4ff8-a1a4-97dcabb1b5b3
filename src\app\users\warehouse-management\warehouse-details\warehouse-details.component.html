<div class="page-content" [pageAccess]="{page: utilsService.enumForPage.WAREHOUSE, action: utilsService.enumForPage.VIEW_WAREHOUSE, view: true}">
  <div class="content-area details-content-area">
    <div class="details-page">
      <div class="details-page-left">
        <div class="details-page-left-header">
          <div class="details-page-header-left">

            <div class="form-group form-group-sm theme-ngselect form-border-less">
              <ng-select (change)="onChangeActive()" class="" placeholder="Status" [multiple]="false" [clearable]="true"
                [items]="activeInactiveStatus" bindLabel="label" bindValue="value" [(ngModel)]="activeFlag">
              </ng-select>
            </div>

          </div>
          <div class="details-page-header-right">
            <button [pageAccess]="{page: utilsService.enumForPage.WAREHOUSE, action: utilsService.enumForPage.ADD_WAREHOUSE}"
              (click)="openAddEditModal(null, 'Add')" class="btn btn-icon-text btn-primary btn-sm"> <i
                class="th th-outline-add-circle"></i> Add New</button>
          </div>
        </div>
        <div class="details-page-left-header-body warehouse-details-list">
          <ul class="warehouse-details-list-wrapper">
            <li (click)="selectedWarehouseIndex !== i ? changeWarehouse(i, item.id) : null"
              class="warehouse-details-list-item active" [ngClass]="{'active': selectedWarehouseIndex === i}"
              *ngFor="let item of warehouseDetailsList; index as i">
              <div class="details-list-item-icon">
                <i class="th th-outline-house"></i>
              </div>
              <div class="details-list-item-content">
                <h6 class="d-flex align-items-center gap-1">
                  <i *ngIf="item.isMainWarehouse" class="bi bi-star-fill text-warning"></i>
                  <i *ngIf="item.isSalesPoint" class="th th-bold-flag-2"></i>
                  {{item.warehouseName}}
                </h6>
                <p>Manager: {{item.userName}}</p>
              </div>
              <div class="details-list-item-status">
                <p class="" [ngClass]="{'text-success': item.isActive}">{{item.isActive ? 'Active' : 'Inactive'}}</p>
              </div>
            </li>
          </ul>
        </div>
      </div>
      <div class="details-page-right" *ngIf="!utilsService.isEmptyObjectOrNullUndefined(warehouseForEdit)">
        <div class="warehouse-details-content-wrapper">
          <div class="page-title-wrapper page-title-with-tab">
            <div class="page-title-left">
              <h4>{{warehouseForEdit?.warehouseName}}</h4>
            </div>
            <div class="page-title-right">
              <button [pageAccess]="{page: utilsService.enumForPage.WAREHOUSE, action: utilsService.enumForPage.EDIT_WAREHOUSE}"
                *ngIf="selectedTab === enumForTab.OVERVIEW" (click)="openAddEditModal(warehouseForEdit, 'Edit')"
                class="btn btn-sm btn-outline-white btn-icon-text">
                <i class="th th-outline-edit"></i>Edit
              </button>
              <button [routerLink]="['/users/purchases/carton-mapping/warehouse-allocation']"
                *ngIf="selectedTab !== enumForTab.OVERVIEW" class="btn btn-sm btn-primary btn-icon-text">
                <i class="th th-outline-box"></i>Carton Mapping
              </button>
              <button [pageAccess]="{page: utilsService.enumForPage.WAREHOUSE, action: utilsService.enumForPage.ADD_AISLE}"
                (click)="redirectFromDetails()" *ngIf="selectedTab === enumForTab.AR" class="btn btn-sm btn-primary btn-icon-text"
                [routerLink]="['/users/warehouse-management/warehouse-details/' + warehouseId + '/new-aisle']">
                <i class="th th-outline-add-circle"></i>New Aisle
              </button>
              <button
                [pageAccess]="{page: utilsService.enumForPage.WAREHOUSE, action: utilsService.enumForPage.ADD_DROP_OFF_LOCATION}"
                *ngIf="selectedTab === enumForTab.DROP_LOCATION" class="btn btn-sm btn-primary btn-icon-text"
                (click)="openAddEditDropOffLoc(null, 'Add')">
                <i class="th th-outline-add-circle"></i>New Drop-Off Location
              </button>
              <ng-container
                *ngIf="utilsService.checkPageAccess([this.utilsService.enumForPage.EDIT_WAREHOUSE, this.utilsService.enumForPage.DELETE_WAREHOUSE])">
                <div class="dropdown" *ngIf="!(warehouseForEdit?.isActive && warehouseForEdit?.isMainWarehouse)">
                  <button id="actionDropDown" data-bs-toggle="dropdown" aria-expanded="false" class="btn btn-xs btn-outline-white"
                    data-bs-popper-config='{"strategy":"fixed"}'>
                    <i class="bi bi-three-dots"></i>
                  </button>
                  <ul aria-labelledby="actionDropDown" class="dropdown-menu">
              
                    <ng-container
                      [pageAccess]="{page: utilsService.enumForPage.WAREHOUSE, action: utilsService.enumForPage.EDIT_WAREHOUSE}">
                      <li (click)="openMarkAsPrimaryDetail(warehouseForEdit)"
                        *ngIf="!warehouseForEdit?.isMainWarehouse && warehouseForEdit?.isActive"><a class="dropdown-item">
                          <i class="th th-outline-star"></i>Mark as Main Warehouse</a>
                      </li>
                      <li (click)="changeActiveInactive()" *ngIf="!warehouseForEdit?.isActive"><a class="dropdown-item"> <i
                            class="th th-outline-tick-circle"></i> Mark as Active</a>
                      </li>
                      <li (click)="changeActiveInactive()" *ngIf="warehouseForEdit?.isActive && !warehouseForEdit.isMainWarehouse"><a
                          class="dropdown-item"> <i class="th th-outline-slash"></i> Mark as Inactive </a>
                      </li>
                    </ng-container>
              
              
                    <ng-container
                      [pageAccess]="{page: utilsService.enumForPage.WAREHOUSE, action: utilsService.enumForPage.DELETE_WAREHOUSE}">
                      <hr class="m-0">
                      <li (click)="openWRDeleteModal()"><a class="dropdown-item text-danger"> <i class="th th-outline-trash"></i>
                          Delete</a>
                      </li>
                    </ng-container>
                  </ul>
                </div>
              </ng-container>
              <button class="btn btn-sm btn-icon btn-outline-white" [routerLink]="['/users/warehouse-management']">
                <i class="th th-close"></i>
              </button>
            </div>
          </div>

          <div class='nav-tabs-outer nav-tabs-style2'>
            <nav>
              <div class="nav nav-tabs " id="nav-tab" role="tablist">
                <button [ngClass]="{'active': selectedTab === enumForTab.OVERVIEW}"
                  (click)="onChangeTab(enumForTab.OVERVIEW)" class="nav-link" id="nav-overview-tab" data-bs-toggle="tab"
                  data-bs-target="#nav-overview" type="button" role="tab" aria-controls="nav-overview"
                  aria-selected="false">
                  Overview
                </button>

                <button [pageAccess]="{page: utilsService.enumForPage.WAREHOUSE, action: utilsService.enumForPage.VIEW_AR}"
                  [ngClass]="{'active': selectedTab === enumForTab.AR}" (click)="onChangeTab(enumForTab.AR)" class="nav-link"
                  id="nav-sisle-tab" data-bs-toggle="tab" data-bs-target="#nav-sisle" type="button" role="tab" aria-controls="nav-sisle"
                  aria-selected="true">Aisle & Racks
                </button>

                <button
                  [pageAccess]="{page: utilsService.enumForPage.WAREHOUSE, action: utilsService.enumForPage.VIEW_DROP_OFF_LOCATION}"
                  [ngClass]="{'active': selectedTab === enumForTab.DROP_LOCATION}" (click)="onChangeTab(enumForTab.DROP_LOCATION)"
                  class="nav-link" id="nav-drop-off-tab" data-bs-toggle="tab" data-bs-target="#nav-drop-off" type="button" role="tab"
                  aria-controls="nav-drop-off" aria-selected="false">Drop-off
                  Locations
                </button>
              </div>
            </nav>
            <div class="tab-content" id="nav-tabContent">
              <div [ngClass]="{'show active': selectedTab === enumForTab.OVERVIEW}" class="tab-pane fade"
                id="nav-overview" role="tabpanel" aria-labelledby="nav-overview-tab">
                <app-warehouse-overview [warehouseDetails]="warehouseDetails" [managerDetails]="managerDetails"
                  [warehouseEmp]="warehouseEmp" [warehouseEmpPage]="warehouseEmpPage"
                  (getAllWarehouseDetails)="getWarehouseDetails(this.selectedWarehouseIndex, this.warehouseId)"
                  (openWarehouseEmpDelete)="openWarehouseEmpDelete($event)">
                </app-warehouse-overview>
              </div>
              <div [ngClass]="{'show active': selectedTab === enumForTab.AR}" class="tab-pane fade " id="nav-sisle"
                role="tabpanel" aria-labelledby="nav-sisle-tab">
                <app-warehouse-aisle-racks [selectedItem]="selectedItem" [itemDropdown]="itemDropdown"
                  [flagForExpandAllRacks]="flagForExpandAllRacks" [aiscleList]="aiscleList" [isExpandedIDs]="isExpandedIDs"
                  [aisleStatus]="aisleStatus" [aisleRackName]="aisleRackName" (onSearchAR)="onSearchAR($event)"
                  [warehouseId]="warehouseId" (openDeleteRackModal)="openDeleteRackModal($event)"
                  (getAllWarehouseDetails)="getWarehouseDetails(this.selectedWarehouseIndex, this.warehouseId)"
                  (openDeleteAisleModal)="openDeleteAisleModal($event)" (redirectFromDetails)="redirectFromDetails()"
                  (getRackQR)="getRackQR($event)" (getAisleQR)="getAisleQR($event)">
                </app-warehouse-aisle-racks>
              </div>
              <div [ngClass]="{'show active': selectedTab === enumForTab.DROP_LOCATION}" class="tab-pane fade pt-0"
                id="nav-drop-off" role="tabpanel" aria-labelledby="nav-drop-off-tab">
                <app-warehouse-drop-off-locations [dropOffLocationList]="dropOffLocationList"
                  [dropOffLocObj]="dropOffLocObj" [dropOffFormGroup]="dropOffFormGroup" [dropOffTH]="dropOffTH"
                  [searchText]="dropOffParam?.searchText"
                  (getAllWarehouseDetails)="getWarehouseDetails(this.selectedWarehouseIndex, this.warehouseId)"
                  (openDeleteDropLocationModal)="openDeleteDropLocationModal($event.obj)"
                  (openAddEditDropOffLoc)="openAddEditDropOffLoc($event.obj, $event.status)"
                  (onSearchDO)="onSearchDO($event)" (getDropOffQR)="getDropOffQR($event)" [destroy]="destroy$">
                </app-warehouse-drop-off-locations>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="details-page-right" *ngIf="utilsService.isEmptyObjectOrNullUndefined(warehouseForEdit)">
        <app-no-record />
      </div>
    </div>
  </div>
</div>


<!-- -------------------------- Add New Warehouse -------------------------- -->
<div class="color-modal modal modal-lg modal-theme fade" id="addNewWareHouseDetails" tabindex="-1"
  aria-labelledby="addNewWareHouseLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <app-warehouse-add-edit *ngIf="showAddEditModal" [warehouseForm]="warehouseForm" [warehouseObj]="warehouseObj"
      [dropdownsList]="dropdownsList" [addNewWareHouse]="addNewWareHouseDetails" [showAddEditModal]="showAddEditModal"
      [statusForModal]="statusForModal"
      (getAllWarehouse)="getWarehouseDetails(this.selectedWarehouseIndex, this.warehouseId)" [isDetails]="true">
    </app-warehouse-add-edit>
  </div>
</div>


<!-- -------------------------- Add New DropOff -------------------------- -->
<div class="color-modal modal modal-theme fade" id="addEditDropOff" tabindex="-1" aria-labelledby="addEditDropOffLabel"
  aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">

    <div class="modal-content" [formGroup]="dropOffFormGroup" cdkTrapFocusAutoCapture="true" cdkTrapFocus
      *ngIf="showAddEditDropOff">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel">{{statusForModal === 'Add' ? 'Add New' : 'Edit'}} Drop-off
          Location</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
            class='th th-close'></i></button>
      </div>
      <div class="modal-body">

        <div class="row">
          <div class="col-md-12">
            <div class="form-group required">
              <label class="form-label">Location Name</label>
              <input (keyup.enter)="onSaveDropOfFLoc()" id="f1" [maxLength]="utilsService.validationService.MAX_50"
                [(ngModel)]="dropOffLocObj.locationName" formControlName="locationName" type="text" class="form-control"
                placeholder="Enter Location Name">
              <div class="message error-message"
                *ngIf="dropOffFormGroup.controls['locationName'].hasError('required') &&  dropOffFormGroup.controls['locationName'].touched">
                {{utilsService.validationService.LOC_NAME_REQ}}
              </div>
              <div class="message error-message"
                *ngIf="!dropOffFormGroup.controls['locationName'].hasError('required') && !dropOffFormGroup.controls['locationName'].valid && dropOffFormGroup.controls['locationName'].touched">
                {{utilsService.validationService.LOC_NAME_INVALID}}
              </div>
            </div>
          </div>
          <div class="col-md-12">
            <div class="form-group">
              <label class="form-label">Note</label>
              <textarea [(ngModel)]="dropOffLocObj.description" formControlName="description"
                [maxLength]="utilsService.validationService.MAX_500" class="form-control"
                placeholder="Enter note"></textarea>
              <div class="message error-message"
                *ngIf="!dropOffFormGroup.controls['description'].hasError('required') && !dropOffFormGroup.controls['description'].valid && dropOffFormGroup.controls['locationName'].touched">
                {{utilsService.validationService.DES_INVALID}}
              </div>
            </div>
          </div>

          <div class="col-12">
            <div class="form-group d-flex justify-content-between required ">
              <label class="form-label">Status</label>
              <div class="switch-box">
                <label class="switch" htmlFor="switch">
                  <input type="checkbox" id='switch' [(ngModel)]="dropOffLocObj.isActive" formControlName="isActive" />
                  <div class="slider round"></div>
                </label>
              </div>
            </div>
          </div>


        </div>

      </div>
      <div class="modal-footer">
        <div class="modal-footer-group full-width-btn">
          <button (click)="onSaveDropOfFLoc()" type="button" class="btn btn-primary btn-icon-text"> <i
              class="th th-outline-tick-circle"></i>
            {{statusForModal === 'Add' ? 'Save' : 'Update'}}</button>
          <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- -------------------------- Add New DropOff -------------------------- -->


<!-- ----------------------------------------------------------------------- -->
<!--                           Delete DropOff Start                            -->
<!-- ----------------------------------------------------------------------- -->
<div class="modal modal-theme modal-confirmation modal-reject fade" id="deleteDropOff" tabindex="-1"
  aria-labelledby="deleteDropOffLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
            class='th th-close'></i></button>
      </div>
      <div class="modal-body">
        <div class="modal-confirmation-container">
          <div class="modal-confirmation-icon">
            <i class="th th-bold-trash"></i>
          </div>
          <div class="modal-confirmation-content">
            <h5>Are You Sure?</h5>
            <p>You want to Delete <b>{{dropOffLocObj.locationName}}</b> Drop-off Location.</p>
          </div>
        </div>
        <div class="modal-button-group modal-full-width-btn">
          <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
          <button (click)="deleteDropLocation()" type="button" class="btn btn-primary btn-icon-text"> <i
              class="th th-outline-tick-circle"></i>
            Delete</button>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                           Delete DropOff End                            -->
<!-- ----------------------------------------------------------------------- -->

<div class="modal modal-theme modal-confirmation modal-reject fade" id="deleteWarehouseDetailModal" tabindex="-1"
  aria-labelledby="deleteWarehouseDetailModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header p-3 pb-0">
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
            class='th th-close'></i></button>
      </div>
      <div class="modal-body">
        <div class="modal-confirmation-container">
          <div class="modal-confirmation-content  mt-0">
            <h5 class="text-start">Delete Warehouse ({{warehouseForEdit?.warehouseName}})</h5>
          </div>

          <div class="form-group form-group-sm">
            <div class="form-label text-start">Password to delete warehouse</div>
            <div class="form-group-icon-start form-group-password"><i class="th th-outline-lock"></i>
              <input placeholder="Enter Password" [formControl]="passwordControl"
                [type]="flagForPasswordHideShow ? 'password' : 'text'" class="form-control">
              <button (click)="flagForPasswordHideShow = !flagForPasswordHideShow" class="btn-password">
                <i class="th th th-outline-eye"
                  [ngClass]="{'th th-outline-eye': flagForPasswordHideShow === false, 'th th-outline-eye-slash': flagForPasswordHideShow}"></i></button>
            </div>
            <div class="message error-message" *ngIf="passwordControl.hasError('required') && passwordControl.touched">
              {{utilsService.validationService.PASSWORD_REQUIRED}}
            </div>
          </div>
        </div>
        <div class="modal-button-group">
          <button (click)="deleteERDetail()" type="button" class="btn btn-danger btn-icon-text"> <i
              class="th th-outline-tick-circle"></i>
            Delete Warehouse</button>
          <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
        </div>
      </div>
    </div>
  </div>
</div>


<!-- ----------------------------------------------------------------------- -->
<!--                           Delete Aisle Start                            -->
<!-- ----------------------------------------------------------------------- -->
<div class="modal modal-theme modal-confirmation modal-reject fade" id="deleteAisleModal" tabindex="-1"
  aria-labelledby="deleteAisleModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
            class='th th-close'></i></button>
      </div>
      <div class="modal-body">
        <div class="modal-confirmation-container">
          <div class="modal-confirmation-icon">
            <i class="th th-bold-trash"></i>
          </div>
          <div class="modal-confirmation-content">
            <h5>Are You Sure?</h5>
            <p>You want to Delete <b>{{aisleObj.aisleName}}</b> Aisle.</p>
          </div>
        </div>
        <div class="modal-button-group modal-full-width-btn">
          <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
          <button (click)="onDeleteAisle()" type="button" class="btn btn-primary btn-icon-text"> <i
              class="th th-outline-tick-circle"></i>
            Delete</button>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                           Delete Aisle End                            -->
<!-- ----------------------------------------------------------------------- -->


<!-- ----------------------------------------------------------------------- -->
<!--                           Delete Rack Start                            -->
<!-- ----------------------------------------------------------------------- -->
<div class="modal modal-theme modal-confirmation modal-reject fade" id="deleteRackModal" tabindex="-1"
  aria-labelledby="deleteRackModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
            class='th th-close'></i></button>
      </div>
      <div class="modal-body">
        <div class="modal-confirmation-container">
          <div class="modal-confirmation-icon">
            <i class="th th-bold-trash"></i>
          </div>
          <div class="modal-confirmation-content">
            <h5>Are You Sure?</h5>
            <p>You want to Delete <b>{{rackObj.rackName}}</b> Rack.</p>
          </div>
        </div>
        <div class="modal-button-group modal-full-width-btn">
          <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
          <button (click)="onDeleteRack()" type="button" class="btn btn-primary btn-icon-text"> <i
              class="th th-outline-tick-circle"></i>
            Delete</button>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                           Delete Rack End                            -->
<!-- ----------------------------------------------------------------------- -->

<!-- MARK AS PRIMARY -->
<div class="modal modal-theme modal-confirmation modal-warning-two fade" id="markAsPrimaryDetailModal" tabindex="-1"
  aria-labelledby="markAsPrimaryDetailModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
            class='th th-close'></i></button>
      </div>
      <div class="modal-body">

        <div class="modal-confirmation-container">
          <div class="modal-confirmation-icon">
            <i class="th th-outline-info-circle"></i>
          </div>
          <div class="modal-confirmation-content">
            <h5>Are You Sure?</h5>
            <p>Do you want make this warehouse as main warehouse? </p>
            <p><b>Note:</b> Existing main warehouse will not be treated as main warehouse</p>
          </div>
        </div>

        <div class="modal-button-group modal-full-width-btn">
          <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
          <button (click)="onMarkAsPrimaryDetail()" type="button" class="btn btn-primary btn-icon-text"> <i
              class="th th-outline-tick-circle"></i>Confirm</button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- ----------------------------------------------------------------------- -->
<!--                           Delete Warehouse Emp Start                            -->
<!-- ----------------------------------------------------------------------- -->
<div class="modal modal-theme modal-confirmation modal-reject fade" id="warehouseRemoveModal" tabindex="-1"
  aria-labelledby="warehouseRemoveModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
            class='th th-close'></i></button>
      </div>
      <div class="modal-body">
        <div class="modal-confirmation-container">
          <div class="modal-confirmation-icon">
            <i class="th th-close"></i>
          </div>
          <div class="modal-confirmation-content">
            <h5>Are You Sure?</h5>
            <p>You want to remove <b>"{{wareHouseEmpObj.name}}"</b> Warehouse Employee.</p>
          </div>
        </div>
        <div class="modal-button-group modal-full-width-btn">
          <button type="button" class="btn btn-outline-white" data-bs-dismiss="modal">Cancel</button>
          <button (click)="onDeleteEmpW()" type="button" class="btn btn-primary btn-icon-text"> <i
              class="th th-outline-tick-circle"></i>
            Remove</button>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- ----------------------------------------------------------------------- -->
<!--                          Delete Warehouse Emp End                            -->
<!-- ----------------------------------------------------------------------- -->


<!-- Drop Off Location -->
<div id="dropOffSingle" [hidden]="true" class="row g-2">
  <div class="col-12 col-sm-6 col-md-4 col-lg-3 mb-3" *ngFor="let image of base64SourceSingle">
    <img [src]="image" alt="valamji" >
  </div>
</div>

