import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgOtpInputComponent, NgOtpInputConfig } from 'ng-otp-input';
import { CountdownComponent } from 'ngx-countdown';
import { EnumForForgetPassword } from 'src/app/shared/enums/EnumForForgetPassword';
import { UtilsService } from 'src/app/shared/services/utils.service';

@Component({
  selector: 'app-forgot-password',
  templateUrl: './forgot-password.component.html',
  styleUrls: ['./forgot-password.component.css']
})
export class ForgotPasswordComponent implements OnInit {

  @ViewChild('cd') counter: CountdownComponent;
  @ViewChild('ngOtpInput', { static: false }) ngOtpInputRef: ElementRef;
  @ViewChild(NgOtpInputComponent, { static: false }) ngOtpInput: NgOtpInputComponent;
  enumForStep = EnumForForgetPassword;
  stepForForgotPassword: string = this.enumForStep.EMAIL;
  forgotPassForm: FormGroup;

  otpInputConfig: NgOtpInputConfig = {
    length: 4,
    placeholder: '-',
    inputClass: 'form-control',
    containerClass: 'otp-group',
  }

  userObj = {
    email: null
  };
  otpValue: any;
  isResend: boolean = false;
  isMobile: boolean = false;
  timerAction: string;

  constructor(public utilsService: UtilsService, private fb: FormBuilder) {
    this.timerAction = 'start'
  }

  ngOnInit() {
    this.utilsService.clearDataLocally('email')
    this.forgotForm();
  }

  forgotForm(): void {
    this.forgotPassForm = this.fb.group({
      email: ['', [Validators.required, Validators.compose([Validators.pattern(this.utilsService.validationService.PATTERN_FOR_EMAIL_OR_PHONE_NO)])]],
    });
  }

  otpGeneration(resent: boolean) {

    if (this.forgotPassForm.invalid) {
      this.forgotPassForm.markAllAsTouched();
      return;
    }

    const param = {
      isResend: this.isResend,
      email: this.userObj.email
    }

    this.utilsService.postMethodAPI(true, this.utilsService.serverVariableService.OTP_GENERATION, param, (response) => {
      if (!this.utilsService.isNullUndefinedOrBlank(response)) {
        this.stepForForgotPassword = this.enumForStep.OTP_VERIFICATION;
        let flag = (/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/).test(this.userObj.email);
        if (!flag) {
          this.isMobile = true;
        }
        if (resent) {
          //restart timer
          this.timerAction = 'start'
        }
      }
    })
  }

  onSubmitOTP() {

    if (this.otpValue?.length === 4) {

      const param = {
        email: this.userObj.email,
        otp: this.otpValue,
      };

      this.utilsService.postMethodAPI(true, this.utilsService.serverVariableService.OTP_VERIFICATION, param, (response) => {
        if (!this.utilsService.isNullUndefinedOrBlank(response)) {
          localStorage.setItem('email', this.userObj.email)
          this.utilsService.redirectTo('/auth/set-new-password')
        }
      })
    }
  }

  onOtpChange(event) {
    this.otpValue = event;
  }

  resendOtp() {
    this.ngOtpInput.setValue('')
    this.otpValue = '';
    this.isResend = true;
    this.otpGeneration(true);
  }

  onBack() {
    this.stepForForgotPassword = this.enumForStep.EMAIL
  }

  handleEvent(event: any) {
    this.timerAction = event.action
  }
}
