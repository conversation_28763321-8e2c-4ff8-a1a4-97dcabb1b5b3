<div class=" page-content">
  <div class="page-title-wrapper">
    <div class="page-title-left">
      <h4>Record Payment</h4>
    </div>
    <div class="page-title-right">
      <button class="btn btn-sm btn-icon btn-outline-white" [routerLink]="['/users/purchases/payments']"
        ngbTooltip="Close" placement="left" container="body" triggers="hover">
        <i class="th th-close"></i>
      </button>
    </div>
  </div>
  <div class="content-area">
    <div class="card card-theme card-forms">
      <div class="card-body">
        <div class="row" [formGroup]="form">
          <div class="col-lg-5 col-md-6 col-sm-12">
            <div class="form-group theme-ngselect form-group-inline-control required">
              <label class="form-label">Supplier</label>
              <div class="form-control-wrapper">
                <ng-select placeholder="Select Supplier" [multiple]="false" [clearable]="false"
                  [items]="dropdownData.suppliers" bindLabel="displayName" bindValue="id" formControlName="supplierId">
                </ng-select>
                <div class="message error-message"
                  *ngIf="form.controls['supplierId'].hasError('required') && form.controls['supplierId'].touched">
                  {{utilsService.validationService.SUPPLIER_REQ}}
                </div>
              </div>
            </div>
            <div class="form-group form-group-inline-control">
              <label class="form-label"></label>
              <div class="form-control-wrapper">
                <div class="form-group-button">
                  <div class="card card-amount">
                    <div class="card-body">
                      <h6>Credit</h6>
                      <p class="text-success">{{formValue.credit ? formValue.credit : 0}}</p>
                    </div>
                  </div>
                  <div class="card card-amount">
                    <div class="card-body">
                      <h6>Due Amount</h6>
                      <p>{{formValue.dueAmount ? formValue.dueAmount : 0}}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="form-group form-group-inline-control required">
              <label class="form-label">Payment Date</label>
              <div class="form-control-wrapper">
                <app-date-time-picker formControlName="paymentDate" [placeholder]="'Select Date'" [timer]="false"
                  [displayFormat]="'DD/MM/YYYY'" [outputDateFormat]="'YYYY-MM-DD'" />
                <div class="message error-message"
                  *ngIf="form.controls['paymentDate'].hasError('required') && form.controls['paymentDate'].touched">
                  {{utilsService.validationService.PAYMENT_DATE_REQ}}
                </div>
              </div>
            </div>
            <div class="form-group form-group-inline-control required">
              <label class="form-label">Payment Amount</label>
              <div class="form-control-wrapper">
                <div class="input-group">
                  <span class="input-group-text">RMB</span>
                  <input mask="separator.4" thousandSeparator="" [maxlength]="utilsService.validationService.MAX_15"
                    type="text" class="form-control" placeholder="Enter Amount" formControlName="paymentAmountRMB">
                </div>
                <div class="message error-message"
                  *ngIf="form.controls['paymentAmountRMB'].hasError('required') && form.controls['paymentAmountRMB'].touched">
                  {{utilsService.validationService.PAYMENT_AMOUNT_REQ_RMB}}
                </div>
                <div class="message error-message"
                  *ngIf="!form.controls['paymentAmountRMB'].hasError('required') && !form.controls['paymentAmountRMB'].valid && form.controls['paymentAmountRMB'].touched">
                  {{utilsService.validationService.PAYMENT_AMOUNT_INVALID_RMB}}
                </div>
              </div>
            </div>
            <div class="form-group form-group-inline-control required">
              <label class="form-label">Conversion Rate <br>(RMB to INR)</label>
              <div class="form-control-wrapper">
                <input type="text" class="form-control" mask="separator.5" thousandSeparator=""
                  [maxlength]="utilsService.validationService.MAX_15" placeholder="Enter Conversion Rate"
                  formControlName="conversionRate">
                <div class="message error-message"
                  *ngIf="form.controls['conversionRate'].hasError('required') && form.controls['conversionRate'].touched">
                  {{utilsService.validationService.CONV_RATE_REQ}}
                </div>
                <div class="message error-message"
                  *ngIf="!form.controls['conversionRate'].hasError('required') && !form.controls['conversionRate'].valid && form.controls['conversionRate'].touched">
                  {{utilsService.validationService.CONV_RATE_INVALID}}
                </div>
              </div>
            </div>
            <div class="form-group form-group-inline-control ">
              <label class="form-label">Amount (INR)</label>
              <div class="form-control-wrapper">
                <div class="input-group">
                  <span class="input-group-text">Rs</span>
                  <input type="text" class="form-control" placeholder="Calculated (RMB × Rate)"
                    formControlName="paymentAmountINR">
                </div>
              </div>
            </div>
            <div class="form-group theme-ngselect form-group-inline-control required">
              <label class="form-label">Payment Method</label>
              <div class="form-control-wrapper">
                <ng-select placeholder="Select Payment Method" [multiple]="false" [clearable]="false"
                  [items]="dropdownData.paymentTypes" bindLabel="paymentTypeName" bindValue="id"
                  formControlName="paymentTypeId">
                </ng-select>
                <div class="message error-message"
                  *ngIf="form.controls['paymentTypeId'].hasError('required') && form.controls['paymentTypeId'].touched">
                  {{utilsService.validationService.PAYMENT_METHOD_REQ}}
                </div>
              </div>
            </div>
            <div class="form-group theme-ngselect form-group-inline-control required">
              <label class="form-label">GSTIN</label>
              <div class="form-control-wrapper">
                <ng-select placeholder="Select GSTIN" [multiple]="false" [clearable]="false"
                  [items]="dropdownData.bankGroups" bindLabel="bankName" bindValue="id" formControlName="bankGroupId">
                </ng-select>
                <div class="message error-message"
                  *ngIf="form.controls['bankGroupId'].hasError('required') && form.controls['bankGroupId'].touched">
                  {{utilsService.validationService.GSTIN_REQ}}
                </div>
              </div>
            </div>
            <div class="form-group form-group-inline-control required">
              <label class="form-label">Paid By</label>
              <div class="form-control-wrapper">
                <input type="text" class="form-control" placeholder="Enter Paid By" formControlName="paidBy"
                  [maxlength]="utilsService.validationService.MAX_100">
                <div class="message error-message"
                  *ngIf="form.controls['paidBy'].hasError('required') && form.controls['paidBy'].touched">
                  {{utilsService.validationService.PAID_BY_REQ}}
                </div>
              </div>
            </div>
            <div class="form-group form-group-inline-control">
              <div class="form-label">Note</div>
              <div class="form-control-wrapper">
                <textarea class="form-control" placeholder="Enter Note" formControlName="notes"
                  [maxlength]="utilsService.validationService.MAX_1000"></textarea>
              </div>
            </div>
          </div>

          <div class="col-lg-5 col-md-6 col-sm-12" (dragover)="onSelectAttachments($event);doc.value = ''"
            (drop)="onSelectAttachments($event);doc.value = ''" (paste)="onSelectAttachments($event)">
            <div class="d-flex flex-column h-100">
              <div class="attachments-wrapper">
                <div class="form-group">
                  <div class="form-label">Upload Files<i class="th th-outline-info-circle ms-1"
                          [ngbTooltip]="utilsService.validationService.DOC_INFO" placement="bottom"
                          container="body" triggers="hover"></i>
                  </div>
                </div>
                <div class='attachments-container'>
                  <div class='attachments-content'>
                    <button class='btn btn-attachments'><i class="bi bi-upload"></i></button>
                    <p>Drag and Drop file here or <span class='text-primary'>Choose file</span></p>
                  </div>
                  <input #doc type="file" ref={imageRef} multiple (change)="onSelectAttachments($event);doc.value = ''" />
                </div>
                <div class='attachments-upload-grid-container attachments-upload-grid-container2'>
                  <div class='attachments-upload-row'>
                    @for (item of paymentObj()?.paymentImgs; track $index) {
                      <div class="attachments-upload-col">
                        <div class="card-attachments-upload">
                          <div class="attachments-image">
                            @if (utilsService.isImage(item.originalName)) {
                              <img
                                loading="lazy"
                                (click)="openLink(item.file ? null : item.formattedName, item.file ? item.formattedName : null)"
                                [src]="item.formattedName ? (item.file ? item.formattedName : utilsService.imgPath + item.formattedName) : null"
                                alt="valamji"
                              />
                            } @else if (utilsService.isMedia(item.originalName)) {
                              <img
                                (click)="openLink(item.file ? null : item.formattedName, item.file ? item.formattedName : null)"
                                src="assets/images/files/file-video.svg"
                                alt="valamji"
                              />
                            } @else if (utilsService.isExcel(item.originalName)) {
                              <img
                                (click)="openLink(item.file ? null : item.formattedName, item.file ? item.formattedName : null)"
                                src="assets/images/files/file-excel.svg"
                                alt="valamji"
                              />
                            } @else if (utilsService.isDocument(item.originalName)) {
                              <img
                                (click)="openLink(item.file ? null : item.formattedName, item.file ? item.formattedName : null)"
                                src="assets/images/files/file-pdf.svg"
                                alt="valamji"
                              />
                            }
                          </div>
                          <div class="attachments-text">
                            <h6 class="file-name">{{ item.originalName }}</h6>
                          </div>
                          <button (click)="removeAttachment($index, item)" class="btn-close" variant="close"><i class="th th-close"></i></button>
                        </div>
                      </div>
                    }
                  </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class='bottombar-wrapper bottom-fixed'>
      <div class='bottombar-container'>
        <div class='bottombar-left'>
          <button (click)="onSave()" type="button" class="btn btn-primary btn-icon-text btn-sm"> <i
              class="th th-outline-tick-circle"></i>Submit</button>
          <button type="button" class="btn btn-outline-white btn-icon-text btn-sm"><i
              class="th th-outline-close-circle"></i>Cancel</button>
        </div>
        <div class='bottombar-right'>

        </div>
      </div>
    </div>
  </div>
</div>

<!-- Purchases > Payment > Edit screen  -->
<div *ngIf="false" class=" page-content">
  <div class="page-title-wrapper">
    <div class="page-title-left">
      <h4>Edit Record Bill Payment</h4>
    </div>
    <div class="page-title-right">
      <button class="btn btn-sm btn-icon btn-outline-white" [routerLink]="['/users/purchases/payments']"
        ngbTooltip="Close" placement="left" container="body" triggers="hover">
        <i class="th th-close"></i>
      </button>
    </div>
  </div>
  <div class="content-area">
    <div class="card card-theme card-forms">
      <div class="card-body">
        <div class="row">
          <div class="col-lg-5 col-md-6 col-sm-12">
            <div class="form-group theme-ngselect form-group-inline-control required">
              <label class="form-label">Select Supplier</label>
              <div class="form-control-wrapper">
                <ng-select placeholder="Supplier" [multiple]="false" [clearable]="false" bindLabel="name"
                  bindValue="id" appendTo="body" disabled>
                </ng-select>
              </div>
            </div>

            <div class="form-group form-group-inline-control required">
              <label class="form-label">Payment Date</label>
              <div class="form-control-wrapper">
                <div class="form-group-icon-end">
                  <i class="th th-outline-calendar-1"></i>
                  <input type="text" class="form-control" placeholder="Enter Date" disabled>
                </div>
              </div>
            </div>
            <div class="form-group form-group-inline-control required">
              <label class="form-label">Payment Amount</label>
              <div class="form-control-wrapper">
                <div class="input-group">
                  <span class="input-group-text">CBM</span>
                  <input type="text" class="form-control" placeholder="Enter Amount" aria-label="Amount"
                    aria-describedby="button-addon1" disabled>
                </div>
              </div>
            </div>
            <div class="form-group form-group-inline-control required">
              <label class="form-label">Conversion Rate <br>(RMB to INR)</label>
              <div class="form-control-wrapper">
                <input type="text" class="form-control" placeholder="" disabled>
              </div>
            </div>
            <div class="form-group form-group-inline-control ">
              <label class="form-label">Amount (INR)</label>
              <div class="form-control-wrapper">
                <div class="input-group">
                  <span class="input-group-text">Rs</span>
                  <input type="text" class="form-control" placeholder="Calculated (RMB × Rate)" aria-label="Amount"
                    aria-describedby="button-addon1" disabled>
                </div>
              </div>
            </div>
            <div class="form-group theme-ngselect form-group-inline-control required">
              <label class="form-label">Payment Method</label>
              <div class="form-control-wrapper">
                <ng-select placeholder="Select Cash / Bank / Cheque / UPI" [multiple]="false" [clearable]="false"
                  bindLabel="name" bindValue="id" appendTo="body" disabled>
                </ng-select>
              </div>
            </div>
            <div class="form-group theme-ngselect form-group-inline-control required">
              <label class="form-label">GSTIN</label>
              <div class="form-control-wrapper">
                <ng-select placeholder="Select GSTIN" [multiple]="false" [clearable]="false" bindLabel="name"
                  bindValue="id" appendTo="body" disabled>
                </ng-select>
              </div>
            </div>
            <div class="form-group form-group-inline-control required">
              <label class="form-label">Paid By</label>
              <div class="form-control-wrapper">
                <input type="text" class="form-control" placeholder="" disabled>
              </div>
            </div>
            <div class="form-group form-group-inline-control">
              <div class="form-label">Note</div>
              <div class="form-control-wrapper">
                <textarea class="form-control" placeholder="Enter Note" disabled></textarea>
              </div>
            </div>

          </div>

          <div class="col-lg-5 col-md-6 col-sm-12">
            <div class="d-flex flex-column h-100">
              <div class="attachments-wrapper">
                <div class='attachments-container h-100'>
                  <div class='attachments-content'>
                    <button class='btn btn-attachments'><i class="bi bi-upload"></i></button>
                    <p>Drag and Drop file here or <span class='text-primary'>Choose file</span></p>
                  </div>
                  <input type="file" ref={imageRef} multiple />
                </div>
                <div class='attachments-upload-grid-container attachments-upload-grid-container2'>
                  <div class='attachments-upload-row'>
                    <div class='attachments-upload-col'>
                      <div class='card-attachments-upload'>
                        <div class='attachments-image'>
                          <img src="assets/images/avatar.jpg" alt="valamji" />
                        </div>
                        <div class="attachments-text">
                          <h6 class="file-name">Filename.jpg</h6>
                          <p class="file-size">Size: 5mb</p>
                        </div>
                        <button class="btn-close" variant="close"><i class='th th-close'></i></button>
                      </div>
                      <div class="radio radio-primary">
                        <input type="radio" id="thumb" name="thumb" checked="">
                        <label for="thumb">Mark default</label>
                      </div>
                    </div>
                    <div class='attachments-upload-col'>
                      <div class='card-attachments-upload'>
                        <div class='attachments-image'>
                          <img src="assets/images/avatar.jpg" alt="valamji" />
                        </div>
                        <div class="attachments-text">
                          <h6 class="file-name">Filename.jpg</h6>
                          <p class="file-size">Size: 5mb</p>
                        </div>
                        <button class="btn-close" variant="close"><i class='th th-close'></i></button>
                      </div>
                      <div class="radio radio-primary">
                        <input type="radio" id="thumb2" name="thumb" checked="">
                        <label for="thumb2">Mark default</label>
                      </div>
                    </div>
                    <div class='attachments-upload-col'>
                      <div class='attachments-container attachments-container2'>
                        <div class='attachments-content'>
                          <button class='btn btn-primary btn-icon btn-sm btn-round'><i
                              class="th th-outline-add"></i></button>
                        </div>
                        <input type="file" ref={imageRef} multiple />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="row mt-4">
          <div class="col-lg-12 col-md-12 col-sm-12">
            <div class="table-responsive">
              <table class="table-theme table table-bordered table-sticky">
                <thead class="border-less">
                  <tr>
                    <th>
                      Receive ID
                    </th>
                    <th>Date</th>
                    <th>Amount (RMB)</th>
                    <th>Conversion Rate</th>
                    <th>Amount In INR</th>
                    <th>Payment Status</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let item of [1,2,3,4]">
                    <td>54554554</td>
                    <td>08/07/2025</td>
                    <td>300</td>
                    <td>30</td>
                    <td>50000</td>
                    <td>
                      <span class="text-success"> Paid</span>
                    </td>
                  </tr>
                </tbody>
                <tfoot>
                  <tr class="tbl-add-new">
                    <td colspan="100">
                      <button class="btn btn-sm btn-link btn-icon-text text-primary"> <i class="th-bold-add-circle"></i>
                        Add New Row
                      </button>
                    </td>
                  </tr>
                </tfoot>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class='bottombar-wrapper bottom-fixed'>
      <div class='bottombar-container'>
        <div class='bottombar-left'>
          <button type="button" class="btn btn-primary btn-icon-text btn-sm"> <i
              class="th th-outline-tick-circle"></i>ok</button>
        </div>
        <div class='bottombar-right'>

        </div>
      </div>
    </div>
  </div>
</div>