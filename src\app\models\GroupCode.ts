import { deserializeAs, serializeAs } from 'cerialize';

export class GroupCode {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('isActive')
    @deserializeAs('isActive')
    private _isActive: boolean;

    @deserializeAs('isSelected')
    private _isSelected: boolean;

    @serializeAs('groupCodeName')
    @deserializeAs('groupCodeName')
    private _groupCodeName: string;

    @serializeAs('groupName')
    @deserializeAs('groupName')
    private _groupName: string;

    @serializeAs('groupCodeId')
    @deserializeAs('groupCodeId')
    private _groupCodeId: string;

    @serializeAs('formattedCategoryName')
    @deserializeAs('formattedCategoryName')
    private _formattedCategoryName: string;

    @serializeAs('deletedDocsID')
    @deserializeAs('deletedDocsID')
    private _deletedDocsID: any[];

    @serializeAs('linkedItemID')
    @deserializeAs('linkedItemID')
    private _linkedItemID: number[];

    @serializeAs('deleteLinkedItemID')
    @deserializeAs('deleteLinkedItemID')
    private _deleteLinkedItemID: number[];

    @serializeAs('categoryId')
    @deserializeAs('categoryId')
    private _categoryId: number;

    @deserializeAs('docs')
    private _docs: any[];

    @deserializeAs('image')
    private _image: string;

    constructor() {
        this.isActive = false;
        this.isSelected = false;
        this.docs = [];
        this.deletedDocsID = [];
        this.linkedItemID = [];
        this.deleteLinkedItemID = [];
    }


    /**
     * Getter image
     * @return {string}
     */
	public get image(): string {
		return this._image;
	}

    /**
     * Setter image
     * @param {string} value
     */
	public set image(value: string) {
		this._image = value;
	}


    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Getter isActive
     * @return {boolean}
     */
	public get isActive(): boolean {
		return this._isActive;
	}

    /**
     * Getter isSelected
     * @return {boolean}
     */
	public get isSelected(): boolean {
		return this._isSelected;
	}

    /**
     * Getter groupCodeName
     * @return {string}
     */
	public get groupCodeName(): string {
		return this._groupCodeName;
	}

    /**
     * Getter groupName
     * @return {string}
     */
	public get groupName(): string {
		return this._groupName;
	}

    /**
     * Getter groupCodeId
     * @return {string}
     */
	public get groupCodeId(): string {
		return this._groupCodeId;
	}

    /**
     * Getter formattedCategoryName
     * @return {string}
     */
	public get formattedCategoryName(): string {
		return this._formattedCategoryName;
	}

    /**
     * Getter deletedDocsID
     * @return {any[]}
     */
	public get deletedDocsID(): any[] {
		return this._deletedDocsID;
	}

    /**
     * Getter linkedItemID
     * @return {number[]}
     */
	public get linkedItemID(): number[] {
		return this._linkedItemID;
	}

    /**
     * Getter deleteLinkedItemID
     * @return {number[]}
     */
	public get deleteLinkedItemID(): number[] {
		return this._deleteLinkedItemID;
	}

    /**
     * Getter categoryId
     * @return {number}
     */
	public get categoryId(): number {
		return this._categoryId;
	}

    /**
     * Getter docs
     * @return {any[]}
     */
	public get docs(): any[] {
		return this._docs;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Setter isActive
     * @param {boolean} value
     */
	public set isActive(value: boolean) {
		this._isActive = value;
	}

    /**
     * Setter isSelected
     * @param {boolean} value
     */
	public set isSelected(value: boolean) {
		this._isSelected = value;
	}

    /**
     * Setter groupCodeName
     * @param {string} value
     */
	public set groupCodeName(value: string) {
		this._groupCodeName = value;
	}

    /**
     * Setter groupName
     * @param {string} value
     */
	public set groupName(value: string) {
		this._groupName = value;
	}

    /**
     * Setter groupCodeId
     * @param {string} value
     */
	public set groupCodeId(value: string) {
		this._groupCodeId = value;
	}

    /**
     * Setter formattedCategoryName
     * @param {string} value
     */
	public set formattedCategoryName(value: string) {
		this._formattedCategoryName = value;
	}

    /**
     * Setter deletedDocsID
     * @param {any[]} value
     */
	public set deletedDocsID(value: any[]) {
		this._deletedDocsID = value;
	}

    /**
     * Setter linkedItemID
     * @param {number[]} value
     */
	public set linkedItemID(value: number[]) {
		this._linkedItemID = value;
	}

    /**
     * Setter deleteLinkedItemID
     * @param {number[]} value
     */
	public set deleteLinkedItemID(value: number[]) {
		this._deleteLinkedItemID = value;
	}

    /**
     * Setter categoryId
     * @param {number} value
     */
	public set categoryId(value: number) {
		this._categoryId = value;
	}

    /**
     * Setter docs
     * @param {any[]} value
     */
	public set docs(value: any[]) {
		this._docs = value;
	}


}

export class GroupCodeItem {

    @serializeAs('id')
    @deserializeAs('id')
    private _id: number;

    @serializeAs('itemId')
    @deserializeAs('itemId')
    private _itemId: number;

    @deserializeAs('itemDropdown')
    private _itemDropdown: any[];

    @deserializeAs('displayName')
    private _displayName: string;

    @deserializeAs('formattedName')
    private _formattedName: string;

    @deserializeAs('originalName')
    private _originalName: string;

    @deserializeAs('skuId')
    private _skuId: string;

    @deserializeAs('hsnCode')
    private _hsnCode: string;

    constructor() {
        this.itemDropdown = [];
    }


    /**
     * Getter hsnCode
     * @return {string}
     */
	public get hsnCode(): string {
		return this._hsnCode;
	}

    /**
     * Setter hsnCode
     * @param {string} value
     */
	public set hsnCode(value: string) {
		this._hsnCode = value;
	}
    

    /**
     * Getter id
     * @return {number}
     */
	public get id(): number {
		return this._id;
	}

    /**
     * Setter id
     * @param {number} value
     */
	public set id(value: number) {
		this._id = value;
	}

    /**
     * Getter itemId
     * @return {number}
     */
	public get itemId(): number {
		return this._itemId;
	}

    /**
     * Setter itemId
     * @param {number} value
     */
	public set itemId(value: number) {
		this._itemId = value;
	}

    /**
     * Getter itemDropdown
     * @return {any[]}
     */
	public get itemDropdown(): any[] {
		return this._itemDropdown;
	}

    /**
     * Setter itemDropdown
     * @param {any[]} value
     */
	public set itemDropdown(value: any[]) {
		this._itemDropdown = value;
	}

    /**
     * Getter displayName
     * @return {string}
     */
	public get displayName(): string {
		return this._displayName;
	}

    /**
     * Setter displayName
     * @param {string} value
     */
	public set displayName(value: string) {
		this._displayName = value;
	}


    /**
     * Getter formattedName
     * @return {string}
     */
	public get formattedName(): string {
		return this._formattedName;
	}

    /**
     * Setter formattedName
     * @param {string} value
     */
	public set formattedName(value: string) {
		this._formattedName = value;
	}

    /**
     * Getter originalName
     * @return {string}
     */
	public get originalName(): string {
		return this._originalName;
	}

    /**
     * Setter originalName
     * @param {string} value
     */
	public set originalName(value: string) {
		this._originalName = value;
	}

    /**
     * Getter skuId
     * @return {string}
     */
	public get skuId(): string {
		return this._skuId;
	}

    /**
     * Setter skuId
     * @param {string} value
     */
	public set skuId(value: string) {
		this._skuId = value;
	}

    

}