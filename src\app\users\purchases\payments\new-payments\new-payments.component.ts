import { Component, inject, OnDestroy, OnInit, signal } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { PaymentSave, PaymentDTO } from '@modal/Payment';
import { UtilsService } from '@service/utils.service';
import { combineLatest, Subject, takeUntil, tap } from 'rxjs';

const viewType = {
  add_edit: 'add_edit',
  view: 'view',
} as const

type dropdownData = {
  suppliers: any[],
  bankGroups: any[],
  paymentTypes: any[],
}

@Component({
  selector: 'app-new-payments',
  templateUrl: './new-payments.component.html',
  styleUrls: ['./new-payments.component.css']
})
export class NewPaymentsComponent implements OnInit, OnDestroy {

  utilsService = inject(UtilsService)

  dropdownData: dropdownData = {
    suppliers: [],
    bankGroups: [],
    paymentTypes: [],
  }

  form: FormGroup;
  viewType = '';

  defaultPayment: Partial<PaymentDTO> = {
    paymentImgs: [],
    deletedDocumentID: [],
  };
  paymentObj = signal<PaymentDTO>({} as PaymentDTO)
  private destroy$ = new Subject<void>();

  constructor(private fb: FormBuilder, private route: ActivatedRoute) {

    this.initForm();

    if (this.route.snapshot.paramMap.get('id')) {
      this.viewType = viewType.add_edit;
      this.id.setValue(+this.route.snapshot.paramMap.get('id'));
    }
    if (this.route.snapshot.paramMap.get('viewId')) {
      this.viewType = viewType.view;
      this.id.setValue(+this.route.snapshot.paramMap.get('viewId'));
    }
  }

  ngOnInit() {

    // init
    this.paymentObj.set(this.defaultPayment as PaymentDTO);
    this.getRequiredData();

    //Subscription
    this.calculateAmountINR();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  get formValue(): PaymentDTO {
    return this.form.value;
  }

  get id(): FormControl<number> {
    return this.form.get('id') as FormControl<number>;
  }

  initForm = () => {
    this.form = this.fb.group({
      id: [null],
      supplierId: [null, Validators.compose([Validators.required])],
      paymentDate: [null, Validators.compose([Validators.required])],
      paymentAmountRMB: [null, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.ONLY_GREATER_THAN_ZERO)])],
      conversionRate: [null, Validators.compose([Validators.required, Validators.pattern(this.utilsService.validationService.ONLY_GREATER_THAN_ZERO)])],
      paidBy: [null, Validators.compose([Validators.required])],
      notes: [null],
      bankGroupId: [null, Validators.compose([Validators.required])],
      paymentTypeId: [null, Validators.compose([Validators.required])],
      paymentAmountINR: [{ value: null, disabled: true }],
      credit: [null],
      dueAmount: [null],
    })
  }

  getRequiredData = () => {
    this.utilsService.get(this.utilsService.serverVariableService.PAYMENTS_REQ_DATA).pipe(
      tap((res: any) => {
        if (!this.utilsService.isEmptyObjectOrNullUndefined(res.data)) {
          this.dropdownData.bankGroups = (res.data.bankGroups || [])
          this.dropdownData.paymentTypes = (res.data.paymentTypes || [])
          this.dropdownData.suppliers = (res.data.suppliers || [])
        }
      }),
      takeUntil(this.destroy$)
    ).subscribe()
  }

  calculateAmountINR = (): void => {

    const conversionRate$ = this.form.get('conversionRate').valueChanges;
    const paymentAmountRMB$ = this.form.get('paymentAmountRMB').valueChanges;

    combineLatest([conversionRate$, paymentAmountRMB$]).pipe(
      tap(([conversionRate, paymentAmountRMB]) => {
        if (conversionRate && paymentAmountRMB) {
          const amountINR = Number((conversionRate * paymentAmountRMB).toFixed(2)).toLocaleString('en-IN');
          this.form.get('paymentAmountINR').setValue(amountINR, { emitEvent: false });
        }
      }),
      takeUntil(this.destroy$)
    ).subscribe();
  }

  onSelectAttachments = (event: any): void => {

    let selectedFiles: FileList | null = null;
    if (event.type === 'drop') {
      event.preventDefault();
      selectedFiles = event.dataTransfer?.files;
    }

    if (event.type === 'dragover') {
      event.preventDefault();
    }

    if (event.type === 'change') {
      selectedFiles = event.target.files;
    }

    if (event.type === 'paste') {
      const items = (event.clipboardData.items);
      const dataTransfer = new DataTransfer();

      for (const item of items) {
        if (item.type.indexOf('image') === 0) {
          const blob = item.getAsFile();
          const fileName = blob?.name;
          if (blob !== null && fileName) {
            const fileName = `${Date.now()}-image.${item.type.split('/')[1]}`;
            const fileFromBlob = new File([blob], fileName, { type: item.type });
            dataTransfer.items.add(fileFromBlob);
          }
        }
      }
      selectedFiles = dataTransfer.files;
    }

    const max_file_size = 5242880;

    if (selectedFiles) {
      Array.from(selectedFiles).forEach((file: File, i: number) => {
        const ext = file.name.substr(file.name.lastIndexOf('.') + 1).toLowerCase();

        if (['jpeg', 'png', 'jpg', 'jfif', 'webp', 'avif', 'csv', 'xlsx', 'xlss', 'pdf', 'xls'].includes(ext)) {
          if (file.size > max_file_size) {
            this.utilsService.toasterService.error(this.utilsService.validationService.ATTACHMENT_MAX_FILE_SIZE + "5 MB");
          } else {
            const fileUrl = URL.createObjectURL(file);
            let fileData = null;
            fileData = {
              id: null,
              file: file,
              originalName: file.name,
              formattedName: fileUrl,
            };
            if (this.paymentObj().paymentImgs.length >= 10) {
              this.utilsService.toasterService.error('You have exceeded the file upload limit of 10', '', {
                positionClass: 'toast-top-right',
                closeButton: true,
                timeOut: 10000
              });
              selectedFiles = null;
              return;
            }

            this.paymentObj.update(obj => ({ ...obj, paymentImgs: obj.paymentImgs.concat(fileData) }));
            selectedFiles = null;
          }
        } else {
          this.utilsService.toasterService.error(this.utilsService.validationService.ATTACHMENT_INVALID_EXTENSION);
        }
      });
    }
  }

  removeAttachment = (i: number, file: any) => {
    this.paymentObj.update(obj => ({
      ...obj,
      paymentImgs: obj.paymentImgs.filter((_, index) => index !== i),
      deletedDocumentID: file.id ? [...obj.deletedDocumentID, file.id] : obj.deletedDocumentID
    }))
  }

  openLink = (link: string, newUpload: any) => {
    const filePreview = link ? `${this.utilsService.imgPath}${link}` : null

    if (!this.utilsService.isEmptyObjectOrNullUndefined(newUpload)) {
      window.open(newUpload, "_blank");
      return;
    }

    if (!this.utilsService.isEmptyObjectOrNullUndefined(filePreview)) {
      window.open(filePreview, "_blank");
      return;
    }
  }

  onSave = () => {

    const formData = new FormData();

    if(this.form.invalid){
      this.form.markAllAsTouched();
      return;
    }

    if (this.paymentObj().paymentImgs) {
      for (const file of this.paymentObj().paymentImgs || []) {
        if (file.file) {
          formData.append('paymentImgs', file.file);
        }
      }
    }

    const param: PaymentSave = {
      ...this.formValue,
      deletedDocumentID: this.paymentObj().deletedDocumentID || []
    };
    
    formData.set('requestInfo', JSON.stringify(param));

    this.utilsService.post(this.utilsService.serverVariableService.PAYMENTS_SAVE_EDIT_DELETE, formData, { toast: true }).pipe(
      this.utilsService.skipEmitIfEmpty(),
      tap(() => this.utilsService.redirectTo('/users/purchases/payments')),
      takeUntil(this.destroy$)
    ).subscribe()
  }
}
