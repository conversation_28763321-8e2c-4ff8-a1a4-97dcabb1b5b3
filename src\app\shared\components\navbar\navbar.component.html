<header id="page-topbar">
  <div class="layout-width">
    <div class="navbar-header">
      <div class="navbar-header-left ">

        <button type="button" class="btn btn-menu   btn-trasparent btn-icon" (click)="sidebarToggle()">
          <i class='th th-outline-menu-1'></i>
        </button>

        <form class=" d-none d-md-block" *ngIf="false">
          <div class="form-group mb-0">
            <div class="form-group-icon-start">
              <i class='th th-outline-search-normal-1 icon-broder '></i>
              <input type="text" placeholder="Search here ..." class="form-control">
            </div>
          </div>
        </form>
      </div>

      <div class="navbar-header-right d-flex align-items-center">
        <div class="header-item navbar-btn">
          <button class='btn btn-sm btn-primary btn-icon' *ngIf="false"> <i class='th th-outline-add'></i> </button>

          <button class='btn btn-sm btn-outline-white btn-icon' (click)="toggleFullscreen()">
            <i class="th th-outline-maximize-21"></i>
          </button>

          <div class="dropdown dropdown-notification header-item" *ngIf="false">
            <button type="button" class="btn btn-sm btn-outline-white btn-icon" type="button"
              data-bs-auto-close="outside" data-bs-toggle="dropdown" aria-expanded="false">
              <i class="th-outline-notification"></i>
            </button>

            <div class="dropdown-menu dropdown-menu-end">
              <div class="dropdown-notification-header">
                <div class="notification-header-left">
                  <div class="notification-header-title">
                    <h3>Notifications</h3>
                  </div>
                </div>
                <div class="notification-header-right">
                  <a class="notification-header-link" href="#">Mark All As Read</a>
                </div>
              </div>
              <div class="dropdown-notification-body">
                <div class='nav-tabs-outer nav-tabs-style2'>
                  <nav>
                    <div class="nav nav-tabs" id="nav-tab" role="tablist">
                      <button class="nav-link active" id="nav-high-tab" data-bs-toggle="tab" data-bs-target="#nav-high"
                        type="button" role="tab" aria-controls="nav-high" aria-selected="true">High</button>

                      <button class="nav-link" id="nav-medium-tab" data-bs-toggle="tab" data-bs-target="#nav-medium"
                        type="button" role="tab" aria-controls="nav-medium" aria-selected="false">Medium</button>
                      <button class="nav-link" id="nav-low-tab" data-bs-toggle="tab" data-bs-target="#nav-low"
                        type="button" role="tab" aria-controls="nav-low" aria-selected="false">Low</button>

                    </div>
                  </nav>
                  <div class="tab-content" id="nav-tabContent">
                    <div class="tab-pane fade show active" id="nav-high" role="tabpanel" aria-labelledby="nav-high-tab">
                      <div class="notification-list">
                        <div class="notification-list-wrapper">
                          <div class="notification-list-title">
                            <h6>Today</h6>
                          </div>
                          <div class="notification-list-item">
                            <div class="notification-list-icon" style="background-color: #FCE6FF; color: #CC01FF;">
                              <i class="th th-outline-box"></i>
                            </div>
                            <div class="notification-list-content">
                              <div class="notification-list-text">
                                <p>Item <b>“VO-85656”</b> Purchase breach qty low, Need to oeder now</p>
                              </div>
                            </div>
                            <div class="notification-list-action">
                              <p class="notification-time">1m ago</p>
                              <div class="notification-list-info">
                                <a href="#"><i class="th th-outline-info-circle"></i></a>
                              </div>
                            </div>
                          </div>
                          <div class="notification-list-item">
                            <div class="notification-list-icon" style="background-color: #D2EDE0; color: #0F9D58;">
                              AB
                            </div>
                            <div class="notification-list-content">
                              <div class="notification-list-text">
                                <p>Item <b>“VO-85656”</b> Purchase breach qty low, Need to oeder now</p>
                              </div>
                            </div>
                            <div class="notification-list-action">
                              <p class="notification-time">1m ago</p>
                              <div class="notification-list-info">
                                <a href="#"><i class="th th-outline-info-circle"></i></a>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div class="notification-list-wrapper">
                          <div class="notification-list-title">
                            <h6>This Week</h6>
                          </div>
                          <div class="notification-list-item">
                            <div class="notification-list-icon" style="background-color: #FFECD0; color: #FFA114;">
                              <i class="th th-outline-box"></i>
                            </div>
                            <div class="notification-list-content">
                              <div class="notification-list-text">
                                <p>Item <b>“VO-85656”</b> Purchase breach qty low, Need to oeder now</p>
                              </div>
                            </div>
                            <div class="notification-list-action">
                              <p class="notification-time">1m ago</p>
                              <div class="notification-list-info">
                                <a href="#"><i class="th th-outline-info-circle"></i></a>
                              </div>
                            </div>
                          </div>
                          <div class="notification-list-item">
                            <div class="notification-list-icon" style="background-color: #FFDFDF; color: #FF5D5D;">
                              AB
                            </div>
                            <div class="notification-list-content">
                              <div class="notification-list-text">
                                <p>Item <b>“VO-85656”</b> Purchase breach qty low, Need to oeder now</p>
                              </div>
                            </div>
                            <div class="notification-list-action">
                              <p class="notification-time">1m ago</p>
                              <div class="notification-list-info">
                                <a href="#"><i class="th th-outline-info-circle"></i></a>
                              </div>
                            </div>
                          </div>
                        </div>

                      </div>
                    </div>
                    <div class="tab-pane fade" id="nav-medium" role="tabpanel" aria-labelledby="nav-medium-tab">
                      Medium
                    </div>
                    <div class="tab-pane fade" id="nav-low" role="tabpanel" aria-labelledby="nav-low-tab">
                      Low
                    </div>
                  </div>
                </div>
              </div>
              <div class="dropdown-notification-footer">
                <a href="#"><i class="th th-outline-notification-bing"></i> View All Notification</a>
              </div>

            </div>
          </div>
        </div>
        <div class="dropdown header-item topbar-user">
          <button type="button" class="topbar-user-btn" id="page-header-user-dropdown" data-bs-toggle="dropdown"
            aria-haspopup="true" aria-expanded="false">
            <span class="d-flex align-items-center">
              <img class="rounded-circle header-profile-user"
                [src]="!utilsService.isEmptyObjectOrNullUndefined(utilsService.userProfilePicture) ? (utilsService.imgPath + utilsService.userProfilePicture) : 'assets/images/avatar-default.svg'"
                alt="Valamji" />
              <span class="text-start">
                <span class="d-none d-xl-inline-block  user-name-text">{{utilsService.username}}</span>
                <span class="d-none d-xl-block  user-name-sub-text">{{utilsService.roleName}}</span>
              </span>
            </span>
          </button>
          <div class="dropdown-menu dropdown-menu-end">
            <a class="dropdown-item" [routerLink]="['/users/my-profile']" *ngIf="false">
              <i class="th th-outline-user me-1"></i>
              <span class="align-middle">My Profile</span>
            </a>
            <a class="dropdown-item" [routerLink]="['/users/my-profile']" *ngIf="false">
              <i class="th th-outline-key me-1"></i>
              <span class="align-middle">Change Password</span>
            </a>
            <a class="dropdown-item" [routerLink]="['/users/price-updates']" *ngIf="false">
              <i class="th th-outline-dollar-circle me-1"></i>
              <span class="align-middle">Price Updates</span>
            </a>
            <a class="dropdown-item" [routerLink]="['/users/virtual-sctual-stock']" *ngIf="false">
              <i class="th th-outline-dollar-circle me-1"></i>
              <span class="align-middle">Virtual v/s Actual Stock</span>
            </a>
            <a class="dropdown-item" [routerLink]="['/users/employee-fault']" *ngIf="false">
              <i class="th th-outline-security-user me-1"></i>
              <span class="align-middle">Employee Fault</span>
            </a>
            <a class="dropdown-item" [routerLink]="['/users/extra-time-requests']" *ngIf="false">
              <i class="th th-outline-clock me-1"></i>
              <span class="align-middle">Extra Time Requestss</span>
            </a>
            <a class="dropdown-item" [routerLink]="['/users/find-stock']" *ngIf="false">
              <i class="th th-outline-box me-1"></i>
              <span class="align-middle">Find Stock</span>
            </a>
            <a class="dropdown-item" *ngIf="false">
              <i class="th th-outline-house-2 me-1"></i>
              <span class="align-middle">Warehouse Display</span>
            </a>
            <hr *ngIf="false">

            <a class="dropdown-item text-danger" (click)="openLogoutModal()">
              <i class="th th-outline-logout me-1 "></i>
              <span class="align-middle" data-key="t-logout">Log Out</span>
            </a>
          </div>
        </div>

      </div>
    </div>
  </div>
</header>


<!-- Logout Modal -->
<div class="modal modal-theme modal-confirmation modal-warning-two fade" id="logoutModal" tabindex="-1"
  aria-labelledby="logoutModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <button (click)="logoutModal.hide()" type="button" class="btn-close" data-bs-dismiss="modal"
          aria-label="Close"><i class='th th-close'></i></button>
      </div>
      <div class="modal-body">

        <div class="modal-confirmation-container">
          <div class="modal-confirmation-icon">
            <i class="th th-outline-info-circle"></i>
          </div>
          <div class="modal-confirmation-content">
            <h5>Are you sure?</h5>
            <p>You want to log out of your account</p>
          </div>
        </div>



        <div class="modal-button-group modal-full-width-btn">
          <button (click)="logoutModal.hide()" type="button" class="btn btn-outline-white"
            data-bs-dismiss="modal">Cancel</button>
          <button (click)="confirmLogout()" type="button" class="btn btn-primary btn-icon-text"> <i
              class="th th-outline-tick-circle"></i>
            Yes, Log out!</button>
        </div>
      </div>
    </div>
  </div>
</div>
