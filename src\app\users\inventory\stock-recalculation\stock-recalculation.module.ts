import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { Routes, RouterModule } from "@angular/router";
import { SharedModule } from "src/app/shared/shared.module";
import { CompletedStockRecalculationComponent } from "./completed-stock-recalculation/completed-stock-recalculation.component";
import { NewStockRecalculationComponent } from "./new-stock-recalculation/new-stock-recalculation.component";
import { StockRecalculationComponent } from "./stock-recalculation.component";

const routes: Routes = [
    { path: '', component: StockRecalculationComponent },
]

@NgModule({
    imports: [
        CommonModule,
        SharedModule.forRoot(),
        RouterModule.forChild(routes)
    ],
    declarations: [StockRecalculationComponent, NewStockRecalculationComponent, CompletedStockRecalculationComponent]
})

export class StockRecalculationModule { }
