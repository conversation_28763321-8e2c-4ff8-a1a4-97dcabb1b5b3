import { RouterModule, Routes } from "@angular/router";
import { MismatchComponent } from "./mismatch.component";
import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { SharedModule } from "src/app/shared/shared.module";
import { CreditLimitMismatchComponent } from "./credit-limit-mismatch/credit-limit-mismatch.component";
import { SalePriceMismatchComponent } from "./sale-price-mismatch/sale-price-mismatch.component";
import { WarehouseStockMismatchComponent } from "./warehouse-stock-mismatch/warehouse-stock-mismatch.component";

const routes: Routes = [
    { path: '', component: MismatchComponent },
]

@NgModule({
  declarations: [
    MismatchComponent,
    WarehouseStockMismatchComponent,
    SalePriceMismatchComponent,
    CreditLimitMismatchComponent,
  ],
  imports: [
    CommonModule,
    SharedModule.forRoot(),
    RouterModule.forChild(routes)
  ],
})
export class MismatchModule { }