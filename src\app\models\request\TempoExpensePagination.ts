import { FormControl } from "@angular/forms";
import { serializeAs } from "cerialize";

export class TempoExpensePagination {

    @serializeAs("sortOrder")
    private _sortOrder: string;

    @serializeAs("sortColumn")
    private _sortColumn: string;

    @serializeAs("pageNo")
    private _pageNo: number;

    @serializeAs("pageSize")
    private _pageSize: string;

    @serializeAs("searchText")
    private _searchText: string;

    @serializeAs("priceTo")
    private _priceTo: number;

    @serializeAs("priceFrom")
    private _priceFrom: number;

    @serializeAs('fromDate')
    private _fromDate: string;

    @serializeAs('toDate')
    private _toDate: string;

    private _dateRangeControl: FormControl<any>;

    private _flagForSelectAll: boolean;

    private _totalData: number;

    private _pagination: any;

    constructor() {
        this.pageNo = 1;
        this.pageSize = "100";
        this.flagForSelectAll = false;
        this.searchText = '';
        this.dateRangeControl = new FormControl<any>('');
    }


    /**
     * Getter sortOrder
     * @return {string}
     */
	public get sortOrder(): string {
		return this._sortOrder;
	}

    /**
     * Getter sortColumn
     * @return {string}
     */
	public get sortColumn(): string {
		return this._sortColumn;
	}

    /**
     * Getter pageNo
     * @return {number}
     */
	public get pageNo(): number {
		return this._pageNo;
	}

    /**
     * Getter pageSize
     * @return {string}
     */
	public get pageSize(): string {
		return this._pageSize;
	}

    /**
     * Getter searchText
     * @return {string}
     */
	public get searchText(): string {
		return this._searchText;
	}

    /**
     * Getter priceTo
     * @return {number}
     */
	public get priceTo(): number {
		return this._priceTo;
	}

    /**
     * Getter priceFrom
     * @return {number}
     */
	public get priceFrom(): number {
		return this._priceFrom;
	}

    /**
     * Getter fromDate
     * @return {string}
     */
	public get fromDate(): string {
		return this._fromDate;
	}

    /**
     * Getter toDate
     * @return {string}
     */
	public get toDate(): string {
		return this._toDate;
	}

    /**
     * Getter dateRangeControl
     * @return {FormControl<any>}
     */
	public get dateRangeControl(): FormControl<any> {
		return this._dateRangeControl;
	}

    /**
     * Getter flagForSelectAll
     * @return {boolean}
     */
	public get flagForSelectAll(): boolean {
		return this._flagForSelectAll;
	}

    /**
     * Getter totalData
     * @return {number}
     */
	public get totalData(): number {
		return this._totalData;
	}

    /**
     * Getter pagination
     * @return {any}
     */
	public get pagination(): any {
		return this._pagination;
	}

    /**
     * Setter sortOrder
     * @param {string} value
     */
	public set sortOrder(value: string) {
		this._sortOrder = value;
	}

    /**
     * Setter sortColumn
     * @param {string} value
     */
	public set sortColumn(value: string) {
		this._sortColumn = value;
	}

    /**
     * Setter pageNo
     * @param {number} value
     */
	public set pageNo(value: number) {
		this._pageNo = value;
	}

    /**
     * Setter pageSize
     * @param {string} value
     */
	public set pageSize(value: string) {
		this._pageSize = value;
	}

    /**
     * Setter searchText
     * @param {string} value
     */
	public set searchText(value: string) {
		this._searchText = value;
	}

    /**
     * Setter priceTo
     * @param {number} value
     */
	public set priceTo(value: number) {
		this._priceTo = value;
	}

    /**
     * Setter priceFrom
     * @param {number} value
     */
	public set priceFrom(value: number) {
		this._priceFrom = value;
	}

    /**
     * Setter fromDate
     * @param {string} value
     */
	public set fromDate(value: string) {
		this._fromDate = value;
	}

    /**
     * Setter toDate
     * @param {string} value
     */
	public set toDate(value: string) {
		this._toDate = value;
	}

    /**
     * Setter dateRangeControl
     * @param {FormControl<any>} value
     */
	public set dateRangeControl(value: FormControl<any>) {
		this._dateRangeControl = value;
	}

    /**
     * Setter flagForSelectAll
     * @param {boolean} value
     */
	public set flagForSelectAll(value: boolean) {
		this._flagForSelectAll = value;
	}

    /**
     * Setter totalData
     * @param {number} value
     */
	public set totalData(value: number) {
		this._totalData = value;
	}

    /**
     * Setter pagination
     * @param {any} value
     */
	public set pagination(value: any) {
		this._pagination = value;
	}

    
}